{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\module.slt.2.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\slteditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\definitions.slt.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\dragdropleveldesigner.gen.cpp", "g:\\gamedev\\slt\\source\\slt\\core\\design\\dragdropleveldesigner.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\dragdropleveldesigner.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\enemycharacter.gen.cpp", "g:\\gamedev\\slt\\source\\slt\\core\\enemies\\enemycharacter.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\character.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\charactermovementreplication.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\serialization\\irisobjectreferencepackagemap.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\irisobjectreferencepackagemap.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\charactermovementreplication.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\gameframework\\rootmotionsource.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\rootmotionsource.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\character.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\enemycharacter.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\interactable.gen.cpp", "g:\\gamedev\\slt\\source\\slt\\core\\interfaces\\interactable.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\interactable.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}