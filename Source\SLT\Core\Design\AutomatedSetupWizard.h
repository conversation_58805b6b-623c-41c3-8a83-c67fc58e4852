#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "AutomatedSetupWizard.generated.h"

class UWorld;
class AActor;
class UActorComponent;

UENUM(BlueprintType)
enum class ESetupWizardStep : uint8
{
	Welcome			UMETA(DisplayName = "Welcome"),
	ProjectSetup	UMETA(DisplayName = "Project Setup"),
	LevelSetup		UMETA(DisplayName = "Level Setup"),
	PlayerSetup		UMETA(DisplayName = "Player Setup"),
	InventorySetup	UMETA(DisplayName = "Inventory Setup"),
	WeaponSetup		UMETA(DisplayName = "Weapon Setup"),
	EnemySetup		UMETA(DisplayName = "Enemy Setup"),
	PuzzleSetup		UMETA(DisplayName = "Puzzle Setup"),
	UISetup			UMETA(DisplayName = "UI Setup"),
	Testing			UMETA(DisplayName = "Testing"),
	Finalization	UMETA(DisplayName = "Finalization"),
	Complete		UMETA(DisplayName = "Complete")
};

UENUM(BlueprintType)
enum class ESetupComplexity : uint8
{
	QuickStart		UMETA(DisplayName = "Quick Start (5 min)"),
	Basic			UMETA(DisplayName = "Basic Setup (15 min)"),
	Standard		UMETA(DisplayName = "Standard Setup (30 min)"),
	Advanced		UMETA(DisplayName = "Advanced Setup (60 min)"),
	Expert			UMETA(DisplayName = "Expert Setup (120+ min)")
};

USTRUCT(BlueprintType)
struct FSetupWizardConfiguration
{
	GENERATED_BODY()

	FSetupWizardConfiguration()
	{
		SetupComplexity = ESetupComplexity::Basic;
		bIncludeInventorySystem = true;
		bIncludeWeaponSystem = true;
		bIncludeEnemyAI = true;
		bIncludePuzzleSystem = true;
		bIncludeResourceManagement = true;
		bIncludeStatistics = true;
		bIncludeLoadingSystem = true;
		bSetupSampleContent = true;
		bCreateTestLevel = true;
		bSetupInputMappings = true;
		bConfigureUI = true;
		bEnableDebugTools = true;
		ProjectName = TEXT("MyRE4Game");
		PlayerCharacterClass = nullptr;
	}

	// Basic configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	ESetupComplexity SetupComplexity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	FString ProjectName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	TSubclassOf<APawn> PlayerCharacterClass;

	// System inclusion flags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeInventorySystem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeWeaponSystem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeEnemyAI;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludePuzzleSystem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeResourceManagement;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeStatistics;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Systems")
	bool bIncludeLoadingSystem;

	// Content setup flags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
	bool bSetupSampleContent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
	bool bCreateTestLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
	bool bSetupInputMappings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
	bool bConfigureUI;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Content")
	bool bEnableDebugTools;

	// Advanced settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	TMap<FString, FString> CustomSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	TArray<FString> AdditionalPlugins;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	FGameplayTagContainer FeatureTags;
};

USTRUCT(BlueprintType)
struct FSetupWizardStepData
{
	GENERATED_BODY()

	FSetupWizardStepData()
	{
		StepType = ESetupWizardStep::Welcome;
		StepName = FText::GetEmpty();
		bIsRequired = true;
		bIsCompleted = false;
		EstimatedTime = 1.0f;
		Progress = 0.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	ESetupWizardStep StepType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	FText StepName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	FText StepDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	bool bIsRequired;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	bool bIsCompleted;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	float EstimatedTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	float Progress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	TArray<FText> Instructions;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	TArray<FText> Tips;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	TArray<FString> RequiredAssets;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	TMap<FString, FString> StepParameters;
};

USTRUCT(BlueprintType)
struct FSetupValidationResult
{
	GENERATED_BODY()

	FSetupValidationResult()
	{
		bIsValid = true;
		ErrorCount = 0;
		WarningCount = 0;
		CompletionPercentage = 0.0f;
	}

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	bool bIsValid;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	int32 ErrorCount;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	int32 WarningCount;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	float CompletionPercentage;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	TArray<FText> ErrorMessages;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	TArray<FText> WarningMessages;

	UPROPERTY(BlueprintReadOnly, Category = "Validation")
	TArray<FText> SuccessMessages;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSetupStepCompleted, ESetupWizardStep, CompletedStep, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnSetupWizardCompleted, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSetupValidated, const FSetupValidationResult&, Result, ESetupWizardStep, CurrentStep);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSetupError, const FString&, ErrorMessage, ESetupWizardStep, FailedStep);

UCLASS(BlueprintType, Blueprintable)
class SLT_API UAutomatedSetupWizard : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UAutomatedSetupWizard();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Current wizard state
	UPROPERTY(BlueprintReadOnly, Category = "Wizard State")
	ESetupWizardStep CurrentStep = ESetupWizardStep::Welcome;

	UPROPERTY(BlueprintReadOnly, Category = "Wizard State")
	FSetupWizardConfiguration WizardConfiguration;

	UPROPERTY(BlueprintReadOnly, Category = "Wizard State")
	bool bIsWizardActive = false;

	UPROPERTY(BlueprintReadOnly, Category = "Wizard State")
	float OverallProgress = 0.0f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSetupStepCompleted OnSetupStepCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSetupWizardCompleted OnSetupWizardCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSetupValidated OnSetupValidated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSetupError OnSetupError;

	// Main wizard functions
	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void StartSetupWizard(const FSetupWizardConfiguration& Configuration);

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void StopSetupWizard();

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void NextStep();

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void PreviousStep();

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void GoToStep(ESetupWizardStep TargetStep);

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void CompleteCurrentStep();

	UFUNCTION(BlueprintCallable, Category = "Setup Wizard")
	void SkipCurrentStep();

	// Automated setup functions
	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void RunQuickSetup();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void RunFullAutomatedSetup();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupInventorySystem();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupWeaponSystem();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupEnemyAI();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupPuzzleSystem();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupPlayerCharacter();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupInputMappings();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void SetupUI();

	UFUNCTION(BlueprintCallable, Category = "Automated Setup")
	void CreateSampleContent();

	// Validation functions
	UFUNCTION(BlueprintCallable, Category = "Validation")
	FSetupValidationResult ValidateCurrentSetup();

	UFUNCTION(BlueprintCallable, Category = "Validation")
	bool ValidateStep(ESetupWizardStep Step);

	UFUNCTION(BlueprintCallable, Category = "Validation")
	void AutoFixCommonIssues();

	// Information functions
	UFUNCTION(BlueprintCallable, Category = "Information")
	FSetupWizardStepData GetCurrentStepData() const;

	UFUNCTION(BlueprintCallable, Category = "Information")
	TArray<FSetupWizardStepData> GetAllSteps() const;

	UFUNCTION(BlueprintCallable, Category = "Information")
	float GetEstimatedRemainingTime() const;

	UFUNCTION(BlueprintCallable, Category = "Information")
	TArray<FText> GetCurrentStepInstructions() const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SaveWizardProgress();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool LoadWizardProgress();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ResetWizardProgress();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ExportSetupConfiguration(const FString& FilePath) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool ImportSetupConfiguration(const FString& FilePath);

protected:
	// Internal state
	UPROPERTY()
	TArray<FSetupWizardStepData> WizardSteps;

	UPROPERTY()
	TMap<ESetupWizardStep, bool> CompletedSteps;

	UPROPERTY()
	float WizardStartTime;

	// Internal functions
	void InitializeWizardSteps();
	void UpdateProgress();
	bool ExecuteStepSetup(ESetupWizardStep Step);
	void CreateDefaultDataTables();
	void SetupProjectSettings();
	void CreateTestLevel();
	AActor* SpawnActorWithComponents(TSubclassOf<AActor> ActorClass, const FVector& Location);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnWizardStarted(const FSetupWizardConfiguration& Configuration);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStepChanged(ESetupWizardStep OldStep, ESetupWizardStep NewStep);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnProgressUpdated(float NewProgress, ESetupWizardStep CurrentStep);
};
