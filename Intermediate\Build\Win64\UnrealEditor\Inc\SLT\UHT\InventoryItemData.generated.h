// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InventorySystem/Data/InventoryItemData.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef SLT_InventoryItemData_generated_h
#error "InventoryItemData.generated.h already included, missing '#pragma once' in InventoryItemData.h"
#endif
#define SLT_InventoryItemData_generated_h

#define FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_37_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FInventoryItemData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FInventoryItemData>();

#define FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_139_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FInventorySlot_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FInventorySlot>();

#define FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_182_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FInventorySlotSaveData>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h


#define FOREACH_ENUM_EITEMTYPE(op) \
	op(EItemType::None) \
	op(EItemType::Weapon) \
	op(EItemType::Ammo) \
	op(EItemType::Consumable) \
	op(EItemType::KeyItem) \
	op(EItemType::Treasure) \
	op(EItemType::Material) 

enum class EItemType : uint8;
template<> struct TIsUEnumClass<EItemType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EItemType>();

#define FOREACH_ENUM_EITEMRARITY(op) \
	op(EItemRarity::Common) \
	op(EItemRarity::Uncommon) \
	op(EItemRarity::Rare) \
	op(EItemRarity::Epic) \
	op(EItemRarity::Legendary) 

enum class EItemRarity : uint8;
template<> struct TIsUEnumClass<EItemRarity> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EItemRarity>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
