// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Interfaces/Interactable.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInteractable() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UInteractable();
SLT_API UClass* Z_Construct_UClass_UInteractable_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EInteractionType();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInteractionData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EInteractionType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EInteractionType;
static UEnum* EInteractionType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EInteractionType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EInteractionType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EInteractionType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EInteractionType"));
	}
	return Z_Registration_Info_UEnum_EInteractionType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EInteractionType>()
{
	return EInteractionType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EInteractionType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Activate.DisplayName", "Activate" },
		{ "Activate.Name", "EInteractionType::Activate" },
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EInteractionType::Custom" },
		{ "Examine.DisplayName", "Examine" },
		{ "Examine.Name", "EInteractionType::Examine" },
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EInteractionType::None" },
		{ "Open.DisplayName", "Open" },
		{ "Open.Name", "EInteractionType::Open" },
		{ "Pickup.DisplayName", "Pickup" },
		{ "Pickup.Name", "EInteractionType::Pickup" },
		{ "Talk.DisplayName", "Talk" },
		{ "Talk.Name", "EInteractionType::Talk" },
		{ "Use.DisplayName", "Use" },
		{ "Use.Name", "EInteractionType::Use" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EInteractionType::None", (int64)EInteractionType::None },
		{ "EInteractionType::Pickup", (int64)EInteractionType::Pickup },
		{ "EInteractionType::Use", (int64)EInteractionType::Use },
		{ "EInteractionType::Examine", (int64)EInteractionType::Examine },
		{ "EInteractionType::Open", (int64)EInteractionType::Open },
		{ "EInteractionType::Activate", (int64)EInteractionType::Activate },
		{ "EInteractionType::Talk", (int64)EInteractionType::Talk },
		{ "EInteractionType::Custom", (int64)EInteractionType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EInteractionType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EInteractionType",
	"EInteractionType",
	Z_Construct_UEnum_SLT_EInteractionType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EInteractionType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EInteractionType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EInteractionType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EInteractionType()
{
	if (!Z_Registration_Info_UEnum_EInteractionType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EInteractionType.InnerSingleton, Z_Construct_UEnum_SLT_EInteractionType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EInteractionType.InnerSingleton;
}
// End Enum EInteractionType

// Begin ScriptStruct FInteractionData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_InteractionData;
class UScriptStruct* FInteractionData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_InteractionData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_InteractionData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FInteractionData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("InteractionData"));
	}
	return Z_Registration_Info_UScriptStruct_InteractionData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FInteractionData>()
{
	return FInteractionData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FInteractionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionType_MetaData[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Type of interaction\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type of interaction" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionText_MetaData[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Text to display in interaction prompt\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Text to display in interaction prompt" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionDuration_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How long the interaction takes (0 = instant)\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How long the interaction takes (0 = instant)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresItem_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Does this interaction require a specific item?\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Does this interaction require a specific item?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredItemID_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Required item ID (if bRequiresItem is true)\n" },
#endif
		{ "EditCondition", "bRequiresItem" },
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required item ID (if bRequiresItem is true)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Required gameplay tags\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required gameplay tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanInteractMultipleTimes_MetaData[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Can this object be interacted with multiple times?\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can this object be interacted with multiple times?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionRange_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum interaction range\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum interaction range" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInteractionPrompt_MetaData[] = {
		{ "Category", "UI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Should we show the interaction prompt UI?\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Should we show the interaction prompt UI?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom interaction data\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom interaction data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InteractionType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InteractionType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_InteractionText;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionDuration;
	static void NewProp_bRequiresItem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresItem;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredItemID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static void NewProp_bCanInteractMultipleTimes_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanInteractMultipleTimes;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionRange;
	static void NewProp_bShowInteractionPrompt_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInteractionPrompt;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FInteractionData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionType = { "InteractionType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, InteractionType), Z_Construct_UEnum_SLT_EInteractionType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionType_MetaData), NewProp_InteractionType_MetaData) }; // 6992440
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionText = { "InteractionText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, InteractionText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionText_MetaData), NewProp_InteractionText_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionDuration = { "InteractionDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, InteractionDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionDuration_MetaData), NewProp_InteractionDuration_MetaData) };
void Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bRequiresItem_SetBit(void* Obj)
{
	((FInteractionData*)Obj)->bRequiresItem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bRequiresItem = { "bRequiresItem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInteractionData), &Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bRequiresItem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresItem_MetaData), NewProp_bRequiresItem_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_RequiredItemID = { "RequiredItemID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, RequiredItemID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredItemID_MetaData), NewProp_RequiredItemID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
void Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bCanInteractMultipleTimes_SetBit(void* Obj)
{
	((FInteractionData*)Obj)->bCanInteractMultipleTimes = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bCanInteractMultipleTimes = { "bCanInteractMultipleTimes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInteractionData), &Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bCanInteractMultipleTimes_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanInteractMultipleTimes_MetaData), NewProp_bCanInteractMultipleTimes_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionRange = { "InteractionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, InteractionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionRange_MetaData), NewProp_InteractionRange_MetaData) };
void Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bShowInteractionPrompt_SetBit(void* Obj)
{
	((FInteractionData*)Obj)->bShowInteractionPrompt = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bShowInteractionPrompt = { "bShowInteractionPrompt", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInteractionData), &Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bShowInteractionPrompt_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInteractionPrompt_MetaData), NewProp_bShowInteractionPrompt_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInteractionData, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FInteractionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bRequiresItem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_RequiredItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bCanInteractMultipleTimes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_InteractionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_bShowInteractionPrompt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInteractionData_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInteractionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FInteractionData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"InteractionData",
	Z_Construct_UScriptStruct_FInteractionData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInteractionData_Statics::PropPointers),
	sizeof(FInteractionData),
	alignof(FInteractionData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInteractionData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FInteractionData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FInteractionData()
{
	if (!Z_Registration_Info_UScriptStruct_InteractionData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_InteractionData.InnerSingleton, Z_Construct_UScriptStruct_FInteractionData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_InteractionData.InnerSingleton;
}
// End ScriptStruct FInteractionData

// Begin Interface UInteractable Function CanInteract
struct Interactable_eventCanInteract_Parms
{
	APawn* InteractingPawn;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	Interactable_eventCanInteract_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInteractable::CanInteract(APawn* InteractingPawn) const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CanInteract instead.");
	Interactable_eventCanInteract_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInteractable_CanInteract = FName(TEXT("CanInteract"));
bool IInteractable::Execute_CanInteract(const UObject* O, APawn* InteractingPawn)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventCanInteract_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_CanInteract);
	if (Func)
	{
		Parms.InteractingPawn=InteractingPawn;
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		Parms.ReturnValue = I->CanInteract_Implementation(InteractingPawn);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInteractable_CanInteract_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Check if this object can be interacted with by the given pawn\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if this object can be interacted with by the given pawn" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventCanInteract_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((Interactable_eventCanInteract_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Interactable_eventCanInteract_Parms), &Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_CanInteract_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_CanInteract_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CanInteract_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_CanInteract_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "CanInteract", nullptr, nullptr, Z_Construct_UFunction_UInteractable_CanInteract_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CanInteract_Statics::PropPointers), sizeof(Interactable_eventCanInteract_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CanInteract_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_CanInteract_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventCanInteract_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_CanInteract()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_CanInteract_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execCanInteract)
{
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanInteract_Implementation(Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Interface UInteractable Function CanInteract

// Begin Interface UInteractable Function CheckInteractionRequirements
struct Interactable_eventCheckInteractionRequirements_Parms
{
	APawn* InteractingPawn;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	Interactable_eventCheckInteractionRequirements_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInteractable::CheckInteractionRequirements(APawn* InteractingPawn) const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CheckInteractionRequirements instead.");
	Interactable_eventCheckInteractionRequirements_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInteractable_CheckInteractionRequirements = FName(TEXT("CheckInteractionRequirements"));
bool IInteractable::Execute_CheckInteractionRequirements(const UObject* O, APawn* InteractingPawn)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventCheckInteractionRequirements_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_CheckInteractionRequirements);
	if (Func)
	{
		Parms.InteractingPawn=InteractingPawn;
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		Parms.ReturnValue = I->CheckInteractionRequirements_Implementation(InteractingPawn);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called to check if interaction requirements are met\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to check if interaction requirements are met" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventCheckInteractionRequirements_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((Interactable_eventCheckInteractionRequirements_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(Interactable_eventCheckInteractionRequirements_Parms), &Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "CheckInteractionRequirements", nullptr, nullptr, Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::PropPointers), sizeof(Interactable_eventCheckInteractionRequirements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventCheckInteractionRequirements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_CheckInteractionRequirements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_CheckInteractionRequirements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execCheckInteractionRequirements)
{
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CheckInteractionRequirements_Implementation(Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Interface UInteractable Function CheckInteractionRequirements

// Begin Interface UInteractable Function GetInteractionData
struct Interactable_eventGetInteractionData_Parms
{
	FInteractionData ReturnValue;
};
FInteractionData IInteractable::GetInteractionData() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetInteractionData instead.");
	Interactable_eventGetInteractionData_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInteractable_GetInteractionData = FName(TEXT("GetInteractionData"));
FInteractionData IInteractable::Execute_GetInteractionData(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventGetInteractionData_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_GetInteractionData);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		Parms.ReturnValue = I->GetInteractionData_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInteractable_GetInteractionData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get interaction data for this object\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get interaction data for this object" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventGetInteractionData_Parms, ReturnValue), Z_Construct_UScriptStruct_FInteractionData, METADATA_PARAMS(0, nullptr) }; // 3029509838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "GetInteractionData", nullptr, nullptr, Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::PropPointers), sizeof(Interactable_eventGetInteractionData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventGetInteractionData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_GetInteractionData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_GetInteractionData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execGetInteractionData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FInteractionData*)Z_Param__Result=P_THIS->GetInteractionData_Implementation();
	P_NATIVE_END;
}
// End Interface UInteractable Function GetInteractionData

// Begin Interface UInteractable Function GetInteractionLocation
struct Interactable_eventGetInteractionLocation_Parms
{
	FVector ReturnValue;

	/** Constructor, initializes return property only **/
	Interactable_eventGetInteractionLocation_Parms()
		: ReturnValue(ForceInit)
	{
	}
};
FVector IInteractable::GetInteractionLocation() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetInteractionLocation instead.");
	Interactable_eventGetInteractionLocation_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInteractable_GetInteractionLocation = FName(TEXT("GetInteractionLocation"));
FVector IInteractable::Execute_GetInteractionLocation(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventGetInteractionLocation_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_GetInteractionLocation);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		Parms.ReturnValue = I->GetInteractionLocation_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get the world location for interaction prompts\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get the world location for interaction prompts" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventGetInteractionLocation_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "GetInteractionLocation", nullptr, nullptr, Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::PropPointers), sizeof(Interactable_eventGetInteractionLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C820C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventGetInteractionLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_GetInteractionLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_GetInteractionLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execGetInteractionLocation)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->GetInteractionLocation_Implementation();
	P_NATIVE_END;
}
// End Interface UInteractable Function GetInteractionLocation

// Begin Interface UInteractable Function OnInteractionCancel
struct Interactable_eventOnInteractionCancel_Parms
{
	APawn* InteractingPawn;
};
void IInteractable::OnInteractionCancel(APawn* InteractingPawn)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_OnInteractionCancel instead.");
}
static FName NAME_UInteractable_OnInteractionCancel = FName(TEXT("OnInteractionCancel"));
void IInteractable::Execute_OnInteractionCancel(UObject* O, APawn* InteractingPawn)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventOnInteractionCancel_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_OnInteractionCancel);
	if (Func)
	{
		Parms.InteractingPawn=InteractingPawn;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		I->OnInteractionCancel_Implementation(InteractingPawn);
	}
}
struct Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when interaction is cancelled or interrupted\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when interaction is cancelled or interrupted" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventOnInteractionCancel_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "OnInteractionCancel", nullptr, nullptr, Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::PropPointers), sizeof(Interactable_eventOnInteractionCancel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventOnInteractionCancel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_OnInteractionCancel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_OnInteractionCancel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execOnInteractionCancel)
{
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionCancel_Implementation(Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Interface UInteractable Function OnInteractionCancel

// Begin Interface UInteractable Function OnInteractionComplete
struct Interactable_eventOnInteractionComplete_Parms
{
	APawn* InteractingPawn;
};
void IInteractable::OnInteractionComplete(APawn* InteractingPawn)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_OnInteractionComplete instead.");
}
static FName NAME_UInteractable_OnInteractionComplete = FName(TEXT("OnInteractionComplete"));
void IInteractable::Execute_OnInteractionComplete(UObject* O, APawn* InteractingPawn)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventOnInteractionComplete_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_OnInteractionComplete);
	if (Func)
	{
		Parms.InteractingPawn=InteractingPawn;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		I->OnInteractionComplete_Implementation(InteractingPawn);
	}
}
struct Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when interaction completes successfully\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when interaction completes successfully" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventOnInteractionComplete_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "OnInteractionComplete", nullptr, nullptr, Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::PropPointers), sizeof(Interactable_eventOnInteractionComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventOnInteractionComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_OnInteractionComplete()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_OnInteractionComplete_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execOnInteractionComplete)
{
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionComplete_Implementation(Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Interface UInteractable Function OnInteractionComplete

// Begin Interface UInteractable Function OnInteractionStart
struct Interactable_eventOnInteractionStart_Parms
{
	APawn* InteractingPawn;
};
void IInteractable::OnInteractionStart(APawn* InteractingPawn)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_OnInteractionStart instead.");
}
static FName NAME_UInteractable_OnInteractionStart = FName(TEXT("OnInteractionStart"));
void IInteractable::Execute_OnInteractionStart(UObject* O, APawn* InteractingPawn)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInteractable::StaticClass()));
	Interactable_eventOnInteractionStart_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInteractable_OnInteractionStart);
	if (Func)
	{
		Parms.InteractingPawn=InteractingPawn;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInteractable*)(O->GetNativeInterfaceAddress(UInteractable::StaticClass())))
	{
		I->OnInteractionStart_Implementation(InteractingPawn);
	}
}
struct Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when interaction begins\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when interaction begins" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(Interactable_eventOnInteractionStart_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractable, nullptr, "OnInteractionStart", nullptr, nullptr, Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::PropPointers), sizeof(Interactable_eventOnInteractionStart_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::Function_MetaDataParams) };
static_assert(sizeof(Interactable_eventOnInteractionStart_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractable_OnInteractionStart()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractable_OnInteractionStart_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInteractable::execOnInteractionStart)
{
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnInteractionStart_Implementation(Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Interface UInteractable Function OnInteractionStart

// Begin Interface UInteractable
void UInteractable::StaticRegisterNativesUInteractable()
{
	UClass* Class = UInteractable::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanInteract", &IInteractable::execCanInteract },
		{ "CheckInteractionRequirements", &IInteractable::execCheckInteractionRequirements },
		{ "GetInteractionData", &IInteractable::execGetInteractionData },
		{ "GetInteractionLocation", &IInteractable::execGetInteractionLocation },
		{ "OnInteractionCancel", &IInteractable::execOnInteractionCancel },
		{ "OnInteractionComplete", &IInteractable::execOnInteractionComplete },
		{ "OnInteractionStart", &IInteractable::execOnInteractionStart },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UInteractable);
UClass* Z_Construct_UClass_UInteractable_NoRegister()
{
	return UInteractable::StaticClass();
}
struct Z_Construct_UClass_UInteractable_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Interfaces/Interactable.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UInteractable_CanInteract, "CanInteract" }, // 3340172220
		{ &Z_Construct_UFunction_UInteractable_CheckInteractionRequirements, "CheckInteractionRequirements" }, // 2611046540
		{ &Z_Construct_UFunction_UInteractable_GetInteractionData, "GetInteractionData" }, // 258917876
		{ &Z_Construct_UFunction_UInteractable_GetInteractionLocation, "GetInteractionLocation" }, // 858266435
		{ &Z_Construct_UFunction_UInteractable_OnInteractionCancel, "OnInteractionCancel" }, // 153812604
		{ &Z_Construct_UFunction_UInteractable_OnInteractionComplete, "OnInteractionComplete" }, // 3323918686
		{ &Z_Construct_UFunction_UInteractable_OnInteractionStart, "OnInteractionStart" }, // 1913204323
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IInteractable>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UInteractable_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInteractable_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UInteractable_Statics::ClassParams = {
	&UInteractable::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UInteractable_Statics::Class_MetaDataParams), Z_Construct_UClass_UInteractable_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UInteractable()
{
	if (!Z_Registration_Info_UClass_UInteractable.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UInteractable.OuterSingleton, Z_Construct_UClass_UInteractable_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UInteractable.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UInteractable>()
{
	return UInteractable::StaticClass();
}
UInteractable::UInteractable(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UInteractable);
UInteractable::~UInteractable() {}
// End Interface UInteractable

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EInteractionType_StaticEnum, TEXT("EInteractionType"), &Z_Registration_Info_UEnum_EInteractionType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 6992440U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FInteractionData::StaticStruct, Z_Construct_UScriptStruct_FInteractionData_Statics::NewStructOps, TEXT("InteractionData"), &Z_Registration_Info_UScriptStruct_InteractionData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FInteractionData), 3029509838U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UInteractable, UInteractable::StaticClass, TEXT("UInteractable"), &Z_Registration_Info_UClass_UInteractable, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UInteractable), 206469426U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_958202661(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
