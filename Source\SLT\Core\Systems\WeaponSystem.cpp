#include "WeaponSystem.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"
#include "Core/Systems/ResourceManager.h"
#include "GameFramework/Actor.h"

UWeaponComponent::UWeaponComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	CurrentWeapon = NAME_None;
}

void UWeaponComponent::BeginPlay()
{
	Super::BeginPlay();
	LoadWeaponDatabase();
}

void UWeaponComponent::LoadWeaponDatabase()
{
	if (WeaponDatabase.IsValid())
	{
		CachedWeaponDatabase = WeaponDatabase.LoadSynchronous();
	}
}

bool UWeaponComponent::AddWeapon(FName WeaponID)
{
	if (!CachedWeaponDatabase || WeaponID == NAME_None)
	{
		return false;
	}

	// Check if we already have this weapon
	if (OwnedWeapons.Contains(WeaponID))
	{
		return false;
	}

	// Get weapon data from database
	FWeaponData* WeaponData = CachedWeaponDatabase->FindRow<FWeaponData>(WeaponID, TEXT("AddWeapon"));
	if (!WeaponData)
	{
		return false;
	}

	// Add to owned weapons
	OwnedWeapons.Add(WeaponID, *WeaponData);
	return true;
}

bool UWeaponComponent::RemoveWeapon(FName WeaponID)
{
	if (!OwnedWeapons.Contains(WeaponID))
	{
		return false;
	}

	// Unequip if currently equipped
	for (auto& EquippedPair : EquippedWeapons)
	{
		if (EquippedPair.Value == WeaponID)
		{
			UnequipWeapon(EquippedPair.Key);
		}
	}

	// Remove from owned weapons
	OwnedWeapons.Remove(WeaponID);
	return true;
}

bool UWeaponComponent::HasWeapon(FName WeaponID) const
{
	return OwnedWeapons.Contains(WeaponID);
}

FWeaponData UWeaponComponent::GetWeaponData(FName WeaponID) const
{
	if (const FWeaponData* Data = OwnedWeapons.Find(WeaponID))
	{
		return *Data;
	}
	return FWeaponData();
}

bool UWeaponComponent::EquipWeapon(FName WeaponID, const FString& SlotName)
{
	if (!HasWeapon(WeaponID))
	{
		return false;
	}

	// Unequip current weapon in this slot
	if (EquippedWeapons.Contains(SlotName))
	{
		FName OldWeapon = EquippedWeapons[SlotName];
		if (OldWeapon != NAME_None)
		{
			OnWeaponEquipped.Broadcast(OldWeapon, false);
		}
	}

	// Equip new weapon
	EquippedWeapons.Add(SlotName, WeaponID);
	CurrentWeapon = WeaponID;
	OnWeaponEquipped.Broadcast(WeaponID, true);
	
	return true;
}

bool UWeaponComponent::UnequipWeapon(const FString& SlotName)
{
	if (FName* WeaponID = EquippedWeapons.Find(SlotName))
	{
		if (*WeaponID != NAME_None)
		{
			OnWeaponEquipped.Broadcast(*WeaponID, false);
			EquippedWeapons.Add(SlotName, NAME_None);
			
			if (CurrentWeapon == *WeaponID)
			{
				CurrentWeapon = NAME_None;
			}
			
			return true;
		}
	}
	return false;
}

FName UWeaponComponent::GetEquippedWeapon(const FString& SlotName) const
{
	if (const FName* WeaponID = EquippedWeapons.Find(SlotName))
	{
		return *WeaponID;
	}
	return NAME_None;
}

bool UWeaponComponent::SwitchToWeapon(FName WeaponID)
{
	// Check if weapon is equipped
	for (const auto& EquippedPair : EquippedWeapons)
	{
		if (EquippedPair.Value == WeaponID)
		{
			CurrentWeapon = WeaponID;
			return true;
		}
	}
	return false;
}

bool UWeaponComponent::CanFire() const
{
	if (CurrentWeapon == NAME_None)
	{
		return false;
	}

	const FWeaponData* WeaponData = OwnedWeapons.Find(CurrentWeapon);
	if (!WeaponData)
	{
		return false;
	}

	// Check ammo
	if (WeaponData->CurrentAmmo <= 0)
	{
		return false;
	}

	// Check weapon condition
	if (WeaponData->Condition == EWeaponCondition::Broken)
	{
		return false;
	}

	return true;
}

bool UWeaponComponent::Fire()
{
	if (!CanFire())
	{
		return false;
	}

	FWeaponData* WeaponData = OwnedWeapons.Find(CurrentWeapon);
	if (!WeaponData)
	{
		return false;
	}

	// Consume ammo
	WeaponData->CurrentAmmo--;
	
	// Damage weapon slightly
	DamageWeapon(CurrentWeapon, 0.1f);

	// Broadcast events
	OnWeaponFired.Broadcast(CurrentWeapon, WeaponData->CurrentAmmo);
	OnAmmoChanged(CurrentWeapon, WeaponData->CurrentAmmo, WeaponData->BaseStats.MagazineSize);

	return true;
}

bool UWeaponComponent::Reload()
{
	if (CurrentWeapon == NAME_None)
	{
		return false;
	}

	FWeaponData* WeaponData = OwnedWeapons.Find(CurrentWeapon);
	if (!WeaponData)
	{
		return false;
	}

	// Check if we need to reload
	if (WeaponData->CurrentAmmo >= WeaponData->BaseStats.MagazineSize)
	{
		return false;
	}

	// Calculate ammo needed
	int32 AmmoNeeded = WeaponData->BaseStats.MagazineSize - WeaponData->CurrentAmmo;

	// Try to consume ammo from inventory/resource manager
	if (UResourceManager* ResourceManager = GetOwner()->FindComponentByClass<UResourceManager>())
	{
		FName AmmoResourceID = FName(*FString::Printf(TEXT("Ammo_%s"), *UEnum::GetValueAsString(WeaponData->AmmoType)));
		float AvailableAmmo = ResourceManager->GetResourceAmount(AmmoResourceID);
		
		if (AvailableAmmo < AmmoNeeded)
		{
			AmmoNeeded = FMath::FloorToInt(AvailableAmmo);
		}

		if (AmmoNeeded > 0)
		{
			ResourceManager->ModifyResource(AmmoResourceID, -AmmoNeeded);
			WeaponData->CurrentAmmo += AmmoNeeded;
			
			OnWeaponReloaded.Broadcast(CurrentWeapon, WeaponData->CurrentAmmo);
			OnAmmoChanged(CurrentWeapon, WeaponData->CurrentAmmo, WeaponData->BaseStats.MagazineSize);
			return true;
		}
	}

	return false;
}

float UWeaponComponent::GetDamage() const
{
	if (CurrentWeapon == NAME_None)
	{
		return 0.0f;
	}

	const FWeaponData* WeaponData = OwnedWeapons.Find(CurrentWeapon);
	if (!WeaponData)
	{
		return 0.0f;
	}

	FWeaponStats ModifiedStats = CalculateModifiedStats(*WeaponData);
	return ModifiedStats.BaseDamage;
}

float UWeaponComponent::GetAccuracy() const
{
	if (CurrentWeapon == NAME_None)
	{
		return 0.0f;
	}

	const FWeaponData* WeaponData = OwnedWeapons.Find(CurrentWeapon);
	if (!WeaponData)
	{
		return 0.0f;
	}

	FWeaponStats ModifiedStats = CalculateModifiedStats(*WeaponData);
	return ModifiedStats.Accuracy;
}

bool UWeaponComponent::CanUpgradeWeapon(FName WeaponID, FName UpgradeID) const
{
	const FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData || !WeaponData->bCanBeUpgraded)
	{
		return false;
	}

	// Find the upgrade
	const FWeaponUpgrade* Upgrade = WeaponData->AvailableUpgrades.FindByPredicate([UpgradeID](const FWeaponUpgrade& Up)
	{
		return Up.UpgradeID == UpgradeID;
	});

	if (!Upgrade)
	{
		return false;
	}

	// Check if upgrade is unlocked and not at max level
	return Upgrade->bIsUnlocked && Upgrade->UpgradeLevel < Upgrade->MaxLevel;
}

bool UWeaponComponent::UpgradeWeapon(FName WeaponID, FName UpgradeID)
{
	if (!CanUpgradeWeapon(WeaponID, UpgradeID))
	{
		return false;
	}

	FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return false;
	}

	// Find the upgrade
	FWeaponUpgrade* Upgrade = WeaponData->AvailableUpgrades.FindByPredicate([UpgradeID](const FWeaponUpgrade& Up)
	{
		return Up.UpgradeID == UpgradeID;
	});

	if (!Upgrade)
	{
		return false;
	}

	// Check cost and consume currency
	int32 Cost = GetUpgradeCost(WeaponID, UpgradeID);
	if (UResourceManager* ResourceManager = GetOwner()->FindComponentByClass<UResourceManager>())
	{
		if (ResourceManager->GetResourceAmount(TEXT("Currency")) >= Cost)
		{
			ResourceManager->ModifyResource(TEXT("Currency"), -Cost);
			
			// Apply upgrade
			Upgrade->UpgradeLevel++;
			
			OnWeaponUpgraded.Broadcast(WeaponID, UpgradeID, Upgrade->UpgradeLevel);
			OnWeaponStatsChanged(WeaponID, CalculateModifiedStats(*WeaponData));
			
			return true;
		}
	}

	return false;
}

int32 UWeaponComponent::GetUpgradeCost(FName WeaponID, FName UpgradeID) const
{
	const FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return 0;
	}

	const FWeaponUpgrade* Upgrade = WeaponData->AvailableUpgrades.FindByPredicate([UpgradeID](const FWeaponUpgrade& Up)
	{
		return Up.UpgradeID == UpgradeID;
	});

	if (!Upgrade)
	{
		return 0;
	}

	return Upgrade->CostPerLevel * (Upgrade->UpgradeLevel + 1);
}

TArray<FWeaponUpgrade> UWeaponComponent::GetAvailableUpgrades(FName WeaponID) const
{
	const FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return TArray<FWeaponUpgrade>();
	}

	TArray<FWeaponUpgrade> AvailableUpgrades;
	for (const FWeaponUpgrade& Upgrade : WeaponData->AvailableUpgrades)
	{
		if (Upgrade.bIsUnlocked && Upgrade.UpgradeLevel < Upgrade.MaxLevel)
		{
			AvailableUpgrades.Add(Upgrade);
		}
	}

	return AvailableUpgrades;
}

void UWeaponComponent::DamageWeapon(FName WeaponID, float DamageAmount)
{
	FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return;
	}

	WeaponData->BaseStats.CurrentDurability = FMath::Max(0.0f, WeaponData->BaseStats.CurrentDurability - DamageAmount);
	UpdateWeaponCondition(WeaponID);
}

void UWeaponComponent::RepairWeapon(FName WeaponID, float RepairAmount)
{
	FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return;
	}

	WeaponData->BaseStats.CurrentDurability = FMath::Min(WeaponData->BaseStats.MaxDurability, WeaponData->BaseStats.CurrentDurability + RepairAmount);
	UpdateWeaponCondition(WeaponID);
}

EWeaponCondition UWeaponComponent::GetWeaponCondition(FName WeaponID) const
{
	const FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return EWeaponCondition::Broken;
	}

	return WeaponData->Condition;
}

TArray<FName> UWeaponComponent::GetOwnedWeapons() const
{
	TArray<FName> WeaponIDs;
	OwnedWeapons.GetKeys(WeaponIDs);
	return WeaponIDs;
}

TArray<FName> UWeaponComponent::GetWeaponsByType(EWeaponType WeaponType) const
{
	TArray<FName> FilteredWeapons;
	
	for (const auto& WeaponPair : OwnedWeapons)
	{
		if (WeaponPair.Value.WeaponType == WeaponType)
		{
			FilteredWeapons.Add(WeaponPair.Key);
		}
	}
	
	return FilteredWeapons;
}

bool UWeaponComponent::ConsumeAmmo(EAmmoType AmmoType, int32 Amount)
{
	if (UResourceManager* ResourceManager = GetOwner()->FindComponentByClass<UResourceManager>())
	{
		FName AmmoResourceID = FName(*FString::Printf(TEXT("Ammo_%s"), *UEnum::GetValueAsString(AmmoType)));
		return ResourceManager->ModifyResource(AmmoResourceID, -Amount);
	}
	return false;
}

FWeaponStats UWeaponComponent::CalculateModifiedStats(const FWeaponData& WeaponData) const
{
	FWeaponStats ModifiedStats = WeaponData.BaseStats;

	// Apply upgrade modifiers
	for (const FWeaponUpgrade& Upgrade : WeaponData.AvailableUpgrades)
	{
		if (Upgrade.UpgradeLevel > 0)
		{
			float Modifier = Upgrade.StatModifier * Upgrade.UpgradeLevel;
			
			// Apply modifier based on upgrade type (this would be more sophisticated in a real implementation)
			if (Upgrade.UpgradeID.ToString().Contains(TEXT("Damage")))
			{
				ModifiedStats.BaseDamage += Modifier;
			}
			else if (Upgrade.UpgradeID.ToString().Contains(TEXT("Accuracy")))
			{
				ModifiedStats.Accuracy = FMath::Clamp(ModifiedStats.Accuracy + Modifier, 0.0f, 1.0f);
			}
			else if (Upgrade.UpgradeID.ToString().Contains(TEXT("Range")))
			{
				ModifiedStats.Range += Modifier;
			}
			// Add more upgrade types as needed
		}
	}

	// Apply condition modifiers
	float ConditionMultiplier = 1.0f;
	switch (WeaponData.Condition)
	{
		case EWeaponCondition::Broken:
			ConditionMultiplier = 0.0f;
			break;
		case EWeaponCondition::Poor:
			ConditionMultiplier = 0.5f;
			break;
		case EWeaponCondition::Fair:
			ConditionMultiplier = 0.7f;
			break;
		case EWeaponCondition::Good:
			ConditionMultiplier = 0.9f;
			break;
		case EWeaponCondition::Excellent:
			ConditionMultiplier = 1.0f;
			break;
		case EWeaponCondition::Perfect:
			ConditionMultiplier = 1.1f;
			break;
	}

	ModifiedStats.BaseDamage *= ConditionMultiplier;
	ModifiedStats.Accuracy *= ConditionMultiplier;

	return ModifiedStats;
}

EWeaponCondition UWeaponComponent::CalculateCondition(float DurabilityPercentage) const
{
	if (DurabilityPercentage <= 0.0f)
	{
		return EWeaponCondition::Broken;
	}
	else if (DurabilityPercentage <= 0.2f)
	{
		return EWeaponCondition::Poor;
	}
	else if (DurabilityPercentage <= 0.4f)
	{
		return EWeaponCondition::Fair;
	}
	else if (DurabilityPercentage <= 0.8f)
	{
		return EWeaponCondition::Good;
	}
	else if (DurabilityPercentage < 1.0f)
	{
		return EWeaponCondition::Excellent;
	}
	else
	{
		return EWeaponCondition::Perfect;
	}
}

void UWeaponComponent::UpdateWeaponCondition(FName WeaponID)
{
	FWeaponData* WeaponData = OwnedWeapons.Find(WeaponID);
	if (!WeaponData)
	{
		return;
	}

	float DurabilityPercentage = WeaponData->BaseStats.CurrentDurability / WeaponData->BaseStats.MaxDurability;
	EWeaponCondition NewCondition = CalculateCondition(DurabilityPercentage);

	if (NewCondition != WeaponData->Condition)
	{
		WeaponData->Condition = NewCondition;
		OnWeaponConditionChanged.Broadcast(WeaponID, NewCondition);
		OnWeaponStatsChanged(WeaponID, CalculateModifiedStats(*WeaponData));
	}
}
