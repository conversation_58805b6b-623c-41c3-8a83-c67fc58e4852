// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/PuzzleSystem/Components/PuzzleComponent.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePuzzleComponent() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UPuzzleComponent();
SLT_API UClass* Z_Construct_UClass_UPuzzleComponent_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPuzzleState();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPuzzleType();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPuzzleStep();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EPuzzleType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPuzzleType;
static UEnum* EPuzzleType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPuzzleType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPuzzleType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EPuzzleType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EPuzzleType"));
	}
	return Z_Registration_Info_UEnum_EPuzzleType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EPuzzleType>()
{
	return EPuzzleType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EPuzzleType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Alignment.DisplayName", "Alignment" },
		{ "Alignment.Name", "EPuzzleType::Alignment" },
		{ "BlueprintType", "true" },
		{ "Combination.DisplayName", "Combination" },
		{ "Combination.Name", "EPuzzleType::Combination" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EPuzzleType::Custom" },
		{ "ItemPlacement.DisplayName", "Item Placement" },
		{ "ItemPlacement.Name", "EPuzzleType::ItemPlacement" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPuzzleType::None" },
		{ "Pattern.DisplayName", "Pattern" },
		{ "Pattern.Name", "EPuzzleType::Pattern" },
		{ "Sequence.DisplayName", "Sequence" },
		{ "Sequence.Name", "EPuzzleType::Sequence" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPuzzleType::None", (int64)EPuzzleType::None },
		{ "EPuzzleType::Sequence", (int64)EPuzzleType::Sequence },
		{ "EPuzzleType::Combination", (int64)EPuzzleType::Combination },
		{ "EPuzzleType::Pattern", (int64)EPuzzleType::Pattern },
		{ "EPuzzleType::ItemPlacement", (int64)EPuzzleType::ItemPlacement },
		{ "EPuzzleType::Alignment", (int64)EPuzzleType::Alignment },
		{ "EPuzzleType::Custom", (int64)EPuzzleType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EPuzzleType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EPuzzleType",
	"EPuzzleType",
	Z_Construct_UEnum_SLT_EPuzzleType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPuzzleType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPuzzleType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EPuzzleType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EPuzzleType()
{
	if (!Z_Registration_Info_UEnum_EPuzzleType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPuzzleType.InnerSingleton, Z_Construct_UEnum_SLT_EPuzzleType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPuzzleType.InnerSingleton;
}
// End Enum EPuzzleType

// Begin Enum EPuzzleState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPuzzleState;
static UEnum* EPuzzleState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPuzzleState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPuzzleState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EPuzzleState, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EPuzzleState"));
	}
	return Z_Registration_Info_UEnum_EPuzzleState.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EPuzzleState>()
{
	return EPuzzleState_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EPuzzleState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Active.DisplayName", "Active" },
		{ "Active.Name", "EPuzzleState::Active" },
		{ "BlueprintType", "true" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "EPuzzleState::Failed" },
		{ "Inactive.DisplayName", "Inactive" },
		{ "Inactive.Name", "EPuzzleState::Inactive" },
		{ "InProgress.DisplayName", "In Progress" },
		{ "InProgress.Name", "EPuzzleState::InProgress" },
		{ "Locked.DisplayName", "Locked" },
		{ "Locked.Name", "EPuzzleState::Locked" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
		{ "Solved.DisplayName", "Solved" },
		{ "Solved.Name", "EPuzzleState::Solved" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPuzzleState::Inactive", (int64)EPuzzleState::Inactive },
		{ "EPuzzleState::Active", (int64)EPuzzleState::Active },
		{ "EPuzzleState::InProgress", (int64)EPuzzleState::InProgress },
		{ "EPuzzleState::Solved", (int64)EPuzzleState::Solved },
		{ "EPuzzleState::Failed", (int64)EPuzzleState::Failed },
		{ "EPuzzleState::Locked", (int64)EPuzzleState::Locked },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EPuzzleState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EPuzzleState",
	"EPuzzleState",
	Z_Construct_UEnum_SLT_EPuzzleState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPuzzleState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPuzzleState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EPuzzleState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EPuzzleState()
{
	if (!Z_Registration_Info_UEnum_EPuzzleState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPuzzleState.InnerSingleton, Z_Construct_UEnum_SLT_EPuzzleState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPuzzleState.InnerSingleton;
}
// End Enum EPuzzleState

// Begin ScriptStruct FPuzzleStep
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PuzzleStep;
class UScriptStruct* FPuzzleStep::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PuzzleStep.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PuzzleStep.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPuzzleStep, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PuzzleStep"));
	}
	return Z_Registration_Info_UScriptStruct_PuzzleStep.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPuzzleStep>()
{
	return FPuzzleStep::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPuzzleStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepID_MetaData[] = {
		{ "Category", "Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier for this step\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for this step" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepValue_MetaData[] = {
		{ "Category", "Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// The value or action for this step\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The value or action for this step" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Step" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Whether this step has been completed\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Whether this step has been completed" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresItem_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Required item for this step (optional)\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required item for this step (optional)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredItemID_MetaData[] = {
		{ "Category", "Requirements" },
		{ "EditCondition", "bRequiresItem" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Required gameplay tags\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Required gameplay tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom step data\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom step data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StepID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepValue;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static void NewProp_bRequiresItem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresItem;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredItemID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPuzzleStep>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_StepID = { "StepID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleStep, StepID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepID_MetaData), NewProp_StepID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_StepValue = { "StepValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleStep, StepValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepValue_MetaData), NewProp_StepValue_MetaData) };
void Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FPuzzleStep*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPuzzleStep), &Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
void Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bRequiresItem_SetBit(void* Obj)
{
	((FPuzzleStep*)Obj)->bRequiresItem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bRequiresItem = { "bRequiresItem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPuzzleStep), &Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bRequiresItem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresItem_MetaData), NewProp_bRequiresItem_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_RequiredItemID = { "RequiredItemID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleStep, RequiredItemID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredItemID_MetaData), NewProp_RequiredItemID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleStep, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleStep, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPuzzleStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_StepID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_StepValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_bRequiresItem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_RequiredItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPuzzleStep_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PuzzleStep",
	Z_Construct_UScriptStruct_FPuzzleStep_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleStep_Statics::PropPointers),
	sizeof(FPuzzleStep),
	alignof(FPuzzleStep),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleStep_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPuzzleStep_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPuzzleStep()
{
	if (!Z_Registration_Info_UScriptStruct_PuzzleStep.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PuzzleStep.InnerSingleton, Z_Construct_UScriptStruct_FPuzzleStep_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PuzzleStep.InnerSingleton;
}
// End ScriptStruct FPuzzleStep

// Begin Delegate FOnPuzzleStateChanged
struct Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPuzzleStateChanged_Parms
	{
		EPuzzleState OldState;
		EPuzzleState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleStateChanged_Parms, OldState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(0, nullptr) }; // 3710896167
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(0, nullptr) }; // 3710896167
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPuzzleStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPuzzleStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleStateChanged, EPuzzleState OldState, EPuzzleState NewState)
{
	struct _Script_SLT_eventOnPuzzleStateChanged_Parms
	{
		EPuzzleState OldState;
		EPuzzleState NewState;
	};
	_Script_SLT_eventOnPuzzleStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnPuzzleStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleStateChanged

// Begin Delegate FOnPuzzleSolved
struct Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPuzzleSolved_Parms
	{
		APawn* SolvingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolvingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::NewProp_SolvingPawn = { "SolvingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleSolved_Parms, SolvingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::NewProp_SolvingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPuzzleSolved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleSolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleSolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPuzzleSolved_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleSolved, APawn* SolvingPawn)
{
	struct _Script_SLT_eventOnPuzzleSolved_Parms
	{
		APawn* SolvingPawn;
	};
	_Script_SLT_eventOnPuzzleSolved_Parms Parms;
	Parms.SolvingPawn=SolvingPawn;
	OnPuzzleSolved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleSolved

// Begin Delegate FOnPuzzleFailed
struct Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPuzzleFailed_Parms
	{
		APawn* FailingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FailingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::NewProp_FailingPawn = { "FailingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleFailed_Parms, FailingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::NewProp_FailingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPuzzleFailed__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPuzzleFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleFailed, APawn* FailingPawn)
{
	struct _Script_SLT_eventOnPuzzleFailed_Parms
	{
		APawn* FailingPawn;
	};
	_Script_SLT_eventOnPuzzleFailed_Parms Parms;
	Parms.FailingPawn=FailingPawn;
	OnPuzzleFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleFailed

// Begin Delegate FOnPuzzleStepCompleted
struct Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPuzzleStepCompleted_Parms
	{
		int32 StepIndex;
		FPuzzleStep CompletedStep;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedStep_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletedStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleStepCompleted_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep = { "CompletedStep", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPuzzleStepCompleted_Parms, CompletedStep), Z_Construct_UScriptStruct_FPuzzleStep, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedStep_MetaData), NewProp_CompletedStep_MetaData) }; // 2834563514
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPuzzleStepCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleStepCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleStepCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPuzzleStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleStepCompleted, int32 StepIndex, FPuzzleStep const& CompletedStep)
{
	struct _Script_SLT_eventOnPuzzleStepCompleted_Parms
	{
		int32 StepIndex;
		FPuzzleStep CompletedStep;
	};
	_Script_SLT_eventOnPuzzleStepCompleted_Parms Parms;
	Parms.StepIndex=StepIndex;
	Parms.CompletedStep=CompletedStep;
	OnPuzzleStepCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleStepCompleted

// Begin Delegate FOnPuzzleReset
struct Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPuzzleReset_Parms
	{
		bool bWasForced;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bWasForced_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasForced;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::NewProp_bWasForced_SetBit(void* Obj)
{
	((_Script_SLT_eventOnPuzzleReset_Parms*)Obj)->bWasForced = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::NewProp_bWasForced = { "bWasForced", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnPuzzleReset_Parms), &Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::NewProp_bWasForced_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::NewProp_bWasForced,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPuzzleReset__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleReset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::_Script_SLT_eventOnPuzzleReset_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPuzzleReset_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleReset, bool bWasForced)
{
	struct _Script_SLT_eventOnPuzzleReset_Parms
	{
		bool bWasForced;
	};
	_Script_SLT_eventOnPuzzleReset_Parms Parms;
	Parms.bWasForced=bWasForced ? true : false;
	OnPuzzleReset.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleReset

// Begin Class UPuzzleComponent Function ActivatePuzzle
struct Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "ActivatePuzzle", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execActivatePuzzle)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivatePuzzle();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function ActivatePuzzle

// Begin Class UPuzzleComponent Function CanAttemptPuzzle
struct Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics
{
	struct PuzzleComponent_eventCanAttemptPuzzle_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventCanAttemptPuzzle_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventCanAttemptPuzzle_Parms), &Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "CanAttemptPuzzle", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PuzzleComponent_eventCanAttemptPuzzle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::PuzzleComponent_eventCanAttemptPuzzle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execCanAttemptPuzzle)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAttemptPuzzle();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function CanAttemptPuzzle

// Begin Class UPuzzleComponent Function CanCompleteStep
struct Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics
{
	struct PuzzleComponent_eventCanCompleteStep_Parms
	{
		int32 StepIndex;
		APawn* InteractingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Steps" },
		{ "CPP_Default_InteractingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventCanCompleteStep_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventCanCompleteStep_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventCanCompleteStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventCanCompleteStep_Parms), &Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "CanCompleteStep", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PuzzleComponent_eventCanCompleteStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::PuzzleComponent_eventCanCompleteStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execCanCompleteStep)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepIndex);
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanCompleteStep(Z_Param_StepIndex,Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function CanCompleteStep

// Begin Class UPuzzleComponent Function DeactivatePuzzle
struct Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "DeactivatePuzzle", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execDeactivatePuzzle)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivatePuzzle();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function DeactivatePuzzle

// Begin Class UPuzzleComponent Function FailPuzzle
struct Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics
{
	struct PuzzleComponent_eventFailPuzzle_Parms
	{
		APawn* FailingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_FailingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FailingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::NewProp_FailingPawn = { "FailingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventFailPuzzle_Parms, FailingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::NewProp_FailingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "FailPuzzle", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PuzzleComponent_eventFailPuzzle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::PuzzleComponent_eventFailPuzzle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_FailPuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_FailPuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execFailPuzzle)
{
	P_GET_OBJECT(APawn,Z_Param_FailingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->FailPuzzle(Z_Param_FailingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function FailPuzzle

// Begin Class UPuzzleComponent Function GetCompletedSteps
struct Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics
{
	struct PuzzleComponent_eventGetCompletedSteps_Parms
	{
		TArray<FPuzzleStep> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPuzzleStep, METADATA_PARAMS(0, nullptr) }; // 2834563514
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventGetCompletedSteps_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2834563514
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "GetCompletedSteps", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PuzzleComponent_eventGetCompletedSteps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::PuzzleComponent_eventGetCompletedSteps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execGetCompletedSteps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FPuzzleStep>*)Z_Param__Result=P_THIS->GetCompletedSteps();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function GetCompletedSteps

// Begin Class UPuzzleComponent Function GetCompletionPercentage
struct Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics
{
	struct PuzzleComponent_eventGetCompletionPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventGetCompletionPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "GetCompletionPercentage", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PuzzleComponent_eventGetCompletionPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::PuzzleComponent_eventGetCompletionPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execGetCompletionPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCompletionPercentage();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function GetCompletionPercentage

// Begin Class UPuzzleComponent Function GetCurrentStep
struct Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics
{
	struct PuzzleComponent_eventGetCurrentStep_Parms
	{
		FPuzzleStep ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventGetCurrentStep_Parms, ReturnValue), Z_Construct_UScriptStruct_FPuzzleStep, METADATA_PARAMS(0, nullptr) }; // 2834563514
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "GetCurrentStep", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PuzzleComponent_eventGetCurrentStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::PuzzleComponent_eventGetCurrentStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execGetCurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPuzzleStep*)Z_Param__Result=P_THIS->GetCurrentStep();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function GetCurrentStep

// Begin Class UPuzzleComponent Function GetRemainingAttempts
struct Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics
{
	struct PuzzleComponent_eventGetRemainingAttempts_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventGetRemainingAttempts_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "GetRemainingAttempts", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PuzzleComponent_eventGetRemainingAttempts_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::PuzzleComponent_eventGetRemainingAttempts_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execGetRemainingAttempts)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetRemainingAttempts();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function GetRemainingAttempts

// Begin Class UPuzzleComponent Function IsPuzzleActive
struct Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics
{
	struct PuzzleComponent_eventIsPuzzleActive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventIsPuzzleActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventIsPuzzleActive_Parms), &Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "IsPuzzleActive", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PuzzleComponent_eventIsPuzzleActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::PuzzleComponent_eventIsPuzzleActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execIsPuzzleActive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPuzzleActive();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function IsPuzzleActive

// Begin Class UPuzzleComponent Function IsPuzzleSolved
struct Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics
{
	struct PuzzleComponent_eventIsPuzzleSolved_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State queries\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State queries" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventIsPuzzleSolved_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventIsPuzzleSolved_Parms), &Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "IsPuzzleSolved", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PuzzleComponent_eventIsPuzzleSolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::PuzzleComponent_eventIsPuzzleSolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execIsPuzzleSolved)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPuzzleSolved();
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function IsPuzzleSolved

// Begin Class UPuzzleComponent Function IsStepCompleted
struct Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics
{
	struct PuzzleComponent_eventIsStepCompleted_Parms
	{
		int32 StepIndex;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Steps" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Step management\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Step management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventIsStepCompleted_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventIsStepCompleted_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventIsStepCompleted_Parms), &Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "IsStepCompleted", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PuzzleComponent_eventIsStepCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::PuzzleComponent_eventIsStepCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execIsStepCompleted)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsStepCompleted(Z_Param_StepIndex);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function IsStepCompleted

// Begin Class UPuzzleComponent Function OnPuzzleActivated
static const FName NAME_UPuzzleComponent_OnPuzzleActivated = FName(TEXT("OnPuzzleActivated"));
void UPuzzleComponent::OnPuzzleActivated()
{
	UFunction* Func = FindFunctionChecked(NAME_UPuzzleComponent_OnPuzzleActivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "OnPuzzleActivated", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UPuzzleComponent Function OnPuzzleActivated

// Begin Class UPuzzleComponent Function OnPuzzleDeactivated
static const FName NAME_UPuzzleComponent_OnPuzzleDeactivated = FName(TEXT("OnPuzzleDeactivated"));
void UPuzzleComponent::OnPuzzleDeactivated()
{
	UFunction* Func = FindFunctionChecked(NAME_UPuzzleComponent_OnPuzzleDeactivated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "OnPuzzleDeactivated", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UPuzzleComponent Function OnPuzzleDeactivated

// Begin Class UPuzzleComponent Function OnStepAttempted
struct PuzzleComponent_eventOnStepAttempted_Parms
{
	int32 StepIndex;
	FString AttemptedValue;
	bool bSuccess;
};
static const FName NAME_UPuzzleComponent_OnStepAttempted = FName(TEXT("OnStepAttempted"));
void UPuzzleComponent::OnStepAttempted(int32 StepIndex, const FString& AttemptedValue, bool bSuccess)
{
	PuzzleComponent_eventOnStepAttempted_Parms Parms;
	Parms.StepIndex=StepIndex;
	Parms.AttemptedValue=AttemptedValue;
	Parms.bSuccess=bSuccess ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_UPuzzleComponent_OnStepAttempted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttemptedValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttemptedValue;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventOnStepAttempted_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_AttemptedValue = { "AttemptedValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventOnStepAttempted_Parms, AttemptedValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttemptedValue_MetaData), NewProp_AttemptedValue_MetaData) };
void Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((PuzzleComponent_eventOnStepAttempted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventOnStepAttempted_Parms), &Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_AttemptedValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "OnStepAttempted", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::PropPointers), sizeof(PuzzleComponent_eventOnStepAttempted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::Function_MetaDataParams) };
static_assert(sizeof(PuzzleComponent_eventOnStepAttempted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UPuzzleComponent Function OnStepAttempted

// Begin Class UPuzzleComponent Function ResetPuzzle
struct Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics
{
	struct PuzzleComponent_eventResetPuzzle_Parms
	{
		bool bForceReset;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_bForceReset", "false" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bForceReset_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bForceReset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::NewProp_bForceReset_SetBit(void* Obj)
{
	((PuzzleComponent_eventResetPuzzle_Parms*)Obj)->bForceReset = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::NewProp_bForceReset = { "bForceReset", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventResetPuzzle_Parms), &Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::NewProp_bForceReset_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::NewProp_bForceReset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "ResetPuzzle", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PuzzleComponent_eventResetPuzzle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::PuzzleComponent_eventResetPuzzle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execResetPuzzle)
{
	P_GET_UBOOL(Z_Param_bForceReset);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPuzzle(Z_Param_bForceReset);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function ResetPuzzle

// Begin Class UPuzzleComponent Function SetStepCompleted
struct Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics
{
	struct PuzzleComponent_eventSetStepCompleted_Parms
	{
		int32 StepIndex;
		bool bCompleted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Steps" },
		{ "CPP_Default_bCompleted", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static void NewProp_bCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventSetStepCompleted_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_bCompleted_SetBit(void* Obj)
{
	((PuzzleComponent_eventSetStepCompleted_Parms*)Obj)->bCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_bCompleted = { "bCompleted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventSetStepCompleted_Parms), &Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_bCompleted_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::NewProp_bCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "SetStepCompleted", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PuzzleComponent_eventSetStepCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::PuzzleComponent_eventSetStepCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execSetStepCompleted)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepIndex);
	P_GET_UBOOL(Z_Param_bCompleted);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStepCompleted(Z_Param_StepIndex,Z_Param_bCompleted);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function SetStepCompleted

// Begin Class UPuzzleComponent Function SolvePuzzle
struct Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics
{
	struct PuzzleComponent_eventSolvePuzzle_Parms
	{
		APawn* SolvingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_SolvingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolvingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::NewProp_SolvingPawn = { "SolvingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventSolvePuzzle_Parms, SolvingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::NewProp_SolvingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "SolvePuzzle", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PuzzleComponent_eventSolvePuzzle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::PuzzleComponent_eventSolvePuzzle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execSolvePuzzle)
{
	P_GET_OBJECT(APawn,Z_Param_SolvingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SolvePuzzle(Z_Param_SolvingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function SolvePuzzle

// Begin Class UPuzzleComponent Function TryCompleteCurrentStep
struct Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics
{
	struct PuzzleComponent_eventTryCompleteCurrentStep_Parms
	{
		FString StepValue;
		APawn* InteractingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_InteractingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_StepValue = { "StepValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTryCompleteCurrentStep_Parms, StepValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepValue_MetaData), NewProp_StepValue_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTryCompleteCurrentStep_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventTryCompleteCurrentStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventTryCompleteCurrentStep_Parms), &Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_StepValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "TryCompleteCurrentStep", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PuzzleComponent_eventTryCompleteCurrentStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::PuzzleComponent_eventTryCompleteCurrentStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execTryCompleteCurrentStep)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_StepValue);
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TryCompleteCurrentStep(Z_Param_StepValue,Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function TryCompleteCurrentStep

// Begin Class UPuzzleComponent Function TryCompleteStep
struct Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics
{
	struct PuzzleComponent_eventTryCompleteStep_Parms
	{
		int32 StepIndex;
		FString StepValue;
		APawn* InteractingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_InteractingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_StepIndex;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_StepIndex = { "StepIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTryCompleteStep_Parms, StepIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_StepValue = { "StepValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTryCompleteStep_Parms, StepValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepValue_MetaData), NewProp_StepValue_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTryCompleteStep_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventTryCompleteStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventTryCompleteStep_Parms), &Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_StepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_StepValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "TryCompleteStep", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PuzzleComponent_eventTryCompleteStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::PuzzleComponent_eventTryCompleteStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execTryCompleteStep)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_StepIndex);
	P_GET_PROPERTY(FStrProperty,Z_Param_StepValue);
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TryCompleteStep(Z_Param_StepIndex,Z_Param_StepValue,Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function TryCompleteStep

// Begin Class UPuzzleComponent Function TrySolvePuzzle
struct Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics
{
	struct PuzzleComponent_eventTrySolvePuzzle_Parms
	{
		FName SolutionCode;
		APawn* SolvingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main puzzle functions\n" },
#endif
		{ "CPP_Default_SolvingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main puzzle functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_SolutionCode;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolvingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_SolutionCode = { "SolutionCode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTrySolvePuzzle_Parms, SolutionCode), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_SolvingPawn = { "SolvingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleComponent_eventTrySolvePuzzle_Parms, SolvingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleComponent_eventTrySolvePuzzle_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleComponent_eventTrySolvePuzzle_Parms), &Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_SolutionCode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_SolvingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPuzzleComponent, nullptr, "TrySolvePuzzle", nullptr, nullptr, Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PropPointers), sizeof(Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PuzzleComponent_eventTrySolvePuzzle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::PuzzleComponent_eventTrySolvePuzzle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UPuzzleComponent::execTrySolvePuzzle)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_SolutionCode);
	P_GET_OBJECT(APawn,Z_Param_SolvingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TrySolvePuzzle(Z_Param_SolutionCode,Z_Param_SolvingPawn);
	P_NATIVE_END;
}
// End Class UPuzzleComponent Function TrySolvePuzzle

// Begin Class UPuzzleComponent
void UPuzzleComponent::StaticRegisterNativesUPuzzleComponent()
{
	UClass* Class = UPuzzleComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivatePuzzle", &UPuzzleComponent::execActivatePuzzle },
		{ "CanAttemptPuzzle", &UPuzzleComponent::execCanAttemptPuzzle },
		{ "CanCompleteStep", &UPuzzleComponent::execCanCompleteStep },
		{ "DeactivatePuzzle", &UPuzzleComponent::execDeactivatePuzzle },
		{ "FailPuzzle", &UPuzzleComponent::execFailPuzzle },
		{ "GetCompletedSteps", &UPuzzleComponent::execGetCompletedSteps },
		{ "GetCompletionPercentage", &UPuzzleComponent::execGetCompletionPercentage },
		{ "GetCurrentStep", &UPuzzleComponent::execGetCurrentStep },
		{ "GetRemainingAttempts", &UPuzzleComponent::execGetRemainingAttempts },
		{ "IsPuzzleActive", &UPuzzleComponent::execIsPuzzleActive },
		{ "IsPuzzleSolved", &UPuzzleComponent::execIsPuzzleSolved },
		{ "IsStepCompleted", &UPuzzleComponent::execIsStepCompleted },
		{ "ResetPuzzle", &UPuzzleComponent::execResetPuzzle },
		{ "SetStepCompleted", &UPuzzleComponent::execSetStepCompleted },
		{ "SolvePuzzle", &UPuzzleComponent::execSolvePuzzle },
		{ "TryCompleteCurrentStep", &UPuzzleComponent::execTryCompleteCurrentStep },
		{ "TryCompleteStep", &UPuzzleComponent::execTryCompleteStep },
		{ "TrySolvePuzzle", &UPuzzleComponent::execTrySolvePuzzle },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UPuzzleComponent);
UClass* Z_Construct_UClass_UPuzzleComponent_NoRegister()
{
	return UPuzzleComponent::StaticClass();
}
struct Z_Construct_UClass_UPuzzleComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "PuzzleSystem/Components/PuzzleComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleType_MetaData[] = {
		{ "Category", "Puzzle Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Puzzle configuration\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Puzzle configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleName_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleDescription_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleSteps_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequireSequentialCompletion_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowReset_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAttempts_MetaData[] = {
		{ "Category", "Puzzle Settings" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResetDelayAfterFailure_MetaData[] = {
		{ "Category", "Puzzle Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0 = unlimited\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0 = unlimited" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State tracking\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStepIndex_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttemptCount_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSolved_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleStateChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleSolved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleStepCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleReset_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Components/PuzzleComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PuzzleType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PuzzleType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_PuzzleName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_PuzzleDescription;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PuzzleSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PuzzleSteps;
	static void NewProp_bRequireSequentialCompletion_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequireSequentialCompletion;
	static void NewProp_bAllowReset_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowReset;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAttempts;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ResetDelayAfterFailure;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentStepIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttemptCount;
	static void NewProp_bIsSolved_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSolved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleSolved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleStepCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleReset;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPuzzleComponent_ActivatePuzzle, "ActivatePuzzle" }, // 2240238736
		{ &Z_Construct_UFunction_UPuzzleComponent_CanAttemptPuzzle, "CanAttemptPuzzle" }, // 476109481
		{ &Z_Construct_UFunction_UPuzzleComponent_CanCompleteStep, "CanCompleteStep" }, // 3019647304
		{ &Z_Construct_UFunction_UPuzzleComponent_DeactivatePuzzle, "DeactivatePuzzle" }, // 3942833044
		{ &Z_Construct_UFunction_UPuzzleComponent_FailPuzzle, "FailPuzzle" }, // 3494876556
		{ &Z_Construct_UFunction_UPuzzleComponent_GetCompletedSteps, "GetCompletedSteps" }, // 294080375
		{ &Z_Construct_UFunction_UPuzzleComponent_GetCompletionPercentage, "GetCompletionPercentage" }, // 1285946542
		{ &Z_Construct_UFunction_UPuzzleComponent_GetCurrentStep, "GetCurrentStep" }, // 2788946699
		{ &Z_Construct_UFunction_UPuzzleComponent_GetRemainingAttempts, "GetRemainingAttempts" }, // 868814558
		{ &Z_Construct_UFunction_UPuzzleComponent_IsPuzzleActive, "IsPuzzleActive" }, // 420464910
		{ &Z_Construct_UFunction_UPuzzleComponent_IsPuzzleSolved, "IsPuzzleSolved" }, // 439613741
		{ &Z_Construct_UFunction_UPuzzleComponent_IsStepCompleted, "IsStepCompleted" }, // 2179864151
		{ &Z_Construct_UFunction_UPuzzleComponent_OnPuzzleActivated, "OnPuzzleActivated" }, // 235706277
		{ &Z_Construct_UFunction_UPuzzleComponent_OnPuzzleDeactivated, "OnPuzzleDeactivated" }, // 1266873426
		{ &Z_Construct_UFunction_UPuzzleComponent_OnStepAttempted, "OnStepAttempted" }, // 1964436962
		{ &Z_Construct_UFunction_UPuzzleComponent_ResetPuzzle, "ResetPuzzle" }, // 901969516
		{ &Z_Construct_UFunction_UPuzzleComponent_SetStepCompleted, "SetStepCompleted" }, // 2086537184
		{ &Z_Construct_UFunction_UPuzzleComponent_SolvePuzzle, "SolvePuzzle" }, // 1294821971
		{ &Z_Construct_UFunction_UPuzzleComponent_TryCompleteCurrentStep, "TryCompleteCurrentStep" }, // 13826785
		{ &Z_Construct_UFunction_UPuzzleComponent_TryCompleteStep, "TryCompleteStep" }, // 2409261058
		{ &Z_Construct_UFunction_UPuzzleComponent_TrySolvePuzzle, "TrySolvePuzzle" }, // 1907050407
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UPuzzleComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleType = { "PuzzleType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, PuzzleType), Z_Construct_UEnum_SLT_EPuzzleType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleType_MetaData), NewProp_PuzzleType_MetaData) }; // 3905127025
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleName = { "PuzzleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, PuzzleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleName_MetaData), NewProp_PuzzleName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleDescription = { "PuzzleDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, PuzzleDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleDescription_MetaData), NewProp_PuzzleDescription_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleSteps_Inner = { "PuzzleSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPuzzleStep, METADATA_PARAMS(0, nullptr) }; // 2834563514
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleSteps = { "PuzzleSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, PuzzleSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleSteps_MetaData), NewProp_PuzzleSteps_MetaData) }; // 2834563514
void Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bRequireSequentialCompletion_SetBit(void* Obj)
{
	((UPuzzleComponent*)Obj)->bRequireSequentialCompletion = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bRequireSequentialCompletion = { "bRequireSequentialCompletion", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPuzzleComponent), &Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bRequireSequentialCompletion_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequireSequentialCompletion_MetaData), NewProp_bRequireSequentialCompletion_MetaData) };
void Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bAllowReset_SetBit(void* Obj)
{
	((UPuzzleComponent*)Obj)->bAllowReset = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bAllowReset = { "bAllowReset", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPuzzleComponent), &Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bAllowReset_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowReset_MetaData), NewProp_bAllowReset_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_MaxAttempts = { "MaxAttempts", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, MaxAttempts), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAttempts_MetaData), NewProp_MaxAttempts_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_ResetDelayAfterFailure = { "ResetDelayAfterFailure", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, ResetDelayAfterFailure), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResetDelayAfterFailure_MetaData), NewProp_ResetDelayAfterFailure_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, CurrentState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 3710896167
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentStepIndex = { "CurrentStepIndex", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, CurrentStepIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStepIndex_MetaData), NewProp_CurrentStepIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_AttemptCount = { "AttemptCount", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, AttemptCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttemptCount_MetaData), NewProp_AttemptCount_MetaData) };
void Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bIsSolved_SetBit(void* Obj)
{
	((UPuzzleComponent*)Obj)->bIsSolved = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bIsSolved = { "bIsSolved", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UPuzzleComponent), &Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bIsSolved_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSolved_MetaData), NewProp_bIsSolved_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleStateChanged = { "OnPuzzleStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, OnPuzzleStateChanged), Z_Construct_UDelegateFunction_SLT_OnPuzzleStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleStateChanged_MetaData), NewProp_OnPuzzleStateChanged_MetaData) }; // 1226561880
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleSolved = { "OnPuzzleSolved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, OnPuzzleSolved), Z_Construct_UDelegateFunction_SLT_OnPuzzleSolved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleSolved_MetaData), NewProp_OnPuzzleSolved_MetaData) }; // 2895021916
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleFailed = { "OnPuzzleFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, OnPuzzleFailed), Z_Construct_UDelegateFunction_SLT_OnPuzzleFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleFailed_MetaData), NewProp_OnPuzzleFailed_MetaData) }; // 2733622312
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleStepCompleted = { "OnPuzzleStepCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, OnPuzzleStepCompleted), Z_Construct_UDelegateFunction_SLT_OnPuzzleStepCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleStepCompleted_MetaData), NewProp_OnPuzzleStepCompleted_MetaData) }; // 2905960424
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleReset = { "OnPuzzleReset", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UPuzzleComponent, OnPuzzleReset), Z_Construct_UDelegateFunction_SLT_OnPuzzleReset__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleReset_MetaData), NewProp_OnPuzzleReset_MetaData) }; // 828825305
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UPuzzleComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_PuzzleSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bRequireSequentialCompletion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bAllowReset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_MaxAttempts,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_ResetDelayAfterFailure,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_CurrentStepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_AttemptCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_bIsSolved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleSolved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleStepCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UPuzzleComponent_Statics::NewProp_OnPuzzleReset,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPuzzleComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UPuzzleComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPuzzleComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPuzzleComponent_Statics::ClassParams = {
	&UPuzzleComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UPuzzleComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UPuzzleComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPuzzleComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UPuzzleComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPuzzleComponent()
{
	if (!Z_Registration_Info_UClass_UPuzzleComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPuzzleComponent.OuterSingleton, Z_Construct_UClass_UPuzzleComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPuzzleComponent.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UPuzzleComponent>()
{
	return UPuzzleComponent::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPuzzleComponent);
UPuzzleComponent::~UPuzzleComponent() {}
// End Class UPuzzleComponent

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EPuzzleType_StaticEnum, TEXT("EPuzzleType"), &Z_Registration_Info_UEnum_EPuzzleType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3905127025U) },
		{ EPuzzleState_StaticEnum, TEXT("EPuzzleState"), &Z_Registration_Info_UEnum_EPuzzleState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3710896167U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPuzzleStep::StaticStruct, Z_Construct_UScriptStruct_FPuzzleStep_Statics::NewStructOps, TEXT("PuzzleStep"), &Z_Registration_Info_UScriptStruct_PuzzleStep, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPuzzleStep), 2834563514U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPuzzleComponent, UPuzzleComponent::StaticClass, TEXT("UPuzzleComponent"), &Z_Registration_Info_UClass_UPuzzleComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPuzzleComponent), 3515495788U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_1047727828(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
