// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InventorySystem/Components/InteractionComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class APawn;
class UEnhancedInputComponent;
struct FInteractionData;
#ifdef SLT_InteractionComponent_generated_h
#error "InteractionComponent.generated.h already included, missing '#pragma once' in InteractionComponent.h"
#endif
#define SLT_InteractionComponent_generated_h

#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_14_DELEGATE \
SLT_API void FOnInteractionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionStarted, AActor* InteractableActor, APawn* InteractingPawn);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_15_DELEGATE \
SLT_API void FOnInteractionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionCompleted, AActor* InteractableActor, APawn* InteractingPawn);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_16_DELEGATE \
SLT_API void FOnInteractionCancelled_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionCancelled, AActor* InteractableActor, APawn* InteractingPawn);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_17_DELEGATE \
SLT_API void FOnInteractableFound_DelegateWrapper(const FMulticastScriptDelegate& OnInteractableFound, AActor* InteractableActor, FInteractionData const& InteractionData);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_18_DELEGATE \
SLT_API void FOnInteractableLost_DelegateWrapper(const FMulticastScriptDelegate& OnInteractableLost);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetupInputComponent); \
	DECLARE_FUNCTION(execCanInteractWith); \
	DECLARE_FUNCTION(execFindBestInteractable); \
	DECLARE_FUNCTION(execFindInteractablesInRange); \
	DECLARE_FUNCTION(execGetCurrentInteractionData); \
	DECLARE_FUNCTION(execGetCurrentInteractable); \
	DECLARE_FUNCTION(execIsInteracting); \
	DECLARE_FUNCTION(execCancelInteraction); \
	DECLARE_FUNCTION(execStartInteraction);


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUInteractionComponent(); \
	friend struct Z_Construct_UClass_UInteractionComponent_Statics; \
public: \
	DECLARE_CLASS(UInteractionComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UInteractionComponent)


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UInteractionComponent(UInteractionComponent&&); \
	UInteractionComponent(const UInteractionComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UInteractionComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UInteractionComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UInteractionComponent) \
	NO_API virtual ~UInteractionComponent();


#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_20_PROLOG
#define FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_23_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UInteractionComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
