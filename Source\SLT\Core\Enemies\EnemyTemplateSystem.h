#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "BaseEnemyCharacter.h"
#include "../Design/AIBehaviorDesigner.h"
#include "EnemyTemplateSystem.generated.h"

USTRUCT(BlueprintType)
struct FEnemyTemplate : public FTableRowBase
{
	GENERATED_BODY()

	FEnemyTemplate()
	{
		TemplateID = NAME_None;
		TemplateName = FText::GetEmpty();
		EnemyType = EEnemyType::BasicZombie;
		EnemyClass = nullptr;
		Difficulty = EEnemyDifficulty::Normal;
		bIsBuiltIn = true;
		bRequiresSetup = false;
		SetupComplexity = 1;
		EstimatedSetupTime = 5.0f;
		RecommendedPlayerLevel = 1;
		ThreatLevel = 1;
		bCanBeCustomized = true;
		bIsUnlocked = true;
	}

	// Template identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Info")
	FName TemplateID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Info")
	FText TemplateName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Info")
	FText TemplateDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Info")
	EEnemyType EnemyType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Info")
	TSubclassOf<ABaseEnemyCharacter> EnemyClass;

	// Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	EEnemyDifficulty Difficulty;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	FEnemyConfiguration EnemyConfiguration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	FAIBehaviorTemplate AIBehaviorTemplate;

	// Template properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsBuiltIn;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bRequiresSetup;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties", meta = (ClampMin = "1", ClampMax = "5"))
	int32 SetupComplexity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	float EstimatedSetupTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	int32 RecommendedPlayerLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties", meta = (ClampMin = "1", ClampMax = "10"))
	int32 ThreatLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bCanBeCustomized;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsUnlocked;

	// Visual assets
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UTexture2D> TemplateIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<USkeletalMesh> PreviewMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FLinearColor TemplateColor = FLinearColor::White;

	// Spawning data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	FVector DefaultSpawnScale = FVector::OneVector;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	bool bRandomizeAppearance = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TArray<TSoftObjectPtr<USkeletalMesh>> VariantMeshes;

	// Gameplay integration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
	TArray<FName> RequiredItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
	TArray<FName> CounterItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay")
	TArray<FName> WeaknessTypes;

	// Tags and categorization
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer TemplateTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	TArray<FString> SearchKeywords;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;

	// Balancing data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balancing")
	float DifficultyMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Balancing")
	TMap<EEnemyDifficulty, float> DifficultyScaling;
};

USTRUCT(BlueprintType)
struct FEnemySpawnRequest
{
	GENERATED_BODY()

	FEnemySpawnRequest()
	{
		TemplateID = NAME_None;
		SpawnLocation = FVector::ZeroVector;
		SpawnRotation = FRotator::ZeroRotator;
		Difficulty = EEnemyDifficulty::Normal;
		bUseCustomConfiguration = false;
		bApplyRandomization = true;
		SpawnDelay = 0.0f;
		bAttachToSpawner = false;
		SpawnerActor = nullptr;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	FName TemplateID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	FVector SpawnLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	FRotator SpawnRotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	EEnemyDifficulty Difficulty;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	bool bUseCustomConfiguration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request", meta = (EditCondition = "bUseCustomConfiguration"))
	FEnemyConfiguration CustomConfiguration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	bool bApplyRandomization;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	float SpawnDelay;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	bool bAttachToSpawner;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request", meta = (EditCondition = "bAttachToSpawner"))
	AActor* SpawnerActor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	FGameplayTagContainer SpawnTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Request")
	TMap<FString, FString> SpawnParameters;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyTemplateSpawned, ABaseEnemyCharacter*, SpawnedEnemy, FName, TemplateID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyTemplateRegistered, FName, TemplateID, const FEnemyTemplate&, Template);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyTemplateUnregistered, FName, TemplateID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTemplateValidated, FName, TemplateID, bool, bIsValid);

UCLASS(BlueprintType, Blueprintable)
class SLT_API UEnemyTemplateSystem : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UEnemyTemplateSystem();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Template database
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Database")
	TSoftObjectPtr<UDataTable> TemplateDatabase;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template Database")
	TSoftObjectPtr<UDataTable> CustomTemplateDatabase;

	// Current templates
	UPROPERTY(BlueprintReadOnly, Category = "Templates")
	TMap<FName, FEnemyTemplate> LoadedTemplates;

	UPROPERTY(BlueprintReadOnly, Category = "Templates")
	TArray<FName> BuiltInTemplates;

	UPROPERTY(BlueprintReadOnly, Category = "Templates")
	TArray<FName> CustomTemplates;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyTemplateSpawned OnEnemyTemplateSpawned;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyTemplateRegistered OnEnemyTemplateRegistered;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyTemplateUnregistered OnEnemyTemplateUnregistered;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTemplateValidated OnTemplateValidated;

	// Main template functions
	UFUNCTION(BlueprintCallable, Category = "Enemy Templates")
	ABaseEnemyCharacter* SpawnEnemyFromTemplate(const FEnemySpawnRequest& SpawnRequest);

	UFUNCTION(BlueprintCallable, Category = "Enemy Templates")
	void RegisterEnemyTemplate(const FEnemyTemplate& Template);

	UFUNCTION(BlueprintCallable, Category = "Enemy Templates")
	void UnregisterEnemyTemplate(FName TemplateID);

	UFUNCTION(BlueprintCallable, Category = "Enemy Templates")
	bool HasTemplate(FName TemplateID) const;

	UFUNCTION(BlueprintCallable, Category = "Enemy Templates")
	FEnemyTemplate GetTemplate(FName TemplateID) const;

	// Template management
	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> GetAllTemplates() const;

	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> GetTemplatesByType(EEnemyType EnemyType) const;

	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> GetTemplatesByDifficulty(EEnemyDifficulty Difficulty) const;

	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> GetTemplatesByThreatLevel(int32 MinThreatLevel, int32 MaxThreatLevel) const;

	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> SearchTemplates(const FString& SearchTerm) const;

	UFUNCTION(BlueprintCallable, Category = "Template Management")
	TArray<FEnemyTemplate> GetTemplatesWithTags(const FGameplayTagContainer& RequiredTags) const;

	// Template creation and customization
	UFUNCTION(BlueprintCallable, Category = "Template Creation")
	FEnemyTemplate CreateCustomTemplate(const FString& TemplateName, EEnemyType BaseType);

	UFUNCTION(BlueprintCallable, Category = "Template Creation")
	FEnemyTemplate DuplicateTemplate(FName SourceTemplateID, const FString& NewTemplateName);

	UFUNCTION(BlueprintCallable, Category = "Template Creation")
	bool SaveCustomTemplate(const FEnemyTemplate& Template);

	UFUNCTION(BlueprintCallable, Category = "Template Creation")
	bool DeleteCustomTemplate(FName TemplateID);

	// Template validation
	UFUNCTION(BlueprintCallable, Category = "Template Validation")
	bool ValidateTemplate(const FEnemyTemplate& Template, TArray<FString>& OutErrors) const;

	UFUNCTION(BlueprintCallable, Category = "Template Validation")
	void ValidateAllTemplates();

	UFUNCTION(BlueprintCallable, Category = "Template Validation")
	bool CanSpawnTemplate(FName TemplateID, const FVector& Location) const;

	// Batch operations
	UFUNCTION(BlueprintCallable, Category = "Batch Operations")
	TArray<ABaseEnemyCharacter*> SpawnMultipleEnemies(const TArray<FEnemySpawnRequest>& SpawnRequests);

	UFUNCTION(BlueprintCallable, Category = "Batch Operations")
	void LoadTemplatesFromDatabase();

	UFUNCTION(BlueprintCallable, Category = "Batch Operations")
	void SaveTemplatesToDatabase();

	UFUNCTION(BlueprintCallable, Category = "Batch Operations")
	void ReloadAllTemplates();

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Template Utility")
	FName GenerateUniqueTemplateID(const FString& BaseName) const;

	UFUNCTION(BlueprintCallable, Category = "Template Utility")
	TSubclassOf<ABaseEnemyCharacter> GetEnemyClassForType(EEnemyType EnemyType) const;

	UFUNCTION(BlueprintCallable, Category = "Template Utility")
	FEnemyTemplate GetRandomTemplate(EEnemyType EnemyType = EEnemyType::Custom) const;

	UFUNCTION(BlueprintCallable, Category = "Template Utility")
	TArray<FName> GetTemplateIDsForDifficulty(EEnemyDifficulty Difficulty) const;

	// Integration functions
	UFUNCTION(BlueprintCallable, Category = "Integration")
	void ApplyAIBehaviorTemplate(ABaseEnemyCharacter* Enemy, const FAIBehaviorTemplate& BehaviorTemplate);

	UFUNCTION(BlueprintCallable, Category = "Integration")
	void ConfigureEnemyFromTemplate(ABaseEnemyCharacter* Enemy, const FEnemyTemplate& Template);

	UFUNCTION(BlueprintCallable, Category = "Integration")
	void ApplyDifficultyScaling(ABaseEnemyCharacter* Enemy, EEnemyDifficulty Difficulty);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Template Events")
	void OnTemplateSystemInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Template Events")
	void OnTemplateSpawned(ABaseEnemyCharacter* SpawnedEnemy, FName TemplateID);

	UFUNCTION(BlueprintImplementableEvent, Category = "Template Events")
	void OnTemplateCreated(FName TemplateID, const FEnemyTemplate& Template);

	UFUNCTION(BlueprintImplementableEvent, Category = "Template Events")
	void OnTemplateModified(FName TemplateID, const FEnemyTemplate& OldTemplate, const FEnemyTemplate& NewTemplate);

protected:
	// Internal functions
	void InitializeBuiltInTemplates();
	void LoadCustomTemplates();
	ABaseEnemyCharacter* SpawnEnemyActor(TSubclassOf<ABaseEnemyCharacter> EnemyClass, const FVector& Location, const FRotator& Rotation);
	void ApplyTemplateConfiguration(ABaseEnemyCharacter* Enemy, const FEnemyTemplate& Template);
	void ApplyRandomization(ABaseEnemyCharacter* Enemy, const FEnemyTemplate& Template);
	bool ValidateSpawnLocation(const FVector& Location, TSubclassOf<ABaseEnemyCharacter> EnemyClass) const;
	void SetupEnemyComponents(ABaseEnemyCharacter* Enemy, const FEnemyTemplate& Template);

	// Built-in template creation
	void CreateBasicZombieTemplates();
	void CreateFastRunnerTemplates();
	void CreateHeavyBruteTemplates();
	void CreateRangedShooterTemplates();
	void CreateStealthAmbusherTemplates();
	void CreateSwarmTypeTemplates();
	void CreateBossVariantTemplates();
};
