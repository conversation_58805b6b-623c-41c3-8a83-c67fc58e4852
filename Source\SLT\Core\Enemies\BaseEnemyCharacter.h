#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "Components/WidgetComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "BaseEnemyCharacter.generated.h"

class UPawnSensingComponent;
class UBehaviorTreeComponent;
class UBlackboardComponent;
class UHealthBarWidget;
class UEnemyAIComponent;
class UEnemyStateComponent;
class UEnemyAttackComponent;
class UEnemyMovementComponent;

UENUM(BlueprintType)
enum class EEnemyType : uint8
{
	BasicZombie		UMETA(DisplayName = "Basic Zombie"),
	FastRunner		UMETA(DisplayName = "Fast Runner"),
	HeavyBrute		UMETA(DisplayName = "Heavy Brute"),
	RangedShooter	UMETA(DisplayName = "Ranged Shooter"),
	StealthAmbusher	UMETA(DisplayName = "Stealth Ambusher"),
	SwarmType		UMETA(DisplayName = "Swarm Type"),
	BossVariant		UMETA(DisplayName = "Boss Variant"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EBaseEnemyState : uint8
{
	Idle			UMETA(DisplayName = "Idle"),
	Patrolling		UMETA(DisplayName = "Patrolling"),
	Investigating	UMETA(DisplayName = "Investigating"),
	Chasing			UMETA(DisplayName = "Chasing"),
	Attacking		UMETA(DisplayName = "Attacking"),
	Stunned			UMETA(DisplayName = "Stunned"),
	Fleeing			UMETA(DisplayName = "Fleeing"),
	Dead			UMETA(DisplayName = "Dead")
};

UENUM(BlueprintType)
enum class EEnemyDifficulty : uint8
{
	Easy			UMETA(DisplayName = "Easy"),
	Normal			UMETA(DisplayName = "Normal"),
	Hard			UMETA(DisplayName = "Hard"),
	Nightmare		UMETA(DisplayName = "Nightmare"),
	Adaptive		UMETA(DisplayName = "Adaptive")
};

USTRUCT(BlueprintType)
struct FEnemyStats : public FTableRowBase
{
	GENERATED_BODY()

	FEnemyStats()
	{
		MaxHealth = 100.0f;
		MovementSpeed = 300.0f;
		AttackDamage = 25.0f;
		AttackRange = 150.0f;
		AttackCooldown = 2.0f;
		SightRange = 800.0f;
		HearingRange = 600.0f;
		PatrolRadius = 500.0f;
		StunDuration = 3.0f;
		ExperienceReward = 10;
		LootDropChance = 0.3f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float MaxHealth;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float MovementSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float AttackDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float AttackRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float AttackCooldown;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float SightRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float HearingRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	float PatrolRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float StunDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	int32 ExperienceReward;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	float LootDropChance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	TArray<FName> PossibleLootItems;
};

USTRUCT(BlueprintType)
struct FEnemyConfiguration
{
	GENERATED_BODY()

	FEnemyConfiguration()
	{
		EnemyType = EEnemyType::BasicZombie;
		Difficulty = EEnemyDifficulty::Normal;
		bUseCustomStats = false;
		DifficultyMultiplier = 1.0f;
		bCanCallForHelp = true;
		bCanUseWeapons = false;
		bCanTakeCover = false;
		bCanFlank = false;
		bCanRetreat = true;
		AggroRange = 1000.0f;
		DeaggroRange = 1500.0f;
		bRespawns = false;
		RespawnTime = 30.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	EEnemyType EnemyType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	EEnemyDifficulty Difficulty;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	bool bUseCustomStats;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration", meta = (EditCondition = "bUseCustomStats"))
	FEnemyStats CustomStats;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Difficulty")
	float DifficultyMultiplier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanCallForHelp;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanUseWeapons;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanTakeCover;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanFlank;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanRetreat;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	float AggroRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	float DeaggroRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	bool bRespawns;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning", meta = (EditCondition = "bRespawns"))
	float RespawnTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer EnemyTags;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyStateChanged, EBaseEnemyState, OldState, EBaseEnemyState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyHealthChanged, float, CurrentHealth, float, MaxHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyDeath, ABaseEnemyCharacter*, DeadEnemy);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyDamaged, float, DamageAmount, AActor*, DamageSource);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnEnemyPlayerDetected, APawn*, DetectedPlayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnEnemyPlayerLost);

UCLASS(BlueprintType, Blueprintable)
class SLT_API ABaseEnemyCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	ABaseEnemyCharacter();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;

	// Enemy configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy Configuration")
	FEnemyConfiguration EnemyConfig;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy Configuration")
	TSoftObjectPtr<UDataTable> EnemyStatsDatabase;

	// Current state
	UPROPERTY(BlueprintReadOnly, Category = "State")
	EBaseEnemyState CurrentState = EBaseEnemyState::Idle;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	float CurrentHealth;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	FEnemyStats CurrentStats;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	APawn* TargetPlayer;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	bool bIsPlayerDetected = false;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UPawnSensingComponent* PawnSensingComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UWidgetComponent* HealthBarWidget;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UEnemyAIComponent* AIComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UEnemyStateComponent* StateComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UEnemyAttackComponent* AttackComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UEnemyMovementComponent* MovementComponent;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyStateChanged OnEnemyStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyHealthChanged OnEnemyHealthChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyDeath OnEnemyDeath;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyDamaged OnEnemyDamaged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyPlayerDetected OnEnemyPlayerDetected;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyPlayerLost OnEnemyPlayerLost;

	// Main functions
	UFUNCTION(BlueprintCallable, Category = "Enemy")
	void InitializeEnemy();

	UFUNCTION(BlueprintCallable, Category = "Enemy")
	void SetEnemyState(EBaseEnemyState NewState);

	UFUNCTION(BlueprintCallable, Category = "Combat")
	virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

	UFUNCTION(BlueprintCallable, Category = "Combat")
	void Die();

	UFUNCTION(BlueprintCallable, Category = "Combat")
	void Stun(float Duration);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetTarget(APawn* NewTarget);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void LoseTarget();

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void ApplyDifficultyScaling();

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void LoadStatsFromDatabase();

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnEnemyInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStateChangedBP(EBaseEnemyState OldState, EBaseEnemyState NewState);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnDeathBP();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnDamagedBP(float DamageAmount, AActor* DamageSource);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPlayerDetectedBP(APawn* DetectedPlayer);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPlayerLostBP();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStunnedBP(float Duration);

protected:
	// Internal functions
	void UpdateHealthBar();
	void SpawnLoot();
	float CalculateDifficultyMultiplier() const;

	// Sensing callbacks
	UFUNCTION()
	void OnPlayerSeen(APawn* SeenPawn);

	UFUNCTION()
	void OnPlayerHeard(APawn* HeardPawn, const FVector& Location, float Volume);

	// Timer handles
	FTimerHandle StunTimerHandle;
	FTimerHandle RespawnTimerHandle;
};
