// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Design/VisualDesignTools.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class UVisualDesignComponent;
enum class EDesignVisualizationType : uint8;
struct FDesignTemplate;
struct FDesignValidationResult;
struct FDesignValidationRule;
#ifdef SLT_VisualDesignTools_generated_h
#error "VisualDesignTools.generated.h already included, missing '#pragma once' in VisualDesignTools.h"
#endif
#define SLT_VisualDesignTools_generated_h

#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_47_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDesignTemplate_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FDesignTemplate>();

#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_124_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDesignValidationRule_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FDesignValidationRule>();

#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_163_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDesignValidationResult_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FDesignValidationResult>();

#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_195_DELEGATE \
SLT_API void FOnDesignValidated_DelegateWrapper(const FMulticastScriptDelegate& OnDesignValidated, AActor* ValidatedActor, FDesignValidationResult const& Result);


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_196_DELEGATE \
SLT_API void FOnTemplateApplied_DelegateWrapper(const FMulticastScriptDelegate& OnTemplateApplied, AActor* TargetActor, FName TemplateID);


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_197_DELEGATE \
SLT_API void FOnVisualizationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVisualizationChanged, AActor* Actor, EDesignVisualizationType Type, bool bEnabled);


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execImportDesignData); \
	DECLARE_FUNCTION(execExportDesignData); \
	DECLARE_FUNCTION(execCopyDesignSettings); \
	DECLARE_FUNCTION(execAlignToSurface); \
	DECLARE_FUNCTION(execSnapToGrid); \
	DECLARE_FUNCTION(execGetValidationRules); \
	DECLARE_FUNCTION(execRemoveValidationRule); \
	DECLARE_FUNCTION(execAddValidationRule); \
	DECLARE_FUNCTION(execCreateCustomTemplate); \
	DECLARE_FUNCTION(execGetTemplate); \
	DECLARE_FUNCTION(execGetAvailableTemplates); \
	DECLARE_FUNCTION(execHideDesignHelper); \
	DECLARE_FUNCTION(execShowDesignHelper); \
	DECLARE_FUNCTION(execAutoFixCommonIssues); \
	DECLARE_FUNCTION(execValidateDesign); \
	DECLARE_FUNCTION(execApplyTemplate); \
	DECLARE_FUNCTION(execSetVisualizationType);


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUVisualDesignComponent(); \
	friend struct Z_Construct_UClass_UVisualDesignComponent_Statics; \
public: \
	DECLARE_CLASS(UVisualDesignComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UVisualDesignComponent)


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UVisualDesignComponent(UVisualDesignComponent&&); \
	UVisualDesignComponent(const UVisualDesignComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UVisualDesignComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UVisualDesignComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UVisualDesignComponent) \
	NO_API virtual ~UVisualDesignComponent();


#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_199_PROLOG
#define FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_202_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UVisualDesignComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h


#define FOREACH_ENUM_EDESIGNVISUALIZATIONTYPE(op) \
	op(EDesignVisualizationType::None) \
	op(EDesignVisualizationType::InventoryGrid) \
	op(EDesignVisualizationType::InteractionRange) \
	op(EDesignVisualizationType::PatrolPath) \
	op(EDesignVisualizationType::TriggerZone) \
	op(EDesignVisualizationType::SpawnPoint) \
	op(EDesignVisualizationType::Checkpoint) \
	op(EDesignVisualizationType::ResourceNode) \
	op(EDesignVisualizationType::WeaponUpgrade) \
	op(EDesignVisualizationType::PuzzleElement) \
	op(EDesignVisualizationType::LootSpawn) \
	op(EDesignVisualizationType::CameraPoint) \
	op(EDesignVisualizationType::AudioZone) \
	op(EDesignVisualizationType::LightingSetup) \
	op(EDesignVisualizationType::NavMeshDebug) 

enum class EDesignVisualizationType : uint8;
template<> struct TIsUEnumClass<EDesignVisualizationType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EDesignVisualizationType>();

#define FOREACH_ENUM_EDESIGNCOMPLEXITY(op) \
	op(EDesignComplexity::Beginner) \
	op(EDesignComplexity::Intermediate) \
	op(EDesignComplexity::Advanced) \
	op(EDesignComplexity::Expert) 

enum class EDesignComplexity : uint8;
template<> struct TIsUEnumClass<EDesignComplexity> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EDesignComplexity>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
