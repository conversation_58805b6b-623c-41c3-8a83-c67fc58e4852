#include "LevelProgressionManager.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Core/Systems/InventorySaveGame.h"
#include "Core/Player/SLTPlayerCharacter.h"

ULevelProgressionManager::ULevelProgressionManager()
{
	CurrentProgress = FProgressionSaveData();
}

void ULevelProgressionManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	LoadDatabases();
	LoadProgression();
}

void ULevelProgressionManager::Deinitialize()
{
	SaveProgression();
	Super::Deinitialize();
}

void ULevelProgressionManager::LoadDatabases()
{
	if (CheckpointDatabase.IsValid())
	{
		CachedCheckpointDatabase = CheckpointDatabase.LoadSynchronous();
	}

	if (LevelDatabase.IsValid())
	{
		CachedLevelDatabase = LevelDatabase.LoadSynchronous();
	}
}

bool ULevelProgressionManager::ActivateCheckpoint(FName CheckpointID)
{
	if (!CachedCheckpointDatabase)
	{
		return false;
	}

	FCheckpointData* CheckpointData = CachedCheckpointDatabase->FindRow<FCheckpointData>(CheckpointID, TEXT("ActivateCheckpoint"));
	if (!CheckpointData || !CheckpointData->bIsActive)
	{
		return false;
	}

	// Check requirements
	if (!CheckCheckpointRequirements(*CheckpointData))
	{
		return false;
	}

	// Update current progress
	CurrentProgress.LastCheckpoint = CheckpointID;
	CurrentProgress.CurrentLevel = FName(*CheckpointData->LevelName);

	// Add to unlocked checkpoints
	CurrentProgress.UnlockedCheckpoints.AddUnique(CheckpointID);

	// Unlock tags
	for (const FGameplayTag& Tag : CheckpointData->UnlockTags)
	{
		CurrentProgress.UnlockedTags.AddTag(Tag);
	}

	// Unlock other checkpoints
	for (FName UnlockCheckpoint : CheckpointData->UnlockCheckpoints)
	{
		CurrentProgress.UnlockedCheckpoints.AddUnique(UnlockCheckpoint);
	}

	// Save if required
	if (CheckpointData->bSaveProgress)
	{
		SaveProgression();
	}

	// Broadcast event
	OnCheckpointReached.Broadcast(CheckpointID, *CheckpointData);

	UpdateUnlockedContent();
	return true;
}

bool ULevelProgressionManager::LoadCheckpoint(FName CheckpointID)
{
	if (!CachedCheckpointDatabase)
	{
		return false;
	}

	FCheckpointData* CheckpointData = CachedCheckpointDatabase->FindRow<FCheckpointData>(CheckpointID, TEXT("LoadCheckpoint"));
	if (!CheckpointData)
	{
		return false;
	}

	// Load the level if different
	FName TargetLevel = FName(*CheckpointData->LevelName);
	if (TargetLevel != CurrentProgress.CurrentLevel)
	{
		if (!LoadLevel(TargetLevel))
		{
			return false;
		}
	}

	// Move player to checkpoint location
	if (UWorld* World = GetWorld())
	{
		if (ASLTPlayerCharacter* Player = Cast<ASLTPlayerCharacter>(UGameplayStatics::GetPlayerCharacter(World, 0)))
		{
			Player->SetActorLocationAndRotation(CheckpointData->PlayerLocation, CheckpointData->PlayerRotation);
		}
	}

	// Update current checkpoint
	CurrentProgress.LastCheckpoint = CheckpointID;
	return true;
}

FCheckpointData ULevelProgressionManager::GetCheckpointData(FName CheckpointID) const
{
	if (CachedCheckpointDatabase)
	{
		if (FCheckpointData* Data = CachedCheckpointDatabase->FindRow<FCheckpointData>(CheckpointID, TEXT("GetCheckpointData")))
		{
			return *Data;
		}
	}
	return FCheckpointData();
}

TArray<FName> ULevelProgressionManager::GetAvailableCheckpoints() const
{
	TArray<FName> AvailableCheckpoints;

	if (!CachedCheckpointDatabase)
	{
		return AvailableCheckpoints;
	}

	TArray<FName> RowNames = CachedCheckpointDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		if (FCheckpointData* CheckpointData = CachedCheckpointDatabase->FindRow<FCheckpointData>(RowName, TEXT("GetAvailableCheckpoints")))
		{
			if (CheckpointData->bIsActive && CheckCheckpointRequirements(*CheckpointData))
			{
				AvailableCheckpoints.Add(RowName);
			}
		}
	}

	return AvailableCheckpoints;
}

bool ULevelProgressionManager::LoadLevel(FName LevelID)
{
	if (!CachedLevelDatabase)
	{
		return false;
	}

	FLevelData* LevelData = CachedLevelDatabase->FindRow<FLevelData>(LevelID, TEXT("LoadLevel"));
	if (!LevelData)
	{
		return false;
	}

	// Check requirements
	if (!CheckLevelRequirements(*LevelData))
	{
		return false;
	}

	// Broadcast transition start
	OnLevelTransition.Broadcast(CurrentProgress.CurrentLevel, LevelID, true);

	// Load the level
	if (UWorld* World = GetWorld())
	{
		FName CurrentLevelName = CurrentProgress.CurrentLevel;
		CurrentProgress.CurrentLevel = LevelID;

		// Set level state to in progress
		SetLevelState(LevelID, ELevelState::InProgress);

		// Use UE's level streaming or direct loading
		UGameplayStatics::OpenLevel(World, FName(*LevelData->LevelPath));

		// Broadcast transition complete
		OnLevelTransition.Broadcast(CurrentLevelName, LevelID, false);
		return true;
	}

	return false;
}

void ULevelProgressionManager::SetLevelState(FName LevelID, ELevelState NewState)
{
	ELevelState* CurrentState = CurrentProgress.LevelStates.Find(LevelID);
	ELevelState OldState = CurrentState ? *CurrentState : ELevelState::Locked;

	if (OldState != NewState)
	{
		CurrentProgress.LevelStates.Add(LevelID, NewState);
		OnLevelStateChanged.Broadcast(LevelID, NewState);
		UpdateUnlockedContent();
	}
}

ELevelState ULevelProgressionManager::GetLevelState(FName LevelID) const
{
	if (const ELevelState* State = CurrentProgress.LevelStates.Find(LevelID))
	{
		return *State;
	}
	return ELevelState::Locked;
}

FLevelData ULevelProgressionManager::GetLevelData(FName LevelID) const
{
	if (CachedLevelDatabase)
	{
		if (FLevelData* Data = CachedLevelDatabase->FindRow<FLevelData>(LevelID, TEXT("GetLevelData")))
		{
			return *Data;
		}
	}
	return FLevelData();
}

TArray<FName> ULevelProgressionManager::GetUnlockedLevels() const
{
	TArray<FName> UnlockedLevels;

	if (!CachedLevelDatabase)
	{
		return UnlockedLevels;
	}

	TArray<FName> RowNames = CachedLevelDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		ELevelState State = GetLevelState(RowName);
		if (State != ELevelState::Locked)
		{
			UnlockedLevels.Add(RowName);
		}
	}

	return UnlockedLevels;
}

void ULevelProgressionManager::CompleteObjective(int32 ObjectiveIndex)
{
	CurrentProgress.CompletedObjectives = FMath::Max(CurrentProgress.CompletedObjectives, ObjectiveIndex + 1);
	OnObjectiveCompleted.Broadcast(ObjectiveIndex, CurrentProgress.CompletedObjectives);
	UpdateUnlockedContent();
}

void ULevelProgressionManager::SetTotalObjectives(int32 Total)
{
	CurrentProgress.TotalObjectives = Total;
}

float ULevelProgressionManager::GetObjectiveProgress() const
{
	if (CurrentProgress.TotalObjectives <= 0)
	{
		return 0.0f;
	}
	return (float)CurrentProgress.CompletedObjectives / (float)CurrentProgress.TotalObjectives;
}

void ULevelProgressionManager::UnlockTag(const FGameplayTag& Tag)
{
	CurrentProgress.UnlockedTags.AddTag(Tag);
	UpdateUnlockedContent();
}

bool ULevelProgressionManager::HasUnlockedTag(const FGameplayTag& Tag) const
{
	return CurrentProgress.UnlockedTags.HasTag(Tag);
}

void ULevelProgressionManager::SetPlayerLevel(int32 NewLevel)
{
	CurrentProgress.PlayerLevel = FMath::Max(1, NewLevel);
	UpdateUnlockedContent();
}

bool ULevelProgressionManager::SaveProgression()
{
	if (UInventorySaveGame* SaveGame = Cast<UInventorySaveGame>(UGameplayStatics::CreateSaveGameObject(UInventorySaveGame::StaticClass())))
	{
		// Save progression data to custom data
		SaveGame->SetCustomData(TEXT("CurrentLevel"), CurrentProgress.CurrentLevel.ToString());
		SaveGame->SetCustomData(TEXT("LastCheckpoint"), CurrentProgress.LastCheckpoint.ToString());
		SaveGame->SetCustomData(TEXT("PlayerLevel"), FString::FromInt(CurrentProgress.PlayerLevel));
		SaveGame->SetCustomData(TEXT("TotalPlayTime"), FString::SanitizeFloat(CurrentProgress.TotalPlayTime));
		SaveGame->SetCustomData(TEXT("CompletedObjectives"), FString::FromInt(CurrentProgress.CompletedObjectives));
		SaveGame->SetCustomData(TEXT("TotalObjectives"), FString::FromInt(CurrentProgress.TotalObjectives));

		// Save level states
		for (const auto& LevelState : CurrentProgress.LevelStates)
		{
			FString Key = FString::Printf(TEXT("LevelState_%s"), *LevelState.Key.ToString());
			SaveGame->SetCustomData(Key, FString::FromInt((int32)LevelState.Value));
		}

		// Save unlocked checkpoints
		FString CheckpointList;
		for (int32 i = 0; i < CurrentProgress.UnlockedCheckpoints.Num(); i++)
		{
			if (i > 0) CheckpointList += TEXT(",");
			CheckpointList += CurrentProgress.UnlockedCheckpoints[i].ToString();
		}
		SaveGame->SetCustomData(TEXT("UnlockedCheckpoints"), CheckpointList);

		return UGameplayStatics::SaveGameToSlot(SaveGame, TEXT("ProgressionSave"), 0);
	}
	return false;
}

bool ULevelProgressionManager::LoadProgression()
{
	if (UInventorySaveGame* SaveGame = Cast<UInventorySaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("ProgressionSave"), 0)))
	{
		// Load progression data
		CurrentProgress.CurrentLevel = FName(*SaveGame->GetCustomData(TEXT("CurrentLevel")));
		CurrentProgress.LastCheckpoint = FName(*SaveGame->GetCustomData(TEXT("LastCheckpoint")));
		CurrentProgress.PlayerLevel = FCString::Atoi(*SaveGame->GetCustomData(TEXT("PlayerLevel"), TEXT("1")));
		CurrentProgress.TotalPlayTime = FCString::Atof(*SaveGame->GetCustomData(TEXT("TotalPlayTime"), TEXT("0.0")));
		CurrentProgress.CompletedObjectives = FCString::Atoi(*SaveGame->GetCustomData(TEXT("CompletedObjectives"), TEXT("0")));
		CurrentProgress.TotalObjectives = FCString::Atoi(*SaveGame->GetCustomData(TEXT("TotalObjectives"), TEXT("0")));

		// Load level states
		CurrentProgress.LevelStates.Empty();
		if (CachedLevelDatabase)
		{
			TArray<FName> RowNames = CachedLevelDatabase->GetRowNames();
			for (FName RowName : RowNames)
			{
				FString Key = FString::Printf(TEXT("LevelState_%s"), *RowName.ToString());
				FString StateString = SaveGame->GetCustomData(Key);
				if (!StateString.IsEmpty())
				{
					ELevelState State = (ELevelState)FCString::Atoi(*StateString);
					CurrentProgress.LevelStates.Add(RowName, State);
				}
			}
		}

		// Load unlocked checkpoints
		CurrentProgress.UnlockedCheckpoints.Empty();
		FString CheckpointList = SaveGame->GetCustomData(TEXT("UnlockedCheckpoints"));
		if (!CheckpointList.IsEmpty())
		{
			TArray<FString> CheckpointStrings;
			CheckpointList.ParseIntoArray(CheckpointStrings, TEXT(","));
			for (const FString& CheckpointString : CheckpointStrings)
			{
				CurrentProgress.UnlockedCheckpoints.Add(FName(*CheckpointString));
			}
		}

		UpdateUnlockedContent();
		return true;
	}
	return false;
}

void ULevelProgressionManager::ResetProgression()
{
	CurrentProgress = FProgressionSaveData();
	UGameplayStatics::DeleteGameInSlot(TEXT("ProgressionSave"), 0);
}

bool ULevelProgressionManager::CheckCheckpointRequirements(const FCheckpointData& CheckpointData) const
{
	// Check required objectives
	if (CurrentProgress.CompletedObjectives < CheckpointData.RequiredObjectives)
	{
		return false;
	}

	// Check required tags
	for (const FGameplayTag& RequiredTag : CheckpointData.RequiredTags)
	{
		if (!CurrentProgress.UnlockedTags.HasTag(RequiredTag))
		{
			return false;
		}
	}

	// Check required checkpoints
	for (FName RequiredCheckpoint : CheckpointData.RequiredCheckpoints)
	{
		if (!CurrentProgress.UnlockedCheckpoints.Contains(RequiredCheckpoint))
		{
			return false;
		}
	}

	return true;
}

bool ULevelProgressionManager::CheckLevelRequirements(const FLevelData& LevelData) const
{
	// Check player level
	if (CurrentProgress.PlayerLevel < LevelData.RequiredLevel)
	{
		return false;
	}

	// Check required tags
	for (const FGameplayTag& RequiredTag : LevelData.RequiredTags)
	{
		if (!CurrentProgress.UnlockedTags.HasTag(RequiredTag))
		{
			return false;
		}
	}

	// Check required levels
	for (FName RequiredLevel : LevelData.RequiredLevels)
	{
		ELevelState State = GetLevelState(RequiredLevel);
		if (State != ELevelState::Completed && State != ELevelState::Mastered)
		{
			return false;
		}
	}

	return true;
}

void ULevelProgressionManager::UpdateUnlockedContent()
{
	// Update level states based on current progress
	if (!CachedLevelDatabase)
	{
		return;
	}

	TArray<FName> RowNames = CachedLevelDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		if (FLevelData* LevelData = CachedLevelDatabase->FindRow<FLevelData>(RowName, TEXT("UpdateUnlockedContent")))
		{
			ELevelState CurrentState = GetLevelState(RowName);
			if (CurrentState == ELevelState::Locked && CheckLevelRequirements(*LevelData))
			{
				SetLevelState(RowName, ELevelState::Available);
			}
		}
	}
}
