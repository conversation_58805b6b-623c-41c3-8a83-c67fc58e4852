#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "InventorySystem/Data/InventoryItemData.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "WeaponSystem.generated.h"

class USkeletalMeshComponent;
class UStaticMeshComponent;
class UAnimMontage;
class USoundBase;
class UParticleSystem;
class UNiagaraSystem;

UENUM(BlueprintType)
enum class EWeaponType : uint8
{
	None			UMETA(DisplayName = "None"),
	Pistol			UMETA(DisplayName = "Pistol"),
	Rifle			UMETA(DisplayName = "Rifle"),
	Shotgun			UMETA(DisplayName = "Shotgun"),
	Sniper			UMETA(DisplayName = "Sniper"),
	Melee			UMETA(DisplayName = "Melee"),
	Explosive		UMETA(DisplayName = "Explosive")
};

UENUM(BlueprintType)
enum class EWeaponCondition : uint8
{
	Broken			UMETA(DisplayName = "Broken"),
	Poor			UMETA(DisplayName = "Poor"),
	Fair			UMETA(DisplayName = "Fair"),
	Good			UMETA(DisplayName = "Good"),
	Excellent		UMETA(DisplayName = "Excellent"),
	Perfect			UMETA(DisplayName = "Perfect")
};

UENUM(BlueprintType)
enum class EAmmoType : uint8
{
	None			UMETA(DisplayName = "None"),
	Pistol9mm		UMETA(DisplayName = "9mm Pistol"),
	Rifle556		UMETA(DisplayName = "5.56 Rifle"),
	Shotgun12g		UMETA(DisplayName = "12 Gauge"),
	Sniper762		UMETA(DisplayName = "7.62 Sniper"),
	Explosive40mm	UMETA(DisplayName = "40mm Explosive"),
	Custom			UMETA(DisplayName = "Custom")
};

USTRUCT(BlueprintType)
struct FWeaponUpgrade
{
	GENERATED_BODY()

	FWeaponUpgrade()
	{
		UpgradeID = NAME_None;
		UpgradeName = FText::GetEmpty();
		UpgradeLevel = 0;
		MaxLevel = 5;
		CostPerLevel = 1000;
		StatModifier = 0.0f;
		bIsUnlocked = false;
		RequiredLevel = 1;
	}

	// Upgrade identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FName UpgradeID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FText UpgradeName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrade")
	FText UpgradeDescription;

	// Upgrade progression
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	int32 UpgradeLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	int32 MaxLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	int32 CostPerLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progression")
	float StatModifier; // Multiplier or additive value

	// Requirements
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	bool bIsUnlocked;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	int32 RequiredLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	// Visual changes
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UStaticMesh> UpgradeMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UMaterialInterface> UpgradeMaterial;
};

USTRUCT(BlueprintType)
struct FWeaponStats
{
	GENERATED_BODY()

	FWeaponStats()
	{
		BaseDamage = 25.0f;
		Accuracy = 0.8f;
		Range = 1000.0f;
		FireRate = 1.0f;
		ReloadTime = 2.0f;
		MagazineSize = 10;
		MaxDurability = 100.0f;
		CurrentDurability = 100.0f;
		CriticalChance = 0.1f;
		CriticalMultiplier = 2.0f;
		RecoilStrength = 1.0f;
	}

	// Combat stats
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float BaseDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float Accuracy; // 0.0 to 1.0

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float Range;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float FireRate; // Shots per second

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float ReloadTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	int32 MagazineSize;

	// Durability
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Durability")
	float MaxDurability;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Durability")
	float CurrentDurability;

	// Advanced stats
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	float CriticalChance; // 0.0 to 1.0

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	float CriticalMultiplier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	float RecoilStrength;
};

USTRUCT(BlueprintType)
struct FWeaponData : public FTableRowBase
{
	GENERATED_BODY()

	FWeaponData()
	{
		WeaponID = NAME_None;
		WeaponName = FText::GetEmpty();
		WeaponType = EWeaponType::None;
		AmmoType = EAmmoType::None;
		BaseStats = FWeaponStats();
		Condition = EWeaponCondition::Good;
		CurrentAmmo = 0;
		bIsAutomatic = false;
		bCanBeUpgraded = true;
		bRequiresTwoHands = true;
		WeaponMesh = nullptr;
		WeaponIcon = nullptr;
	}

	// Basic info
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	FName WeaponID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	FText WeaponName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	FText WeaponDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	EWeaponType WeaponType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Weapon")
	EAmmoType AmmoType;

	// Stats and condition
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	FWeaponStats BaseStats;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	EWeaponCondition Condition;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats")
	int32 CurrentAmmo;

	// Weapon properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsAutomatic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bCanBeUpgraded;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bRequiresTwoHands;

	// Upgrades
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Upgrades")
	TArray<FWeaponUpgrade> AvailableUpgrades;

	// Assets
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<USkeletalMesh> WeaponMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<UTexture2D> WeaponIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<UAnimMontage> FireAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<UAnimMontage> ReloadAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<USoundBase> FireSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<USoundBase> ReloadSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<UNiagaraSystem> MuzzleFlash;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponFired, FName, WeaponID, int32, RemainingAmmo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponReloaded, FName, WeaponID, int32, NewAmmoCount);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnWeaponUpgraded, FName, WeaponID, FName, UpgradeID, int32, NewLevel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponConditionChanged, FName, WeaponID, EWeaponCondition, NewCondition);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnWeaponEquipped, FName, WeaponID, bool, bIsEquipped);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UWeaponComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UWeaponComponent();

protected:
	virtual void BeginPlay() override;

public:
	// Weapon database
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TSoftObjectPtr<UDataTable> WeaponDatabase;

	// Current equipped weapons
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Equipment")
	TMap<FString, FName> EquippedWeapons; // Slot -> WeaponID

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Equipment")
	FName CurrentWeapon;

	// Weapon inventory
	UPROPERTY(SaveGame, VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	TMap<FName, FWeaponData> OwnedWeapons;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponFired OnWeaponFired;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponReloaded OnWeaponReloaded;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponUpgraded OnWeaponUpgraded;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponConditionChanged OnWeaponConditionChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnWeaponEquipped OnWeaponEquipped;

	// Weapon management
	UFUNCTION(BlueprintCallable, Category = "Weapons")
	bool AddWeapon(FName WeaponID);

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	bool RemoveWeapon(FName WeaponID);

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	bool HasWeapon(FName WeaponID) const;

	UFUNCTION(BlueprintCallable, Category = "Weapons")
	FWeaponData GetWeaponData(FName WeaponID) const;

	// Equipment functions
	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool EquipWeapon(FName WeaponID, const FString& SlotName = TEXT("Primary"));

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool UnequipWeapon(const FString& SlotName);

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	FName GetEquippedWeapon(const FString& SlotName) const;

	UFUNCTION(BlueprintCallable, Category = "Equipment")
	bool SwitchToWeapon(FName WeaponID);

	// Combat functions
	UFUNCTION(BlueprintCallable, Category = "Combat")
	bool CanFire() const;

	UFUNCTION(BlueprintCallable, Category = "Combat")
	bool Fire();

	UFUNCTION(BlueprintCallable, Category = "Combat")
	bool Reload();

	UFUNCTION(BlueprintCallable, Category = "Combat")
	float GetDamage() const;

	UFUNCTION(BlueprintCallable, Category = "Combat")
	float GetAccuracy() const;

	// Upgrade functions
	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	bool CanUpgradeWeapon(FName WeaponID, FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	bool UpgradeWeapon(FName WeaponID, FName UpgradeID);

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	int32 GetUpgradeCost(FName WeaponID, FName UpgradeID) const;

	UFUNCTION(BlueprintCallable, Category = "Upgrades")
	TArray<FWeaponUpgrade> GetAvailableUpgrades(FName WeaponID) const;

	// Condition functions
	UFUNCTION(BlueprintCallable, Category = "Condition")
	void DamageWeapon(FName WeaponID, float DamageAmount);

	UFUNCTION(BlueprintCallable, Category = "Condition")
	void RepairWeapon(FName WeaponID, float RepairAmount);

	UFUNCTION(BlueprintCallable, Category = "Condition")
	EWeaponCondition GetWeaponCondition(FName WeaponID) const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<FName> GetOwnedWeapons() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<FName> GetWeaponsByType(EWeaponType WeaponType) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool ConsumeAmmo(EAmmoType AmmoType, int32 Amount);

protected:
	// Cached database
	UPROPERTY()
	UDataTable* CachedWeaponDatabase;

	// Internal functions
	void LoadWeaponDatabase();
	FWeaponStats CalculateModifiedStats(const FWeaponData& WeaponData) const;
	EWeaponCondition CalculateCondition(float DurabilityPercentage) const;
	void UpdateWeaponCondition(FName WeaponID);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnWeaponStatsChanged(FName WeaponID, const FWeaponStats& NewStats);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnAmmoChanged(FName WeaponID, int32 CurrentAmmo, int32 MaxAmmo);
};
