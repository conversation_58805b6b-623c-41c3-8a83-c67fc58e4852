#include "LoadingManager.h"
#include "Blueprint/UserWidget.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Engine/LevelStreaming.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/PlatformFilemanager.h"

ULoadingManager::ULoadingManager()
{
	bIsLoading = false;
	MinLoadingScreenTime = 2.0f;
	bShowProgressBar = true;
	bShowLoadingTips = true;
	MaxConcurrentLoads = 5;
	AssetTimeoutSeconds = 30.0f;
	LoadingScreenWidget = nullptr;

	// Initialize default loading tips
	FLoadingTip Tip1;
	Tip1.TipText = FText::FromString(TEXT("Check your inventory regularly to manage space efficiently."));
	Tip1.Category = TEXT("Inventory");
	Tip1.MinDisplayTime = 3.0f;
	LoadingTips.Add(Tip1);

	FLoadingTip Tip2;
	Tip2.TipText = FText::FromString(TEXT("Rotate items to fit them better in your inventory grid."));
	Tip2.Category = TEXT("Inventory");
	Tip2.MinDisplayTime = 3.0f;
	LoadingTips.Add(Tip2);

	FLoadingTip Tip3;
	Tip3.TipText = FText::FromString(TEXT("Upgrade your weapons at merchants to increase their effectiveness."));
	Tip3.Category = TEXT("Combat");
	Tip3.MinDisplayTime = 3.0f;
	LoadingTips.Add(Tip3);

	FLoadingTip Tip4;
	Tip4.TipText = FText::FromString(TEXT("Save ammunition by aiming for critical hits."));
	Tip4.Category = TEXT("Combat");
	Tip4.MinDisplayTime = 3.0f;
	LoadingTips.Add(Tip4);

	FLoadingTip Tip5;
	Tip5.TipText = FText::FromString(TEXT("Explore thoroughly to find hidden items and secrets."));
	Tip5.Category = TEXT("Exploration");
	Tip5.MinDisplayTime = 3.0f;
	LoadingTips.Add(Tip5);
}

void ULoadingManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	
	// Initialize current progress
	CurrentProgress = FLoadingProgress();
	CurrentProgress.LoadingStartTime = FPlatformTime::Seconds();
}

void ULoadingManager::Deinitialize()
{
	StopLoading();
	
	// Clean up active handles
	for (auto& Handle : ActiveHandles)
	{
		if (Handle.IsValid())
		{
			Handle->CancelHandle();
		}
	}
	ActiveHandles.Empty();
	
	Super::Deinitialize();
}

void ULoadingManager::StartLoading(const FString& LoadingContext)
{
	if (bIsLoading)
	{
		return;
	}

	bIsLoading = true;
	CurrentProgress = FLoadingProgress();
	CurrentProgress.LoadingStartTime = FPlatformTime::Seconds();
	CurrentProgress.CurrentState = ELoadingState::Preparing;

	UpdateLoadingState(ELoadingState::Preparing);
	ShowLoadingScreen();
	
	AddLoadingMessage(FString::Printf(TEXT("Starting loading: %s"), *LoadingContext));
}

void ULoadingManager::StopLoading()
{
	if (!bIsLoading)
	{
		return;
	}

	bIsLoading = false;
	UpdateLoadingState(ELoadingState::Complete);
	
	// Delay hiding loading screen to respect minimum time
	float LoadingDuration = FPlatformTime::Seconds() - CurrentProgress.LoadingStartTime;
	float DelayTime = FMath::Max(0.0f, MinLoadingScreenTime - LoadingDuration);
	
	if (DelayTime > 0.0f)
	{
		FTimerHandle TimerHandle;
		if (UWorld* World = GetWorld())
		{
			World->GetTimerManager().SetTimer(TimerHandle, [this]()
			{
				HideLoadingScreen();
				OnLoadingComplete.Broadcast(true);
			}, DelayTime, false);
		}
	}
	else
	{
		HideLoadingScreen();
		OnLoadingComplete.Broadcast(true);
	}
}

void ULoadingManager::LoadAssetBundle(const FString& BundleName, EAssetPriority Priority)
{
	// Asset bundles functionality would be implemented here
	// For now, just log the request
	UE_LOG(LogTemp, Warning, TEXT("Asset bundle loading not implemented: %s"), *BundleName);
	OnLoadingError.Broadcast(FString::Printf(TEXT("Asset bundle '%s' not implemented"), *BundleName), BundleName);
}

void ULoadingManager::LoadAssets(const TArray<FSoftObjectPath>& AssetPaths, EAssetPriority Priority)
{
	if (AssetPaths.Num() == 0)
	{
		return;
	}

	FAssetLoadRequest Request;
	Request.AssetPaths = AssetPaths;
	Request.Priority = Priority;
	Request.RequestID = FGuid::NewGuid().ToString();
	
	LoadingQueue.Add(Request);
	
	if (!bIsLoading)
	{
		ProcessLoadingQueue();
	}
}

void ULoadingManager::LoadAssetAsync(const FSoftObjectPath& AssetPath, EAssetPriority Priority)
{
	TArray<FSoftObjectPath> SingleAsset;
	SingleAsset.Add(AssetPath);
	LoadAssets(SingleAsset, Priority);
}

void ULoadingManager::PreloadAssets(const TArray<FSoftObjectPath>& AssetPaths)
{
	for (const FSoftObjectPath& AssetPath : AssetPaths)
	{
		OnAssetLoadStarted(AssetPath.ToString());
		
		TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
			AssetPath,
			FStreamableDelegate::CreateUFunction(this, FName("OnAssetLoadComplete"), AssetPath.ToString(), true),
			FStreamableManager::AsyncLoadHighPriority
		);
		
		if (Handle.IsValid())
		{
			ActiveHandles.Add(Handle);
		}
	}
}

void ULoadingManager::UnloadAssets(const TArray<FSoftObjectPath>& AssetPaths)
{
	for (const FSoftObjectPath& AssetPath : AssetPaths)
	{
		StreamableManager.Unload(AssetPath);
	}
}

void ULoadingManager::StreamLevel(const FString& LevelName, bool bShouldBlock)
{
	if (UWorld* World = GetWorld())
	{
		UpdateLoadingState(ELoadingState::Streaming);
		AddLoadingMessage(FString::Printf(TEXT("Streaming level: %s"), *LevelName));
		
		FLatentActionInfo LatentInfo;
		LatentInfo.CallbackTarget = this;
		LatentInfo.ExecutionFunction = FName("OnLevelStreamComplete");
		LatentInfo.Linkage = 0;
		LatentInfo.UUID = FMath::Rand();
		
		UGameplayStatics::LoadStreamLevel(World, FName(*LevelName), true, bShouldBlock, LatentInfo);
	}
}

void ULoadingManager::UnstreamLevel(const FString& LevelName)
{
	if (UWorld* World = GetWorld())
	{
		FLatentActionInfo LatentInfo;
		UGameplayStatics::UnloadStreamLevel(World, FName(*LevelName), LatentInfo, true);
	}
}

bool ULoadingManager::IsLevelStreamed(const FString& LevelName) const
{
	if (UWorld* World = GetWorld())
	{
		for (ULevelStreaming* StreamingLevel : World->GetStreamingLevels())
		{
			if (StreamingLevel && StreamingLevel->GetWorldAssetPackageName() == LevelName)
			{
				return StreamingLevel->IsLevelLoaded();
			}
		}
	}
	return false;
}

void ULoadingManager::ShowLoadingScreen()
{
	if (LoadingScreenWidgetClass && !LoadingScreenWidget)
	{
		if (UWorld* World = GetWorld())
		{
			LoadingScreenWidget = CreateWidget<UUserWidget>(World, LoadingScreenWidgetClass);
			if (LoadingScreenWidget)
			{
				LoadingScreenWidget->AddToViewport(1000); // High Z-order
				OnLoadingScreenShown();
			}
		}
	}
}

void ULoadingManager::HideLoadingScreen()
{
	if (LoadingScreenWidget)
	{
		LoadingScreenWidget->RemoveFromParent();
		LoadingScreenWidget = nullptr;
		OnLoadingScreenHidden();
	}
}

FLoadingTip ULoadingManager::GetRandomLoadingTip(const FString& Category) const
{
	TArray<FLoadingTip> FilteredTips;
	
	if (Category.IsEmpty())
	{
		FilteredTips = LoadingTips;
	}
	else
	{
		for (const FLoadingTip& Tip : LoadingTips)
		{
			if (Tip.Category == Category)
			{
				FilteredTips.Add(Tip);
			}
		}
	}
	
	if (FilteredTips.Num() > 0)
	{
		int32 RandomIndex = FMath::RandRange(0, FilteredTips.Num() - 1);
		return FilteredTips[RandomIndex];
	}
	
	return FLoadingTip();
}

void ULoadingManager::UpdateProgress(float NewProgress, const FString& CurrentTask)
{
	CurrentProgress.OverallProgress = FMath::Clamp(NewProgress, 0.0f, 1.0f);
	CurrentProgress.CurrentAssetName = CurrentTask;
	CurrentProgress.EstimatedTimeRemaining = CalculateEstimatedTime();
	
	CalculateProgress();
	OnLoadingProgressUpdated.Broadcast(CurrentProgress);
}

void ULoadingManager::AddLoadingMessage(const FString& Message)
{
	CurrentProgress.LoadingMessages.Add(Message);
	
	// Keep only the last 10 messages
	if (CurrentProgress.LoadingMessages.Num() > 10)
	{
		CurrentProgress.LoadingMessages.RemoveAt(0);
	}
	
	UE_LOG(LogTemp, Log, TEXT("Loading: %s"), *Message);
}

float ULoadingManager::GetEstimatedTimeRemaining() const
{
	return CurrentProgress.EstimatedTimeRemaining;
}

void ULoadingManager::SetAssetPriority(const FSoftObjectPath& AssetPath, EAssetPriority Priority)
{
	// Find existing request and update priority
	for (FAssetLoadRequest& Request : LoadingQueue)
	{
		if (Request.AssetPaths.Contains(AssetPath))
		{
			Request.Priority = Priority;
			break;
		}
	}
}

void ULoadingManager::ClearAssetCache()
{
	// Clear streamable manager cache
	// Note: GetStreamableRenderAssetRegistry() may not be available in all UE versions
	// StreamableManager.GetStreamableRenderAssetRegistry().Empty();

	// Alternative: Force garbage collection to clear unused assets
	GEngine->ForceGarbageCollection(true);
	AddLoadingMessage(TEXT("Asset cache cleared"));
}

int32 ULoadingManager::GetMemoryUsage() const
{
	// This would return actual memory usage in a real implementation
	return 0;
}

void ULoadingManager::OptimizeMemory()
{
	// Force garbage collection
	GEngine->ForceGarbageCollection(true);
	AddLoadingMessage(TEXT("Memory optimized"));
}

void ULoadingManager::ProcessLoadingQueue()
{
	if (LoadingQueue.Num() == 0)
	{
		return;
	}

	// Sort queue by priority
	LoadingQueue.Sort([](const FAssetLoadRequest& A, const FAssetLoadRequest& B)
	{
		return (int32)A.Priority < (int32)B.Priority;
	});

	// Process high priority requests first
	int32 ProcessedCount = 0;
	for (int32 i = 0; i < LoadingQueue.Num() && ProcessedCount < MaxConcurrentLoads; i++)
	{
		const FAssetLoadRequest& Request = LoadingQueue[i];
		
		for (const FSoftObjectPath& AssetPath : Request.AssetPaths)
		{
			OnAssetLoadStarted(AssetPath.ToString());
			
			TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
				AssetPath,
				FStreamableDelegate::CreateUFunction(this, FName("OnAssetLoadComplete"), AssetPath.ToString(), true)
			);
			
			if (Handle.IsValid())
			{
				ActiveHandles.Add(Handle);
				ProcessedCount++;
				
				if (ProcessedCount >= MaxConcurrentLoads)
				{
					break;
				}
			}
		}
	}

	// Remove processed requests
	LoadingQueue.RemoveAt(0, FMath::Min(ProcessedCount, LoadingQueue.Num()));
}

void ULoadingManager::OnAssetLoadComplete(const FString& AssetPath, bool bSuccess)
{
	CurrentProgress.LoadedAssets++;
	OnAssetLoaded.Broadcast(AssetPath, bSuccess);
	
	if (bSuccess)
	{
		AddLoadingMessage(FString::Printf(TEXT("Loaded: %s"), *AssetPath));
	}
	else
	{
		OnLoadingError.Broadcast(FString::Printf(TEXT("Failed to load: %s"), *AssetPath), AssetPath);
	}
	
	CalculateProgress();
	
	// Continue processing queue
	if (LoadingQueue.Num() > 0)
	{
		ProcessLoadingQueue();
	}
	else if (ActiveHandles.Num() == 0)
	{
		// All loading complete
		UpdateLoadingState(ELoadingState::Complete);
	}
}

void ULoadingManager::UpdateLoadingState(ELoadingState NewState)
{
	if (CurrentProgress.CurrentState != NewState)
	{
		CurrentProgress.CurrentState = NewState;
		OnLoadingStateChanged.Broadcast(NewState);
		
		FString StateString = UEnum::GetValueAsString(NewState);
		AddLoadingMessage(FString::Printf(TEXT("State changed to: %s"), *StateString));
	}
}

void ULoadingManager::CalculateProgress()
{
	if (CurrentProgress.TotalAssets > 0)
	{
		CurrentProgress.AssetProgress = (float)CurrentProgress.LoadedAssets / (float)CurrentProgress.TotalAssets;
	}
	
	// Calculate overall progress based on current state
	switch (CurrentProgress.CurrentState)
	{
		case ELoadingState::Preparing:
			CurrentProgress.OverallProgress = 0.1f;
			break;
		case ELoadingState::Loading:
			CurrentProgress.OverallProgress = 0.1f + (CurrentProgress.AssetProgress * 0.7f);
			break;
		case ELoadingState::Streaming:
			CurrentProgress.OverallProgress = 0.8f + (CurrentProgress.StreamingProgress * 0.15f);
			break;
		case ELoadingState::Finalizing:
			CurrentProgress.OverallProgress = 0.95f;
			break;
		case ELoadingState::Complete:
			CurrentProgress.OverallProgress = 1.0f;
			break;
		default:
			break;
	}
	
	CurrentProgress.EstimatedTimeRemaining = CalculateEstimatedTime();
}

float ULoadingManager::CalculateEstimatedTime() const
{
	float ElapsedTime = FPlatformTime::Seconds() - CurrentProgress.LoadingStartTime;
	
	if (CurrentProgress.OverallProgress > 0.0f && CurrentProgress.OverallProgress < 1.0f)
	{
		float TotalEstimatedTime = ElapsedTime / CurrentProgress.OverallProgress;
		return TotalEstimatedTime - ElapsedTime;
	}
	
	return 0.0f;
}
