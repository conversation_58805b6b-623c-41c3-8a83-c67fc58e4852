#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "../../Core/Interfaces/Interactable.h"
#include "../Data/InventoryItemData.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "ItemActor.generated.h"

class UInventoryGridComponent;

UCLASS(BlueprintType, Blueprintable)
class SLT_API AItemActor : public AActor, public IInteractable
{
	GENERATED_BODY()
	
public:	
	AItemActor();

protected:
	virtual void BeginPlay() override;

public:
	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* MeshComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USphereComponent* InteractionSphere;

	// Item properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item", meta = (ExposeOnSpawn = "true"))
	FName ItemID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item", meta = (ExposeOnSpawn = "true", ClampMin = "1"))
	int32 Quantity = 1;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item")
	TSoftObjectPtr<UDataTable> ItemDatabase;

	// Interaction settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	FText CustomInteractionText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bUseCustomInteractionText = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bAutoPickup = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bDestroyOnPickup = true;

	// Visual settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	bool bEnableFloating = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bEnableFloating"))
	float FloatAmplitude = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bEnableFloating"))
	float FloatSpeed = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	bool bEnableRotation = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bEnableRotation"))
	float RotationSpeed = 45.0f;

	// Events
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnItemPickedUp, AItemActor*, ItemActor, APawn*, PickupPawn);
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnItemPickedUp OnItemPickedUp;

	// Public functions
	UFUNCTION(BlueprintCallable, Category = "Item")
	bool InitializeFromItemData(FName InItemID, int32 InQuantity = 1);

	UFUNCTION(BlueprintCallable, Category = "Item")
	bool GetItemData(FInventoryItemData& OutItemData) const;

	UFUNCTION(BlueprintCallable, Category = "Item")
	void SetQuantity(int32 NewQuantity);

	UFUNCTION(BlueprintCallable, Category = "Item")
	int32 GetQuantity() const { return Quantity; }

	UFUNCTION(BlueprintCallable, Category = "Item")
	FName GetItemID() const { return ItemID; }

	// IInteractable interface
	virtual FInteractionData GetInteractionData_Implementation() const override;
	virtual bool CanInteract_Implementation(APawn* InteractingPawn) const override;
	virtual void OnInteractionStart_Implementation(APawn* InteractingPawn) override;
	virtual void OnInteractionComplete_Implementation(APawn* InteractingPawn) override;
	virtual void OnInteractionCancel_Implementation(APawn* InteractingPawn) override;
	virtual FVector GetInteractionLocation_Implementation() const override;

protected:
	virtual void Tick(float DeltaTime) override;

	// Internal data
	UPROPERTY()
	FInventoryItemData CachedItemData;

	UPROPERTY()
	bool bItemDataCached = false;

	// Animation state
	float FloatTimeAccumulator = 0.0f;
	FVector InitialLocation;

	// Internal functions
	void UpdateFloatingAnimation(float DeltaTime);
	void UpdateRotationAnimation(float DeltaTime);
	void LoadItemDataFromDatabase();
	void UpdateMeshFromItemData();
	bool TryAddToInventory(APawn* InteractingPawn);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnItemInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPickupAttempt(APawn* InteractingPawn, bool bSuccess);
};
