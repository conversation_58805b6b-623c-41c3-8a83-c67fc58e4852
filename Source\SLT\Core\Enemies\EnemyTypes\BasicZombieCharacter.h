#pragma once

#include "CoreMinimal.h"
#include "../BaseEnemyCharacter.h"
#include "BasicZombieCharacter.generated.h"

UENUM(BlueprintType)
enum class EZombieVariant : uint8
{
	Standard		UMETA(DisplayName = "Standard"),
	Crawler			UMETA(DisplayName = "Crawler"),
	Shambler		UMETA(DisplayName = "Shambler"),
	Infected		UMETA(DisplayName = "Infected"),
	Decayed			UMETA(DisplayName = "Decayed")
};

USTRUCT(BlueprintType)
struct FZombieConfiguration
{
	GENERATED_BODY()

	FZombieConfiguration()
	{
		ZombieVariant = EZombieVariant::Standard;
		bCanGrab = true;
		bCanBite = true;
		bCanInfect = false;
		InfectionChance = 0.1f;
		GrabRange = 120.0f;
		BiteRange = 80.0f;
		GrabDuration = 2.0f;
		BiteDamage = 30.0f;
		GrabDamage = 15.0f;
		bCanBreakDoors = false;
		DoorBreakTime = 5.0f;
		bAttractedToNoise = true;
		NoiseAttractionRange = 800.0f;
		bCanCallOthers = true;
		CallRange = 600.0f;
		DeathSoundRange = 400.0f;
		bDropsLoot = true;
		LootDropChance = 0.3f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Zombie Configuration")
	EZombieVariant ZombieVariant;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanGrab;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanBite;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanInfect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanInfect", ClampMin = "0.0", ClampMax = "1.0"))
	float InfectionChance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float GrabRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float BiteRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float GrabDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float BiteDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float GrabDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment")
	bool bCanBreakDoors;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Environment", meta = (EditCondition = "bCanBreakDoors"))
	float DoorBreakTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bAttractedToNoise;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior", meta = (EditCondition = "bAttractedToNoise"))
	float NoiseAttractionRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bCanCallOthers;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior", meta = (EditCondition = "bCanCallOthers"))
	float CallRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	float DeathSoundRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot")
	bool bDropsLoot;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot", meta = (EditCondition = "bDropsLoot", ClampMin = "0.0", ClampMax = "1.0"))
	float LootDropChance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot")
	TArray<FName> PossibleLootItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TArray<TSoftObjectPtr<USkeletalMesh>> VariantMeshes;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TArray<TSoftObjectPtr<USoundBase>> IdleSounds;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TArray<TSoftObjectPtr<USoundBase>> AttackSounds;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TArray<TSoftObjectPtr<USoundBase>> DeathSounds;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnZombieGrabStarted, APawn*, GrabbedTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnZombieGrabEnded, APawn*, ReleasedTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnZombieBite, APawn*, BittenTarget, bool, bInfected);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnZombieCallOthers, const FVector&, CallLocation);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDoorBreakStarted, AActor*, Door);

UCLASS(BlueprintType, Blueprintable)
class SLT_API ABasicZombieCharacter : public ABaseEnemyCharacter
{
	GENERATED_BODY()

public:
	ABasicZombieCharacter();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;

	// Zombie-specific configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Zombie Configuration")
	FZombieConfiguration ZombieConfig;

	// Current zombie state
	UPROPERTY(BlueprintReadOnly, Category = "Zombie State")
	bool bIsGrabbing = false;

	UPROPERTY(BlueprintReadOnly, Category = "Zombie State")
	APawn* GrabbedTarget = nullptr;

	UPROPERTY(BlueprintReadOnly, Category = "Zombie State")
	bool bIsBreakingDoor = false;

	UPROPERTY(BlueprintReadOnly, Category = "Zombie State")
	AActor* TargetDoor = nullptr;

	UPROPERTY(BlueprintReadOnly, Category = "Zombie State")
	float DoorBreakProgress = 0.0f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Zombie Events")
	FOnZombieGrabStarted OnZombieGrabStarted;

	UPROPERTY(BlueprintAssignable, Category = "Zombie Events")
	FOnZombieGrabEnded OnZombieGrabEnded;

	UPROPERTY(BlueprintAssignable, Category = "Zombie Events")
	FOnZombieBite OnZombieBite;

	UPROPERTY(BlueprintAssignable, Category = "Zombie Events")
	FOnZombieCallOthers OnZombieCallOthers;

	UPROPERTY(BlueprintAssignable, Category = "Zombie Events")
	FOnDoorBreakStarted OnDoorBreakStarted;

	// Zombie-specific functions
	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void StartGrab(APawn* Target);

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void EndGrab();

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void PerformBite(APawn* Target);

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void CallOtherZombies();

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void StartBreakingDoor(AActor* Door);

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void StopBreakingDoor();

	UFUNCTION(BlueprintCallable, Category = "Zombie Actions")
	void RespondToNoise(const FVector& NoiseLocation, float NoiseVolume);

	// Zombie AI functions
	UFUNCTION(BlueprintCallable, Category = "Zombie AI")
	bool ShouldGrabTarget() const;

	UFUNCTION(BlueprintCallable, Category = "Zombie AI")
	bool ShouldBiteTarget() const;

	UFUNCTION(BlueprintCallable, Category = "Zombie AI")
	bool ShouldBreakDoor() const;

	UFUNCTION(BlueprintCallable, Category = "Zombie AI")
	AActor* FindNearestDoor() const;

	UFUNCTION(BlueprintCallable, Category = "Zombie AI")
	TArray<ABasicZombieCharacter*> GetNearbyZombies(float Range = 600.0f) const;

	// Configuration functions
	UFUNCTION(BlueprintCallable, Category = "Zombie Configuration")
	void SetZombieVariant(EZombieVariant NewVariant);

	UFUNCTION(BlueprintCallable, Category = "Zombie Configuration")
	void ApplyVariantSettings();

	UFUNCTION(BlueprintCallable, Category = "Zombie Configuration")
	void RandomizeAppearance();

	// Override base functions
	virtual void Die() override;
	virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnZombieInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnGrabStartedBP(APawn* Target);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnGrabEndedBP(APawn* Target);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnBitePerformedBP(APawn* Target, bool bInfected);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnDoorBreakStartedBP(AActor* Door);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnDoorBrokenBP(AActor* Door);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnNoiseHeardBP(const FVector& NoiseLocation, float Volume);

	UFUNCTION(BlueprintImplementableEvent, Category = "Zombie Events")
	void OnVariantChangedBP(EZombieVariant OldVariant, EZombieVariant NewVariant);

protected:
	// Timer handles
	FTimerHandle GrabTimerHandle;
	FTimerHandle DoorBreakTimerHandle;
	FTimerHandle CallCooldownTimerHandle;

	// Internal state
	float LastCallTime = 0.0f;
	float CallCooldown = 10.0f;

	// Internal functions
	void InitializeZombie();
	void UpdateGrab(float DeltaTime);
	void UpdateDoorBreaking(float DeltaTime);
	void ProcessZombieAI();
	void PlayZombieSound(const TArray<TSoftObjectPtr<USoundBase>>& SoundArray);
	void ApplyInfection(APawn* Target);
	bool CanGrabTarget(APawn* Target) const;
	bool CanBiteTarget(APawn* Target) const;
	void SetupZombieAttacks();
	void ConfigureZombieMovement();
};
