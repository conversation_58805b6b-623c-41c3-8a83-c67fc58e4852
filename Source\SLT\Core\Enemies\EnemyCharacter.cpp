#include "EnemyCharacter.h"
#include "Perception/PawnSensingComponent.h"
#include "Components/WidgetComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/KismetMathLibrary.h"
#include "InventorySystem/Actors/ItemActor.h"

AEnemyCharacter::AEnemyCharacter()
{
	PrimaryActorTick.bCanEverTick = true;

	// Set up collision
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Camera, ECollisionResponse::ECR_Ignore);

	// Create pawn sensing component
	PawnSensingComponent = CreateDefaultSubobject<UPawnSensingComponent>(TEXT("PawnSensingComponent"));
	PawnSensingComponent->SightRadius = SightRadius;
	PawnSensingComponent->HearingThreshold = HearingRadius;
	PawnSensingComponent->LOSHearingThreshold = HearingRadius * 0.5f;
	PawnSensingComponent->bOnlySensePlayers = true;
	PawnSensingComponent->bSeePawns = true;
	PawnSensingComponent->bHearNoises = true;

	// Create health bar widget
	HealthBarWidget = CreateDefaultSubobject<UWidgetComponent>(TEXT("HealthBarWidget"));
	HealthBarWidget->SetupAttachment(RootComponent);
	HealthBarWidget->SetRelativeLocation(FVector(0.0f, 0.0f, 100.0f));
	HealthBarWidget->SetWidgetSpace(EWidgetSpace::Screen);
	HealthBarWidget->SetDrawSize(FVector2D(100.0f, 20.0f));

	// Default stats
	MaxHealth = 100.0f;
	CurrentHealth = MaxHealth;
	AttackDamage = 25.0f;
	AttackRange = 150.0f;
	AttackCooldown = 2.0f;
	SightRadius = 800.0f;
	HearingRadius = 600.0f;
	LosePlayerTime = 5.0f;
	PatrolRadius = 500.0f;
	PatrolWaitTime = 3.0f;
	LootDropChance = 0.5f;

	// AI settings
	bUseRandomPatrol = true;
	CurrentState = EEnemyState::Idle;
	bIsDead = false;
	TargetPlayer = nullptr;
	LastAttackTime = 0.0f;
	CurrentPatrolIndex = 0;

	// Set up character movement
	GetCharacterMovement()->MaxWalkSpeed = 300.0f;
	GetCharacterMovement()->bOrientRotationToMovement = true;
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 540.0f, 0.0f);
}

void AEnemyCharacter::BeginPlay()
{
	Super::BeginPlay();
	
	HomeLocation = GetActorLocation();
	CurrentHealth = MaxHealth;

	// Bind sensing events
	if (PawnSensingComponent)
	{
		PawnSensingComponent->OnSeePawn.AddDynamic(this, &AEnemyCharacter::OnSeePawn);
		PawnSensingComponent->OnHearNoise.AddDynamic(this, &AEnemyCharacter::OnHearNoise);
	}

	InitializeAI();
	UpdateHealthBar();

	// Start patrolling if we have patrol points
	if (PatrolPoints.Num() > 0 || bUseRandomPatrol)
	{
		StartPatrol();
	}
}

void AEnemyCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update health bar visibility based on distance to player
	if (HealthBarWidget && TargetPlayer)
	{
		float Distance = FVector::Dist(GetActorLocation(), TargetPlayer->GetActorLocation());
		bool bShouldShowHealthBar = Distance < SightRadius && CurrentHealth < MaxHealth;
		HealthBarWidget->SetVisibility(bShouldShowHealthBar);
	}
}

void AEnemyCharacter::InitializeAI()
{
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		if (BehaviorTree)
		{
			AIController->RunBehaviorTree(BehaviorTree);
		}
	}
}

void AEnemyCharacter::TakeDamage(float DamageAmount, AActor* DamageSource)
{
	if (bIsDead || DamageAmount <= 0.0f)
	{
		return;
	}

	CurrentHealth = FMath::Max(0.0f, CurrentHealth - DamageAmount);
	
	OnEnemyDamaged.Broadcast(this, DamageAmount, DamageSource);
	OnDamageReceived(DamageAmount, DamageSource);
	
	UpdateHealthBar();

	// If we took damage from a player, start chasing them
	if (APawn* PlayerPawn = Cast<APawn>(DamageSource))
	{
		StartChasing(PlayerPawn);
	}

	// Check if we died
	if (CurrentHealth <= 0.0f)
	{
		Die(DamageSource);
	}
	else
	{
		// Enter alert state if not already chasing
		if (CurrentState != EEnemyState::Chasing && CurrentState != EEnemyState::Attacking)
		{
			SetEnemyState(EEnemyState::Alert);
		}
	}
}

void AEnemyCharacter::Die(AActor* Killer)
{
	if (bIsDead)
	{
		return;
	}

	bIsDead = true;
	CurrentHealth = 0.0f;
	SetEnemyState(EEnemyState::Dead);

	// Stop AI
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		AIController->BrainComponent->StopLogic(TEXT("Dead"));
	}

	// Clear timers
	GetWorld()->GetTimerManager().ClearTimer(LosePlayerTimerHandle);
	GetWorld()->GetTimerManager().ClearTimer(PatrolTimerHandle);

	// Disable collision
	GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	GetCharacterMovement()->DisableMovement();

	// Spawn loot
	SpawnLoot();

	OnEnemyDeath.Broadcast(this, Killer);
	OnDeathEvent(Killer);

	// Destroy after a delay (or let Blueprint handle it)
	FTimerHandle DestroyTimerHandle;
	GetWorld()->GetTimerManager().SetTimer(DestroyTimerHandle, [this]()
	{
		Destroy();
	}, 5.0f, false);
}

bool AEnemyCharacter::CanAttack() const
{
	if (bIsDead || !TargetPlayer)
	{
		return false;
	}

	float TimeSinceLastAttack = GetWorld()->GetTimeSeconds() - LastAttackTime;
	if (TimeSinceLastAttack < AttackCooldown)
	{
		return false;
	}

	float DistanceToTarget = GetDistanceToPlayer();
	return DistanceToTarget <= AttackRange;
}

void AEnemyCharacter::Attack(AActor* Target)
{
	if (!CanAttack() || !Target)
	{
		return;
	}

	LastAttackTime = GetWorld()->GetTimeSeconds();
	SetEnemyState(EEnemyState::Attacking);

	// Deal damage to target if it's a character
	if (ACharacter* TargetCharacter = Cast<ACharacter>(Target))
	{
		// This would need to be implemented based on your damage system
		// For now, we'll just call a Blueprint event
	}

	OnAttackExecuted(Target);

	// Return to chasing after attack
	FTimerHandle AttackRecoveryTimer;
	GetWorld()->GetTimerManager().SetTimer(AttackRecoveryTimer, [this]()
	{
		if (TargetPlayer && !bIsDead)
		{
			SetEnemyState(EEnemyState::Chasing);
		}
	}, 1.0f, false);
}

void AEnemyCharacter::SetEnemyState(EEnemyState NewState)
{
	if (CurrentState == NewState || bIsDead)
	{
		return;
	}

	EEnemyState OldState = CurrentState;
	CurrentState = NewState;

	OnEnemyStateChanged.Broadcast(OldState, NewState);
	OnStateChanged(OldState, NewState);

	// Update AI blackboard
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
		{
			BlackboardComp->SetValueAsEnum(TEXT("EnemyState"), (uint8)NewState);
		}
	}
}

void AEnemyCharacter::StartPatrol()
{
	if (bIsDead)
	{
		return;
	}

	SetEnemyState(EEnemyState::Patrol);
	TargetPlayer = nullptr;

	// Clear lose player timer
	GetWorld()->GetTimerManager().ClearTimer(LosePlayerTimerHandle);
}

void AEnemyCharacter::StartChasing(APawn* Player)
{
	if (bIsDead || !Player)
	{
		return;
	}

	TargetPlayer = Player;
	LastKnownPlayerLocation = Player->GetActorLocation();
	SetEnemyState(EEnemyState::Chasing);

	OnPlayerDetected.Broadcast(this, Player);
	OnPlayerSpotted(Player);

	// Clear lose player timer
	GetWorld()->GetTimerManager().ClearTimer(LosePlayerTimerHandle);

	// Update AI blackboard
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
		{
			BlackboardComp->SetValueAsObject(TEXT("TargetPlayer"), Player);
			BlackboardComp->SetValueAsVector(TEXT("LastKnownPlayerLocation"), LastKnownPlayerLocation);
		}
	}
}

void AEnemyCharacter::LosePlayer()
{
	if (bIsDead)
	{
		return;
	}

	// Start timer to lose player
	GetWorld()->GetTimerManager().SetTimer(LosePlayerTimerHandle, this, &AEnemyCharacter::OnLosePlayerTimer, LosePlayerTime, false);
	
	SetEnemyState(EEnemyState::Investigating);
	Investigate(LastKnownPlayerLocation);
}

void AEnemyCharacter::OnLosePlayerTimer()
{
	TargetPlayer = nullptr;
	SetEnemyState(EEnemyState::Patrol);
	
	OnPlayerLost.Broadcast(this);

	// Update AI blackboard
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
		{
			BlackboardComp->ClearValue(TEXT("TargetPlayer"));
		}
	}
}

void AEnemyCharacter::Investigate(const FVector& Location)
{
	if (bIsDead)
	{
		return;
	}

	LastKnownPlayerLocation = Location;
	SetEnemyState(EEnemyState::Investigating);
	
	OnInvestigationStarted(Location);

	// Update AI blackboard
	if (AAIController* AIController = Cast<AAIController>(GetController()))
	{
		if (UBlackboardComponent* BlackboardComp = AIController->GetBlackboardComponent())
		{
			BlackboardComp->SetValueAsVector(TEXT("InvestigateLocation"), Location);
		}
	}
}

void AEnemyCharacter::OnSeePawn(APawn* Pawn)
{
	if (bIsDead || !Pawn || Pawn == this)
	{
		return;
	}

	// Check if it's a player
	if (Pawn->IsPlayerControlled())
	{
		StartChasing(Pawn);
	}
}

void AEnemyCharacter::OnHearNoise(APawn* NoiseInstigator, const FVector& Location, float Volume)
{
	if (bIsDead || CurrentState == EEnemyState::Chasing)
	{
		return;
	}

	// If we hear a player, investigate the location
	if (NoiseInstigator && NoiseInstigator->IsPlayerControlled())
	{
		Investigate(Location);
	}
}

bool AEnemyCharacter::HasLineOfSightToPlayer() const
{
	if (!TargetPlayer)
	{
		return false;
	}

	FVector Start = GetActorLocation();
	FVector End = TargetPlayer->GetActorLocation();

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(this);
	QueryParams.AddIgnoredActor(TargetPlayer);

	bool bHit = GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECollisionChannel::ECC_Visibility, QueryParams);
	return !bHit;
}

float AEnemyCharacter::GetDistanceToPlayer() const
{
	if (!TargetPlayer)
	{
		return FLT_MAX;
	}

	return FVector::Dist(GetActorLocation(), TargetPlayer->GetActorLocation());
}

void AEnemyCharacter::SpawnLoot()
{
	if (LootTable.Num() == 0 || FMath::RandRange(0.0f, 1.0f) > LootDropChance)
	{
		return;
	}

	// Pick a random item from the loot table
	FName LootItemID = LootTable[FMath::RandRange(0, LootTable.Num() - 1)];
	
	// Spawn the item actor
	FVector SpawnLocation = GetActorLocation() + FVector(0.0f, 0.0f, 50.0f);
	FRotator SpawnRotation = FRotator::ZeroRotator;

	if (AItemActor* LootItem = GetWorld()->SpawnActor<AItemActor>(AItemActor::StaticClass(), SpawnLocation, SpawnRotation))
	{
		LootItem->InitializeFromItemData(LootItemID, 1);
	}
}

void AEnemyCharacter::UpdateHealthBar()
{
	// This would update the health bar widget
	// Implementation depends on your UI system
}

FVector AEnemyCharacter::GetNextPatrolPoint()
{
	if (PatrolPoints.Num() == 0)
	{
		// Random patrol around home location
		FVector RandomDirection = UKismetMathLibrary::RandomUnitVector();
		RandomDirection.Z = 0.0f; // Keep on ground level
		return HomeLocation + RandomDirection * FMath::RandRange(100.0f, PatrolRadius);
	}

	// Use predefined patrol points
	if (bUseRandomPatrol)
	{
		CurrentPatrolIndex = FMath::RandRange(0, PatrolPoints.Num() - 1);
	}
	else
	{
		CurrentPatrolIndex = (CurrentPatrolIndex + 1) % PatrolPoints.Num();
	}

	return PatrolPoints[CurrentPatrolIndex];
}
