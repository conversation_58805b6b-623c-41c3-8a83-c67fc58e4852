#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../Enemies/BaseEnemyCharacter.h"
#include "GameplayLoopManager.generated.h"

UENUM(BlueprintType)
enum class EGameplayPhase : uint8
{
	Exploration		UMETA(DisplayName = "Exploration"),
	Combat			UMETA(DisplayName = "Combat"),
	Puzzle			UMETA(DisplayName = "Puzzle"),
	Stealth			UMETA(DisplayName = "Stealth"),
	Survival		UMETA(DisplayName = "Survival"),
	Boss			UMETA(DisplayName = "Boss"),
	Cutscene		UMETA(DisplayName = "Cutscene"),
	Transition		UMETA(DisplayName = "Transition")
};

UENUM(BlueprintType)
enum class EDifficultyScaling : uint8
{
	Static			UMETA(DisplayName = "Static"),
	Progressive		UMETA(DisplayName = "Progressive"),
	Adaptive		UMETA(DisplayName = "Adaptive"),
	PlayerBased		UMETA(DisplayName = "Player Based"),
	TimeBased		UMETA(DisplayName = "Time Based")
};

USTRUCT(BlueprintType)
struct FGameplayObjective
{
	GENERATED_BODY()

	FGameplayObjective()
	{
		ObjectiveID = NAME_None;
		ObjectiveName = FText::GetEmpty();
		bIsMainObjective = true;
		bIsCompleted = false;
		bIsActive = true;
		Priority = 1;
		RequiredProgress = 1;
		CurrentProgress = 0;
		TimeLimit = 0.0f;
		ExperienceReward = 100;
		bShowInUI = true;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	FName ObjectiveID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	FText ObjectiveName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	FText ObjectiveDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	bool bIsMainObjective;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	bool bIsCompleted;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	bool bIsActive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objective")
	int32 Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
	int32 RequiredProgress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
	int32 CurrentProgress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
	float TimeLimit;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	int32 ExperienceReward;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	TArray<FName> ItemRewards;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	bool bShowInUI;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSoftObjectPtr<UTexture2D> ObjectiveIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	TArray<FName> RequiredItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;
};

USTRUCT(BlueprintType)
struct FGameplayEncounter
{
	GENERATED_BODY()

	FGameplayEncounter()
	{
		EncounterID = NAME_None;
		EncounterName = FText::GetEmpty();
		Phase = EGameplayPhase::Combat;
		DifficultyLevel = 1;
		bIsActive = false;
		bIsCompleted = false;
		MaxDuration = 300.0f;
		MinEnemies = 1;
		MaxEnemies = 5;
		SpawnDelay = 2.0f;
		bRespawnEnemies = false;
		RespawnDelay = 30.0f;
		ExperienceReward = 200;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounter")
	FName EncounterID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounter")
	FText EncounterName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounter")
	FText EncounterDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounter")
	EGameplayPhase Phase;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounter")
	int32 DifficultyLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
	bool bIsActive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State")
	bool bIsCompleted;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float MaxDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemies")
	int32 MinEnemies;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemies")
	int32 MaxEnemies;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemies")
	TArray<FName> EnemyTemplates;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	float SpawnDelay;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	bool bRespawnEnemies;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning", meta = (EditCondition = "bRespawnEnemies"))
	float RespawnDelay;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TArray<FVector> SpawnLocations;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	int32 ExperienceReward;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rewards")
	TArray<FName> LootRewards;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	TArray<FGameplayObjective> Objectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	FGameplayTagContainer TriggerTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> StartSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> CompleteSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> BackgroundMusic;
};

USTRUCT(BlueprintType)
struct FResourceScarcitySettings
{
	GENERATED_BODY()

	FResourceScarcitySettings()
	{
		AmmoScarcityLevel = 0.7f;
		HealthItemScarcity = 0.6f;
		InventorySpacePressure = 0.8f;
		KeyItemRarity = 0.3f;
		WeaponDurabilityRate = 0.5f;
		bDynamicScarcity = true;
		ScarcityIncreaseRate = 0.1f;
		ScarcityDecreaseRate = 0.05f;
		MinScarcityLevel = 0.2f;
		MaxScarcityLevel = 0.9f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float AmmoScarcityLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float HealthItemScarcity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float InventorySpacePressure;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float KeyItemRarity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float WeaponDurabilityRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic")
	bool bDynamicScarcity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic", meta = (EditCondition = "bDynamicScarcity"))
	float ScarcityIncreaseRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic", meta = (EditCondition = "bDynamicScarcity"))
	float ScarcityDecreaseRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic", meta = (EditCondition = "bDynamicScarcity"))
	float MinScarcityLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic", meta = (EditCondition = "bDynamicScarcity"))
	float MaxScarcityLevel;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnGameplayPhaseChanged, EGameplayPhase, OldPhase, EGameplayPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnObjectiveCompleted, FName, ObjectiveID, const FGameplayObjective&, Objective);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEncounterStarted, FName, EncounterID, const FGameplayEncounter&, Encounter);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEncounterCompleted, FName, EncounterID, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDifficultyAdjusted, float, OldDifficulty, float, NewDifficulty);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCheckpointReached, FName, CheckpointID);

UCLASS(BlueprintType, Blueprintable)
class SLT_API UGameplayLoopManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UGameplayLoopManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Current gameplay state
	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	EGameplayPhase CurrentPhase = EGameplayPhase::Exploration;

	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	float CurrentDifficultyLevel = 1.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	TArray<FGameplayObjective> ActiveObjectives;

	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	TArray<FGameplayEncounter> ActiveEncounters;

	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	float SessionStartTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Gameplay State")
	float TotalPlayTime = 0.0f;

	// Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	EDifficultyScaling DifficultyScaling = EDifficultyScaling::Adaptive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	FResourceScarcitySettings ResourceSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	TSoftObjectPtr<UDataTable> ObjectivesDatabase;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
	TSoftObjectPtr<UDataTable> EncountersDatabase;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnGameplayPhaseChanged OnGameplayPhaseChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnObjectiveCompleted OnObjectiveCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEncounterStarted OnEncounterStarted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEncounterCompleted OnEncounterCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDifficultyAdjusted OnDifficultyAdjusted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnCheckpointReached OnCheckpointReached;

	// Main gameplay functions
	UFUNCTION(BlueprintCallable, Category = "Gameplay Loop")
	void StartGameplaySession();

	UFUNCTION(BlueprintCallable, Category = "Gameplay Loop")
	void EndGameplaySession();

	UFUNCTION(BlueprintCallable, Category = "Gameplay Loop")
	void SetGameplayPhase(EGameplayPhase NewPhase);

	UFUNCTION(BlueprintCallable, Category = "Gameplay Loop")
	void UpdateGameplayLoop(float DeltaTime);

	// Objective management
	UFUNCTION(BlueprintCallable, Category = "Objectives")
	void AddObjective(const FGameplayObjective& Objective);

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	void CompleteObjective(FName ObjectiveID);

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	void UpdateObjectiveProgress(FName ObjectiveID, int32 ProgressAmount);

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	bool IsObjectiveCompleted(FName ObjectiveID) const;

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	TArray<FGameplayObjective> GetActiveObjectives() const;

	// Encounter management
	UFUNCTION(BlueprintCallable, Category = "Encounters")
	void StartEncounter(FName EncounterID);

	UFUNCTION(BlueprintCallable, Category = "Encounters")
	void CompleteEncounter(FName EncounterID, bool bSuccess = true);

	UFUNCTION(BlueprintCallable, Category = "Encounters")
	void SpawnEncounterEnemies(const FGameplayEncounter& Encounter);

	UFUNCTION(BlueprintCallable, Category = "Encounters")
	bool IsEncounterActive(FName EncounterID) const;

	// Difficulty management
	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	void AdjustDifficulty(float NewDifficultyLevel);

	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	void UpdateAdaptiveDifficulty();

	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	float CalculatePlayerPerformance() const;

	UFUNCTION(BlueprintCallable, Category = "Difficulty")
	void ApplyDifficultyToEnemies();

	// Resource management
	UFUNCTION(BlueprintCallable, Category = "Resources")
	void UpdateResourceScarcity();

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool ShouldSpawnResource(FName ResourceType) const;

	UFUNCTION(BlueprintCallable, Category = "Resources")
	float GetResourceSpawnChance(FName ResourceType) const;

	UFUNCTION(BlueprintCallable, Category = "Resources")
	void AdjustResourceScarcity(float ScarcityChange);

	// Checkpoint system
	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	void CreateCheckpoint(FName CheckpointID);

	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	void LoadCheckpoint(FName CheckpointID);

	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	TArray<FName> GetAvailableCheckpoints() const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	float GetSessionDuration() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	int32 GetCompletedObjectivesCount() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	float GetOverallProgress() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void LoadGameplayData();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SaveGameplayData();

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Gameplay Events")
	void OnGameplaySessionStarted();

	UFUNCTION(BlueprintImplementableEvent, Category = "Gameplay Events")
	void OnGameplaySessionEnded();

	UFUNCTION(BlueprintImplementableEvent, Category = "Gameplay Events")
	void OnPhaseTransition(EGameplayPhase FromPhase, EGameplayPhase ToPhase);

	UFUNCTION(BlueprintImplementableEvent, Category = "Gameplay Events")
	void OnObjectiveAdded(const FGameplayObjective& Objective);

	UFUNCTION(BlueprintImplementableEvent, Category = "Gameplay Events")
	void OnEncounterSpawned(const FGameplayEncounter& Encounter);

protected:
	// Internal state
	TMap<FName, FGameplayObjective> ObjectiveMap;
	TMap<FName, FGameplayEncounter> EncounterMap;
	TArray<FName> CompletedObjectives;
	TArray<FName> AvailableCheckpoints;

	// Performance tracking
	float PlayerDeathCount = 0.0f;
	float EnemiesKilled = 0.0f;
	float DamageDealt = 0.0f;
	float DamageTaken = 0.0f;
	float ResourcesUsed = 0.0f;
	float TimeInCombat = 0.0f;

	// Timer handles
	FTimerHandle GameplayUpdateTimerHandle;
	FTimerHandle DifficultyUpdateTimerHandle;
	FTimerHandle ResourceUpdateTimerHandle;

	// Internal functions
	void InitializeGameplayData();
	void ProcessPhaseTransition(EGameplayPhase NewPhase);
	void UpdateObjectiveTimers(float DeltaTime);
	void UpdateEncounterTimers(float DeltaTime);
	void CalculateAdaptiveDifficulty();
	void ApplyResourceScarcityChanges();
	bool ValidateObjective(const FGameplayObjective& Objective) const;
	bool ValidateEncounter(const FGameplayEncounter& Encounter) const;
};
