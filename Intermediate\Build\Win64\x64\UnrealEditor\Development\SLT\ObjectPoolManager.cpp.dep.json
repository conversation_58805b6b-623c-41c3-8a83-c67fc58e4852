{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\slt\\source\\slt\\core\\systems\\objectpoolmanager.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\slteditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\definitions.slt.h", "g:\\gamedev\\slt\\source\\slt\\core\\systems\\objectpoolmanager.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\objectpoolmanager.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}