#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../BaseEnemyCharacter.h"
#include "EnemyStateComponent.generated.h"

class UAnimMontage;
class UParticleSystem;
class USoundBase;
class UNiagaraSystem;

USTRUCT(BlueprintType)
struct FEnemyStateVisuals
{
	GENERATED_BODY()

	FEnemyStateVisuals()
	{
		StateColor = FLinearColor::White;
		bShowParticleEffect = false;
		bPlaySound = false;
		bPlayAnimation = false;
		ParticleEffect = nullptr;
		SoundEffect = nullptr;
		AnimationMontage = nullptr;
		EffectScale = 1.0f;
		SoundVolume = 1.0f;
		AnimationPlayRate = 1.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FLinearColor StateColor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	bool bShowParticleEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	bool bPlaySound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	bool bPlayAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects", meta = (EditCondition = "bShowParticleEffect"))
	TSoftObjectPtr<UParticleSystem> ParticleEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects", meta = (EditCondition = "bShowParticleEffect"))
	TSoftObjectPtr<UNiagaraSystem> NiagaraEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects", meta = (EditCondition = "bPlaySound"))
	TSoftObjectPtr<USoundBase> SoundEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects", meta = (EditCondition = "bPlayAnimation"))
	TSoftObjectPtr<UAnimMontage> AnimationMontage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	float EffectScale;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	float SoundVolume;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
	float AnimationPlayRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer StateTags;
};

USTRUCT(BlueprintType)
struct FEnemyStateConfiguration
{
	GENERATED_BODY()

	FEnemyStateConfiguration()
	{
		bCanTransitionTo = true;
		MinDuration = 0.0f;
		MaxDuration = 0.0f;
		Priority = 0;
		bInterruptible = true;
		bRequiresTarget = false;
		MovementSpeedMultiplier = 1.0f;
		DamageMultiplier = 1.0f;
		DefenseMultiplier = 1.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transition")
	bool bCanTransitionTo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration")
	float MinDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration")
	float MaxDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority")
	int32 Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	bool bInterruptible;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	bool bRequiresTarget;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Modifiers")
	float MovementSpeedMultiplier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Modifiers")
	float DamageMultiplier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Modifiers")
	float DefenseMultiplier;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer BlockedTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FEnemyStateVisuals StateVisuals;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnStateTransition, EEnemyState, FromState, EEnemyState, ToState, float, TransitionTime);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStateEffectTriggered, EEnemyState, State, const FString&, EffectName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStateModifierApplied, EEnemyState, State, const FString&, ModifierName);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UEnemyStateComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UEnemyStateComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// State Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Configuration")
	TMap<EEnemyState, FEnemyStateConfiguration> StateConfigurations;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "State Configuration")
	TSoftObjectPtr<UDataTable> StateVisualsDatabase;

	// Current State Info
	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	EEnemyState CurrentState = EEnemyState::Idle;

	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	EEnemyState PreviousState = EEnemyState::Idle;

	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	float StateStartTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	float StateDuration = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	bool bIsTransitioning = false;

	UPROPERTY(BlueprintReadOnly, Category = "Current State")
	TArray<FString> ActiveModifiers;

	// Visual Feedback Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Feedback")
	bool bEnableVisualFeedback = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Feedback")
	bool bEnableAudioFeedback = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Feedback")
	bool bEnableAnimationFeedback = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual Feedback")
	float VisualFeedbackIntensity = 1.0f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnStateTransition OnStateTransition;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnStateEffectTriggered OnStateEffectTriggered;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnStateModifierApplied OnStateModifierApplied;

	// Main State Functions
	UFUNCTION(BlueprintCallable, Category = "State")
	void InitializeState(ABaseEnemyCharacter* OwnerEnemy);

	UFUNCTION(BlueprintCallable, Category = "State")
	void OnStateChanged(EEnemyState OldState, EEnemyState NewState);

	UFUNCTION(BlueprintCallable, Category = "State")
	bool CanTransitionToState(EEnemyState TargetState) const;

	UFUNCTION(BlueprintCallable, Category = "State")
	void ForceStateTransition(EEnemyState TargetState);

	UFUNCTION(BlueprintCallable, Category = "State")
	float GetStateProgress() const;

	UFUNCTION(BlueprintCallable, Category = "State")
	bool IsStateExpired() const;

	// Visual Feedback Functions
	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void TriggerStateVisuals(EEnemyState State);

	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void StopStateVisuals();

	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void UpdateStateColor(const FLinearColor& NewColor);

	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void PlayStateAnimation(UAnimMontage* Animation, float PlayRate = 1.0f);

	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void PlayStateSound(USoundBase* Sound, float Volume = 1.0f);

	UFUNCTION(BlueprintCallable, Category = "Visual Feedback")
	void SpawnStateParticleEffect(UParticleSystem* Effect, const FVector& Location, float Scale = 1.0f);

	// State Modifier Functions
	UFUNCTION(BlueprintCallable, Category = "State Modifiers")
	void ApplyStateModifier(const FString& ModifierName, float Duration = -1.0f);

	UFUNCTION(BlueprintCallable, Category = "State Modifiers")
	void RemoveStateModifier(const FString& ModifierName);

	UFUNCTION(BlueprintCallable, Category = "State Modifiers")
	bool HasStateModifier(const FString& ModifierName) const;

	UFUNCTION(BlueprintCallable, Category = "State Modifiers")
	void ClearAllModifiers();

	// Configuration Functions
	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void SetStateConfiguration(EEnemyState State, const FEnemyStateConfiguration& Configuration);

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	FEnemyStateConfiguration GetStateConfiguration(EEnemyState State) const;

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void LoadStateVisualsFromDatabase();

	// Utility Functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<EEnemyState> GetValidTransitions() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	EEnemyState GetHighestPriorityState(const TArray<EEnemyState>& States) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	float GetMovementSpeedMultiplier() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	float GetDamageMultiplier() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	float GetDefenseMultiplier() const;

	// Blueprint Events
	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnStateInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnStateEntered(EEnemyState EnteredState);

	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnStateExited(EEnemyState ExitedState);

	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnStateUpdate(EEnemyState CurrentState, float DeltaTime);

	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnTransitionStarted(EEnemyState FromState, EEnemyState ToState);

	UFUNCTION(BlueprintImplementableEvent, Category = "State Events")
	void OnTransitionCompleted(EEnemyState FromState, EEnemyState ToState);

protected:
	// Internal references
	UPROPERTY()
	ABaseEnemyCharacter* OwnerEnemyCharacter;

	// Internal state
	UPROPERTY()
	TMap<FString, float> ModifierTimers;

	UPROPERTY()
	TArray<UActorComponent*> ActiveEffectComponents;

	// Timer handles
	FTimerHandle StateTimerHandle;
	FTimerHandle TransitionTimerHandle;

	// Internal functions
	void InitializeDefaultConfigurations();
	void ProcessStateTransition(EEnemyState FromState, EEnemyState ToState);
	void ApplyStateEffects(EEnemyState State);
	void RemoveStateEffects(EEnemyState State);
	void UpdateModifierTimers(float DeltaTime);
	void UpdateStateVisuals();
	FLinearColor GetStateColor(EEnemyState State) const;
};
