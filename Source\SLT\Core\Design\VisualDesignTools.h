#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "VisualDesignTools.generated.h"

class UWidgetComponent;
class UTextRenderComponent;
class UStaticMeshComponent;
class UParticleSystemComponent;
class UNiagaraComponent;

UENUM(BlueprintType)
enum class EDesignVisualizationType : uint8
{
	None				UMETA(DisplayName = "None"),
	InventoryGrid		UMETA(DisplayName = "Inventory Grid"),
	InteractionRange	UMETA(DisplayName = "Interaction Range"),
	PatrolPath			UMETA(DisplayName = "Patrol Path"),
	TriggerZone			UMETA(DisplayName = "Trigger Zone"),
	SpawnPoint			UMETA(DisplayName = "Spawn Point"),
	Checkpoint			UMETA(DisplayName = "Checkpoint"),
	ResourceNode		UMETA(DisplayName = "Resource Node"),
	WeaponUpgrade		UMETA(DisplayName = "Weapon Upgrade"),
	PuzzleElement		UMETA(DisplayName = "Puzzle Element"),
	LootSpawn			UMETA(DisplayName = "Loot Spawn"),
	CameraPoint			UMETA(DisplayName = "Camera Point"),
	AudioZone			UMETA(DisplayName = "Audio Zone"),
	LightingSetup		UMETA(DisplayName = "Lighting Setup"),
	NavMeshDebug		UMETA(DisplayName = "NavMesh Debug")
};

UENUM(BlueprintType)
enum class EDesignComplexity : uint8
{
	Beginner		UMETA(DisplayName = "Beginner"),
	Intermediate	UMETA(DisplayName = "Intermediate"),
	Advanced		UMETA(DisplayName = "Advanced"),
	Expert			UMETA(DisplayName = "Expert")
};

USTRUCT(BlueprintType)
struct FDesignTemplate
{
	GENERATED_BODY()

	FDesignTemplate()
	{
		TemplateID = NAME_None;
		TemplateName = FText::GetEmpty();
		Complexity = EDesignComplexity::Beginner;
		bIsBuiltIn = false;
		bRequiresValidation = true;
		EstimatedSetupTime = 5.0f;
		Category = TEXT("General");
	}

	// Template identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FName TemplateID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FText TemplateName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FText TemplateDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	EDesignComplexity Complexity;

	// Template properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsBuiltIn;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bRequiresValidation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	float EstimatedSetupTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	FString Category;

	// Visual assets
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UTexture2D> PreviewImage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UStaticMesh> PreviewMesh;

	// Template data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TSubclassOf<AActor> ActorClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TMap<FString, FString> DefaultProperties;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TArray<FName> RequiredComponents;

	// Setup instructions
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instructions")
	TArray<FText> SetupSteps;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instructions")
	TArray<FText> DesignTips;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Instructions")
	TArray<FText> CommonMistakes;

	// Tags and categorization
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer TemplateTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	TArray<FString> SearchKeywords;
};

USTRUCT(BlueprintType)
struct FDesignValidationRule
{
	GENERATED_BODY()

	FDesignValidationRule()
	{
		RuleID = NAME_None;
		RuleName = FText::GetEmpty();
		bIsEnabled = true;
		bIsWarning = false;
		Priority = 0;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FName RuleID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FText RuleName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FText RuleDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	bool bIsEnabled;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	bool bIsWarning; // false = error

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	int32 Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FGameplayTagContainer ApplicableTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	TArray<TSubclassOf<AActor>> ApplicableClasses;
};

USTRUCT(BlueprintType)
struct FDesignValidationResult
{
	GENERATED_BODY()

	FDesignValidationResult()
	{
		bIsValid = true;
		ErrorCount = 0;
		WarningCount = 0;
		ValidationTime = 0.0f;
	}

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	bool bIsValid;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	int32 ErrorCount;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	int32 WarningCount;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	float ValidationTime;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	TArray<FText> ErrorMessages;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	TArray<FText> WarningMessages;

	UPROPERTY(BlueprintReadOnly, Category = "Result")
	TArray<FText> SuggestionMessages;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDesignValidated, AActor*, ValidatedActor, const FDesignValidationResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnTemplateApplied, AActor*, TargetActor, FName, TemplateID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnVisualizationChanged, AActor*, Actor, EDesignVisualizationType, Type, bool, bEnabled);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UVisualDesignComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UVisualDesignComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Design visualization settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	EDesignVisualizationType VisualizationType = EDesignVisualizationType::None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	bool bShowInGame = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	bool bShowInEditor = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	FLinearColor VisualizationColor = FLinearColor::Green;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visualization")
	float VisualizationScale = 1.0f;

	// Design template
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FName CurrentTemplate = NAME_None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	bool bAutoApplyTemplate = true;

	// Validation settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	bool bEnableValidation = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	bool bAutoValidate = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Validation")
	float ValidationInterval = 1.0f;

	// Design metadata
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
	FText DesignNotes;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
	FGameplayTagContainer DesignTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Metadata")
	EDesignComplexity DesignComplexity = EDesignComplexity::Beginner;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDesignValidated OnDesignValidated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTemplateApplied OnTemplateApplied;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnVisualizationChanged OnVisualizationChanged;

	// Main design functions
	UFUNCTION(BlueprintCallable, Category = "Design")
	void SetVisualizationType(EDesignVisualizationType NewType);

	UFUNCTION(BlueprintCallable, Category = "Design")
	void ApplyTemplate(FName TemplateID);

	UFUNCTION(BlueprintCallable, Category = "Design")
	FDesignValidationResult ValidateDesign();

	UFUNCTION(BlueprintCallable, Category = "Design")
	void AutoFixCommonIssues();

	UFUNCTION(BlueprintCallable, Category = "Design")
	void ShowDesignHelper();

	UFUNCTION(BlueprintCallable, Category = "Design")
	void HideDesignHelper();

	// Template functions
	UFUNCTION(BlueprintCallable, Category = "Templates")
	TArray<FDesignTemplate> GetAvailableTemplates() const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	FDesignTemplate GetTemplate(FName TemplateID) const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	bool CreateCustomTemplate(const FDesignTemplate& Template);

	// Validation functions
	UFUNCTION(BlueprintCallable, Category = "Validation")
	void AddValidationRule(const FDesignValidationRule& Rule);

	UFUNCTION(BlueprintCallable, Category = "Validation")
	void RemoveValidationRule(FName RuleID);

	UFUNCTION(BlueprintCallable, Category = "Validation")
	TArray<FDesignValidationRule> GetValidationRules() const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SnapToGrid(float GridSize = 100.0f);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void AlignToSurface();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void CopyDesignSettings(UVisualDesignComponent* SourceComponent);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ExportDesignData(const FString& FilePath) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool ImportDesignData(const FString& FilePath);

protected:
	// Visual components
	UPROPERTY()
	UStaticMeshComponent* VisualizationMesh;

	UPROPERTY()
	UWidgetComponent* InfoWidget;

	UPROPERTY()
	UTextRenderComponent* LabelText;

	UPROPERTY()
	UParticleSystemComponent* EffectComponent;

	// Internal state
	UPROPERTY()
	FDesignValidationResult LastValidationResult;

	UPROPERTY()
	float LastValidationTime;

	// Internal functions
	void CreateVisualizationComponents();
	void UpdateVisualization();
	void UpdateInfoDisplay();
	bool RunValidationRule(const FDesignValidationRule& Rule, FDesignValidationResult& Result);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnVisualizationUpdated();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnValidationCompleted(const FDesignValidationResult& Result);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnTemplateChanged(FName OldTemplate, FName NewTemplate);
};
