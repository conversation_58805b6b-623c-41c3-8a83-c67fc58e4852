#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "LevelProgressionManager.generated.h"

class ULevelStreaming;
class ASLTPlayerCharacter;

UENUM(BlueprintType)
enum class ECheckpointType : uint8
{
	Manual			UMETA(DisplayName = "Manual"),
	Automatic		UMETA(DisplayName = "Automatic"),
	LevelTransition	UMETA(DisplayName = "Level Transition"),
	BossDefeated	UMETA(DisplayName = "Boss Defeated"),
	PuzzleSolved	UMETA(DisplayName = "Puzzle Solved"),
	AreaCleared		UMETA(DisplayName = "Area Cleared")
};

UENUM(BlueprintType)
enum class ELevelState : uint8
{
	Locked			UMETA(DisplayName = "Locked"),
	Available		UMETA(DisplayName = "Available"),
	InProgress		UMETA(DisplayName = "In Progress"),
	Completed		UMETA(DisplayName = "Completed"),
	Mastered		UMETA(DisplayName = "Mastered")
};

USTRUCT(BlueprintType)
struct FCheckpointData : public FTableRowBase
{
	GENERATED_BODY()

	FCheckpointData()
	{
		CheckpointID = NAME_None;
		CheckpointName = FText::GetEmpty();
		CheckpointType = ECheckpointType::Manual;
		LevelName = TEXT("");
		PlayerLocation = FVector::ZeroVector;
		PlayerRotation = FRotator::ZeroRotator;
		bIsActive = true;
		bSaveInventory = true;
		bSaveProgress = true;
		RequiredObjectives = 0;
		UnlockTags = FGameplayTagContainer();
	}

	// Unique identifier
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Checkpoint")
	FName CheckpointID;

	// Display name
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Checkpoint")
	FText CheckpointName;

	// Type of checkpoint
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Checkpoint")
	ECheckpointType CheckpointType;

	// Level this checkpoint belongs to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Checkpoint")
	FString LevelName;

	// Player spawn location
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
	FVector PlayerLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Transform")
	FRotator PlayerRotation;

	// Checkpoint settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bIsActive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bSaveInventory;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bSaveProgress;

	// Requirements to unlock this checkpoint
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	int32 RequiredObjectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	TArray<FName> RequiredCheckpoints;

	// What this checkpoint unlocks
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlocks")
	FGameplayTagContainer UnlockTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Unlocks")
	TArray<FName> UnlockCheckpoints;

	// Custom data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomData;
};

USTRUCT(BlueprintType)
struct FLevelData : public FTableRowBase
{
	GENERATED_BODY()

	FLevelData()
	{
		LevelID = NAME_None;
		LevelName = FText::GetEmpty();
		LevelPath = TEXT("");
		State = ELevelState::Locked;
		RequiredLevel = 1;
		RecommendedLevel = 1;
		MaxObjectives = 0;
		bIsMainLevel = true;
		bAllowReplay = true;
		LoadingScreenTexture = nullptr;
	}

	// Unique identifier
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level")
	FName LevelID;

	// Display name
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level")
	FText LevelName;

	// Level asset path
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level")
	FString LevelPath;

	// Current state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level")
	ELevelState State;

	// Level requirements
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	int32 RequiredLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	int32 RecommendedLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	TArray<FName> RequiredLevels;

	// Level info
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Info")
	int32 MaxObjectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Info")
	bool bIsMainLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Info")
	bool bAllowReplay;

	// UI
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSoftObjectPtr<UTexture2D> LoadingScreenTexture;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	FText LevelDescription;

	// Custom data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomData;
};

USTRUCT(BlueprintType)
struct FProgressionSaveData
{
	GENERATED_BODY()

	FProgressionSaveData()
	{
		CurrentLevel = NAME_None;
		LastCheckpoint = NAME_None;
		PlayerLevel = 1;
		TotalPlayTime = 0.0f;
		CompletedObjectives = 0;
		TotalObjectives = 0;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FName CurrentLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FName LastCheckpoint;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 PlayerLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	float TotalPlayTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 CompletedObjectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 TotalObjectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FName, ELevelState> LevelStates;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FName> UnlockedCheckpoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FGameplayTagContainer UnlockedTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FString> CustomProgressData;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCheckpointReached, FName, CheckpointID, const FCheckpointData&, CheckpointData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLevelStateChanged, FName, LevelID, ELevelState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnLevelTransition, FName, FromLevel, FName, ToLevel, bool, bIsLoading);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnObjectiveCompleted, int32, ObjectiveIndex, int32, TotalCompleted);

UCLASS(BlueprintType, Blueprintable)
class SLT_API ULevelProgressionManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	ULevelProgressionManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Data tables
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TSoftObjectPtr<UDataTable> CheckpointDatabase;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TSoftObjectPtr<UDataTable> LevelDatabase;

	// Current state
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	FProgressionSaveData CurrentProgress;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnCheckpointReached OnCheckpointReached;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLevelStateChanged OnLevelStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLevelTransition OnLevelTransition;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnObjectiveCompleted OnObjectiveCompleted;

	// Checkpoint functions
	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	bool ActivateCheckpoint(FName CheckpointID);

	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	bool LoadCheckpoint(FName CheckpointID);

	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	FCheckpointData GetCheckpointData(FName CheckpointID) const;

	UFUNCTION(BlueprintCallable, Category = "Checkpoints")
	TArray<FName> GetAvailableCheckpoints() const;

	// Level functions
	UFUNCTION(BlueprintCallable, Category = "Levels")
	bool LoadLevel(FName LevelID);

	UFUNCTION(BlueprintCallable, Category = "Levels")
	void SetLevelState(FName LevelID, ELevelState NewState);

	UFUNCTION(BlueprintCallable, Category = "Levels")
	ELevelState GetLevelState(FName LevelID) const;

	UFUNCTION(BlueprintCallable, Category = "Levels")
	FLevelData GetLevelData(FName LevelID) const;

	UFUNCTION(BlueprintCallable, Category = "Levels")
	TArray<FName> GetUnlockedLevels() const;

	// Objective functions
	UFUNCTION(BlueprintCallable, Category = "Objectives")
	void CompleteObjective(int32 ObjectiveIndex);

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	void SetTotalObjectives(int32 Total);

	UFUNCTION(BlueprintCallable, Category = "Objectives")
	float GetObjectiveProgress() const;

	// Progression functions
	UFUNCTION(BlueprintCallable, Category = "Progression")
	void UnlockTag(const FGameplayTag& Tag);

	UFUNCTION(BlueprintCallable, Category = "Progression")
	bool HasUnlockedTag(const FGameplayTag& Tag) const;

	UFUNCTION(BlueprintCallable, Category = "Progression")
	void SetPlayerLevel(int32 NewLevel);

	UFUNCTION(BlueprintCallable, Category = "Progression")
	int32 GetPlayerLevel() const { return CurrentProgress.PlayerLevel; }

	// Save/Load
	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool SaveProgression();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool LoadProgression();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void ResetProgression();

protected:
	// Internal functions
	void LoadDatabases();
	bool CheckCheckpointRequirements(const FCheckpointData& CheckpointData) const;
	bool CheckLevelRequirements(const FLevelData& LevelData) const;
	void UpdateUnlockedContent();

	// Cached databases
	UPROPERTY()
	UDataTable* CachedCheckpointDatabase;

	UPROPERTY()
	UDataTable* CachedLevelDatabase;
};
