#include "ObjectPoolManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "GameFramework/Actor.h"

UObjectPoolManager::UObjectPoolManager()
{
	bEnableGlobalPooling = true;
	DefaultPoolSettings = FObjectPoolSettings();
}

void UObjectPoolManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	// Start cleanup timer
	if (UWorld* World = GetWorld())
	{
		World->GetTimerManager().SetTimer(
			CleanupTimerHandle,
			this,
			&UObjectPoolManager::OnCleanupTimer,
			DefaultPoolSettings.PoolCleanupInterval,
			true
		);
	}
}

void UObjectPoolManager::Deinitialize()
{
	// Clear cleanup timer
	if (UWorld* World = GetWorld())
	{
		World->GetTimerManager().ClearTimer(CleanupTimerHandle);
	}

	// Clear all pools
	ClearAllPools();

	Super::Deinitialize();
}

bool UObjectPoolManager::ShouldCreateSubsystem(UObject* Outer) const
{
	return bEnableGlobalPooling;
}

AActor* UObjectPoolManager::GetPooledActor(TSubclassOf<AActor> ActorClass, const FVector& Location, const FRotator& Rotation)
{
	if (!ActorClass || !bEnableGlobalPooling)
	{
		return nullptr;
	}

	FActorPool* Pool = GetOrCreatePool(ActorClass);
	if (!Pool || !Pool->Settings.bEnablePooling)
	{
		// Create new actor without pooling
		if (UWorld* World = GetWorld())
		{
			AActor* NewActor = World->SpawnActor<AActor>(ActorClass, Location, Rotation);
			OnActorSpawned.Broadcast(NewActor, ActorClass);
			return NewActor;
		}
		return nullptr;
	}

	AActor* PooledActor = nullptr;

	// Try to get an inactive actor from pool
	for (int32 i = 0; i < Pool->PooledActors.Num(); i++)
	{
		FPooledActorInfo& ActorInfo = Pool->PooledActors[i];
		if (!ActorInfo.bIsActive && ActorInfo.Actor && IsValid(ActorInfo.Actor))
		{
			PooledActor = ActorInfo.Actor;
			ActorInfo.bIsActive = true;
			ActorInfo.LastUsedTime = GetWorld()->GetTimeSeconds();
			ActorInfo.UsageCount++;

			// Move to active list
			Pool->ActiveActors.Add(ActorInfo);
			Pool->PooledActors.RemoveAt(i);

			// Update statistics
			if (int32* HitCount = PoolHitCount.Find(ActorClass))
			{
				(*HitCount)++;
			}
			else
			{
				PoolHitCount.Add(ActorClass, 1);
			}

			break;
		}
	}

	// If no pooled actor available, create new one
	if (!PooledActor)
	{
		if (Pool->ActiveActors.Num() + Pool->PooledActors.Num() < Pool->Settings.MaxPoolSize)
		{
			PooledActor = CreatePooledActor(ActorClass);
			if (PooledActor)
			{
				FPooledActorInfo NewActorInfo;
				NewActorInfo.Actor = PooledActor;
				NewActorInfo.bIsActive = true;
				NewActorInfo.LastUsedTime = GetWorld()->GetTimeSeconds();
				NewActorInfo.UsageCount = 1;
				Pool->ActiveActors.Add(NewActorInfo);
			}
		}

		// Update statistics
		if (int32* MissCount = PoolMissCount.Find(ActorClass))
		{
			(*MissCount)++;
		}
		else
		{
			PoolMissCount.Add(ActorClass, 1);
		}
	}

	if (PooledActor)
	{
		// Set transform
		PooledActor->SetActorLocationAndRotation(Location, Rotation);
		PooledActor->SetActorHiddenInGame(false);
		PooledActor->SetActorEnableCollision(true);
		PooledActor->SetActorTickEnabled(true);

		// Call poolable interface
		if (PooledActor->Implements<UPoolableActor>())
		{
			IPoolableActor::Execute_OnActorPooled(PooledActor);
		}

		OnActorPooled.Broadcast(PooledActor, false);
	}

	return PooledActor;
}

bool UObjectPoolManager::ReturnActorToPool(AActor* Actor)
{
	if (!Actor || !IsValid(Actor) || !bEnableGlobalPooling)
	{
		return false;
	}

	TSubclassOf<AActor> ActorClass = Actor->GetClass();
	FActorPool* Pool = ActorPools.Find(ActorClass);
	
	if (!Pool || !Pool->Settings.bEnablePooling)
	{
		// Not pooled, destroy normally
		Actor->Destroy();
		return false;
	}

	// Check if actor can be pooled
	if (Actor->Implements<UPoolableActor>())
	{
		if (!IPoolableActor::Execute_CanBePooled(Actor))
		{
			Actor->Destroy();
			return false;
		}
	}

	// Find actor in active list
	int32 ActiveIndex = Pool->ActiveActors.IndexOfByPredicate([Actor](const FPooledActorInfo& Info)
	{
		return Info.Actor == Actor;
	});

	if (ActiveIndex != INDEX_NONE)
	{
		FPooledActorInfo ActorInfo = Pool->ActiveActors[ActiveIndex];
		Pool->ActiveActors.RemoveAt(ActiveIndex);

		// Reset actor state
		Actor->SetActorHiddenInGame(true);
		Actor->SetActorEnableCollision(false);
		Actor->SetActorTickEnabled(false);
		Actor->SetActorLocation(FVector(0, 0, -10000)); // Move out of world

		// Call poolable interface
		if (Actor->Implements<UPoolableActor>())
		{
			IPoolableActor::Execute_ResetActorState(Actor);
			IPoolableActor::Execute_OnActorReturned(Actor);
		}

		// Add back to pool
		ActorInfo.bIsActive = false;
		ActorInfo.PooledTime = GetWorld()->GetTimeSeconds();
		Pool->PooledActors.Add(ActorInfo);

		OnActorPooled.Broadcast(Actor, true);
		return true;
	}

	return false;
}

void UObjectPoolManager::RegisterPoolConfiguration(TSubclassOf<AActor> ActorClass, const FObjectPoolSettings& Settings)
{
	if (ActorClass)
	{
		PoolConfigurations.Add(ActorClass, Settings);
	}
}

void UObjectPoolManager::PrewarmPool(TSubclassOf<AActor> ActorClass, int32 Count)
{
	if (!ActorClass)
	{
		return;
	}

	FActorPool* Pool = GetOrCreatePool(ActorClass);
	if (!Pool)
	{
		return;
	}

	int32 TargetCount = (Count > 0) ? Count : Pool->Settings.InitialPoolSize;
	int32 CurrentCount = Pool->PooledActors.Num() + Pool->ActiveActors.Num();

	for (int32 i = CurrentCount; i < TargetCount; i++)
	{
		AActor* NewActor = CreatePooledActor(ActorClass);
		if (NewActor)
		{
			FPooledActorInfo ActorInfo;
			ActorInfo.Actor = NewActor;
			ActorInfo.bIsActive = false;
			ActorInfo.PooledTime = GetWorld()->GetTimeSeconds();
			Pool->PooledActors.Add(ActorInfo);

			// Hide and disable the actor
			NewActor->SetActorHiddenInGame(true);
			NewActor->SetActorEnableCollision(false);
			NewActor->SetActorTickEnabled(false);
			NewActor->SetActorLocation(FVector(0, 0, -10000));
		}
	}
}

void UObjectPoolManager::ClearPool(TSubclassOf<AActor> ActorClass)
{
	if (FActorPool* Pool = ActorPools.Find(ActorClass))
	{
		// Destroy all pooled actors
		for (const FPooledActorInfo& ActorInfo : Pool->PooledActors)
		{
			if (ActorInfo.Actor && IsValid(ActorInfo.Actor))
			{
				ActorInfo.Actor->Destroy();
			}
		}

		// Destroy all active actors
		for (const FPooledActorInfo& ActorInfo : Pool->ActiveActors)
		{
			if (ActorInfo.Actor && IsValid(ActorInfo.Actor))
			{
				ActorInfo.Actor->Destroy();
			}
		}

		ActorPools.Remove(ActorClass);
	}
}

void UObjectPoolManager::ClearAllPools()
{
	for (auto& PoolPair : ActorPools)
	{
		ClearPool(PoolPair.Key);
	}
	ActorPools.Empty();
	PoolHitCount.Empty();
	PoolMissCount.Empty();
}

void UObjectPoolManager::CleanupIdleActors()
{
	float CurrentTime = GetWorld()->GetTimeSeconds();

	for (auto& PoolPair : ActorPools)
	{
		FActorPool& Pool = PoolPair.Value;
		
		// Remove idle actors from pool
		for (int32 i = Pool.PooledActors.Num() - 1; i >= 0; i--)
		{
			const FPooledActorInfo& ActorInfo = Pool.PooledActors[i];
			float IdleTime = CurrentTime - ActorInfo.PooledTime;
			
			if (IdleTime > Pool.Settings.MaxIdleTime)
			{
				if (ActorInfo.Actor && IsValid(ActorInfo.Actor))
				{
					ActorInfo.Actor->Destroy();
				}
				Pool.PooledActors.RemoveAt(i);
			}
		}
	}
}

void UObjectPoolManager::SetPoolingEnabled(bool bEnabled)
{
	bEnableGlobalPooling = bEnabled;
}

FActorPool* UObjectPoolManager::GetOrCreatePool(TSubclassOf<AActor> ActorClass)
{
	if (FActorPool* ExistingPool = ActorPools.Find(ActorClass))
	{
		return ExistingPool;
	}

	// Create new pool
	FActorPool NewPool;
	NewPool.ActorClass = ActorClass;
	NewPool.Settings = GetPoolSettings(ActorClass);

	ActorPools.Add(ActorClass, NewPool);
	FActorPool* Pool = ActorPools.Find(ActorClass);

	// Prewarm if enabled
	if (Pool->Settings.bPrewarmPool)
	{
		PrewarmPool(ActorClass, Pool->Settings.InitialPoolSize);
	}

	return Pool;
}

AActor* UObjectPoolManager::CreatePooledActor(TSubclassOf<AActor> ActorClass)
{
	if (UWorld* World = GetWorld())
	{
		FVector SpawnLocation(0, 0, -10000); // Spawn out of world
		FRotator SpawnRotation = FRotator::ZeroRotator;
		
		AActor* NewActor = World->SpawnActor<AActor>(ActorClass, SpawnLocation, SpawnRotation);
		return NewActor;
	}
	return nullptr;
}

FObjectPoolSettings UObjectPoolManager::GetPoolSettings(TSubclassOf<AActor> ActorClass) const
{
	if (const FObjectPoolSettings* Settings = PoolConfigurations.Find(ActorClass))
	{
		return *Settings;
	}
	return DefaultPoolSettings;
}

void UObjectPoolManager::OnCleanupTimer()
{
	CleanupIdleActors();
}

int32 UObjectPoolManager::GetPoolSize(TSubclassOf<AActor> ActorClass) const
{
	if (const FActorPool* Pool = ActorPools.Find(ActorClass))
	{
		return Pool->PooledActors.Num() + Pool->ActiveActors.Num();
	}
	return 0;
}

int32 UObjectPoolManager::GetActiveActorCount(TSubclassOf<AActor> ActorClass) const
{
	if (const FActorPool* Pool = ActorPools.Find(ActorClass))
	{
		return Pool->ActiveActors.Num();
	}
	return 0;
}

float UObjectPoolManager::GetPoolEfficiency(TSubclassOf<AActor> ActorClass) const
{
	const int32* HitCount = PoolHitCount.Find(ActorClass);
	const int32* MissCount = PoolMissCount.Find(ActorClass);
	
	if (!HitCount && !MissCount)
	{
		return 0.0f;
	}

	int32 Hits = HitCount ? *HitCount : 0;
	int32 Misses = MissCount ? *MissCount : 0;
	int32 Total = Hits + Misses;

	return Total > 0 ? (float)Hits / (float)Total : 0.0f;
}

TArray<FString> UObjectPoolManager::GetPoolStatistics() const
{
	TArray<FString> Stats;
	
	for (const auto& PoolPair : ActorPools)
	{
		const FActorPool& Pool = PoolPair.Value;
		FString ClassName = PoolPair.Key ? PoolPair.Key->GetName() : TEXT("Unknown");
		
		FString StatLine = FString::Printf(
			TEXT("%s: Pool=%d, Active=%d, Efficiency=%.2f%%"),
			*ClassName,
			Pool.PooledActors.Num(),
			Pool.ActiveActors.Num(),
			GetPoolEfficiency(PoolPair.Key) * 100.0f
		);
		
		Stats.Add(StatLine);
	}
	
	return Stats;
}
