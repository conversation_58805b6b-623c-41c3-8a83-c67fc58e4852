#include "SLTPlayerCharacter.h"
#include "Engine/LocalPlayer.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Controller.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "InventorySystem/Components/InventoryGridComponent.h"
#include "InventorySystem/Components/InteractionComponent.h"
#include "Blueprint/UserWidget.h"
#include "Engine/Engine.h"

ASLTPlayerCharacter::ASLTPlayerCharacter()
{
	// Set size for collision capsule
	GetCapsuleComponent()->InitCapsuleSize(42.f, 96.0f);
		
	// Don't rotate when the controller rotates. Let that just affect the camera.
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;

	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = true; // Character moves in the direction of input...	
	GetCharacterMovement()->RotationRate = FRotator(0.0f, 500.0f, 0.0f); // ...at this rotation rate

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// Create a camera boom (pulls in towards the player if there is a collision)
	CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
	CameraBoom->SetupAttachment(RootComponent);
	CameraBoom->TargetArmLength = 400.0f; // The camera follows at this distance behind the character	
	CameraBoom->bUsePawnControlRotation = true; // Rotate the arm based on the controller

	// Create a follow camera
	FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
	FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName); // Attach the camera to the end of the boom and let the boom adjust to match the controller orientation
	FollowCamera->bUsePawnControlRotation = false; // Camera does not rotate relative to arm

	// Create inventory component
	InventoryComponent = CreateDefaultSubobject<UInventoryGridComponent>(TEXT("InventoryComponent"));

	// Create interaction component
	InteractionComponent = CreateDefaultSubobject<UInteractionComponent>(TEXT("InteractionComponent"));

	// Default settings
	bIsInventoryOpen = false;
}

void ASLTPlayerCharacter::BeginPlay()
{
	// Call the base class  
	Super::BeginPlay();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}

	// Bind to inventory events
	if (InventoryComponent)
	{
		InventoryComponent->OnInventoryItemAdded.AddDynamic(this, &ASLTPlayerCharacter::OnItemPickedUp);
	}
}

void ASLTPlayerCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void ASLTPlayerCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent))
	{
		// Jumping
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Started, this, &ACharacter::Jump);
		EnhancedInputComponent->BindAction(JumpAction, ETriggerEvent::Completed, this, &ACharacter::StopJumping);

		// Moving
		EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &ASLTPlayerCharacter::Move);

		// Looking
		EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &ASLTPlayerCharacter::Look);

		// Interaction
		EnhancedInputComponent->BindAction(InteractAction, ETriggerEvent::Started, this, &ASLTPlayerCharacter::Interact);

		// Inventory
		EnhancedInputComponent->BindAction(OpenInventoryAction, ETriggerEvent::Started, this, &ASLTPlayerCharacter::OpenInventoryInput);

		// Item rotation
		EnhancedInputComponent->BindAction(RotateItemAction, ETriggerEvent::Started, this, &ASLTPlayerCharacter::RotateItem);

		// Setup interaction component input
		if (InteractionComponent)
		{
			InteractionComponent->SetupInputComponent(EnhancedInputComponent);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("'%s' Failed to find an Enhanced Input component! This template is built to use the Enhanced Input system. If you intend to use the legacy system, then you will need to update this C++ file."), *GetNameSafe(this));
	}
}

void ASLTPlayerCharacter::Move(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D MovementVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
	
		// get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);

		// add movement 
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);
	}
}

void ASLTPlayerCharacter::Look(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (Controller != nullptr)
	{
		// add yaw and pitch input to controller
		AddControllerYawInput(LookAxisVector.X);
		AddControllerPitchInput(LookAxisVector.Y);
	}
}

void ASLTPlayerCharacter::Interact(const FInputActionValue& Value)
{
	// Interaction is handled by the InteractionComponent
	// This is just a placeholder for additional interaction logic
}

void ASLTPlayerCharacter::OpenInventoryInput(const FInputActionValue& Value)
{
	ToggleInventory();
}

void ASLTPlayerCharacter::RotateItem(const FInputActionValue& Value)
{
	// This would be used for rotating items in the inventory UI
	// Implementation depends on the UI system
}

void ASLTPlayerCharacter::ToggleInventory()
{
	if (bIsInventoryOpen)
	{
		CloseInventory();
	}
	else
	{
		OpenInventory();
	}
}

void ASLTPlayerCharacter::OpenInventory()
{
	if (bIsInventoryOpen)
	{
		return;
	}

	CreateInventoryWidget();
	
	if (InventoryWidget)
	{
		InventoryWidget->AddToViewport();
		bIsInventoryOpen = true;
		
		// Set input mode to UI
		if (APlayerController* PC = Cast<APlayerController>(GetController()))
		{
			FInputModeGameAndUI InputMode;
			InputMode.SetWidgetToFocus(InventoryWidget->TakeWidget());
			InputMode.SetLockMouseToViewportBehavior(EMouseLockMode::DoNotLock);
			PC->SetInputMode(InputMode);
			PC->SetShowMouseCursor(true);
		}

		OnInventoryOpened();
		OnInventoryToggled.Broadcast(true);
	}
}

void ASLTPlayerCharacter::CloseInventory()
{
	if (!bIsInventoryOpen)
	{
		return;
	}

	if (InventoryWidget)
	{
		InventoryWidget->RemoveFromParent();
	}

	bIsInventoryOpen = false;

	// Set input mode back to game
	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		FInputModeGameOnly InputMode;
		PC->SetInputMode(InputMode);
		PC->SetShowMouseCursor(false);
	}

	OnInventoryClosed();
	OnInventoryToggled.Broadcast(false);
}

void ASLTPlayerCharacter::CreateInventoryWidget()
{
	if (InventoryWidget || !InventoryWidgetClass)
	{
		return;
	}

	if (APlayerController* PC = Cast<APlayerController>(GetController()))
	{
		InventoryWidget = CreateWidget<UUserWidget>(PC, InventoryWidgetClass);
	}
}

void ASLTPlayerCharacter::DestroyInventoryWidget()
{
	if (InventoryWidget)
	{
		InventoryWidget->RemoveFromParent();
		InventoryWidget = nullptr;
	}
}

// IInventoryInterface implementation - delegate to InventoryComponent
bool ASLTPlayerCharacter::AddItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity, FInventorySlot& OutSlot)
{
	return InventoryComponent ? InventoryComponent->AddItem(ItemData, Quantity, OutSlot) : false;
}

bool ASLTPlayerCharacter::RemoveItem_Implementation(FName ItemID, int32 Quantity)
{
	return InventoryComponent ? InventoryComponent->RemoveItem(ItemID, Quantity) : false;
}

bool ASLTPlayerCharacter::RemoveItemBySlotID_Implementation(const FGuid& SlotID)
{
	return InventoryComponent ? InventoryComponent->RemoveItemBySlotID(SlotID) : false;
}

bool ASLTPlayerCharacter::HasItem_Implementation(FName ItemID) const
{
	return InventoryComponent ? InventoryComponent->HasItem(ItemID) : false;
}

int32 ASLTPlayerCharacter::GetItemQuantity_Implementation(FName ItemID) const
{
	return InventoryComponent ? InventoryComponent->GetItemQuantity(ItemID) : 0;
}

TArray<FInventorySlot> ASLTPlayerCharacter::GetAllItems_Implementation() const
{
	return InventoryComponent ? InventoryComponent->GetAllItems() : TArray<FInventorySlot>();
}

TArray<FInventorySlot> ASLTPlayerCharacter::GetItemsByType_Implementation(EItemType ItemType) const
{
	return InventoryComponent ? InventoryComponent->GetItemsByType(ItemType) : TArray<FInventorySlot>();
}

bool ASLTPlayerCharacter::IsInventoryFull_Implementation() const
{
	return InventoryComponent ? InventoryComponent->IsInventoryFull() : true;
}

bool ASLTPlayerCharacter::CanFitItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity) const
{
	return InventoryComponent ? InventoryComponent->CanFitItem(ItemData, Quantity) : false;
}

void ASLTPlayerCharacter::ClearInventory_Implementation()
{
	if (InventoryComponent)
	{
		InventoryComponent->ClearInventory();
	}
}

int32 ASLTPlayerCharacter::GetInventoryCapacity_Implementation() const
{
	return InventoryComponent ? InventoryComponent->GetInventoryCapacity() : 0;
}

float ASLTPlayerCharacter::GetCurrentWeight_Implementation() const
{
	return InventoryComponent ? InventoryComponent->GetCurrentWeight() : 0.0f;
}

float ASLTPlayerCharacter::GetMaxWeight_Implementation() const
{
	return InventoryComponent ? InventoryComponent->GetMaxWeight() : 0.0f;
}

bool ASLTPlayerCharacter::UseItem_Implementation(FName ItemID, int32 Quantity)
{
	bool bSuccess = InventoryComponent ? InventoryComponent->UseItem(ItemID, Quantity) : false;
	
	if (bSuccess)
	{
		OnItemUsed(ItemID, Quantity);
	}
	
	return bSuccess;
}

bool ASLTPlayerCharacter::DropItem_Implementation(FName ItemID, int32 Quantity, const FVector& DropLocation)
{
	FVector ActualDropLocation = DropLocation;
	if (ActualDropLocation == FVector::ZeroVector)
	{
		// Drop in front of the player
		ActualDropLocation = GetActorLocation() + GetActorForwardVector() * 100.0f;
	}
	
	return InventoryComponent ? InventoryComponent->DropItem(ItemID, Quantity, ActualDropLocation) : false;
}
