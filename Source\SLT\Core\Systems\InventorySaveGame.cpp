#include "InventorySaveGame.h"
#include "InventorySystem/Components/InventoryGridComponent.h"
#include "PuzzleSystem/Components/PuzzleComponent.h"
#include "Core/Player/SLTPlayerCharacter.h"
#include "Engine/Engine.h"

UInventorySaveGame::UInventorySaveGame()
{
	SaveSlotName = TEXT("DefaultSave");
	UserIndex = 0;
	SaveDateTime = FDateTime::Now();
	GameVersion = TEXT("1.0.0");
	GridWidth = 8;
	GridHeight = 6;
	MaxWeight = 100.0f;
}

bool UInventorySaveGame::SaveInventoryData(UInventoryGridComponent* InventoryComponent)
{
	if (!InventoryComponent)
	{
		return false;
	}

	// Clear existing data
	InventorySlots.Empty();

	// Save grid settings
	GridWidth = InventoryComponent->GridWidth;
	GridHeight = InventoryComponent->GridHeight;
	MaxWeight = InventoryComponent->MaxWeight;

	// Save all inventory slots
	TArray<FInventorySlot> AllItems = InventoryComponent->GetAllItems();
	for (const FInventorySlot& Slot : AllItems)
	{
		FInventorySlotSaveData SaveData;
		SaveData.ItemID = Slot.ItemData.ID;
		SaveData.Quantity = Slot.Quantity;
		SaveData.bIsRotated = Slot.bIsRotated;
		SaveData.GridX = Slot.GridX;
		SaveData.GridY = Slot.GridY;
		SaveData.RuntimeData = Slot.RuntimeData;

		InventorySlots.Add(SaveData);
	}

	return true;
}

bool UInventorySaveGame::LoadInventoryData(UInventoryGridComponent* InventoryComponent)
{
	if (!InventoryComponent)
	{
		return false;
	}

	// Clear existing inventory
	InventoryComponent->ClearInventory();

	// Restore grid settings
	InventoryComponent->GridWidth = GridWidth;
	InventoryComponent->GridHeight = GridHeight;
	InventoryComponent->MaxWeight = MaxWeight;

	// Restore inventory slots
	for (const FInventorySlotSaveData& SaveData : InventorySlots)
	{
		// Get item data from database
		FInventoryItemData ItemData;
		if (InventoryComponent->GetItemDataByID(SaveData.ItemID, ItemData))
		{
			// Try to place the item at its saved position
			bool bSuccess = InventoryComponent->PlaceItemAt(
				SaveData.GridX, 
				SaveData.GridY, 
				ItemData, 
				SaveData.Quantity, 
				SaveData.bIsRotated
			);

			if (!bSuccess)
			{
				UE_LOG(LogTemp, Warning, TEXT("Failed to restore item %s at position (%d, %d)"), 
					*SaveData.ItemID.ToString(), SaveData.GridX, SaveData.GridY);
				
				// Try to add it anywhere
				FInventorySlot OutSlot;
				InventoryComponent->AddItem(ItemData, SaveData.Quantity, OutSlot);
			}
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Item %s not found in database during load"), *SaveData.ItemID.ToString());
		}
	}

	return true;
}

bool UInventorySaveGame::SavePuzzleData(UPuzzleComponent* PuzzleComponent, FName PuzzleID)
{
	if (!PuzzleComponent || PuzzleID == NAME_None)
	{
		return false;
	}

	// Find existing puzzle data or create new
	FPuzzleSaveData* ExistingData = PuzzleData.FindByPredicate([PuzzleID](const FPuzzleSaveData& Data)
	{
		return Data.PuzzleID == PuzzleID;
	});

	FPuzzleSaveData SaveData;
	SaveData.PuzzleID = PuzzleID;
	SaveData.bIsSolved = PuzzleComponent->bIsSolved;
	SaveData.CurrentStepIndex = PuzzleComponent->CurrentStepIndex;
	SaveData.AttemptCount = PuzzleComponent->AttemptCount;
	SaveData.PuzzleState = PuzzleComponent->CurrentState;

	// Save completed steps
	SaveData.CompletedSteps.Empty();
	for (const FPuzzleStep& Step : PuzzleComponent->PuzzleSteps)
	{
		SaveData.CompletedSteps.Add(Step.bIsCompleted);
	}

	if (ExistingData)
	{
		*ExistingData = SaveData;
	}
	else
	{
		PuzzleData.Add(SaveData);
	}

	return true;
}

bool UInventorySaveGame::LoadPuzzleData(UPuzzleComponent* PuzzleComponent, FName PuzzleID)
{
	if (!PuzzleComponent || PuzzleID == NAME_None)
	{
		return false;
	}

	// Find puzzle data
	const FPuzzleSaveData* SaveData = PuzzleData.FindByPredicate([PuzzleID](const FPuzzleSaveData& Data)
	{
		return Data.PuzzleID == PuzzleID;
	});

	if (!SaveData)
	{
		return false; // No save data for this puzzle
	}

	// Restore puzzle state
	PuzzleComponent->bIsSolved = SaveData->bIsSolved;
	PuzzleComponent->CurrentStepIndex = SaveData->CurrentStepIndex;
	PuzzleComponent->AttemptCount = SaveData->AttemptCount;
	PuzzleComponent->CurrentState = SaveData->PuzzleState;

	// Restore completed steps
	for (int32 i = 0; i < FMath::Min(SaveData->CompletedSteps.Num(), PuzzleComponent->PuzzleSteps.Num()); i++)
	{
		PuzzleComponent->PuzzleSteps[i].bIsCompleted = SaveData->CompletedSteps[i];
	}

	return true;
}

bool UInventorySaveGame::SavePlayerData(ASLTPlayerCharacter* PlayerCharacter)
{
	if (!PlayerCharacter)
	{
		return false;
	}

	// Save player transform
	PlayerData.PlayerLocation = PlayerCharacter->GetActorLocation();
	PlayerData.PlayerRotation = PlayerCharacter->GetActorRotation();

	// Save player stats (these would need to be implemented in the player character)
	// For now, we'll use default values
	PlayerData.Health = 100.0f; // PlayerCharacter->GetHealth();
	PlayerData.MaxHealth = 100.0f; // PlayerCharacter->GetMaxHealth();
	PlayerData.Experience = 0; // PlayerCharacter->GetExperience();
	PlayerData.Level = 1; // PlayerCharacter->GetLevel();

	// Save equipped items (this would need to be implemented)
	PlayerData.EquippedItems.Empty();
	// PlayerData.EquippedItems = PlayerCharacter->GetEquippedItems();

	return true;
}

bool UInventorySaveGame::LoadPlayerData(ASLTPlayerCharacter* PlayerCharacter)
{
	if (!PlayerCharacter)
	{
		return false;
	}

	// Restore player transform
	PlayerCharacter->SetActorLocation(PlayerData.PlayerLocation);
	PlayerCharacter->SetActorRotation(PlayerData.PlayerRotation);

	// Restore player stats (these would need to be implemented in the player character)
	// PlayerCharacter->SetHealth(PlayerData.Health);
	// PlayerCharacter->SetMaxHealth(PlayerData.MaxHealth);
	// PlayerCharacter->SetExperience(PlayerData.Experience);
	// PlayerCharacter->SetLevel(PlayerData.Level);

	// Restore equipped items (this would need to be implemented)
	// PlayerCharacter->SetEquippedItems(PlayerData.EquippedItems);

	return true;
}

void UInventorySaveGame::SetCustomData(const FString& Key, const FString& Value)
{
	CustomSaveData.Add(Key, Value);
}

FString UInventorySaveGame::GetCustomData(const FString& Key, const FString& DefaultValue) const
{
	if (const FString* Value = CustomSaveData.Find(Key))
	{
		return *Value;
	}
	return DefaultValue;
}

bool UInventorySaveGame::HasCustomData(const FString& Key) const
{
	return CustomSaveData.Contains(Key);
}

void UInventorySaveGame::RemoveCustomData(const FString& Key)
{
	CustomSaveData.Remove(Key);
}

void UInventorySaveGame::UpdateMetadata(const FString& InSaveSlotName, uint32 InUserIndex)
{
	SaveSlotName = InSaveSlotName;
	UserIndex = InUserIndex;
	SaveDateTime = FDateTime::Now();
}

bool UInventorySaveGame::IsValidSaveData() const
{
	// Basic validation
	if (SaveSlotName.IsEmpty())
	{
		return false;
	}

	if (GridWidth <= 0 || GridHeight <= 0)
	{
		return false;
	}

	if (MaxWeight < 0.0f)
	{
		return false;
	}

	return true;
}

void UInventorySaveGame::ClearAllData()
{
	InventorySlots.Empty();
	PuzzleData.Empty();
	CustomSaveData.Empty();
	
	PlayerData = FPlayerSaveData();
	WorldData = FWorldSaveData();
	
	GridWidth = 8;
	GridHeight = 6;
	MaxWeight = 100.0f;
}
