// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/PuzzleSystem/Actors/PuzzleActor.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodePuzzleActor() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UBoxComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_APuzzleActor();
SLT_API UClass* Z_Construct_UClass_APuzzleActor_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInteractable_NoRegister();
SLT_API UClass* Z_Construct_UClass_UPuzzleComponent_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPuzzleState();
SLT_API UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnPuzzleActorSolved
struct Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics
{
	struct PuzzleActor_eventOnPuzzleActorSolved_Parms
	{
		APuzzleActor* PuzzleActor;
		APawn* SolvingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolvingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::NewProp_PuzzleActor = { "PuzzleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleActorSolved_Parms, PuzzleActor), Z_Construct_UClass_APuzzleActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::NewProp_SolvingPawn = { "SolvingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleActorSolved_Parms, SolvingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::NewProp_PuzzleActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::NewProp_SolvingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleActorSolved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorSolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorSolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void APuzzleActor::FOnPuzzleActorSolved_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorSolved, APuzzleActor* PuzzleActor, APawn* SolvingPawn)
{
	struct PuzzleActor_eventOnPuzzleActorSolved_Parms
	{
		APuzzleActor* PuzzleActor;
		APawn* SolvingPawn;
	};
	PuzzleActor_eventOnPuzzleActorSolved_Parms Parms;
	Parms.PuzzleActor=PuzzleActor;
	Parms.SolvingPawn=SolvingPawn;
	OnPuzzleActorSolved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleActorSolved

// Begin Delegate FOnPuzzleActorFailed
struct Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics
{
	struct PuzzleActor_eventOnPuzzleActorFailed_Parms
	{
		APuzzleActor* PuzzleActor;
		APawn* FailingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FailingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::NewProp_PuzzleActor = { "PuzzleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleActorFailed_Parms, PuzzleActor), Z_Construct_UClass_APuzzleActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::NewProp_FailingPawn = { "FailingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleActorFailed_Parms, FailingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::NewProp_PuzzleActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::NewProp_FailingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleActorFailed__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void APuzzleActor::FOnPuzzleActorFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorFailed, APuzzleActor* PuzzleActor, APawn* FailingPawn)
{
	struct PuzzleActor_eventOnPuzzleActorFailed_Parms
	{
		APuzzleActor* PuzzleActor;
		APawn* FailingPawn;
	};
	PuzzleActor_eventOnPuzzleActorFailed_Parms Parms;
	Parms.PuzzleActor=PuzzleActor;
	Parms.FailingPawn=FailingPawn;
	OnPuzzleActorFailed.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleActorFailed

// Begin Delegate FOnPuzzleActorReset
struct Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics
{
	struct PuzzleActor_eventOnPuzzleActorReset_Parms
	{
		APuzzleActor* PuzzleActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::NewProp_PuzzleActor = { "PuzzleActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleActorReset_Parms, PuzzleActor), Z_Construct_UClass_APuzzleActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::NewProp_PuzzleActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleActorReset__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorReset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::PuzzleActor_eventOnPuzzleActorReset_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void APuzzleActor::FOnPuzzleActorReset_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorReset, APuzzleActor* PuzzleActor)
{
	struct PuzzleActor_eventOnPuzzleActorReset_Parms
	{
		APuzzleActor* PuzzleActor;
	};
	PuzzleActor_eventOnPuzzleActorReset_Parms Parms;
	Parms.PuzzleActor=PuzzleActor;
	OnPuzzleActorReset.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPuzzleActorReset

// Begin Class APuzzleActor Function GetPuzzleProgress
struct Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics
{
	struct PuzzleActor_eventGetPuzzleProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventGetPuzzleProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "GetPuzzleProgress", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PuzzleActor_eventGetPuzzleProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::PuzzleActor_eventGetPuzzleProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execGetPuzzleProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPuzzleProgress();
	P_NATIVE_END;
}
// End Class APuzzleActor Function GetPuzzleProgress

// Begin Class APuzzleActor Function IsPuzzleSolved
struct Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics
{
	struct PuzzleActor_eventIsPuzzleSolved_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleActor_eventIsPuzzleSolved_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleActor_eventIsPuzzleSolved_Parms), &Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "IsPuzzleSolved", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PuzzleActor_eventIsPuzzleSolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::PuzzleActor_eventIsPuzzleSolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execIsPuzzleSolved)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPuzzleSolved();
	P_NATIVE_END;
}
// End Class APuzzleActor Function IsPuzzleSolved

// Begin Class APuzzleActor Function OnPuzzleFailed
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics
{
	struct PuzzleActor_eventOnPuzzleFailed_Parms
	{
		APawn* FailingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FailingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::NewProp_FailingPawn = { "FailingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleFailed_Parms, FailingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::NewProp_FailingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleFailed", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PuzzleActor_eventOnPuzzleFailed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::PuzzleActor_eventOnPuzzleFailed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execOnPuzzleFailed)
{
	P_GET_OBJECT(APawn,Z_Param_FailingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPuzzleFailed(Z_Param_FailingPawn);
	P_NATIVE_END;
}
// End Class APuzzleActor Function OnPuzzleFailed

// Begin Class APuzzleActor Function OnPuzzleInteracted
struct PuzzleActor_eventOnPuzzleInteracted_Parms
{
	APawn* InteractingPawn;
};
static const FName NAME_APuzzleActor_OnPuzzleInteracted = FName(TEXT("OnPuzzleInteracted"));
void APuzzleActor::OnPuzzleInteracted(APawn* InteractingPawn)
{
	PuzzleActor_eventOnPuzzleInteracted_Parms Parms;
	Parms.InteractingPawn=InteractingPawn;
	UFunction* Func = FindFunctionChecked(NAME_APuzzleActor_OnPuzzleInteracted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleInteracted_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleInteracted", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::PropPointers), sizeof(PuzzleActor_eventOnPuzzleInteracted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::Function_MetaDataParams) };
static_assert(sizeof(PuzzleActor_eventOnPuzzleInteracted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class APuzzleActor Function OnPuzzleInteracted

// Begin Class APuzzleActor Function OnPuzzleReset
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics
{
	struct PuzzleActor_eventOnPuzzleReset_Parms
	{
		bool bWasForced;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bWasForced_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasForced;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::NewProp_bWasForced_SetBit(void* Obj)
{
	((PuzzleActor_eventOnPuzzleReset_Parms*)Obj)->bWasForced = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::NewProp_bWasForced = { "bWasForced", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleActor_eventOnPuzzleReset_Parms), &Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::NewProp_bWasForced_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::NewProp_bWasForced,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleReset", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PuzzleActor_eventOnPuzzleReset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::PuzzleActor_eventOnPuzzleReset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleReset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleReset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execOnPuzzleReset)
{
	P_GET_UBOOL(Z_Param_bWasForced);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPuzzleReset(Z_Param_bWasForced);
	P_NATIVE_END;
}
// End Class APuzzleActor Function OnPuzzleReset

// Begin Class APuzzleActor Function OnPuzzleSolved
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics
{
	struct PuzzleActor_eventOnPuzzleSolved_Parms
	{
		APawn* SolvingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Event handlers\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Event handlers" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SolvingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::NewProp_SolvingPawn = { "SolvingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleSolved_Parms, SolvingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::NewProp_SolvingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleSolved", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PuzzleActor_eventOnPuzzleSolved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::PuzzleActor_eventOnPuzzleSolved_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execOnPuzzleSolved)
{
	P_GET_OBJECT(APawn,Z_Param_SolvingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPuzzleSolved(Z_Param_SolvingPawn);
	P_NATIVE_END;
}
// End Class APuzzleActor Function OnPuzzleSolved

// Begin Class APuzzleActor Function OnPuzzleStateChanged
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics
{
	struct PuzzleActor_eventOnPuzzleStateChanged_Parms
	{
		EPuzzleState OldState;
		EPuzzleState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleStateChanged_Parms, OldState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(0, nullptr) }; // 3710896167
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(0, nullptr) }; // 3710896167
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleStateChanged", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PuzzleActor_eventOnPuzzleStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::PuzzleActor_eventOnPuzzleStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execOnPuzzleStateChanged)
{
	P_GET_ENUM(EPuzzleState,Z_Param_OldState);
	P_GET_ENUM(EPuzzleState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnPuzzleStateChanged(EPuzzleState(Z_Param_OldState),EPuzzleState(Z_Param_NewState));
	P_NATIVE_END;
}
// End Class APuzzleActor Function OnPuzzleStateChanged

// Begin Class APuzzleActor Function OnPuzzleVisualUpdate
struct PuzzleActor_eventOnPuzzleVisualUpdate_Parms
{
	EPuzzleState PuzzleState;
};
static const FName NAME_APuzzleActor_OnPuzzleVisualUpdate = FName(TEXT("OnPuzzleVisualUpdate"));
void APuzzleActor::OnPuzzleVisualUpdate(EPuzzleState PuzzleState)
{
	PuzzleActor_eventOnPuzzleVisualUpdate_Parms Parms;
	Parms.PuzzleState=PuzzleState;
	UFunction* Func = FindFunctionChecked(NAME_APuzzleActor_OnPuzzleVisualUpdate);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PuzzleState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PuzzleState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::NewProp_PuzzleState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::NewProp_PuzzleState = { "PuzzleState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnPuzzleVisualUpdate_Parms, PuzzleState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(0, nullptr) }; // 3710896167
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::NewProp_PuzzleState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::NewProp_PuzzleState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnPuzzleVisualUpdate", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::PropPointers), sizeof(PuzzleActor_eventOnPuzzleVisualUpdate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::Function_MetaDataParams) };
static_assert(sizeof(PuzzleActor_eventOnPuzzleVisualUpdate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class APuzzleActor Function OnPuzzleVisualUpdate

// Begin Class APuzzleActor Function OnSolutionAttempted
struct PuzzleActor_eventOnSolutionAttempted_Parms
{
	FString AttemptedSolution;
	bool bWasCorrect;
};
static const FName NAME_APuzzleActor_OnSolutionAttempted = FName(TEXT("OnSolutionAttempted"));
void APuzzleActor::OnSolutionAttempted(const FString& AttemptedSolution, bool bWasCorrect)
{
	PuzzleActor_eventOnSolutionAttempted_Parms Parms;
	Parms.AttemptedSolution=AttemptedSolution;
	Parms.bWasCorrect=bWasCorrect ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_APuzzleActor_OnSolutionAttempted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttemptedSolution_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AttemptedSolution;
	static void NewProp_bWasCorrect_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasCorrect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_AttemptedSolution = { "AttemptedSolution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventOnSolutionAttempted_Parms, AttemptedSolution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttemptedSolution_MetaData), NewProp_AttemptedSolution_MetaData) };
void Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_bWasCorrect_SetBit(void* Obj)
{
	((PuzzleActor_eventOnSolutionAttempted_Parms*)Obj)->bWasCorrect = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_bWasCorrect = { "bWasCorrect", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleActor_eventOnSolutionAttempted_Parms), &Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_bWasCorrect_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_AttemptedSolution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::NewProp_bWasCorrect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "OnSolutionAttempted", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::PropPointers), sizeof(PuzzleActor_eventOnSolutionAttempted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::Function_MetaDataParams) };
static_assert(sizeof(PuzzleActor_eventOnSolutionAttempted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class APuzzleActor Function OnSolutionAttempted

// Begin Class APuzzleActor Function ResetPuzzle
struct Z_Construct_UFunction_APuzzleActor_ResetPuzzle_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_ResetPuzzle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "ResetPuzzle", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_ResetPuzzle_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_ResetPuzzle_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_APuzzleActor_ResetPuzzle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_ResetPuzzle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execResetPuzzle)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetPuzzle();
	P_NATIVE_END;
}
// End Class APuzzleActor Function ResetPuzzle

// Begin Class APuzzleActor Function TryInputSolution
struct Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics
{
	struct PuzzleActor_eventTryInputSolution_Parms
	{
		FString Solution;
		APawn* InteractingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Public functions\n" },
#endif
		{ "CPP_Default_InteractingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Public functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Solution_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Solution;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_Solution = { "Solution", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventTryInputSolution_Parms, Solution), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Solution_MetaData), NewProp_Solution_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventTryInputSolution_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleActor_eventTryInputSolution_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleActor_eventTryInputSolution_Parms), &Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_Solution,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "TryInputSolution", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PuzzleActor_eventTryInputSolution_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::PuzzleActor_eventTryInputSolution_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_TryInputSolution()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_TryInputSolution_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execTryInputSolution)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Solution);
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TryInputSolution(Z_Param_Solution,Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Class APuzzleActor Function TryInputSolution

// Begin Class APuzzleActor Function TryInputStep
struct Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics
{
	struct PuzzleActor_eventTryInputStep_Parms
	{
		FString StepValue;
		APawn* InteractingPawn;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Puzzle" },
		{ "CPP_Default_InteractingPawn", "None" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepValue;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_StepValue = { "StepValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventTryInputStep_Parms, StepValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepValue_MetaData), NewProp_StepValue_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(PuzzleActor_eventTryInputStep_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PuzzleActor_eventTryInputStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PuzzleActor_eventTryInputStep_Parms), &Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_StepValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_APuzzleActor, nullptr, "TryInputStep", nullptr, nullptr, Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PuzzleActor_eventTryInputStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::PuzzleActor_eventTryInputStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_APuzzleActor_TryInputStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_APuzzleActor_TryInputStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(APuzzleActor::execTryInputStep)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_StepValue);
	P_GET_OBJECT(APawn,Z_Param_InteractingPawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->TryInputStep(Z_Param_StepValue,Z_Param_InteractingPawn);
	P_NATIVE_END;
}
// End Class APuzzleActor Function TryInputStep

// Begin Class APuzzleActor
void APuzzleActor::StaticRegisterNativesAPuzzleActor()
{
	UClass* Class = APuzzleActor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetPuzzleProgress", &APuzzleActor::execGetPuzzleProgress },
		{ "IsPuzzleSolved", &APuzzleActor::execIsPuzzleSolved },
		{ "OnPuzzleFailed", &APuzzleActor::execOnPuzzleFailed },
		{ "OnPuzzleReset", &APuzzleActor::execOnPuzzleReset },
		{ "OnPuzzleSolved", &APuzzleActor::execOnPuzzleSolved },
		{ "OnPuzzleStateChanged", &APuzzleActor::execOnPuzzleStateChanged },
		{ "ResetPuzzle", &APuzzleActor::execResetPuzzle },
		{ "TryInputSolution", &APuzzleActor::execTryInputSolution },
		{ "TryInputStep", &APuzzleActor::execTryInputStep },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(APuzzleActor);
UClass* Z_Construct_UClass_APuzzleActor_NoRegister()
{
	return APuzzleActor::StaticClass();
}
struct Z_Construct_UClass_APuzzleActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "PuzzleSystem/Actors/PuzzleActor.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionBox_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleComponent_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionPrompt_MetaData[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interaction settings\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomPrompt_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPuzzleProgress_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequireInteractionToStart_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bChangeColorOnSolved_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual feedback\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual feedback" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SolvedColor_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bChangeColorOnSolved" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnsolvedColor_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bChangeColorOnSolved" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FailedColor_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bChangeColorOnSolved" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleActorSolved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleActorFailed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPuzzleActorReset_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DynamicMaterial_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "PuzzleSystem/Actors/PuzzleActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionBox;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleComponent;
	static const UECodeGen_Private::FTextPropertyParams NewProp_InteractionPrompt;
	static void NewProp_bUseCustomPrompt_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomPrompt;
	static void NewProp_bShowPuzzleProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPuzzleProgress;
	static void NewProp_bRequireInteractionToStart_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequireInteractionToStart;
	static void NewProp_bChangeColorOnSolved_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bChangeColorOnSolved;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SolvedColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnsolvedColor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FailedColor;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleActorSolved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleActorFailed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPuzzleActorReset;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DynamicMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_APuzzleActor_GetPuzzleProgress, "GetPuzzleProgress" }, // 1047008651
		{ &Z_Construct_UFunction_APuzzleActor_IsPuzzleSolved, "IsPuzzleSolved" }, // 273992392
		{ &Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature, "OnPuzzleActorFailed__DelegateSignature" }, // 3043849478
		{ &Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature, "OnPuzzleActorReset__DelegateSignature" }, // 1036289756
		{ &Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature, "OnPuzzleActorSolved__DelegateSignature" }, // 3514602786
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleFailed, "OnPuzzleFailed" }, // 3808430301
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleInteracted, "OnPuzzleInteracted" }, // 2316572485
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleReset, "OnPuzzleReset" }, // 3207992379
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleSolved, "OnPuzzleSolved" }, // 2981865276
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleStateChanged, "OnPuzzleStateChanged" }, // 1253646131
		{ &Z_Construct_UFunction_APuzzleActor_OnPuzzleVisualUpdate, "OnPuzzleVisualUpdate" }, // 1429522029
		{ &Z_Construct_UFunction_APuzzleActor_OnSolutionAttempted, "OnSolutionAttempted" }, // 2271610192
		{ &Z_Construct_UFunction_APuzzleActor_ResetPuzzle, "ResetPuzzle" }, // 3021149871
		{ &Z_Construct_UFunction_APuzzleActor_TryInputSolution, "TryInputSolution" }, // 3400003184
		{ &Z_Construct_UFunction_APuzzleActor_TryInputStep, "TryInputStep" }, // 1667567650
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<APuzzleActor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_InteractionBox = { "InteractionBox", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, InteractionBox), Z_Construct_UClass_UBoxComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionBox_MetaData), NewProp_InteractionBox_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_PuzzleComponent = { "PuzzleComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, PuzzleComponent), Z_Construct_UClass_UPuzzleComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleComponent_MetaData), NewProp_PuzzleComponent_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_InteractionPrompt = { "InteractionPrompt", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, InteractionPrompt), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionPrompt_MetaData), NewProp_InteractionPrompt_MetaData) };
void Z_Construct_UClass_APuzzleActor_Statics::NewProp_bUseCustomPrompt_SetBit(void* Obj)
{
	((APuzzleActor*)Obj)->bUseCustomPrompt = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_bUseCustomPrompt = { "bUseCustomPrompt", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APuzzleActor), &Z_Construct_UClass_APuzzleActor_Statics::NewProp_bUseCustomPrompt_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomPrompt_MetaData), NewProp_bUseCustomPrompt_MetaData) };
void Z_Construct_UClass_APuzzleActor_Statics::NewProp_bShowPuzzleProgress_SetBit(void* Obj)
{
	((APuzzleActor*)Obj)->bShowPuzzleProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_bShowPuzzleProgress = { "bShowPuzzleProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APuzzleActor), &Z_Construct_UClass_APuzzleActor_Statics::NewProp_bShowPuzzleProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPuzzleProgress_MetaData), NewProp_bShowPuzzleProgress_MetaData) };
void Z_Construct_UClass_APuzzleActor_Statics::NewProp_bRequireInteractionToStart_SetBit(void* Obj)
{
	((APuzzleActor*)Obj)->bRequireInteractionToStart = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_bRequireInteractionToStart = { "bRequireInteractionToStart", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APuzzleActor), &Z_Construct_UClass_APuzzleActor_Statics::NewProp_bRequireInteractionToStart_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequireInteractionToStart_MetaData), NewProp_bRequireInteractionToStart_MetaData) };
void Z_Construct_UClass_APuzzleActor_Statics::NewProp_bChangeColorOnSolved_SetBit(void* Obj)
{
	((APuzzleActor*)Obj)->bChangeColorOnSolved = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_bChangeColorOnSolved = { "bChangeColorOnSolved", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(APuzzleActor), &Z_Construct_UClass_APuzzleActor_Statics::NewProp_bChangeColorOnSolved_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bChangeColorOnSolved_MetaData), NewProp_bChangeColorOnSolved_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_SolvedColor = { "SolvedColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, SolvedColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SolvedColor_MetaData), NewProp_SolvedColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_UnsolvedColor = { "UnsolvedColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, UnsolvedColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnsolvedColor_MetaData), NewProp_UnsolvedColor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_FailedColor = { "FailedColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, FailedColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FailedColor_MetaData), NewProp_FailedColor_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorSolved = { "OnPuzzleActorSolved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, OnPuzzleActorSolved), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorSolved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleActorSolved_MetaData), NewProp_OnPuzzleActorSolved_MetaData) }; // 3514602786
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorFailed = { "OnPuzzleActorFailed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, OnPuzzleActorFailed), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorFailed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleActorFailed_MetaData), NewProp_OnPuzzleActorFailed_MetaData) }; // 3043849478
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorReset = { "OnPuzzleActorReset", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, OnPuzzleActorReset), Z_Construct_UDelegateFunction_APuzzleActor_OnPuzzleActorReset__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPuzzleActorReset_MetaData), NewProp_OnPuzzleActorReset_MetaData) }; // 1036289756
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_APuzzleActor_Statics::NewProp_DynamicMaterial = { "DynamicMaterial", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(APuzzleActor, DynamicMaterial), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DynamicMaterial_MetaData), NewProp_DynamicMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_APuzzleActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_InteractionBox,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_PuzzleComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_InteractionPrompt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_bUseCustomPrompt,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_bShowPuzzleProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_bRequireInteractionToStart,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_bChangeColorOnSolved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_SolvedColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_UnsolvedColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_FailedColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorSolved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorFailed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_OnPuzzleActorReset,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_APuzzleActor_Statics::NewProp_DynamicMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APuzzleActor_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_APuzzleActor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_APuzzleActor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_APuzzleActor_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UInteractable_NoRegister, (int32)VTABLE_OFFSET(APuzzleActor, IInteractable), false },  // 206469426
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_APuzzleActor_Statics::ClassParams = {
	&APuzzleActor::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_APuzzleActor_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_APuzzleActor_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_APuzzleActor_Statics::Class_MetaDataParams), Z_Construct_UClass_APuzzleActor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_APuzzleActor()
{
	if (!Z_Registration_Info_UClass_APuzzleActor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_APuzzleActor.OuterSingleton, Z_Construct_UClass_APuzzleActor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_APuzzleActor.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<APuzzleActor>()
{
	return APuzzleActor::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(APuzzleActor);
APuzzleActor::~APuzzleActor() {}
// End Class APuzzleActor

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_APuzzleActor, APuzzleActor::StaticClass, TEXT("APuzzleActor"), &Z_Registration_Info_UClass_APuzzleActor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(APuzzleActor), 412391252U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_4274039151(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
