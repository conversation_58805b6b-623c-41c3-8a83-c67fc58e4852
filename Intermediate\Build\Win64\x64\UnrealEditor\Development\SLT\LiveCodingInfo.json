{"RemapUnityFiles": {"Module.SLT.1.cpp.obj": ["AIBehaviorDesigner.gen.cpp.obj", "AutomatedSetupWizard.gen.cpp.obj"], "Module.SLT.2.cpp.obj": ["DragDropLevelDesigner.gen.cpp.obj", "EnemyCharacter.gen.cpp.obj", "Interactable.gen.cpp.obj"], "Module.SLT.3.cpp.obj": ["InteractionComponent.gen.cpp.obj", "InventoryGridComponent.gen.cpp.obj", "InventoryInterface.gen.cpp.obj", "InventoryItemData.gen.cpp.obj", "InventorySaveGame.gen.cpp.obj"], "Module.SLT.4.cpp.obj": ["ItemActor.gen.cpp.obj", "LevelProgressionManager.gen.cpp.obj", "LoadingManager.gen.cpp.obj", "ObjectPoolManager.gen.cpp.obj"], "Module.SLT.5.cpp.obj": ["PuzzleActor.gen.cpp.obj", "PuzzleComponent.gen.cpp.obj", "ResourceManager.gen.cpp.obj", "ShaderManager.gen.cpp.obj"], "Module.SLT.6.cpp.obj": ["SLT.init.gen.cpp.obj", "SLTPlayerCharacter.gen.cpp.obj", "StatisticsManager.gen.cpp.obj", "VisualDesignTools.gen.cpp.obj"], "Module.SLT.7.cpp.obj": ["WeaponSystem.gen.cpp.obj", "PerModuleInline.gen.cpp.obj", "AIBehaviorDesigner.cpp.obj", "AutomatedSetupWizard.cpp.obj", "DragDropLevelDesigner.cpp.obj", "VisualDesignTools.cpp.obj", "EnemyCharacter.cpp.obj", "Interactable.cpp.obj", "SLTPlayerCharacter.cpp.obj", "InventorySaveGame.cpp.obj", "LevelProgressionManager.cpp.obj", "LoadingManager.cpp.obj", "ObjectPoolManager.cpp.obj", "ResourceManager.cpp.obj", "ShaderManager.cpp.obj", "StatisticsManager.cpp.obj", "WeaponSystem.cpp.obj", "ItemActor.cpp.obj", "InteractionComponent.cpp.obj"]}}