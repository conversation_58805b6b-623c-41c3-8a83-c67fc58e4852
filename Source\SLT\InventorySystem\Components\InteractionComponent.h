#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "../../Core/Interfaces/Interactable.h"
#include "Engine/Engine.h"
#include "InteractionComponent.generated.h"

class APawn;
class UInputComponent;
class UEnhancedInputComponent;
class UInputAction;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractionStarted, AActor*, InteractableActor, APawn*, InteractingPawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractionCompleted, AActor*, InteractableActor, APawn*, InteractingPawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractionCancelled, AActor*, InteractableActor, APawn*, InteractingPawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractableFound, AActor*, InteractableActor, const FInteractionData&, InteractionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnInteractableLost);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UInteractionComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UInteractionComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Interaction settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction Settings", meta = (ClampMin = "0.0"))
	float InteractionRange = 200.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction Settings", meta = (ClampMin = "0.0"))
	float InteractionSphereRadius = 50.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction Settings")
	bool bUseLineTrace = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction Settings")
	bool bRequireLineOfSight = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction Settings")
	TArray<TEnumAsByte<EObjectTypeQuery>> InteractionObjectTypes;

	// Input settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Input")
	TSoftObjectPtr<UInputAction> InteractAction;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractionStarted OnInteractionStarted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractionCompleted OnInteractionCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractionCancelled OnInteractionCancelled;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractableFound OnInteractableFound;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInteractableLost OnInteractableLost;

	// Main interaction functions
	UFUNCTION(BlueprintCallable, Category = "Interaction")
	void StartInteraction();

	UFUNCTION(BlueprintCallable, Category = "Interaction")
	void CancelInteraction();

	UFUNCTION(BlueprintCallable, Category = "Interaction")
	bool IsInteracting() const { return bIsInteracting; }

	UFUNCTION(BlueprintCallable, Category = "Interaction")
	AActor* GetCurrentInteractable() const { return CurrentInteractable.Get(); }

	UFUNCTION(BlueprintCallable, Category = "Interaction")
	FInteractionData GetCurrentInteractionData() const { return CurrentInteractionData; }

	// Detection functions
	UFUNCTION(BlueprintCallable, Category = "Detection")
	TArray<AActor*> FindInteractablesInRange() const;

	UFUNCTION(BlueprintCallable, Category = "Detection")
	AActor* FindBestInteractable() const;

	UFUNCTION(BlueprintCallable, Category = "Detection")
	bool CanInteractWith(AActor* Actor) const;

	// Setup functions
	UFUNCTION(BlueprintCallable, Category = "Setup")
	void SetupInputComponent(UEnhancedInputComponent* EnhancedInputComponent);

protected:
	// Current interaction state
	UPROPERTY()
	TWeakObjectPtr<AActor> CurrentInteractable;

	UPROPERTY()
	FInteractionData CurrentInteractionData;

	UPROPERTY()
	bool bIsInteracting = false;

	UPROPERTY()
	float InteractionStartTime = 0.0f;

	UPROPERTY()
	FTimerHandle InteractionTimerHandle;

	// Internal functions
	void UpdateInteractableDetection();
	void SetCurrentInteractable(AActor* NewInteractable);
	void CompleteInteraction();
	void OnInteractionTimerComplete();

	// Input handling
	void OnInteractPressed();
	void OnInteractReleased();

	// Helper functions
	bool HasLineOfSight(AActor* Actor) const;
	float GetDistanceToActor(AActor* Actor) const;
	FVector GetInteractionStartLocation() const;
};
