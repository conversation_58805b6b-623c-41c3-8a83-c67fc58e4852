#include "DragDropLevelDesigner.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "DrawDebugHelpers.h"
#include "Kismet/KismetMathLibrary.h"
#include "Engine/Engine.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"

UDragDropLevelDesigner::UDragDropLevelDesigner()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.1f;

	bDesignModeActive = false;
	bShowPreview = true;
	bShowGrid = true;
	bEnableUndo = true;
	CurrentAssetID = NAME_None;
	PreviewActor = nullptr;
	MaxUndoSteps = 50;

	// Default placement settings
	PlacementSettings.PlacementMode = EPlacementMode::Single;
	PlacementSettings.SnapMode = ESnapMode::Grid;
	PlacementSettings.GridSize = 100.0f;
	PlacementSettings.PaintRadius = 500.0f;
	PlacementSettings.PaintDensity = 0.5f;
	PlacementSettings.bAlignToSurface = true;
	PlacementSettings.bRandomizeRotation = false;
	PlacementSettings.bRandomizeScale = false;
	PlacementSettings.ScaleVariation = 0.1f;
	PlacementSettings.RotationVariation = 15.0f;

	// Initialize design stats
	DesignStats.TotalActorsPlaced = 0;
	DesignStats.DesignTime = 0.0f;
	DesignStats.LastModified = FDateTime::Now();
}

void UDragDropLevelDesigner::BeginPlay()
{
	Super::BeginPlay();
	LoadAssetDatabase();
}

void UDragDropLevelDesigner::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (bDesignModeActive)
	{
		DesignStats.DesignTime += DeltaTime;

		// Draw grid if enabled
		if (bShowGrid)
		{
			DrawDebugGrid();
		}

		// Update preview actor if in design mode
		if (bShowPreview && CurrentAssetID != NAME_None)
		{
			UpdatePreviewActorLocation();
		}
	}
}

void UDragDropLevelDesigner::SetDesignModeActive(bool bActive)
{
	if (bDesignModeActive != bActive)
	{
		bDesignModeActive = bActive;
		
		if (!bActive)
		{
			DestroyPreviewActor();
			ClearSelection();
		}
		
		OnDesignModeToggled(bActive);
		UE_LOG(LogTemp, Log, TEXT("Design mode %s"), bActive ? TEXT("activated") : TEXT("deactivated"));
	}
}

void UDragDropLevelDesigner::SelectAssetForPlacement(FName AssetID)
{
	if (CurrentAssetID != AssetID)
	{
		CurrentAssetID = AssetID;
		
		// Destroy old preview
		DestroyPreviewActor();
		
		// Create new preview if valid asset
		if (AssetID != NAME_None)
		{
			PreviewActor = CreatePreviewActor(AssetID);
			FDragDropAsset AssetData = GetAssetData(AssetID);
			OnAssetSelected(AssetID, AssetData);
		}
	}
}

AActor* UDragDropLevelDesigner::PlaceActorAtLocation(const FVector& Location, const FRotator& Rotation)
{
	if (CurrentAssetID == NAME_None || !bDesignModeActive)
	{
		return nullptr;
	}

	FDragDropAsset AssetData = GetAssetData(CurrentAssetID);
	if (AssetData.AssetID == NAME_None || !AssetData.ActorClass)
	{
		return nullptr;
	}

	// Validate placement
	if (!CanPlaceAsset(CurrentAssetID, Location))
	{
		UE_LOG(LogTemp, Warning, TEXT("Cannot place asset %s at location %s"), 
			*CurrentAssetID.ToString(), *Location.ToString());
		return nullptr;
	}

	// Calculate final placement location and rotation
	FVector FinalLocation = Location;
	FRotator FinalRotation = Rotation;

	// Apply snapping
	switch (PlacementSettings.SnapMode)
	{
		case ESnapMode::Grid:
			FinalLocation = SnapLocationToGrid(Location);
			break;
		case ESnapMode::Surface:
			FinalLocation = SnapLocationToSurface(Location);
			break;
		case ESnapMode::Smart:
			FinalLocation = SnapLocationToGrid(SnapLocationToSurface(Location));
			break;
	}

	// Apply randomization
	if (PlacementSettings.bRandomizeRotation)
	{
		float RandomYaw = FMath::RandRange(-PlacementSettings.RotationVariation, PlacementSettings.RotationVariation);
		FinalRotation.Yaw += RandomYaw;
	}

	// Spawn the actor
	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
	
	AActor* SpawnedActor = GetWorld()->SpawnActor<AActor>(AssetData.ActorClass, FinalLocation, FinalRotation, SpawnParams);
	
	if (SpawnedActor)
	{
		// Apply scale randomization
		if (PlacementSettings.bRandomizeScale)
		{
			FVector BaseScale = AssetData.DefaultScale;
			float ScaleMultiplier = FMath::RandRange(1.0f - PlacementSettings.ScaleVariation, 1.0f + PlacementSettings.ScaleVariation);
			SpawnedActor->SetActorScale3D(BaseScale * ScaleMultiplier);
		}
		else
		{
			SpawnedActor->SetActorScale3D(AssetData.DefaultScale);
		}

		// Add to placed actors list
		PlacedActors.Add(SpawnedActor);
		
		// Update statistics
		DesignStats.TotalActorsPlaced++;
		if (int32* CategoryCount = DesignStats.ActorsByCategory.Find(AssetData.Category))
		{
			(*CategoryCount)++;
		}
		else
		{
			DesignStats.ActorsByCategory.Add(AssetData.Category, 1);
		}
		DesignStats.LastModified = FDateTime::Now();

		// Record undo action
		if (bEnableUndo)
		{
			RecordUndoAction(FString::Printf(TEXT("Place_%s_%s"), 
				*CurrentAssetID.ToString(), *FinalLocation.ToString()));
		}

		OnActorPlaced.Broadcast(SpawnedActor, CurrentAssetID, FinalLocation);
		OnLevelLayoutChanged();

		UE_LOG(LogTemp, Log, TEXT("Placed actor %s at %s"), 
			*AssetData.AssetName.ToString(), *FinalLocation.ToString());
	}

	return SpawnedActor;
}

void UDragDropLevelDesigner::RemoveSelectedActors()
{
	if (SelectedActors.Num() == 0)
	{
		return;
	}

	for (AActor* Actor : SelectedActors)
	{
		if (IsValid(Actor))
		{
			PlacedActors.Remove(Actor);
			OnActorRemoved.Broadcast(Actor, NAME_None);
			Actor->Destroy();
		}
	}

	if (bEnableUndo)
	{
		RecordUndoAction(FString::Printf(TEXT("Remove_%d_actors"), SelectedActors.Num()));
	}

	ClearSelection();
	UpdateDesignStats();
	OnLevelLayoutChanged();
}

void UDragDropLevelDesigner::DuplicateSelectedActors()
{
	if (SelectedActors.Num() == 0)
	{
		return;
	}

	TArray<AActor*> NewActors;
	FVector OffsetDirection = FVector(100, 100, 0); // Default offset

	for (AActor* Actor : SelectedActors)
	{
		if (IsValid(Actor))
		{
			FVector NewLocation = Actor->GetActorLocation() + OffsetDirection;
			FRotator NewRotation = Actor->GetActorRotation();
			
			FActorSpawnParameters SpawnParams;
			SpawnParams.Template = Actor;
			SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
			
			AActor* DuplicatedActor = GetWorld()->SpawnActor<AActor>(Actor->GetClass(), NewLocation, NewRotation, SpawnParams);
			if (DuplicatedActor)
			{
				PlacedActors.Add(DuplicatedActor);
				NewActors.Add(DuplicatedActor);
			}
		}
	}

	// Select the new actors
	ClearSelection();
	for (AActor* NewActor : NewActors)
	{
		SelectActor(NewActor, true);
	}

	if (bEnableUndo)
	{
		RecordUndoAction(FString::Printf(TEXT("Duplicate_%d_actors"), NewActors.Num()));
	}

	UpdateDesignStats();
	OnLevelLayoutChanged();
}

void UDragDropLevelDesigner::SelectActor(AActor* Actor, bool bAddToSelection)
{
	if (!IsValid(Actor))
	{
		return;
	}

	if (!bAddToSelection)
	{
		ClearSelection();
	}

	if (!SelectedActors.Contains(Actor))
	{
		SelectedActors.Add(Actor);
		OnSelectionChanged.Broadcast(SelectedActors, bAddToSelection);
	}
}

void UDragDropLevelDesigner::SelectActorsInArea(const FVector& Center, float Radius)
{
	ClearSelection();

	for (AActor* Actor : PlacedActors)
	{
		if (IsValid(Actor))
		{
			float Distance = FVector::Dist(Actor->GetActorLocation(), Center);
			if (Distance <= Radius)
			{
				SelectedActors.Add(Actor);
			}
		}
	}

	OnSelectionChanged.Broadcast(SelectedActors, true);
}

void UDragDropLevelDesigner::SelectActorsByCategory(EDragDropCategory Category)
{
	ClearSelection();

	for (AActor* Actor : PlacedActors)
	{
		if (IsValid(Actor))
		{
			// This would need to check the actor's category
			// For now, we'll select all actors
			SelectedActors.Add(Actor);
		}
	}

	OnSelectionChanged.Broadcast(SelectedActors, true);
}

void UDragDropLevelDesigner::ClearSelection()
{
	if (SelectedActors.Num() > 0)
	{
		SelectedActors.Empty();
		OnSelectionChanged.Broadcast(SelectedActors, false);
	}
}

void UDragDropLevelDesigner::SetPlacementMode(EPlacementMode NewMode)
{
	if (PlacementSettings.PlacementMode != NewMode)
	{
		EPlacementMode OldMode = PlacementSettings.PlacementMode;
		PlacementSettings.PlacementMode = NewMode;
		OnPlacementModeChanged.Broadcast(OldMode, NewMode);
	}
}

void UDragDropLevelDesigner::SetSnapMode(ESnapMode NewMode)
{
	PlacementSettings.SnapMode = NewMode;
}

FVector UDragDropLevelDesigner::SnapLocationToGrid(const FVector& Location) const
{
	FVector SnappedLocation;
	float GridSize = PlacementSettings.GridSize;
	
	SnappedLocation.X = FMath::RoundToFloat(Location.X / GridSize) * GridSize;
	SnappedLocation.Y = FMath::RoundToFloat(Location.Y / GridSize) * GridSize;
	SnappedLocation.Z = Location.Z; // Don't snap Z by default
	
	return SnappedLocation;
}

FVector UDragDropLevelDesigner::SnapLocationToSurface(const FVector& Location) const
{
	FVector StartLocation = Location + FVector(0, 0, 1000);
	FVector EndLocation = Location - FVector(0, 0, 1000);
	
	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(GetOwner());
	
	if (GetWorld()->LineTraceSingleByChannel(HitResult, StartLocation, EndLocation, ECC_WorldStatic, QueryParams))
	{
		return HitResult.Location;
	}
	
	return Location;
}

TArray<FDragDropAsset> UDragDropLevelDesigner::GetAssetsByCategory(EDragDropCategory Category) const
{
	TArray<FDragDropAsset> FilteredAssets;
	
	if (!CachedAssetDatabase)
	{
		return FilteredAssets;
	}

	TArray<FName> RowNames = CachedAssetDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		if (FDragDropAsset* AssetData = CachedAssetDatabase->FindRow<FDragDropAsset>(RowName, TEXT("GetAssetsByCategory")))
		{
			if (AssetData->Category == Category)
			{
				FilteredAssets.Add(*AssetData);
			}
		}
	}
	
	return FilteredAssets;
}

FDragDropAsset UDragDropLevelDesigner::GetAssetData(FName AssetID) const
{
	if (CachedAssetDatabase)
	{
		if (FDragDropAsset* AssetData = CachedAssetDatabase->FindRow<FDragDropAsset>(AssetID, TEXT("GetAssetData")))
		{
			return *AssetData;
		}
	}
	return FDragDropAsset();
}

TArray<FDragDropAsset> UDragDropLevelDesigner::SearchAssets(const FString& SearchTerm) const
{
	TArray<FDragDropAsset> SearchResults;
	
	if (!CachedAssetDatabase || SearchTerm.IsEmpty())
	{
		return SearchResults;
	}

	TArray<FName> RowNames = CachedAssetDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		if (FDragDropAsset* AssetData = CachedAssetDatabase->FindRow<FDragDropAsset>(RowName, TEXT("SearchAssets")))
		{
			// Search in name, description, and keywords
			if (AssetData->AssetName.ToString().Contains(SearchTerm) ||
				AssetData->AssetDescription.ToString().Contains(SearchTerm) ||
				AssetData->SearchKeywords.ContainsByPredicate([SearchTerm](const FString& Keyword)
				{
					return Keyword.Contains(SearchTerm);
				}))
			{
				SearchResults.Add(*AssetData);
			}
		}
	}
	
	return SearchResults;
}

bool UDragDropLevelDesigner::CanPlaceAsset(FName AssetID, const FVector& Location) const
{
	FDragDropAsset AssetData = GetAssetData(AssetID);
	if (AssetData.AssetID == NAME_None)
	{
		return false;
	}

	// Check max instances
	if (AssetData.MaxInstances > 0)
	{
		int32 CurrentCount = 0;
		for (AActor* Actor : PlacedActors)
		{
			if (IsValid(Actor) && Actor->GetClass() == AssetData.ActorClass)
			{
				CurrentCount++;
			}
		}
		
		if (CurrentCount >= AssetData.MaxInstances)
		{
			return false;
		}
	}

	// Check minimum distance from other objects
	if (AssetData.MinDistanceFromOthers > 0.0f)
	{
		for (AActor* Actor : PlacedActors)
		{
			if (IsValid(Actor))
			{
				float Distance = FVector::Dist(Actor->GetActorLocation(), Location);
				if (Distance < AssetData.MinDistanceFromOthers)
				{
					return false;
				}
			}
		}
	}

	return true;
}

void UDragDropLevelDesigner::SaveLevelLayout(const FString& LayoutName)
{
	// This would save the current level layout to a file
	UE_LOG(LogTemp, Log, TEXT("Saving level layout: %s"), *LayoutName);
}

bool UDragDropLevelDesigner::LoadLevelLayout(const FString& LayoutName)
{
	// This would load a level layout from a file
	UE_LOG(LogTemp, Log, TEXT("Loading level layout: %s"), *LayoutName);
	return true;
}

void UDragDropLevelDesigner::ClearLevel()
{
	for (AActor* Actor : PlacedActors)
	{
		if (IsValid(Actor))
		{
			Actor->Destroy();
		}
	}
	
	PlacedActors.Empty();
	ClearSelection();
	
	// Reset stats
	DesignStats.TotalActorsPlaced = 0;
	DesignStats.ActorsByCategory.Empty();
	DesignStats.LastModified = FDateTime::Now();
	
	OnLevelLayoutChanged();
}

void UDragDropLevelDesigner::OptimizeLevel()
{
	// This would perform level optimization like merging static meshes, etc.
	UE_LOG(LogTemp, Log, TEXT("Optimizing level..."));
}

FLevelDesignStats UDragDropLevelDesigner::GetDesignStatistics() const
{
	return DesignStats;
}

void UDragDropLevelDesigner::Undo()
{
	if (CanUndo())
	{
		FString LastAction = UndoStack.Last();
		UndoStack.RemoveAt(UndoStack.Num() - 1);
		RedoStack.Add(LastAction);
		
		// Process undo action
		UE_LOG(LogTemp, Log, TEXT("Undo: %s"), *LastAction);
	}
}

void UDragDropLevelDesigner::Redo()
{
	if (CanRedo())
	{
		FString LastAction = RedoStack.Last();
		RedoStack.RemoveAt(RedoStack.Num() - 1);
		UndoStack.Add(LastAction);
		
		// Process redo action
		UE_LOG(LogTemp, Log, TEXT("Redo: %s"), *LastAction);
	}
}

bool UDragDropLevelDesigner::CanUndo() const
{
	return UndoStack.Num() > 0;
}

bool UDragDropLevelDesigner::CanRedo() const
{
	return RedoStack.Num() > 0;
}

void UDragDropLevelDesigner::LoadAssetDatabase()
{
	if (AssetDatabase.IsValid())
	{
		CachedAssetDatabase = AssetDatabase.LoadSynchronous();
	}
}

AActor* UDragDropLevelDesigner::CreatePreviewActor(FName AssetID)
{
	FDragDropAsset AssetData = GetAssetData(AssetID);
	if (AssetData.AssetID == NAME_None || !AssetData.ActorClass)
	{
		return nullptr;
	}

	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AlwaysSpawn;
	
	AActor* Preview = GetWorld()->SpawnActor<AActor>(AssetData.ActorClass, FVector::ZeroVector, FRotator::ZeroRotator, SpawnParams);
	
	if (Preview)
	{
		// Make preview translucent and disable collision
		if (UStaticMeshComponent* MeshComp = Preview->FindComponentByClass<UStaticMeshComponent>())
		{
			// This would set up translucent material for preview
		}
		
		Preview->SetActorEnableCollision(false);
		Preview->SetActorHiddenInGame(false);
	}
	
	return Preview;
}

void UDragDropLevelDesigner::UpdatePreviewActor(const FVector& Location, const FRotator& Rotation)
{
	if (PreviewActor)
	{
		FVector SnappedLocation = Location;
		
		// Apply snapping to preview
		switch (PlacementSettings.SnapMode)
		{
			case ESnapMode::Grid:
				SnappedLocation = SnapLocationToGrid(Location);
				break;
			case ESnapMode::Surface:
				SnappedLocation = SnapLocationToSurface(Location);
				break;
			case ESnapMode::Smart:
				SnappedLocation = SnapLocationToGrid(SnapLocationToSurface(Location));
				break;
		}
		
		PreviewActor->SetActorLocationAndRotation(SnappedLocation, Rotation);
	}
}

void UDragDropLevelDesigner::DestroyPreviewActor()
{
	if (PreviewActor)
	{
		PreviewActor->Destroy();
		PreviewActor = nullptr;
	}
}

bool UDragDropLevelDesigner::ValidatePlacement(FName AssetID, const FVector& Location) const
{
	return CanPlaceAsset(AssetID, Location);
}

void UDragDropLevelDesigner::RecordUndoAction(const FString& Action)
{
	UndoStack.Add(Action);
	
	// Limit undo stack size
	if (UndoStack.Num() > MaxUndoSteps)
	{
		UndoStack.RemoveAt(0);
	}
	
	// Clear redo stack when new action is performed
	RedoStack.Empty();
}

void UDragDropLevelDesigner::UpdateDesignStats()
{
	DesignStats.TotalActorsPlaced = PlacedActors.Num();
	DesignStats.LastModified = FDateTime::Now();
}

void UDragDropLevelDesigner::DrawDebugGrid()
{
	if (!GetWorld())
	{
		return;
	}

	FVector PlayerLocation = FVector::ZeroVector;
	if (APawn* PlayerPawn = GetWorld()->GetFirstPlayerController()->GetPawn())
	{
		PlayerLocation = PlayerPawn->GetActorLocation();
	}

	float GridSize = PlacementSettings.GridSize;
	int32 GridExtent = 20; // Number of grid lines in each direction
	
	FVector GridCenter = FVector(
		FMath::RoundToFloat(PlayerLocation.X / GridSize) * GridSize,
		FMath::RoundToFloat(PlayerLocation.Y / GridSize) * GridSize,
		PlayerLocation.Z
	);

	// Draw grid lines
	for (int32 i = -GridExtent; i <= GridExtent; i++)
	{
		// X lines
		FVector Start = GridCenter + FVector(i * GridSize, -GridExtent * GridSize, 0);
		FVector End = GridCenter + FVector(i * GridSize, GridExtent * GridSize, 0);
		DrawDebugLine(GetWorld(), Start, End, FColor::Gray, false, 0.1f, 0, 1.0f);
		
		// Y lines
		Start = GridCenter + FVector(-GridExtent * GridSize, i * GridSize, 0);
		End = GridCenter + FVector(GridExtent * GridSize, i * GridSize, 0);
		DrawDebugLine(GetWorld(), Start, End, FColor::Gray, false, 0.1f, 0, 1.0f);
	}
}

void UDragDropLevelDesigner::UpdatePreviewActorLocation()
{
	// This would update preview actor location based on mouse cursor
	// Implementation depends on input system
}

void UDragDropLevelDesigner::GroupSelectedActors()
{
	// This would create a group from selected actors
	UE_LOG(LogTemp, Log, TEXT("Grouping %d selected actors"), SelectedActors.Num());
}

void UDragDropLevelDesigner::UngroupSelectedActors()
{
	// This would ungroup selected actors
	UE_LOG(LogTemp, Log, TEXT("Ungrouping selected actors"));
}

void UDragDropLevelDesigner::InvertSelection()
{
	TArray<AActor*> NewSelection;
	
	for (AActor* Actor : PlacedActors)
	{
		if (IsValid(Actor) && !SelectedActors.Contains(Actor))
		{
			NewSelection.Add(Actor);
		}
	}
	
	SelectedActors = NewSelection;
	OnSelectionChanged.Broadcast(SelectedActors, false);
}
