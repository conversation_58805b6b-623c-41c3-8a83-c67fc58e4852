#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../GameplayLoop/GameplayLoopManager.h"
#include "DemoLevelManager.generated.h"

UENUM(BlueprintType)
enum class EDemoSection : uint8
{
	Introduction	UMETA(DisplayName = "Introduction"),
	Tutorial		UMETA(DisplayName = "Tutorial"),
	Exploration		UMETA(DisplayName = "Exploration"),
	FirstCombat		UMETA(DisplayName = "First Combat"),
	InventoryDemo	UMETA(DisplayName = "Inventory Demo"),
	PuzzleSection	UMETA(DisplayName = "Puzzle Section"),
	StealthSection	UMETA(DisplayName = "Stealth Section"),
	BossEncounter	UMETA(DisplayName = "Boss Encounter"),
	Conclusion		UMETA(DisplayName = "Conclusion")
};

USTRUCT(BlueprintType)
struct FDemoSection
{
	GENERATED_BODY()

	FDemoSection()
	{
		SectionID = NAME_None;
		SectionName = FText::GetEmpty();
		DemoSection = EDemoSection::Introduction;
		EstimatedDuration = 60.0f;
		bIsCompleted = false;
		bIsActive = false;
		bIsOptional = false;
		RequiredProgress = 1;
		CurrentProgress = 0;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	FName SectionID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	FText SectionName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	FText SectionDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	EDemoSection DemoSection;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	float EstimatedDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	bool bIsCompleted;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	bool bIsActive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Section")
	bool bIsOptional;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
	int32 RequiredProgress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Progress")
	int32 CurrentProgress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Objectives")
	TArray<FGameplayObjective> SectionObjectives;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Encounters")
	TArray<FGameplayEncounter> SectionEncounters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TArray<FVector> EnemySpawnPoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TArray<FVector> ItemSpawnPoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TArray<FName> RequiredItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> SectionMusic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> CompletionSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	FText InstructionText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSoftObjectPtr<UTexture2D> SectionIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	TArray<FName> PrerequisiteSections;
};

USTRUCT(BlueprintType)
struct FDemoConfiguration
{
	GENERATED_BODY()

	FDemoConfiguration()
	{
		DemoName = FText::GetEmpty();
		TotalEstimatedTime = 600.0f;
		DifficultyLevel = 1;
		bShowTutorialHints = true;
		bAllowSkipping = true;
		bTrackAnalytics = true;
		bAutoSave = true;
		AutoSaveInterval = 60.0f;
		MaxInventorySlots = 12;
		StartingAmmo = 30;
		StartingHealthItems = 2;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Info")
	FText DemoName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Info")
	FText DemoDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Info")
	float TotalEstimatedTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Info")
	int32 DifficultyLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bShowTutorialHints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bAllowSkipping;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bTrackAnalytics;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bAutoSave;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings", meta = (EditCondition = "bAutoSave"))
	float AutoSaveInterval;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Setup")
	int32 MaxInventorySlots;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Setup")
	int32 StartingAmmo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Setup")
	int32 StartingHealthItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Setup")
	TArray<FName> StartingItems;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Player Setup")
	TArray<FName> StartingWeapons;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> IntroMusic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	TSoftObjectPtr<USoundBase> OutroMusic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UTexture2D> DemoLogo;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomSettings;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDemoSectionStarted, EDemoSection, Section, const FDemoSection&, SectionData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDemoSectionCompleted, EDemoSection, Section, float, CompletionTime);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDemoCompleted, float, TotalTime);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDemoProgressUpdated, float, OverallProgress, EDemoSection, CurrentSection);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTutorialHintShown, const FString&, HintText);

UCLASS(BlueprintType, Blueprintable)
class SLT_API ADemoLevelManager : public AActor
{
	GENERATED_BODY()

public:
	ADemoLevelManager();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;

	// Demo configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Configuration")
	FDemoConfiguration DemoConfig;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Configuration")
	TArray<FDemoSection> DemoSections;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Demo Configuration")
	TSoftObjectPtr<UDataTable> DemoSectionsDatabase;

	// Current demo state
	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	EDemoSection CurrentSection = EDemoSection::Introduction;

	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	float DemoStartTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	float CurrentSectionStartTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	float TotalDemoTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	bool bDemoCompleted = false;

	UPROPERTY(BlueprintReadOnly, Category = "Demo State")
	int32 CompletedSectionsCount = 0;

	// Tutorial system
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial")
	bool bShowHints = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial")
	float HintDisplayDuration = 5.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tutorial")
	TMap<EDemoSection, TArray<FString>> SectionHints;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDemoSectionStarted OnDemoSectionStarted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDemoSectionCompleted OnDemoSectionCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDemoCompleted OnDemoCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDemoProgressUpdated OnDemoProgressUpdated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnTutorialHintShown OnTutorialHintShown;

	// Main demo functions
	UFUNCTION(BlueprintCallable, Category = "Demo Management")
	void StartDemo();

	UFUNCTION(BlueprintCallable, Category = "Demo Management")
	void EndDemo();

	UFUNCTION(BlueprintCallable, Category = "Demo Management")
	void RestartDemo();

	UFUNCTION(BlueprintCallable, Category = "Demo Management")
	void PauseDemo();

	UFUNCTION(BlueprintCallable, Category = "Demo Management")
	void ResumeDemo();

	// Section management
	UFUNCTION(BlueprintCallable, Category = "Section Management")
	void StartSection(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Section Management")
	void CompleteSection(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Section Management")
	void SkipSection(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Section Management")
	void UpdateSectionProgress(EDemoSection Section, int32 ProgressAmount);

	UFUNCTION(BlueprintCallable, Category = "Section Management")
	bool IsSectionCompleted(EDemoSection Section) const;

	UFUNCTION(BlueprintCallable, Category = "Section Management")
	FDemoSection GetSectionData(EDemoSection Section) const;

	// Tutorial functions
	UFUNCTION(BlueprintCallable, Category = "Tutorial")
	void ShowTutorialHint(const FString& HintText);

	UFUNCTION(BlueprintCallable, Category = "Tutorial")
	void ShowSectionHints(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Tutorial")
	void HideTutorialHints();

	UFUNCTION(BlueprintCallable, Category = "Tutorial")
	void ToggleHints(bool bEnabled);

	// Player setup functions
	UFUNCTION(BlueprintCallable, Category = "Player Setup")
	void SetupPlayerForDemo();

	UFUNCTION(BlueprintCallable, Category = "Player Setup")
	void GiveStartingItems();

	UFUNCTION(BlueprintCallable, Category = "Player Setup")
	void ConfigurePlayerInventory();

	UFUNCTION(BlueprintCallable, Category = "Player Setup")
	void ResetPlayerState();

	// Spawning functions
	UFUNCTION(BlueprintCallable, Category = "Spawning")
	void SpawnSectionEnemies(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Spawning")
	void SpawnSectionItems(EDemoSection Section);

	UFUNCTION(BlueprintCallable, Category = "Spawning")
	void ClearSectionActors(EDemoSection Section);

	// Progress tracking
	UFUNCTION(BlueprintCallable, Category = "Progress")
	float GetOverallProgress() const;

	UFUNCTION(BlueprintCallable, Category = "Progress")
	float GetSectionProgress(EDemoSection Section) const;

	UFUNCTION(BlueprintCallable, Category = "Progress")
	float GetEstimatedTimeRemaining() const;

	UFUNCTION(BlueprintCallable, Category = "Progress")
	TArray<EDemoSection> GetCompletedSections() const;

	// Save/Load functions
	UFUNCTION(BlueprintCallable, Category = "Save System")
	void SaveDemoProgress();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void LoadDemoProgress();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void CreateCheckpoint();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void LoadLastCheckpoint();

	// Analytics functions
	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void TrackSectionCompletion(EDemoSection Section, float CompletionTime);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void TrackPlayerAction(const FString& ActionName, const FString& ActionData);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void GenerateAnalyticsReport();

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void LoadDemoConfiguration();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool CanSkipSection(EDemoSection Section) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	EDemoSection GetNextSection(EDemoSection CurrentSection) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool ArePrerequisitesMet(EDemoSection Section) const;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnDemoStarted();

	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnDemoEnded();

	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnSectionStartedBP(EDemoSection Section);

	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnSectionCompletedBP(EDemoSection Section, float CompletionTime);

	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnProgressUpdatedBP(float OverallProgress, EDemoSection CurrentSection);

	UFUNCTION(BlueprintImplementableEvent, Category = "Demo Events")
	void OnHintShownBP(const FString& HintText);

protected:
	// Internal state
	TMap<EDemoSection, FDemoSection> SectionMap;
	TArray<FString> AnalyticsData;
	bool bDemoPaused = false;
	float PauseStartTime = 0.0f;
	float TotalPauseTime = 0.0f;

	// Timer handles
	FTimerHandle AutoSaveTimerHandle;
	FTimerHandle ProgressUpdateTimerHandle;
	FTimerHandle HintTimerHandle;

	// Internal functions
	void InitializeDemoSections();
	void ProcessSectionTransition(EDemoSection NewSection);
	void UpdateDemoProgress();
	void HandleAutoSave();
	void SetupSectionObjectives(const FDemoSection& Section);
	void SetupSectionEncounters(const FDemoSection& Section);
	bool ValidateSectionData(const FDemoSection& Section) const;
	void PlaySectionAudio(const FDemoSection& Section);
	void UpdateUI();
};
