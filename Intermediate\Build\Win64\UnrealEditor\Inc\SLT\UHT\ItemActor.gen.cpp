// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/InventorySystem/Actors/ItemActor.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeItemActor() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_AActor();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USphereComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_AItemActor();
SLT_API UClass* Z_Construct_UClass_AItemActor_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInteractable_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnItemPickedUp
struct Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics
{
	struct ItemActor_eventOnItemPickedUp_Parms
	{
		AItemActor* ItemActor;
		APawn* PickupPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ItemActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PickupPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::NewProp_ItemActor = { "ItemActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventOnItemPickedUp_Parms, ItemActor), Z_Construct_UClass_AItemActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::NewProp_PickupPawn = { "PickupPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventOnItemPickedUp_Parms, PickupPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::NewProp_ItemActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::NewProp_PickupPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "OnItemPickedUp__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::ItemActor_eventOnItemPickedUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::ItemActor_eventOnItemPickedUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void AItemActor::FOnItemPickedUp_DelegateWrapper(const FMulticastScriptDelegate& OnItemPickedUp, AItemActor* ItemActor, APawn* PickupPawn)
{
	struct ItemActor_eventOnItemPickedUp_Parms
	{
		AItemActor* ItemActor;
		APawn* PickupPawn;
	};
	ItemActor_eventOnItemPickedUp_Parms Parms;
	Parms.ItemActor=ItemActor;
	Parms.PickupPawn=PickupPawn;
	OnItemPickedUp.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnItemPickedUp

// Begin Class AItemActor Function GetItemData
struct Z_Construct_UFunction_AItemActor_GetItemData_Statics
{
	struct ItemActor_eventGetItemData_Parms
	{
		FInventoryItemData OutItemData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Item" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutItemData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_OutItemData = { "OutItemData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventGetItemData_Parms, OutItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(0, nullptr) }; // 2976144554
void Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ItemActor_eventGetItemData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ItemActor_eventGetItemData_Parms), &Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_GetItemData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_OutItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_GetItemData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_GetItemData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "GetItemData", nullptr, nullptr, Z_Construct_UFunction_AItemActor_GetItemData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AItemActor_GetItemData_Statics::ItemActor_eventGetItemData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_GetItemData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AItemActor_GetItemData_Statics::ItemActor_eventGetItemData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_GetItemData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_GetItemData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AItemActor::execGetItemData)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_OutItemData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetItemData(Z_Param_Out_OutItemData);
	P_NATIVE_END;
}
// End Class AItemActor Function GetItemData

// Begin Class AItemActor Function GetItemID
struct Z_Construct_UFunction_AItemActor_GetItemID_Statics
{
	struct ItemActor_eventGetItemID_Parms
	{
		FName ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Item" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AItemActor_GetItemID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventGetItemID_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_GetItemID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_GetItemID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_GetItemID_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "GetItemID", nullptr, nullptr, Z_Construct_UFunction_AItemActor_GetItemID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemID_Statics::PropPointers), sizeof(Z_Construct_UFunction_AItemActor_GetItemID_Statics::ItemActor_eventGetItemID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetItemID_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_GetItemID_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AItemActor_GetItemID_Statics::ItemActor_eventGetItemID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_GetItemID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_GetItemID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AItemActor::execGetItemID)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FName*)Z_Param__Result=P_THIS->GetItemID();
	P_NATIVE_END;
}
// End Class AItemActor Function GetItemID

// Begin Class AItemActor Function GetQuantity
struct Z_Construct_UFunction_AItemActor_GetQuantity_Statics
{
	struct ItemActor_eventGetQuantity_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Item" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AItemActor_GetQuantity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventGetQuantity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_GetQuantity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_GetQuantity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetQuantity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_GetQuantity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "GetQuantity", nullptr, nullptr, Z_Construct_UFunction_AItemActor_GetQuantity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetQuantity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AItemActor_GetQuantity_Statics::ItemActor_eventGetQuantity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_GetQuantity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_GetQuantity_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AItemActor_GetQuantity_Statics::ItemActor_eventGetQuantity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_GetQuantity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_GetQuantity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AItemActor::execGetQuantity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetQuantity();
	P_NATIVE_END;
}
// End Class AItemActor Function GetQuantity

// Begin Class AItemActor Function InitializeFromItemData
struct Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics
{
	struct ItemActor_eventInitializeFromItemData_Parms
	{
		FName InItemID;
		int32 InQuantity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Item" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Public functions\n" },
#endif
		{ "CPP_Default_InQuantity", "1" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Public functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_InItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InQuantity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_InItemID = { "InItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventInitializeFromItemData_Parms, InItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_InQuantity = { "InQuantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventInitializeFromItemData_Parms, InQuantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ItemActor_eventInitializeFromItemData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ItemActor_eventInitializeFromItemData_Parms), &Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_InItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_InQuantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "InitializeFromItemData", nullptr, nullptr, Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::PropPointers), sizeof(Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::ItemActor_eventInitializeFromItemData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::ItemActor_eventInitializeFromItemData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_InitializeFromItemData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_InitializeFromItemData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AItemActor::execInitializeFromItemData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_InItemID);
	P_GET_PROPERTY(FIntProperty,Z_Param_InQuantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->InitializeFromItemData(Z_Param_InItemID,Z_Param_InQuantity);
	P_NATIVE_END;
}
// End Class AItemActor Function InitializeFromItemData

// Begin Class AItemActor Function OnItemInitialized
static const FName NAME_AItemActor_OnItemInitialized = FName(TEXT("OnItemInitialized"));
void AItemActor::OnItemInitialized()
{
	UFunction* Func = FindFunctionChecked(NAME_AItemActor_OnItemInitialized);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_AItemActor_OnItemInitialized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_OnItemInitialized_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "OnItemInitialized", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_OnItemInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_OnItemInitialized_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AItemActor_OnItemInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_OnItemInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AItemActor Function OnItemInitialized

// Begin Class AItemActor Function OnPickupAttempt
struct ItemActor_eventOnPickupAttempt_Parms
{
	APawn* InteractingPawn;
	bool bSuccess;
};
static const FName NAME_AItemActor_OnPickupAttempt = FName(TEXT("OnPickupAttempt"));
void AItemActor::OnPickupAttempt(APawn* InteractingPawn, bool bSuccess)
{
	ItemActor_eventOnPickupAttempt_Parms Parms;
	Parms.InteractingPawn=InteractingPawn;
	Parms.bSuccess=bSuccess ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_AItemActor_OnPickupAttempt);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventOnPickupAttempt_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((ItemActor_eventOnPickupAttempt_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ItemActor_eventOnPickupAttempt_Parms), &Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_InteractingPawn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "OnPickupAttempt", nullptr, nullptr, Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::PropPointers), sizeof(ItemActor_eventOnPickupAttempt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::Function_MetaDataParams) };
static_assert(sizeof(ItemActor_eventOnPickupAttempt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_OnPickupAttempt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_OnPickupAttempt_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AItemActor Function OnPickupAttempt

// Begin Class AItemActor Function SetQuantity
struct Z_Construct_UFunction_AItemActor_SetQuantity_Statics
{
	struct ItemActor_eventSetQuantity_Parms
	{
		int32 NewQuantity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Item" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewQuantity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_AItemActor_SetQuantity_Statics::NewProp_NewQuantity = { "NewQuantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ItemActor_eventSetQuantity_Parms, NewQuantity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AItemActor_SetQuantity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AItemActor_SetQuantity_Statics::NewProp_NewQuantity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_SetQuantity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AItemActor_SetQuantity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AItemActor, nullptr, "SetQuantity", nullptr, nullptr, Z_Construct_UFunction_AItemActor_SetQuantity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_SetQuantity_Statics::PropPointers), sizeof(Z_Construct_UFunction_AItemActor_SetQuantity_Statics::ItemActor_eventSetQuantity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AItemActor_SetQuantity_Statics::Function_MetaDataParams), Z_Construct_UFunction_AItemActor_SetQuantity_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AItemActor_SetQuantity_Statics::ItemActor_eventSetQuantity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AItemActor_SetQuantity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AItemActor_SetQuantity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AItemActor::execSetQuantity)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewQuantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetQuantity(Z_Param_NewQuantity);
	P_NATIVE_END;
}
// End Class AItemActor Function SetQuantity

// Begin Class AItemActor
void AItemActor::StaticRegisterNativesAItemActor()
{
	UClass* Class = AItemActor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "GetItemData", &AItemActor::execGetItemData },
		{ "GetItemID", &AItemActor::execGetItemID },
		{ "GetQuantity", &AItemActor::execGetQuantity },
		{ "InitializeFromItemData", &AItemActor::execInitializeFromItemData },
		{ "SetQuantity", &AItemActor::execSetQuantity },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AItemActor);
UClass* Z_Construct_UClass_AItemActor_NoRegister()
{
	return AItemActor::StaticClass();
}
struct Z_Construct_UClass_AItemActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "InventorySystem/Actors/ItemActor.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MeshComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionSphere_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemID_MetaData[] = {
		{ "Category", "Item" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Item properties\n" },
#endif
		{ "ExposeOnSpawn", "true" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Item properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Item" },
		{ "ClampMin", "1" },
		{ "ExposeOnSpawn", "true" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemDatabase_MetaData[] = {
		{ "Category", "Item" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomInteractionText_MetaData[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interaction settings\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomInteractionText_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoPickup_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDestroyOnPickup_MetaData[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableFloating_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual settings\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatAmplitude_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bEnableFloating" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FloatSpeed_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bEnableFloating" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableRotation_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationSpeed_MetaData[] = {
		{ "Category", "Visual" },
		{ "EditCondition", "bEnableRotation" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnItemPickedUp_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedItemData_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal data\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bItemDataCached_MetaData[] = {
		{ "ModuleRelativePath", "InventorySystem/Actors/ItemActor.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MeshComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionSphere;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ItemDatabase;
	static const UECodeGen_Private::FTextPropertyParams NewProp_CustomInteractionText;
	static void NewProp_bUseCustomInteractionText_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomInteractionText;
	static void NewProp_bAutoPickup_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoPickup;
	static void NewProp_bDestroyOnPickup_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDestroyOnPickup;
	static void NewProp_bEnableFloating_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableFloating;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatAmplitude;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FloatSpeed;
	static void NewProp_bEnableRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationSpeed;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnItemPickedUp;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CachedItemData;
	static void NewProp_bItemDataCached_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bItemDataCached;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AItemActor_GetItemData, "GetItemData" }, // 162718260
		{ &Z_Construct_UFunction_AItemActor_GetItemID, "GetItemID" }, // **********
		{ &Z_Construct_UFunction_AItemActor_GetQuantity, "GetQuantity" }, // **********
		{ &Z_Construct_UFunction_AItemActor_InitializeFromItemData, "InitializeFromItemData" }, // 524607304
		{ &Z_Construct_UFunction_AItemActor_OnItemInitialized, "OnItemInitialized" }, // **********
		{ &Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature, "OnItemPickedUp__DelegateSignature" }, // **********
		{ &Z_Construct_UFunction_AItemActor_OnPickupAttempt, "OnPickupAttempt" }, // **********
		{ &Z_Construct_UFunction_AItemActor_SetQuantity, "SetQuantity" }, // **********
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AItemActor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_MeshComponent = { "MeshComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, MeshComponent), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MeshComponent_MetaData), NewProp_MeshComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_InteractionSphere = { "InteractionSphere", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, InteractionSphere), Z_Construct_UClass_USphereComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionSphere_MetaData), NewProp_InteractionSphere_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0011000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, ItemID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemID_MetaData), NewProp_ItemID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0011000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_ItemDatabase = { "ItemDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, ItemDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemDatabase_MetaData), NewProp_ItemDatabase_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_CustomInteractionText = { "CustomInteractionText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, CustomInteractionText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomInteractionText_MetaData), NewProp_CustomInteractionText_MetaData) };
void Z_Construct_UClass_AItemActor_Statics::NewProp_bUseCustomInteractionText_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bUseCustomInteractionText = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bUseCustomInteractionText = { "bUseCustomInteractionText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bUseCustomInteractionText_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomInteractionText_MetaData), NewProp_bUseCustomInteractionText_MetaData) };
void Z_Construct_UClass_AItemActor_Statics::NewProp_bAutoPickup_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bAutoPickup = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bAutoPickup = { "bAutoPickup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bAutoPickup_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoPickup_MetaData), NewProp_bAutoPickup_MetaData) };
void Z_Construct_UClass_AItemActor_Statics::NewProp_bDestroyOnPickup_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bDestroyOnPickup = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bDestroyOnPickup = { "bDestroyOnPickup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bDestroyOnPickup_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDestroyOnPickup_MetaData), NewProp_bDestroyOnPickup_MetaData) };
void Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableFloating_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bEnableFloating = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableFloating = { "bEnableFloating", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableFloating_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableFloating_MetaData), NewProp_bEnableFloating_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_FloatAmplitude = { "FloatAmplitude", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, FloatAmplitude), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatAmplitude_MetaData), NewProp_FloatAmplitude_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_FloatSpeed = { "FloatSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, FloatSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FloatSpeed_MetaData), NewProp_FloatSpeed_MetaData) };
void Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableRotation_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bEnableRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableRotation = { "bEnableRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableRotation_MetaData), NewProp_bEnableRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_RotationSpeed = { "RotationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, RotationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationSpeed_MetaData), NewProp_RotationSpeed_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_OnItemPickedUp = { "OnItemPickedUp", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, OnItemPickedUp), Z_Construct_UDelegateFunction_AItemActor_OnItemPickedUp__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnItemPickedUp_MetaData), NewProp_OnItemPickedUp_MetaData) }; // **********
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_CachedItemData = { "CachedItemData", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AItemActor, CachedItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedItemData_MetaData), NewProp_CachedItemData_MetaData) }; // 2976144554
void Z_Construct_UClass_AItemActor_Statics::NewProp_bItemDataCached_SetBit(void* Obj)
{
	((AItemActor*)Obj)->bItemDataCached = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AItemActor_Statics::NewProp_bItemDataCached = { "bItemDataCached", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AItemActor), &Z_Construct_UClass_AItemActor_Statics::NewProp_bItemDataCached_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bItemDataCached_MetaData), NewProp_bItemDataCached_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AItemActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_MeshComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_InteractionSphere,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_ItemDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_CustomInteractionText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bUseCustomInteractionText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bAutoPickup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bDestroyOnPickup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableFloating,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_FloatAmplitude,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_FloatSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bEnableRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_RotationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_OnItemPickedUp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_CachedItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AItemActor_Statics::NewProp_bItemDataCached,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AItemActor_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AItemActor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_AActor,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AItemActor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_AItemActor_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UInteractable_NoRegister, (int32)VTABLE_OFFSET(AItemActor, IInteractable), false },  // 206469426
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_AItemActor_Statics::ClassParams = {
	&AItemActor::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AItemActor_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AItemActor_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AItemActor_Statics::Class_MetaDataParams), Z_Construct_UClass_AItemActor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AItemActor()
{
	if (!Z_Registration_Info_UClass_AItemActor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AItemActor.OuterSingleton, Z_Construct_UClass_AItemActor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AItemActor.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<AItemActor>()
{
	return AItemActor::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AItemActor);
AItemActor::~AItemActor() {}
// End Class AItemActor

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AItemActor, AItemActor::StaticClass, TEXT("AItemActor"), &Z_Registration_Info_UClass_AItemActor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AItemActor), 1707310144U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_2506281239(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
