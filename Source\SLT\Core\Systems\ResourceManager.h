#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "InventorySystem/Data/InventoryItemData.h"
#include "ResourceManager.generated.h"

UENUM(BlueprintType)
enum class EResourceType : uint8
{
	Health			UMETA(DisplayName = "Health"),
	Ammo			UMETA(DisplayName = "Ammo"),
	Currency		UMETA(DisplayName = "Currency"),
	Energy			UMETA(DisplayName = "Energy"),
	Experience		UMETA(DisplayName = "Experience"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EResourceScarcityLevel : uint8
{
	Abundant		UMETA(DisplayName = "Abundant"),
	Normal			UMETA(DisplayName = "Normal"),
	Scarce			UMETA(DisplayName = "Scarce"),
	Critical		UMETA(DisplayName = "Critical"),
	Empty			UMETA(DisplayName = "Empty")
};

USTRUCT(BlueprintType)
struct FResourceData
{
	GENERATED_BODY()

	FResourceData()
	{
		ResourceType = EResourceType::Health;
		ResourceID = NAME_None;
		CurrentAmount = 0.0f;
		MaxAmount = 100.0f;
		MinAmount = 0.0f;
		RegenerationRate = 0.0f;
		DecayRate = 0.0f;
		bCanRegenerate = false;
		bCanDecay = false;
		bHasMaxLimit = true;
		ScarcityLevel = EResourceScarcityLevel::Normal;
		LastUpdateTime = 0.0f;
	}

	// Resource identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	EResourceType ResourceType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	FName ResourceID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resource")
	FText ResourceName;

	// Current values
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float CurrentAmount;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float MaxAmount;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float MinAmount;

	// Regeneration/Decay
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
	float RegenerationRate; // Per second

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
	float DecayRate; // Per second

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
	bool bCanRegenerate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
	bool bCanDecay;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Regeneration")
	bool bHasMaxLimit;

	// Scarcity management
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity")
	EResourceScarcityLevel ScarcityLevel;

	// Internal tracking
	UPROPERTY()
	float LastUpdateTime;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;
};

USTRUCT(BlueprintType)
struct FResourceConsumptionRule
{
	GENERATED_BODY()

	FResourceConsumptionRule()
	{
		ActionTag = FGameplayTag();
		ResourceID = NAME_None;
		ConsumptionAmount = 1.0f;
		bIsPercentage = false;
		bRequireFullAmount = true;
		Priority = 0;
	}

	// What action triggers this consumption
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FGameplayTag ActionTag;

	// Which resource to consume
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	FName ResourceID;

	// How much to consume
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	float ConsumptionAmount;

	// Is the amount a percentage of max?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	bool bIsPercentage;

	// Must have full amount to perform action?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	bool bRequireFullAmount;

	// Priority for multiple rules
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rule")
	int32 Priority;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnResourceChanged, FName, ResourceID, float, NewAmount, float, MaxAmount);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnResourceDepleted, FName, ResourceID, EResourceType, ResourceType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnResourceScarcityChanged, FName, ResourceID, EResourceScarcityLevel, NewLevel);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnResourceConsumed, FName, ResourceID, float, Amount, FGameplayTag, ActionTag);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UResourceManager : public UActorComponent
{
	GENERATED_BODY()

public:
	UResourceManager();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Resource storage
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Resources")
	TMap<FName, FResourceData> Resources;

	// Consumption rules
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Consumption")
	TArray<FResourceConsumptionRule> ConsumptionRules;

	// Scarcity settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity")
	float AbundantThreshold = 0.8f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity")
	float NormalThreshold = 0.5f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity")
	float ScarceThreshold = 0.2f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Scarcity")
	float CriticalThreshold = 0.05f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnResourceChanged OnResourceChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnResourceDepleted OnResourceDepleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnResourceScarcityChanged OnResourceScarcityChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnResourceConsumed OnResourceConsumed;

	// Resource management functions
	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool AddResource(FName ResourceID, const FResourceData& ResourceData);

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool RemoveResource(FName ResourceID);

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool HasResource(FName ResourceID) const;

	UFUNCTION(BlueprintCallable, Category = "Resources")
	FResourceData GetResourceData(FName ResourceID) const;

	// Resource value functions
	UFUNCTION(BlueprintCallable, Category = "Resources")
	float GetResourceAmount(FName ResourceID) const;

	UFUNCTION(BlueprintCallable, Category = "Resources")
	float GetResourcePercentage(FName ResourceID) const;

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool SetResourceAmount(FName ResourceID, float NewAmount);

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool ModifyResource(FName ResourceID, float Amount);

	UFUNCTION(BlueprintCallable, Category = "Resources")
	bool SetResourceMax(FName ResourceID, float NewMax);

	// Consumption functions
	UFUNCTION(BlueprintCallable, Category = "Consumption")
	bool CanPerformAction(const FGameplayTag& ActionTag) const;

	UFUNCTION(BlueprintCallable, Category = "Consumption")
	bool ConsumeForAction(const FGameplayTag& ActionTag);

	UFUNCTION(BlueprintCallable, Category = "Consumption")
	float GetActionCost(const FGameplayTag& ActionTag, FName ResourceID) const;

	// Scarcity functions
	UFUNCTION(BlueprintCallable, Category = "Scarcity")
	EResourceScarcityLevel GetResourceScarcity(FName ResourceID) const;

	UFUNCTION(BlueprintCallable, Category = "Scarcity")
	TArray<FName> GetResourcesByScarcity(EResourceScarcityLevel ScarcityLevel) const;

	UFUNCTION(BlueprintCallable, Category = "Scarcity")
	bool IsResourceCritical(FName ResourceID) const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<FName> GetAllResourceIDs() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<FName> GetResourcesByType(EResourceType ResourceType) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void RegenerateAllResources(float DeltaTime);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ResetAllResources();

	// Item integration
	UFUNCTION(BlueprintCallable, Category = "Items")
	bool ConsumeItem(const FInventoryItemData& ItemData, int32 Quantity = 1);

	UFUNCTION(BlueprintCallable, Category = "Items")
	float GetItemResourceValue(const FInventoryItemData& ItemData, FName ResourceID) const;

protected:
	// Internal functions
	void UpdateResourceScarcity(FName ResourceID);
	void ProcessResourceRegeneration(FName ResourceID, float DeltaTime);
	void ProcessResourceDecay(FName ResourceID, float DeltaTime);
	EResourceScarcityLevel CalculateScarcityLevel(float Percentage) const;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnResourceUpdated(FName ResourceID, const FResourceData& ResourceData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnActionBlocked(const FGameplayTag& ActionTag, FName InsufficientResource);
};
