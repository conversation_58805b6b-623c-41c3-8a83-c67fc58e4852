#include "Interactable.h"
#include "GameFramework/Pawn.h"
// #include "InventorySystem/Components/InventoryGridComponent.h"

bool IInteractable::CheckInteractionRequirements_Implementation(APawn* InteractingPawn) const
{
	if (!InteractingPawn)
	{
		return false;
	}

	FInteractionData InteractionData = Execute_GetInteractionData(Cast<UObject>(this));
	
	// Check if we need a specific item
	if (InteractionData.bRequiresItem && InteractionData.RequiredItemID != NAME_None)
	{
		// TODO: Implement inventory checking when inventory system is available
		// UInventoryGridComponent* InventoryComponent = InteractingPawn->FindComponentByClass<UInventoryGridComponent>();
		// if (!InventoryComponent || !InventoryComponent->HasItem(InteractionData.RequiredItemID))
		// {
		//     return false;
		// }
	}

	// Check required gameplay tags
	if (InteractionData.RequiredTags.Num() > 0)
	{
		// For now, we'll assume the pawn has the required tags
		// This can be extended with a gameplay tag component
		// TODO: Implement gameplay tag checking
	}

	return true;
}
