// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/LevelProgressionManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class ELevelState : uint8;
struct FCheckpointData;
struct FGameplayTag;
struct FLevelData;
#ifdef SLT_LevelProgressionManager_generated_h
#error "LevelProgressionManager.generated.h already included, missing '#pragma once' in LevelProgressionManager.h"
#endif
#define SLT_LevelProgressionManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_37_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FCheckpointData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FCheckpointData>();

#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_112_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLevelData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FLevelData>();

#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_182_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FProgressionSaveData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FProgressionSaveData>();

#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_225_DELEGATE \
SLT_API void FOnCheckpointReached_DelegateWrapper(const FMulticastScriptDelegate& OnCheckpointReached, FName CheckpointID, FCheckpointData const& CheckpointData);


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_226_DELEGATE \
SLT_API void FOnLevelStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLevelStateChanged, FName LevelID, ELevelState NewState);


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_227_DELEGATE \
SLT_API void FOnLevelTransition_DelegateWrapper(const FMulticastScriptDelegate& OnLevelTransition, FName FromLevel, FName ToLevel, bool bIsLoading);


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_228_DELEGATE \
SLT_API void FOnObjectiveCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCompleted, int32 ObjectiveIndex, int32 TotalCompleted);


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execResetProgression); \
	DECLARE_FUNCTION(execLoadProgression); \
	DECLARE_FUNCTION(execSaveProgression); \
	DECLARE_FUNCTION(execGetPlayerLevel); \
	DECLARE_FUNCTION(execSetPlayerLevel); \
	DECLARE_FUNCTION(execHasUnlockedTag); \
	DECLARE_FUNCTION(execUnlockTag); \
	DECLARE_FUNCTION(execGetObjectiveProgress); \
	DECLARE_FUNCTION(execSetTotalObjectives); \
	DECLARE_FUNCTION(execCompleteObjective); \
	DECLARE_FUNCTION(execGetUnlockedLevels); \
	DECLARE_FUNCTION(execGetLevelData); \
	DECLARE_FUNCTION(execGetLevelState); \
	DECLARE_FUNCTION(execSetLevelState); \
	DECLARE_FUNCTION(execLoadLevel); \
	DECLARE_FUNCTION(execGetAvailableCheckpoints); \
	DECLARE_FUNCTION(execGetCheckpointData); \
	DECLARE_FUNCTION(execLoadCheckpoint); \
	DECLARE_FUNCTION(execActivateCheckpoint);


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesULevelProgressionManager(); \
	friend struct Z_Construct_UClass_ULevelProgressionManager_Statics; \
public: \
	DECLARE_CLASS(ULevelProgressionManager, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(ULevelProgressionManager)


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ULevelProgressionManager(ULevelProgressionManager&&); \
	ULevelProgressionManager(const ULevelProgressionManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ULevelProgressionManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULevelProgressionManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ULevelProgressionManager) \
	NO_API virtual ~ULevelProgressionManager();


#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_230_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_233_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class ULevelProgressionManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h


#define FOREACH_ENUM_ECHECKPOINTTYPE(op) \
	op(ECheckpointType::Manual) \
	op(ECheckpointType::Automatic) \
	op(ECheckpointType::LevelTransition) \
	op(ECheckpointType::BossDefeated) \
	op(ECheckpointType::PuzzleSolved) \
	op(ECheckpointType::AreaCleared) 

enum class ECheckpointType : uint8;
template<> struct TIsUEnumClass<ECheckpointType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ECheckpointType>();

#define FOREACH_ENUM_ELEVELSTATE(op) \
	op(ELevelState::Locked) \
	op(ELevelState::Available) \
	op(ELevelState::InProgress) \
	op(ELevelState::Completed) \
	op(ELevelState::Mastered) 

enum class ELevelState : uint8;
template<> struct TIsUEnumClass<ELevelState> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ELevelState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
