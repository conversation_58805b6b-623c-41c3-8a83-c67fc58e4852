// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/InventorySaveGame.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class ASLTPlayerCharacter;
class UInventoryGridComponent;
class UPuzzleComponent;
#ifdef SLT_InventorySaveGame_generated_h
#error "InventorySaveGame.generated.h already included, missing '#pragma once' in InventorySaveGame.h"
#endif
#define SLT_InventorySaveGame_generated_h

#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_12_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPuzzleSaveData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPuzzleSaveData>();

#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_55_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerSaveData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPlayerSaveData>();

#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_99_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWorldSaveData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FWorldSaveData>();

#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execClearAllData); \
	DECLARE_FUNCTION(execIsValidSaveData); \
	DECLARE_FUNCTION(execUpdateMetadata); \
	DECLARE_FUNCTION(execRemoveCustomData); \
	DECLARE_FUNCTION(execHasCustomData); \
	DECLARE_FUNCTION(execGetCustomData); \
	DECLARE_FUNCTION(execSetCustomData); \
	DECLARE_FUNCTION(execLoadPlayerData); \
	DECLARE_FUNCTION(execSavePlayerData); \
	DECLARE_FUNCTION(execLoadPuzzleData); \
	DECLARE_FUNCTION(execSavePuzzleData); \
	DECLARE_FUNCTION(execLoadInventoryData); \
	DECLARE_FUNCTION(execSaveInventoryData);


#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUInventorySaveGame(); \
	friend struct Z_Construct_UClass_UInventorySaveGame_Statics; \
public: \
	DECLARE_CLASS(UInventorySaveGame, USaveGame, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UInventorySaveGame)


#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UInventorySaveGame(UInventorySaveGame&&); \
	UInventorySaveGame(const UInventorySaveGame&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UInventorySaveGame); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UInventorySaveGame); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UInventorySaveGame) \
	NO_API virtual ~UInventorySaveGame();


#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_135_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_138_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UInventorySaveGame>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
