// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Interfaces/InventoryInterface.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInventoryInterface() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
SLT_API UClass* Z_Construct_UClass_UInventoryInterface();
SLT_API UClass* Z_Construct_UClass_UInventoryInterface_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlot();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnInventoryChanged
struct Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryChanged_Parms
	{
		FInventorySlot ChangedSlot;
		bool bWasAdded;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ChangedSlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ChangedSlot;
	static void NewProp_bWasAdded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasAdded;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_ChangedSlot = { "ChangedSlot", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryChanged_Parms, ChangedSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ChangedSlot_MetaData), NewProp_ChangedSlot_MetaData) }; // 2390674128
void Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_bWasAdded_SetBit(void* Obj)
{
	((_Script_SLT_eventOnInventoryChanged_Parms*)Obj)->bWasAdded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_bWasAdded = { "bWasAdded", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnInventoryChanged_Parms), &Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_bWasAdded_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_ChangedSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::NewProp_bWasAdded,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::_Script_SLT_eventOnInventoryChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::_Script_SLT_eventOnInventoryChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryChanged, FInventorySlot const& ChangedSlot, bool bWasAdded)
{
	struct _Script_SLT_eventOnInventoryChanged_Parms
	{
		FInventorySlot ChangedSlot;
		bool bWasAdded;
	};
	_Script_SLT_eventOnInventoryChanged_Parms Parms;
	Parms.ChangedSlot=ChangedSlot;
	Parms.bWasAdded=bWasAdded ? true : false;
	OnInventoryChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryChanged

// Begin Delegate FOnInventoryOpened
struct Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryOpened_Parms
	{
		bool bIsOpen;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOpen;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::NewProp_bIsOpen_SetBit(void* Obj)
{
	((_Script_SLT_eventOnInventoryOpened_Parms*)Obj)->bIsOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::NewProp_bIsOpen = { "bIsOpen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnInventoryOpened_Parms), &Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::NewProp_bIsOpen_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::NewProp_bIsOpen,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryOpened__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::_Script_SLT_eventOnInventoryOpened_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::_Script_SLT_eventOnInventoryOpened_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryOpened__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryOpened_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryOpened, bool bIsOpen)
{
	struct _Script_SLT_eventOnInventoryOpened_Parms
	{
		bool bIsOpen;
	};
	_Script_SLT_eventOnInventoryOpened_Parms Parms;
	Parms.bIsOpen=bIsOpen ? true : false;
	OnInventoryOpened.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryOpened

// Begin Interface UInventoryInterface Function AddItem
struct InventoryInterface_eventAddItem_Parms
{
	FInventoryItemData ItemData;
	int32 Quantity;
	FInventorySlot OutSlot;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventAddItem_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::AddItem(FInventoryItemData const& ItemData, int32 Quantity, FInventorySlot& OutSlot)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_AddItem instead.");
	InventoryInterface_eventAddItem_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_AddItem = FName(TEXT("AddItem"));
bool IInventoryInterface::Execute_AddItem(UObject* O, FInventoryItemData const& ItemData, int32 Quantity, FInventorySlot& OutSlot)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventAddItem_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_AddItem);
	if (Func)
	{
		Parms.ItemData=ItemData;
		Parms.Quantity=Quantity;
		Parms.OutSlot=OutSlot;
		O->ProcessEvent(Func, &Parms);
		OutSlot=Parms.OutSlot;
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->AddItem_Implementation(ItemData,Quantity,OutSlot);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_AddItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Add an item to the inventory\n" },
#endif
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Add an item to the inventory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutSlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventAddItem_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventAddItem_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_OutSlot = { "OutSlot", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventAddItem_Parms, OutSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(0, nullptr) }; // 2390674128
void Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventAddItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventAddItem_Parms), &Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_OutSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "AddItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::PropPointers), sizeof(InventoryInterface_eventAddItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C420C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventAddItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_AddItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_AddItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execAddItem)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_GET_STRUCT_REF(FInventorySlot,Z_Param_Out_OutSlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddItem_Implementation(Z_Param_Out_ItemData,Z_Param_Quantity,Z_Param_Out_OutSlot);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function AddItem

// Begin Interface UInventoryInterface Function ClearInventory
void IInventoryInterface::ClearInventory()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ClearInventory instead.");
}
static FName NAME_UInventoryInterface_ClearInventory = FName(TEXT("ClearInventory"));
void IInventoryInterface::Execute_ClearInventory(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_ClearInventory);
	if (Func)
	{
		O->ProcessEvent(Func, NULL);
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		I->ClearInventory_Implementation();
	}
}
struct Z_Construct_UFunction_UInventoryInterface_ClearInventory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Clear all items from inventory\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Clear all items from inventory" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_ClearInventory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "ClearInventory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_ClearInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_ClearInventory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UInventoryInterface_ClearInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_ClearInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execClearInventory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearInventory_Implementation();
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function ClearInventory

// Begin Interface UInventoryInterface Function DropItem
struct InventoryInterface_eventDropItem_Parms
{
	FName ItemID;
	int32 Quantity;
	FVector DropLocation;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventDropItem_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::DropItem(FName ItemID, int32 Quantity, FVector const& DropLocation)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_DropItem instead.");
	InventoryInterface_eventDropItem_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_DropItem = FName(TEXT("DropItem"));
bool IInventoryInterface::Execute_DropItem(UObject* O, FName ItemID, int32 Quantity, FVector const& DropLocation)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventDropItem_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_DropItem);
	if (Func)
	{
		Parms.ItemID=ItemID;
		Parms.Quantity=Quantity;
		Parms.DropLocation=DropLocation;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->DropItem_Implementation(ItemID,Quantity,DropLocation);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_DropItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Drop an item from inventory\n" },
#endif
		{ "CPP_Default_DropLocation", "" },
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Drop an item from inventory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DropLocation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DropLocation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventDropItem_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventDropItem_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_DropLocation = { "DropLocation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventDropItem_Parms, DropLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DropLocation_MetaData), NewProp_DropLocation_MetaData) };
void Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventDropItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventDropItem_Parms), &Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_DropLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "DropItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::PropPointers), sizeof(InventoryInterface_eventDropItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0CC20C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventDropItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_DropItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_DropItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execDropItem)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_DropLocation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DropItem_Implementation(Z_Param_ItemID,Z_Param_Quantity,Z_Param_Out_DropLocation);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function DropItem

// Begin Interface UInventoryInterface Function GetCurrentWeight
struct InventoryInterface_eventGetCurrentWeight_Parms
{
	float ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventGetCurrentWeight_Parms()
		: ReturnValue(0)
	{
	}
};
float IInventoryInterface::GetCurrentWeight() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetCurrentWeight instead.");
	InventoryInterface_eventGetCurrentWeight_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_GetCurrentWeight = FName(TEXT("GetCurrentWeight"));
float IInventoryInterface::Execute_GetCurrentWeight(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventGetCurrentWeight_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_GetCurrentWeight);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->GetCurrentWeight_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get current inventory weight\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get current inventory weight" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventGetCurrentWeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "GetCurrentWeight", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::PropPointers), sizeof(InventoryInterface_eventGetCurrentWeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventGetCurrentWeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execGetCurrentWeight)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetCurrentWeight_Implementation();
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function GetCurrentWeight

// Begin Interface UInventoryInterface Function GetInventoryCapacity
struct InventoryInterface_eventGetInventoryCapacity_Parms
{
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventGetInventoryCapacity_Parms()
		: ReturnValue(0)
	{
	}
};
int32 IInventoryInterface::GetInventoryCapacity() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetInventoryCapacity instead.");
	InventoryInterface_eventGetInventoryCapacity_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_GetInventoryCapacity = FName(TEXT("GetInventoryCapacity"));
int32 IInventoryInterface::Execute_GetInventoryCapacity(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventGetInventoryCapacity_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_GetInventoryCapacity);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->GetInventoryCapacity_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get inventory capacity\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get inventory capacity" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventGetInventoryCapacity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "GetInventoryCapacity", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::PropPointers), sizeof(InventoryInterface_eventGetInventoryCapacity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventGetInventoryCapacity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execGetInventoryCapacity)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetInventoryCapacity_Implementation();
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function GetInventoryCapacity

// Begin Interface UInventoryInterface Function GetItemQuantity
struct InventoryInterface_eventGetItemQuantity_Parms
{
	FName ItemID;
	int32 ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventGetItemQuantity_Parms()
		: ReturnValue(0)
	{
	}
};
int32 IInventoryInterface::GetItemQuantity(FName ItemID) const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetItemQuantity instead.");
	InventoryInterface_eventGetItemQuantity_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_GetItemQuantity = FName(TEXT("GetItemQuantity"));
int32 IInventoryInterface::Execute_GetItemQuantity(const UObject* O, FName ItemID)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventGetItemQuantity_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_GetItemQuantity);
	if (Func)
	{
		Parms.ItemID=ItemID;
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->GetItemQuantity_Implementation(ItemID);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get quantity of a specific item\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get quantity of a specific item" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventGetItemQuantity_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventGetItemQuantity_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "GetItemQuantity", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::PropPointers), sizeof(InventoryInterface_eventGetItemQuantity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventGetItemQuantity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_GetItemQuantity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_GetItemQuantity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execGetItemQuantity)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetItemQuantity_Implementation(Z_Param_ItemID);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function GetItemQuantity

// Begin Interface UInventoryInterface Function GetMaxWeight
struct InventoryInterface_eventGetMaxWeight_Parms
{
	float ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventGetMaxWeight_Parms()
		: ReturnValue(0)
	{
	}
};
float IInventoryInterface::GetMaxWeight() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_GetMaxWeight instead.");
	InventoryInterface_eventGetMaxWeight_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_GetMaxWeight = FName(TEXT("GetMaxWeight"));
float IInventoryInterface::Execute_GetMaxWeight(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventGetMaxWeight_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_GetMaxWeight);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->GetMaxWeight_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Get maximum inventory weight\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Get maximum inventory weight" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventGetMaxWeight_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "GetMaxWeight", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::PropPointers), sizeof(InventoryInterface_eventGetMaxWeight_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventGetMaxWeight_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_GetMaxWeight()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_GetMaxWeight_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execGetMaxWeight)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetMaxWeight_Implementation();
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function GetMaxWeight

// Begin Interface UInventoryInterface Function HasItem
struct InventoryInterface_eventHasItem_Parms
{
	FName ItemID;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventHasItem_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::HasItem(FName ItemID) const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_HasItem instead.");
	InventoryInterface_eventHasItem_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_HasItem = FName(TEXT("HasItem"));
bool IInventoryInterface::Execute_HasItem(const UObject* O, FName ItemID)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventHasItem_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_HasItem);
	if (Func)
	{
		Parms.ItemID=ItemID;
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->HasItem_Implementation(ItemID);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_HasItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Check if inventory has a specific item\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if inventory has a specific item" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventHasItem_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventHasItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventHasItem_Parms), &Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "HasItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::PropPointers), sizeof(InventoryInterface_eventHasItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventHasItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_HasItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_HasItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execHasItem)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasItem_Implementation(Z_Param_ItemID);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function HasItem

// Begin Interface UInventoryInterface Function IsInventoryFull
struct InventoryInterface_eventIsInventoryFull_Parms
{
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventIsInventoryFull_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::IsInventoryFull() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_IsInventoryFull instead.");
	InventoryInterface_eventIsInventoryFull_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_IsInventoryFull = FName(TEXT("IsInventoryFull"));
bool IInventoryInterface::Execute_IsInventoryFull(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventIsInventoryFull_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_IsInventoryFull);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->IsInventoryFull_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Check if inventory is full\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if inventory is full" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventIsInventoryFull_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventIsInventoryFull_Parms), &Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "IsInventoryFull", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::PropPointers), sizeof(InventoryInterface_eventIsInventoryFull_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventIsInventoryFull_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_IsInventoryFull()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_IsInventoryFull_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execIsInventoryFull)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInventoryFull_Implementation();
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function IsInventoryFull

// Begin Interface UInventoryInterface Function RemoveItem
struct InventoryInterface_eventRemoveItem_Parms
{
	FName ItemID;
	int32 Quantity;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventRemoveItem_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::RemoveItem(FName ItemID, int32 Quantity)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_RemoveItem instead.");
	InventoryInterface_eventRemoveItem_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_RemoveItem = FName(TEXT("RemoveItem"));
bool IInventoryInterface::Execute_RemoveItem(UObject* O, FName ItemID, int32 Quantity)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventRemoveItem_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_RemoveItem);
	if (Func)
	{
		Parms.ItemID=ItemID;
		Parms.Quantity=Quantity;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->RemoveItem_Implementation(ItemID,Quantity);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove an item from the inventory\n" },
#endif
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove an item from the inventory" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventRemoveItem_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventRemoveItem_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventRemoveItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventRemoveItem_Parms), &Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "RemoveItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::PropPointers), sizeof(InventoryInterface_eventRemoveItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventRemoveItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_RemoveItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_RemoveItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execRemoveItem)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveItem_Implementation(Z_Param_ItemID,Z_Param_Quantity);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function RemoveItem

// Begin Interface UInventoryInterface Function RemoveItemBySlotID
struct InventoryInterface_eventRemoveItemBySlotID_Parms
{
	FGuid SlotID;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventRemoveItemBySlotID_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::RemoveItemBySlotID(FGuid const& SlotID)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_RemoveItemBySlotID instead.");
	InventoryInterface_eventRemoveItemBySlotID_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_RemoveItemBySlotID = FName(TEXT("RemoveItemBySlotID"));
bool IInventoryInterface::Execute_RemoveItemBySlotID(UObject* O, FGuid const& SlotID)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventRemoveItemBySlotID_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_RemoveItemBySlotID);
	if (Func)
	{
		Parms.SlotID=SlotID;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->RemoveItemBySlotID_Implementation(SlotID);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Remove item by slot ID\n" },
#endif
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Remove item by slot ID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_SlotID = { "SlotID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventRemoveItemBySlotID_Parms, SlotID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotID_MetaData), NewProp_SlotID_MetaData) };
void Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventRemoveItemBySlotID_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventRemoveItemBySlotID_Parms), &Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_SlotID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "RemoveItemBySlotID", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::PropPointers), sizeof(InventoryInterface_eventRemoveItemBySlotID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0CC20C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventRemoveItemBySlotID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execRemoveItemBySlotID)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_SlotID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveItemBySlotID_Implementation(Z_Param_Out_SlotID);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function RemoveItemBySlotID

// Begin Interface UInventoryInterface Function UseItem
struct InventoryInterface_eventUseItem_Parms
{
	FName ItemID;
	int32 Quantity;
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	InventoryInterface_eventUseItem_Parms()
		: ReturnValue(false)
	{
	}
};
bool IInventoryInterface::UseItem(FName ItemID, int32 Quantity)
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_UseItem instead.");
	InventoryInterface_eventUseItem_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UInventoryInterface_UseItem = FName(TEXT("UseItem"));
bool IInventoryInterface::Execute_UseItem(UObject* O, FName ItemID, int32 Quantity)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UInventoryInterface::StaticClass()));
	InventoryInterface_eventUseItem_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UInventoryInterface_UseItem);
	if (Func)
	{
		Parms.ItemID=ItemID;
		Parms.Quantity=Quantity;
		O->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (IInventoryInterface*)(O->GetNativeInterfaceAddress(UInventoryInterface::StaticClass())))
	{
		Parms.ReturnValue = I->UseItem_Implementation(ItemID,Quantity);
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UInventoryInterface_UseItem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Use an item from inventory\n" },
#endif
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Use an item from inventory" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventUseItem_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryInterface_eventUseItem_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryInterface_eventUseItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryInterface_eventUseItem_Parms), &Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryInterface, nullptr, "UseItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::PropPointers), sizeof(InventoryInterface_eventUseItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(InventoryInterface_eventUseItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryInterface_UseItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryInterface_UseItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IInventoryInterface::execUseItem)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UseItem_Implementation(Z_Param_ItemID,Z_Param_Quantity);
	P_NATIVE_END;
}
// End Interface UInventoryInterface Function UseItem

// Begin Interface UInventoryInterface
void UInventoryInterface::StaticRegisterNativesUInventoryInterface()
{
	UClass* Class = UInventoryInterface::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddItem", &IInventoryInterface::execAddItem },
		{ "ClearInventory", &IInventoryInterface::execClearInventory },
		{ "DropItem", &IInventoryInterface::execDropItem },
		{ "GetCurrentWeight", &IInventoryInterface::execGetCurrentWeight },
		{ "GetInventoryCapacity", &IInventoryInterface::execGetInventoryCapacity },
		{ "GetItemQuantity", &IInventoryInterface::execGetItemQuantity },
		{ "GetMaxWeight", &IInventoryInterface::execGetMaxWeight },
		{ "HasItem", &IInventoryInterface::execHasItem },
		{ "IsInventoryFull", &IInventoryInterface::execIsInventoryFull },
		{ "RemoveItem", &IInventoryInterface::execRemoveItem },
		{ "RemoveItemBySlotID", &IInventoryInterface::execRemoveItemBySlotID },
		{ "UseItem", &IInventoryInterface::execUseItem },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UInventoryInterface);
UClass* Z_Construct_UClass_UInventoryInterface_NoRegister()
{
	return UInventoryInterface::StaticClass();
}
struct Z_Construct_UClass_UInventoryInterface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Interfaces/InventoryInterface.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UInventoryInterface_AddItem, "AddItem" }, // 1117657194
		{ &Z_Construct_UFunction_UInventoryInterface_ClearInventory, "ClearInventory" }, // 667858161
		{ &Z_Construct_UFunction_UInventoryInterface_DropItem, "DropItem" }, // 2780177699
		{ &Z_Construct_UFunction_UInventoryInterface_GetCurrentWeight, "GetCurrentWeight" }, // 1303038757
		{ &Z_Construct_UFunction_UInventoryInterface_GetInventoryCapacity, "GetInventoryCapacity" }, // 2487669149
		{ &Z_Construct_UFunction_UInventoryInterface_GetItemQuantity, "GetItemQuantity" }, // 2048812962
		{ &Z_Construct_UFunction_UInventoryInterface_GetMaxWeight, "GetMaxWeight" }, // 3565209952
		{ &Z_Construct_UFunction_UInventoryInterface_HasItem, "HasItem" }, // 3395233011
		{ &Z_Construct_UFunction_UInventoryInterface_IsInventoryFull, "IsInventoryFull" }, // 2198593201
		{ &Z_Construct_UFunction_UInventoryInterface_RemoveItem, "RemoveItem" }, // 1737271056
		{ &Z_Construct_UFunction_UInventoryInterface_RemoveItemBySlotID, "RemoveItemBySlotID" }, // 2535698322
		{ &Z_Construct_UFunction_UInventoryInterface_UseItem, "UseItem" }, // 2902562957
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IInventoryInterface>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UInventoryInterface_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryInterface_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UInventoryInterface_Statics::ClassParams = {
	&UInventoryInterface::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryInterface_Statics::Class_MetaDataParams), Z_Construct_UClass_UInventoryInterface_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UInventoryInterface()
{
	if (!Z_Registration_Info_UClass_UInventoryInterface.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UInventoryInterface.OuterSingleton, Z_Construct_UClass_UInventoryInterface_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UInventoryInterface.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UInventoryInterface>()
{
	return UInventoryInterface::StaticClass();
}
UInventoryInterface::UInventoryInterface(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UInventoryInterface);
UInventoryInterface::~UInventoryInterface() {}
// End Interface UInventoryInterface

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UInventoryInterface, UInventoryInterface::StaticClass, TEXT("UInventoryInterface"), &Z_Registration_Info_UClass_UInventoryInterface, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UInventoryInterface), 3420551761U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_1907320856(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
