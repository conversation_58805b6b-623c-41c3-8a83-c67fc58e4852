#pragma once

#include "CoreMinimal.h"
#include "../BaseEnemyCharacter.h"
#include "FastRunnerCharacter.generated.h"

UENUM(BlueprintType)
enum class ERunnerState : uint8
{
	Stalking		UMETA(DisplayName = "Stalking"),
	Charging		UMETA(DisplayName = "Charging"),
	Leaping			UMETA(DisplayName = "Leaping"),
	Recovering		UMETA(DisplayName = "Recovering"),
	Circling		UMETA(DisplayName = "Circling")
};

USTRUCT(BlueprintType)
struct FRunnerConfiguration
{
	GENERATED_BODY()

	FRunnerConfiguration()
	{
		MaxRunSpeed = 800.0f;
		ChargeSpeed = 1200.0f;
		StalkingSpeed = 150.0f;
		CirclingSpeed = 600.0f;
		ChargeRange = 1000.0f;
		LeapRange = 400.0f;
		LeapHeight = 300.0f;
		LeapDamage = 40.0f;
		ChargeDamage = 35.0f;
		ChargeStunDuration = 1.5f;
		bCanLeap = true;
		bCanWallRun = false;
		WallRunDuration = 3.0f;
		bCanClimb = true;
		ClimbSpeed = 400.0f;
		StaminaMax = 100.0f;
		StaminaRegenRate = 20.0f;
		ChargeStaminaCost = 30.0f;
		LeapStaminaCost = 25.0f;
		bPrefersStealth = true;
		StealthDetectionRange = 300.0f;
		bFlankingBehavior = true;
		FlankingRadius = 500.0f;
		CirclingRadius = 400.0f;
		bAvoidsFrontalAssault = true;
	}

	// Speed settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float MaxRunSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float ChargeSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float StalkingSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float CirclingSpeed;

	// Attack settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float ChargeRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float LeapRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float LeapHeight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float LeapDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float ChargeDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
	float ChargeStunDuration;

	// Abilities
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanLeap;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanWallRun;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanWallRun"))
	float WallRunDuration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanClimb;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanClimb"))
	float ClimbSpeed;

	// Stamina system
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stamina")
	float StaminaMax;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stamina")
	float StaminaRegenRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stamina")
	float ChargeStaminaCost;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stamina")
	float LeapStaminaCost;

	// AI Behavior
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bPrefersStealth;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior", meta = (EditCondition = "bPrefersStealth"))
	float StealthDetectionRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bFlankingBehavior;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior", meta = (EditCondition = "bFlankingBehavior"))
	float FlankingRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	float CirclingRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Behavior")
	bool bAvoidsFrontalAssault;

	// Visual and audio
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UAnimMontage> ChargeAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UAnimMontage> LeapAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UAnimMontage> WallRunAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<USoundBase> ChargeSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<USoundBase> LeapSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UParticleSystem> ChargeEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UParticleSystem> LeapEffect;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunnerChargeStarted, const FVector&, ChargeTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunnerChargeEnded, bool, bHitTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRunnerLeap, const FVector&, LeapStart, const FVector&, LeapTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunnerLanded, const FVector&, LandLocation);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRunnerStaminaChanged, float, CurrentStamina, float, MaxStamina);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRunnerStateChanged, ERunnerState, NewState);

UCLASS(BlueprintType, Blueprintable)
class SLT_API AFastRunnerCharacter : public ABaseEnemyCharacter
{
	GENERATED_BODY()

public:
	AFastRunnerCharacter();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;

	// Runner-specific configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Runner Configuration")
	FRunnerConfiguration RunnerConfig;

	// Current runner state
	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	ERunnerState RunnerState = ERunnerState::Stalking;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	float CurrentStamina = 100.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	bool bIsCharging = false;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	bool bIsLeaping = false;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	bool bIsWallRunning = false;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	FVector ChargeTarget = FVector::ZeroVector;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	FVector LeapTarget = FVector::ZeroVector;

	UPROPERTY(BlueprintReadOnly, Category = "Runner State")
	bool bIsExhausted = false;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerChargeStarted OnRunnerChargeStarted;

	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerChargeEnded OnRunnerChargeEnded;

	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerLeap OnRunnerLeap;

	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerLanded OnRunnerLanded;

	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerStaminaChanged OnRunnerStaminaChanged;

	UPROPERTY(BlueprintAssignable, Category = "Runner Events")
	FOnRunnerStateChanged OnRunnerStateChanged;

	// Runner-specific functions
	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StartCharge(const FVector& TargetLocation);

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StopCharge();

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void PerformLeap(const FVector& TargetLocation);

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StartWallRun(AActor* Wall);

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StopWallRun();

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StartCircling(APawn* Target);

	UFUNCTION(BlueprintCallable, Category = "Runner Actions")
	void StopCircling();

	// Stamina functions
	UFUNCTION(BlueprintCallable, Category = "Stamina")
	bool ConsumeStamina(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Stamina")
	void RegenerateStamina(float Amount);

	UFUNCTION(BlueprintCallable, Category = "Stamina")
	bool HasEnoughStamina(float RequiredAmount) const;

	UFUNCTION(BlueprintCallable, Category = "Stamina")
	float GetStaminaPercentage() const;

	// Runner AI functions
	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	bool ShouldCharge() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	bool ShouldLeap() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	bool ShouldFlank() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	bool ShouldCircle() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	FVector GetFlankingPosition() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	FVector GetCirclingPosition() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	bool IsPlayerLookingAtMe() const;

	UFUNCTION(BlueprintCallable, Category = "Runner AI")
	AActor* FindWallForRunning() const;

	// State management
	UFUNCTION(BlueprintCallable, Category = "Runner State")
	void SetRunnerState(ERunnerState NewState);

	UFUNCTION(BlueprintCallable, Category = "Runner State")
	bool CanPerformAction(const FString& ActionName) const;

	// Override base functions
	virtual void Die() override;
	virtual float TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser) override;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnRunnerInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnChargeStartedBP(const FVector& Target);

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnChargeEndedBP(bool bHitTarget);

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnLeapStartedBP(const FVector& LeapTarget);

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnLandedBP(const FVector& LandLocation);

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnWallRunStartedBP(AActor* Wall);

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnWallRunEndedBP();

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnExhaustedBP();

	UFUNCTION(BlueprintImplementableEvent, Category = "Runner Events")
	void OnStaminaRecoveredBP();

protected:
	// Timer handles
	FTimerHandle ChargeTimerHandle;
	FTimerHandle LeapTimerHandle;
	FTimerHandle WallRunTimerHandle;
	FTimerHandle StaminaRegenTimerHandle;
	FTimerHandle CirclingTimerHandle;

	// Internal state
	FVector CirclingCenter = FVector::ZeroVector;
	float CirclingAngle = 0.0f;
	bool bIsCirclingClockwise = true;
	float WallRunStartTime = 0.0f;

	// Internal functions
	void InitializeRunner();
	void UpdateStamina(float DeltaTime);
	void UpdateCharge(float DeltaTime);
	void UpdateLeap(float DeltaTime);
	void UpdateWallRun(float DeltaTime);
	void UpdateCircling(float DeltaTime);
	void ProcessRunnerAI();
	void HandleChargeHit(AActor* HitActor);
	void HandleLeapLanding();
	bool CanSeeTarget() const;
	bool IsInStealthRange() const;
	void SetupRunnerAttacks();
	void ConfigureRunnerMovement();
	void PlayRunnerEffect(UParticleSystem* Effect, const FVector& Location);
	void PlayRunnerSound(USoundBase* Sound);
};
