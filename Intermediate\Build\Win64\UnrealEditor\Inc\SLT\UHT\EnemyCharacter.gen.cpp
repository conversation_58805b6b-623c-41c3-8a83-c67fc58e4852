// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Enemies/EnemyCharacter.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeEnemyCharacter() {}

// Begin Cross Module References
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTree_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UPawnSensingComponent_NoRegister();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_AEnemyCharacter();
SLT_API UClass* Z_Construct_UClass_AEnemyCharacter_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EEnemyState();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature();
UMG_API UClass* Z_Construct_UClass_UWidgetComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EEnemyState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EEnemyState;
static UEnum* EEnemyState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EEnemyState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EEnemyState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EEnemyState, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EEnemyState"));
	}
	return Z_Registration_Info_UEnum_EEnemyState.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EEnemyState>()
{
	return EEnemyState_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EEnemyState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Alert.DisplayName", "Alert" },
		{ "Alert.Name", "EEnemyState::Alert" },
		{ "Attacking.DisplayName", "Attacking" },
		{ "Attacking.Name", "EEnemyState::Attacking" },
		{ "BlueprintType", "true" },
		{ "Chasing.DisplayName", "Chasing" },
		{ "Chasing.Name", "EEnemyState::Chasing" },
		{ "Dead.DisplayName", "Dead" },
		{ "Dead.Name", "EEnemyState::Dead" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "EEnemyState::Idle" },
		{ "Investigating.DisplayName", "Investigating" },
		{ "Investigating.Name", "EEnemyState::Investigating" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
		{ "Patrol.DisplayName", "Patrol" },
		{ "Patrol.Name", "EEnemyState::Patrol" },
		{ "Stunned.DisplayName", "Stunned" },
		{ "Stunned.Name", "EEnemyState::Stunned" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EEnemyState::Idle", (int64)EEnemyState::Idle },
		{ "EEnemyState::Patrol", (int64)EEnemyState::Patrol },
		{ "EEnemyState::Alert", (int64)EEnemyState::Alert },
		{ "EEnemyState::Investigating", (int64)EEnemyState::Investigating },
		{ "EEnemyState::Chasing", (int64)EEnemyState::Chasing },
		{ "EEnemyState::Attacking", (int64)EEnemyState::Attacking },
		{ "EEnemyState::Stunned", (int64)EEnemyState::Stunned },
		{ "EEnemyState::Dead", (int64)EEnemyState::Dead },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EEnemyState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EEnemyState",
	"EEnemyState",
	Z_Construct_UEnum_SLT_EEnemyState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EEnemyState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EEnemyState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EEnemyState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EEnemyState()
{
	if (!Z_Registration_Info_UEnum_EEnemyState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EEnemyState.InnerSingleton, Z_Construct_UEnum_SLT_EEnemyState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EEnemyState.InnerSingleton;
}
// End Enum EEnemyState

// Begin Delegate FOnEnemyStateChanged
struct Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnEnemyStateChanged_Parms
	{
		EEnemyState OldState;
		EEnemyState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyStateChanged_Parms, OldState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(0, nullptr) }; // 2238520129
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(0, nullptr) }; // 2238520129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnEnemyStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnEnemyStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnEnemyStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnEnemyStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyStateChanged, EEnemyState OldState, EEnemyState NewState)
{
	struct _Script_SLT_eventOnEnemyStateChanged_Parms
	{
		EEnemyState OldState;
		EEnemyState NewState;
	};
	_Script_SLT_eventOnEnemyStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	OnEnemyStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnEnemyStateChanged

// Begin Delegate FOnEnemyDamaged
struct Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnEnemyDamaged_Parms
	{
		AEnemyCharacter* Enemy;
		float DamageAmount;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyDamaged_Parms, Enemy), Z_Construct_UClass_AEnemyCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyDamaged_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyDamaged_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_Enemy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnEnemyDamaged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::_Script_SLT_eventOnEnemyDamaged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::_Script_SLT_eventOnEnemyDamaged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnEnemyDamaged_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDamaged, AEnemyCharacter* Enemy, float DamageAmount, AActor* DamageSource)
{
	struct _Script_SLT_eventOnEnemyDamaged_Parms
	{
		AEnemyCharacter* Enemy;
		float DamageAmount;
		AActor* DamageSource;
	};
	_Script_SLT_eventOnEnemyDamaged_Parms Parms;
	Parms.Enemy=Enemy;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageSource=DamageSource;
	OnEnemyDamaged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnEnemyDamaged

// Begin Delegate FOnEnemyDeath
struct Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnEnemyDeath_Parms
	{
		AEnemyCharacter* Enemy;
		AActor* Killer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyDeath_Parms, Enemy), Z_Construct_UClass_AEnemyCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnEnemyDeath_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::NewProp_Enemy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnEnemyDeath__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::_Script_SLT_eventOnEnemyDeath_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::_Script_SLT_eventOnEnemyDeath_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnEnemyDeath_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDeath, AEnemyCharacter* Enemy, AActor* Killer)
{
	struct _Script_SLT_eventOnEnemyDeath_Parms
	{
		AEnemyCharacter* Enemy;
		AActor* Killer;
	};
	_Script_SLT_eventOnEnemyDeath_Parms Parms;
	Parms.Enemy=Enemy;
	Parms.Killer=Killer;
	OnEnemyDeath.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnEnemyDeath

// Begin Delegate FOnPlayerDetected
struct Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPlayerDetected_Parms
	{
		AEnemyCharacter* Enemy;
		APawn* DetectedPlayer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DetectedPlayer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPlayerDetected_Parms, Enemy), Z_Construct_UClass_AEnemyCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::NewProp_DetectedPlayer = { "DetectedPlayer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPlayerDetected_Parms, DetectedPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::NewProp_Enemy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::NewProp_DetectedPlayer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPlayerDetected__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::_Script_SLT_eventOnPlayerDetected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::_Script_SLT_eventOnPlayerDetected_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerDetected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDetected, AEnemyCharacter* Enemy, APawn* DetectedPlayer)
{
	struct _Script_SLT_eventOnPlayerDetected_Parms
	{
		AEnemyCharacter* Enemy;
		APawn* DetectedPlayer;
	};
	_Script_SLT_eventOnPlayerDetected_Parms Parms;
	Parms.Enemy=Enemy;
	Parms.DetectedPlayer=DetectedPlayer;
	OnPlayerDetected.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPlayerDetected

// Begin Delegate FOnPlayerLost
struct Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPlayerLost_Parms
	{
		AEnemyCharacter* Enemy;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Enemy;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::NewProp_Enemy = { "Enemy", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPlayerLost_Parms, Enemy), Z_Construct_UClass_AEnemyCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::NewProp_Enemy,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPlayerLost__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::_Script_SLT_eventOnPlayerLost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::_Script_SLT_eventOnPlayerLost_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlayerLost_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerLost, AEnemyCharacter* Enemy)
{
	struct _Script_SLT_eventOnPlayerLost_Parms
	{
		AEnemyCharacter* Enemy;
	};
	_Script_SLT_eventOnPlayerLost_Parms Parms;
	Parms.Enemy=Enemy;
	OnPlayerLost.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPlayerLost

// Begin Class AEnemyCharacter Function Attack
struct Z_Construct_UFunction_AEnemyCharacter_Attack_Statics
{
	struct EnemyCharacter_eventAttack_Parms
	{
		AActor* Target;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventAttack_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "Attack", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::EnemyCharacter_eventAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::EnemyCharacter_eventAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_Attack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_Attack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execAttack)
{
	P_GET_OBJECT(AActor,Z_Param_Target);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Attack(Z_Param_Target);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function Attack

// Begin Class AEnemyCharacter Function CanAttack
struct Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics
{
	struct EnemyCharacter_eventCanAttack_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((EnemyCharacter_eventCanAttack_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(EnemyCharacter_eventCanAttack_Parms), &Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "CanAttack", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::EnemyCharacter_eventCanAttack_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::EnemyCharacter_eventCanAttack_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_CanAttack()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_CanAttack_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execCanAttack)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanAttack();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function CanAttack

// Begin Class AEnemyCharacter Function Die
struct Z_Construct_UFunction_AEnemyCharacter_Die_Statics
{
	struct EnemyCharacter_eventDie_Parms
	{
		AActor* Killer;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "CPP_Default_Killer", "None" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_Die_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventDie_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_Die_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_Die_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Die_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_Die_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "Die", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_Die_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Die_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_Die_Statics::EnemyCharacter_eventDie_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Die_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_Die_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_Die_Statics::EnemyCharacter_eventDie_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_Die()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_Die_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execDie)
{
	P_GET_OBJECT(AActor,Z_Param_Killer);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Die(Z_Param_Killer);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function Die

// Begin Class AEnemyCharacter Function GetDistanceToPlayer
struct Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics
{
	struct EnemyCharacter_eventGetDistanceToPlayer_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventGetDistanceToPlayer_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "GetDistanceToPlayer", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::EnemyCharacter_eventGetDistanceToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::EnemyCharacter_eventGetDistanceToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execGetDistanceToPlayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDistanceToPlayer();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function GetDistanceToPlayer

// Begin Class AEnemyCharacter Function GetHealthPercentage
struct Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics
{
	struct EnemyCharacter_eventGetHealthPercentage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventGetHealthPercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "GetHealthPercentage", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::EnemyCharacter_eventGetHealthPercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::EnemyCharacter_eventGetHealthPercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execGetHealthPercentage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetHealthPercentage();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function GetHealthPercentage

// Begin Class AEnemyCharacter Function HasLineOfSightToPlayer
struct Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics
{
	struct EnemyCharacter_eventHasLineOfSightToPlayer_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((EnemyCharacter_eventHasLineOfSightToPlayer_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(EnemyCharacter_eventHasLineOfSightToPlayer_Parms), &Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "HasLineOfSightToPlayer", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::EnemyCharacter_eventHasLineOfSightToPlayer_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::EnemyCharacter_eventHasLineOfSightToPlayer_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execHasLineOfSightToPlayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasLineOfSightToPlayer();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function HasLineOfSightToPlayer

// Begin Class AEnemyCharacter Function Investigate
struct Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics
{
	struct EnemyCharacter_eventInvestigate_Parms
	{
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventInvestigate_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "Investigate", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::EnemyCharacter_eventInvestigate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::EnemyCharacter_eventInvestigate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_Investigate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_Investigate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execInvestigate)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Investigate(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function Investigate

// Begin Class AEnemyCharacter Function IsAlive
struct Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics
{
	struct EnemyCharacter_eventIsAlive_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((EnemyCharacter_eventIsAlive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(EnemyCharacter_eventIsAlive_Parms), &Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "IsAlive", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::EnemyCharacter_eventIsAlive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::EnemyCharacter_eventIsAlive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_IsAlive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_IsAlive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execIsAlive)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAlive();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function IsAlive

// Begin Class AEnemyCharacter Function LosePlayer
struct Z_Construct_UFunction_AEnemyCharacter_LosePlayer_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_LosePlayer_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "LosePlayer", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_LosePlayer_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_LosePlayer_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AEnemyCharacter_LosePlayer()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_LosePlayer_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execLosePlayer)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LosePlayer();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function LosePlayer

// Begin Class AEnemyCharacter Function OnAttackExecuted
struct EnemyCharacter_eventOnAttackExecuted_Parms
{
	AActor* Target;
};
static const FName NAME_AEnemyCharacter_OnAttackExecuted = FName(TEXT("OnAttackExecuted"));
void AEnemyCharacter::OnAttackExecuted(AActor* Target)
{
	EnemyCharacter_eventOnAttackExecuted_Parms Parms;
	Parms.Target=Target;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnAttackExecuted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Target;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::NewProp_Target = { "Target", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnAttackExecuted_Parms, Target), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::NewProp_Target,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnAttackExecuted", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::PropPointers), sizeof(EnemyCharacter_eventOnAttackExecuted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnAttackExecuted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnAttackExecuted

// Begin Class AEnemyCharacter Function OnDamageReceived
struct EnemyCharacter_eventOnDamageReceived_Parms
{
	float DamageAmount;
	AActor* DamageSource;
};
static const FName NAME_AEnemyCharacter_OnDamageReceived = FName(TEXT("OnDamageReceived"));
void AEnemyCharacter::OnDamageReceived(float DamageAmount, AActor* DamageSource)
{
	EnemyCharacter_eventOnDamageReceived_Parms Parms;
	Parms.DamageAmount=DamageAmount;
	Parms.DamageSource=DamageSource;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnDamageReceived);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnDamageReceived_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnDamageReceived_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnDamageReceived", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::PropPointers), sizeof(EnemyCharacter_eventOnDamageReceived_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnDamageReceived_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnDamageReceived

// Begin Class AEnemyCharacter Function OnDeathEvent
struct EnemyCharacter_eventOnDeathEvent_Parms
{
	AActor* Killer;
};
static const FName NAME_AEnemyCharacter_OnDeathEvent = FName(TEXT("OnDeathEvent"));
void AEnemyCharacter::OnDeathEvent(AActor* Killer)
{
	EnemyCharacter_eventOnDeathEvent_Parms Parms;
	Parms.Killer=Killer;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnDeathEvent);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Killer;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::NewProp_Killer = { "Killer", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnDeathEvent_Parms, Killer), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::NewProp_Killer,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnDeathEvent", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::PropPointers), sizeof(EnemyCharacter_eventOnDeathEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnDeathEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnDeathEvent

// Begin Class AEnemyCharacter Function OnHearNoise
struct Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics
{
	struct EnemyCharacter_eventOnHearNoise_Parms
	{
		APawn* NoiseInstigator;
		FVector Location;
		float Volume;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_NoiseInstigator;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Volume;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_NoiseInstigator = { "NoiseInstigator", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnHearNoise_Parms, NoiseInstigator), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnHearNoise_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_Volume = { "Volume", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnHearNoise_Parms, Volume), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_NoiseInstigator,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::NewProp_Volume,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnHearNoise", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::EnemyCharacter_eventOnHearNoise_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00C80401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::EnemyCharacter_eventOnHearNoise_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnHearNoise()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnHearNoise_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execOnHearNoise)
{
	P_GET_OBJECT(APawn,Z_Param_NoiseInstigator);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Volume);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnHearNoise(Z_Param_NoiseInstigator,Z_Param_Out_Location,Z_Param_Volume);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function OnHearNoise

// Begin Class AEnemyCharacter Function OnInvestigationStarted
struct EnemyCharacter_eventOnInvestigationStarted_Parms
{
	FVector Location;
};
static const FName NAME_AEnemyCharacter_OnInvestigationStarted = FName(TEXT("OnInvestigationStarted"));
void AEnemyCharacter::OnInvestigationStarted(FVector const& Location)
{
	EnemyCharacter_eventOnInvestigationStarted_Parms Parms;
	Parms.Location=Location;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnInvestigationStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnInvestigationStarted_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnInvestigationStarted", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::PropPointers), sizeof(EnemyCharacter_eventOnInvestigationStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08C80800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnInvestigationStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnInvestigationStarted

// Begin Class AEnemyCharacter Function OnPlayerSpotted
struct EnemyCharacter_eventOnPlayerSpotted_Parms
{
	APawn* Player;
};
static const FName NAME_AEnemyCharacter_OnPlayerSpotted = FName(TEXT("OnPlayerSpotted"));
void AEnemyCharacter::OnPlayerSpotted(APawn* Player)
{
	EnemyCharacter_eventOnPlayerSpotted_Parms Parms;
	Parms.Player=Player;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnPlayerSpotted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnPlayerSpotted_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnPlayerSpotted", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::PropPointers), sizeof(EnemyCharacter_eventOnPlayerSpotted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnPlayerSpotted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnPlayerSpotted

// Begin Class AEnemyCharacter Function OnSeePawn
struct Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics
{
	struct EnemyCharacter_eventOnSeePawn_Parms
	{
		APawn* Pawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Sensing callbacks\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Sensing callbacks" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Pawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::NewProp_Pawn = { "Pawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnSeePawn_Parms, Pawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::NewProp_Pawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnSeePawn", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::EnemyCharacter_eventOnSeePawn_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00080401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::EnemyCharacter_eventOnSeePawn_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnSeePawn()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnSeePawn_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execOnSeePawn)
{
	P_GET_OBJECT(APawn,Z_Param_Pawn);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnSeePawn(Z_Param_Pawn);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function OnSeePawn

// Begin Class AEnemyCharacter Function OnStateChanged
struct EnemyCharacter_eventOnStateChanged_Parms
{
	EEnemyState OldState;
	EEnemyState NewState;
};
static const FName NAME_AEnemyCharacter_OnStateChanged = FName(TEXT("OnStateChanged"));
void AEnemyCharacter::OnStateChanged(EEnemyState OldState, EEnemyState NewState)
{
	EnemyCharacter_eventOnStateChanged_Parms Parms;
	Parms.OldState=OldState;
	Parms.NewState=NewState;
	UFunction* Func = FindFunctionChecked(NAME_AEnemyCharacter_OnStateChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldState;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_OldState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_OldState = { "OldState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnStateChanged_Parms, OldState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(0, nullptr) }; // 2238520129
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventOnStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(0, nullptr) }; // 2238520129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_OldState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_OldState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "OnStateChanged", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::PropPointers), sizeof(EnemyCharacter_eventOnStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(EnemyCharacter_eventOnStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_OnStateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_OnStateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class AEnemyCharacter Function OnStateChanged

// Begin Class AEnemyCharacter Function SetEnemyState
struct Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics
{
	struct EnemyCharacter_eventSetEnemyState_Parms
	{
		EEnemyState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventSetEnemyState_Parms, NewState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(0, nullptr) }; // 2238520129
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "SetEnemyState", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::EnemyCharacter_eventSetEnemyState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::EnemyCharacter_eventSetEnemyState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_SetEnemyState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_SetEnemyState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execSetEnemyState)
{
	P_GET_ENUM(EEnemyState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetEnemyState(EEnemyState(Z_Param_NewState));
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function SetEnemyState

// Begin Class AEnemyCharacter Function SpawnLoot
struct Z_Construct_UFunction_AEnemyCharacter_SpawnLoot_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loot" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_SpawnLoot_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "SpawnLoot", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_SpawnLoot_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_SpawnLoot_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AEnemyCharacter_SpawnLoot()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_SpawnLoot_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execSpawnLoot)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SpawnLoot();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function SpawnLoot

// Begin Class AEnemyCharacter Function StartChasing
struct Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics
{
	struct EnemyCharacter_eventStartChasing_Parms
	{
		APawn* Player;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Player;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::NewProp_Player = { "Player", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventStartChasing_Parms, Player), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::NewProp_Player,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "StartChasing", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::EnemyCharacter_eventStartChasing_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::EnemyCharacter_eventStartChasing_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_StartChasing()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_StartChasing_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execStartChasing)
{
	P_GET_OBJECT(APawn,Z_Param_Player);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartChasing(Z_Param_Player);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function StartChasing

// Begin Class AEnemyCharacter Function StartPatrol
struct Z_Construct_UFunction_AEnemyCharacter_StartPatrol_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_StartPatrol_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "StartPatrol", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_StartPatrol_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_StartPatrol_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_AEnemyCharacter_StartPatrol()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_StartPatrol_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execStartPatrol)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartPatrol();
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function StartPatrol

// Begin Class AEnemyCharacter Function TakeDamage
struct Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics
{
	struct EnemyCharacter_eventTakeDamage_Parms
	{
		float DamageAmount;
		AActor* DamageSource;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main functions\n" },
#endif
		{ "CPP_Default_DamageSource", "None" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DamageSource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventTakeDamage_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::NewProp_DamageSource = { "DamageSource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(EnemyCharacter_eventTakeDamage_Parms, DamageSource), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::NewProp_DamageAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::NewProp_DamageSource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_AEnemyCharacter, nullptr, "TakeDamage", nullptr, nullptr, Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::EnemyCharacter_eventTakeDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020400, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::EnemyCharacter_eventTakeDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_AEnemyCharacter_TakeDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_AEnemyCharacter_TakeDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(AEnemyCharacter::execTakeDamage)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_GET_OBJECT(AActor,Z_Param_DamageSource);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TakeDamage(Z_Param_DamageAmount,Z_Param_DamageSource);
	P_NATIVE_END;
}
// End Class AEnemyCharacter Function TakeDamage

// Begin Class AEnemyCharacter
void AEnemyCharacter::StaticRegisterNativesAEnemyCharacter()
{
	UClass* Class = AEnemyCharacter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "Attack", &AEnemyCharacter::execAttack },
		{ "CanAttack", &AEnemyCharacter::execCanAttack },
		{ "Die", &AEnemyCharacter::execDie },
		{ "GetDistanceToPlayer", &AEnemyCharacter::execGetDistanceToPlayer },
		{ "GetHealthPercentage", &AEnemyCharacter::execGetHealthPercentage },
		{ "HasLineOfSightToPlayer", &AEnemyCharacter::execHasLineOfSightToPlayer },
		{ "Investigate", &AEnemyCharacter::execInvestigate },
		{ "IsAlive", &AEnemyCharacter::execIsAlive },
		{ "LosePlayer", &AEnemyCharacter::execLosePlayer },
		{ "OnHearNoise", &AEnemyCharacter::execOnHearNoise },
		{ "OnSeePawn", &AEnemyCharacter::execOnSeePawn },
		{ "SetEnemyState", &AEnemyCharacter::execSetEnemyState },
		{ "SpawnLoot", &AEnemyCharacter::execSpawnLoot },
		{ "StartChasing", &AEnemyCharacter::execStartChasing },
		{ "StartPatrol", &AEnemyCharacter::execStartPatrol },
		{ "TakeDamage", &AEnemyCharacter::execTakeDamage },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(AEnemyCharacter);
UClass* Z_Construct_UClass_AEnemyCharacter_NoRegister()
{
	return AEnemyCharacter::StaticClass();
}
struct Z_Construct_UClass_AEnemyCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Core/Enemies/EnemyCharacter.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PawnSensingComponent_MetaData[] = {
		{ "Category", "Components" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HealthBarWidget_MetaData[] = {
		{ "Category", "Components" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Stats" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Enemy stats\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Enemy stats" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentHealth_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "Category", "Stats" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Stats" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackCooldown_MetaData[] = {
		{ "Category", "Stats" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTree_MetaData[] = {
		{ "Category", "AI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI Behavior\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI Behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SightRadius_MetaData[] = {
		{ "Category", "AI" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HearingRadius_MetaData[] = {
		{ "Category", "AI" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LosePlayerTime_MetaData[] = {
		{ "Category", "AI" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolRadius_MetaData[] = {
		{ "Category", "AI" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// State management\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "State management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsDead_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TargetPlayer_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastKnownPlayerLocation_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolPoints_MetaData[] = {
		{ "Category", "Patrol" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Patrol settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Patrol settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseRandomPatrol_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolWaitTime_MetaData[] = {
		{ "Category", "Patrol" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LootTable_MetaData[] = {
		{ "Category", "Loot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Loot settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loot settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LootDropChance_MetaData[] = {
		{ "Category", "Loot" },
		{ "ClampMax", "1.0" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnemyTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gameplay tags\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEnemyStateChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEnemyDamaged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnEnemyDeath_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerDetected_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlayerLost_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Enemies/EnemyCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PawnSensingComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_HealthBarWidget;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentHealth;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackCooldown;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_BehaviorTree;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SightRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HearingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LosePlayerTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolRadius;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static void NewProp_bIsDead_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsDead;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetPlayer;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastKnownPlayerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PatrolPoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PatrolPoints;
	static void NewProp_bUseRandomPatrol_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseRandomPatrol;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolWaitTime;
	static const UECodeGen_Private::FNamePropertyParams NewProp_LootTable_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LootTable;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LootDropChance;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EnemyTags;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEnemyStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEnemyDamaged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnEnemyDeath;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerDetected;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlayerLost;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_AEnemyCharacter_Attack, "Attack" }, // 629050987
		{ &Z_Construct_UFunction_AEnemyCharacter_CanAttack, "CanAttack" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_Die, "Die" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_GetDistanceToPlayer, "GetDistanceToPlayer" }, // 598028004
		{ &Z_Construct_UFunction_AEnemyCharacter_GetHealthPercentage, "GetHealthPercentage" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_HasLineOfSightToPlayer, "HasLineOfSightToPlayer" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_Investigate, "Investigate" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_IsAlive, "IsAlive" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_LosePlayer, "LosePlayer" }, // 365142508
		{ &Z_Construct_UFunction_AEnemyCharacter_OnAttackExecuted, "OnAttackExecuted" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_OnDamageReceived, "OnDamageReceived" }, // 853245705
		{ &Z_Construct_UFunction_AEnemyCharacter_OnDeathEvent, "OnDeathEvent" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_OnHearNoise, "OnHearNoise" }, // 666919003
		{ &Z_Construct_UFunction_AEnemyCharacter_OnInvestigationStarted, "OnInvestigationStarted" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_OnPlayerSpotted, "OnPlayerSpotted" }, // **********
		{ &Z_Construct_UFunction_AEnemyCharacter_OnSeePawn, "OnSeePawn" }, // 1756529358
		{ &Z_Construct_UFunction_AEnemyCharacter_OnStateChanged, "OnStateChanged" }, // 594110426
		{ &Z_Construct_UFunction_AEnemyCharacter_SetEnemyState, "SetEnemyState" }, // 1755017243
		{ &Z_Construct_UFunction_AEnemyCharacter_SpawnLoot, "SpawnLoot" }, // 3617862124
		{ &Z_Construct_UFunction_AEnemyCharacter_StartChasing, "StartChasing" }, // 1752942237
		{ &Z_Construct_UFunction_AEnemyCharacter_StartPatrol, "StartPatrol" }, // 3055090607
		{ &Z_Construct_UFunction_AEnemyCharacter_TakeDamage, "TakeDamage" }, // 526766098
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<AEnemyCharacter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PawnSensingComponent = { "PawnSensingComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, PawnSensingComponent), Z_Construct_UClass_UPawnSensingComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PawnSensingComponent_MetaData), NewProp_PawnSensingComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_HealthBarWidget = { "HealthBarWidget", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, HealthBarWidget), Z_Construct_UClass_UWidgetComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HealthBarWidget_MetaData), NewProp_HealthBarWidget_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentHealth = { "CurrentHealth", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, CurrentHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentHealth_MetaData), NewProp_CurrentHealth_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackDamage = { "AttackDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, AttackDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackCooldown = { "AttackCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, AttackCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackCooldown_MetaData), NewProp_AttackCooldown_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_BehaviorTree = { "BehaviorTree", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, BehaviorTree), Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTree_MetaData), NewProp_BehaviorTree_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_SightRadius = { "SightRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, SightRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SightRadius_MetaData), NewProp_SightRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_HearingRadius = { "HearingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, HearingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HearingRadius_MetaData), NewProp_HearingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LosePlayerTime = { "LosePlayerTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, LosePlayerTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LosePlayerTime_MetaData), NewProp_LosePlayerTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolRadius = { "PatrolRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, PatrolRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolRadius_MetaData), NewProp_PatrolRadius_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, CurrentState), Z_Construct_UEnum_SLT_EEnemyState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 2238520129
void Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bIsDead_SetBit(void* Obj)
{
	((AEnemyCharacter*)Obj)->bIsDead = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bIsDead = { "bIsDead", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AEnemyCharacter), &Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bIsDead_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsDead_MetaData), NewProp_bIsDead_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_TargetPlayer = { "TargetPlayer", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, TargetPlayer), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TargetPlayer_MetaData), NewProp_TargetPlayer_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LastKnownPlayerLocation = { "LastKnownPlayerLocation", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, LastKnownPlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastKnownPlayerLocation_MetaData), NewProp_LastKnownPlayerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolPoints_Inner = { "PatrolPoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolPoints = { "PatrolPoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, PatrolPoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolPoints_MetaData), NewProp_PatrolPoints_MetaData) };
void Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bUseRandomPatrol_SetBit(void* Obj)
{
	((AEnemyCharacter*)Obj)->bUseRandomPatrol = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bUseRandomPatrol = { "bUseRandomPatrol", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AEnemyCharacter), &Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bUseRandomPatrol_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseRandomPatrol_MetaData), NewProp_bUseRandomPatrol_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolWaitTime = { "PatrolWaitTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, PatrolWaitTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolWaitTime_MetaData), NewProp_PatrolWaitTime_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootTable_Inner = { "LootTable", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootTable = { "LootTable", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, LootTable), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LootTable_MetaData), NewProp_LootTable_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootDropChance = { "LootDropChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, LootDropChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LootDropChance_MetaData), NewProp_LootDropChance_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_EnemyTags = { "EnemyTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, EnemyTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnemyTags_MetaData), NewProp_EnemyTags_MetaData) }; // 3352185621
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyStateChanged = { "OnEnemyStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, OnEnemyStateChanged), Z_Construct_UDelegateFunction_SLT_OnEnemyStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEnemyStateChanged_MetaData), NewProp_OnEnemyStateChanged_MetaData) }; // 3051504212
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyDamaged = { "OnEnemyDamaged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, OnEnemyDamaged), Z_Construct_UDelegateFunction_SLT_OnEnemyDamaged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEnemyDamaged_MetaData), NewProp_OnEnemyDamaged_MetaData) }; // 1469039499
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyDeath = { "OnEnemyDeath", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, OnEnemyDeath), Z_Construct_UDelegateFunction_SLT_OnEnemyDeath__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnEnemyDeath_MetaData), NewProp_OnEnemyDeath_MetaData) }; // 3887517867
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnPlayerDetected = { "OnPlayerDetected", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, OnPlayerDetected), Z_Construct_UDelegateFunction_SLT_OnPlayerDetected__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerDetected_MetaData), NewProp_OnPlayerDetected_MetaData) }; // 2421910984
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnPlayerLost = { "OnPlayerLost", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AEnemyCharacter, OnPlayerLost), Z_Construct_UDelegateFunction_SLT_OnPlayerLost__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlayerLost_MetaData), NewProp_OnPlayerLost_MetaData) }; // **********
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_AEnemyCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PawnSensingComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_HealthBarWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_AttackCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_BehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_SightRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_HearingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LosePlayerTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bIsDead,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_TargetPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LastKnownPlayerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolPoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolPoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_bUseRandomPatrol,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_PatrolWaitTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootTable_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootTable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_LootDropChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_EnemyTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyDamaged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnEnemyDeath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnPlayerDetected,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_AEnemyCharacter_Statics::NewProp_OnPlayerLost,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AEnemyCharacter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_AEnemyCharacter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_AEnemyCharacter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_AEnemyCharacter_Statics::ClassParams = {
	&AEnemyCharacter::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_AEnemyCharacter_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_AEnemyCharacter_Statics::PropPointers),
	0,
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_AEnemyCharacter_Statics::Class_MetaDataParams), Z_Construct_UClass_AEnemyCharacter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_AEnemyCharacter()
{
	if (!Z_Registration_Info_UClass_AEnemyCharacter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_AEnemyCharacter.OuterSingleton, Z_Construct_UClass_AEnemyCharacter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_AEnemyCharacter.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<AEnemyCharacter>()
{
	return AEnemyCharacter::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(AEnemyCharacter);
AEnemyCharacter::~AEnemyCharacter() {}
// End Class AEnemyCharacter

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EEnemyState_StaticEnum, TEXT("EEnemyState"), &Z_Registration_Info_UEnum_EEnemyState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2238520129U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_AEnemyCharacter, AEnemyCharacter::StaticClass, TEXT("AEnemyCharacter"), &Z_Registration_Info_UClass_AEnemyCharacter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(AEnemyCharacter), 3613909539U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_2001004599(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_Statics::ClassInfo),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
