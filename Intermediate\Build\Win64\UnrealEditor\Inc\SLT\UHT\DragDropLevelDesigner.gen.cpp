// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Design/DragDropLevelDesigner.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeDragDropLevelDesigner() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UDragDropLevelDesigner();
SLT_API UClass* Z_Construct_UClass_UDragDropLevelDesigner_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EDragDropCategory();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPlacementMode();
SLT_API UEnum* Z_Construct_UEnum_SLT_ESnapMode();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FDragDropAsset();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FLevelDesignStats();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPlacementSettings();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EDragDropCategory
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDragDropCategory;
static UEnum* EDragDropCategory_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDragDropCategory.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDragDropCategory.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EDragDropCategory, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EDragDropCategory"));
	}
	return Z_Registration_Info_UEnum_EDragDropCategory.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EDragDropCategory>()
{
	return EDragDropCategory_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EDragDropCategory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Audio.DisplayName", "Audio" },
		{ "Audio.Name", "EDragDropCategory::Audio" },
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EDragDropCategory::Custom" },
		{ "Decorative.DisplayName", "Decorative" },
		{ "Decorative.Name", "EDragDropCategory::Decorative" },
		{ "Enemies.DisplayName", "Enemies" },
		{ "Enemies.Name", "EDragDropCategory::Enemies" },
		{ "Environment.DisplayName", "Environment" },
		{ "Environment.Name", "EDragDropCategory::Environment" },
		{ "Interactive.DisplayName", "Interactive" },
		{ "Interactive.Name", "EDragDropCategory::Interactive" },
		{ "Items.DisplayName", "Items" },
		{ "Items.Name", "EDragDropCategory::Items" },
		{ "Lighting.DisplayName", "Lighting" },
		{ "Lighting.Name", "EDragDropCategory::Lighting" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
		{ "Puzzles.DisplayName", "Puzzles" },
		{ "Puzzles.Name", "EDragDropCategory::Puzzles" },
		{ "Spawners.DisplayName", "Spawners" },
		{ "Spawners.Name", "EDragDropCategory::Spawners" },
		{ "Triggers.DisplayName", "Triggers" },
		{ "Triggers.Name", "EDragDropCategory::Triggers" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDragDropCategory::Environment", (int64)EDragDropCategory::Environment },
		{ "EDragDropCategory::Interactive", (int64)EDragDropCategory::Interactive },
		{ "EDragDropCategory::Enemies", (int64)EDragDropCategory::Enemies },
		{ "EDragDropCategory::Items", (int64)EDragDropCategory::Items },
		{ "EDragDropCategory::Puzzles", (int64)EDragDropCategory::Puzzles },
		{ "EDragDropCategory::Triggers", (int64)EDragDropCategory::Triggers },
		{ "EDragDropCategory::Spawners", (int64)EDragDropCategory::Spawners },
		{ "EDragDropCategory::Decorative", (int64)EDragDropCategory::Decorative },
		{ "EDragDropCategory::Lighting", (int64)EDragDropCategory::Lighting },
		{ "EDragDropCategory::Audio", (int64)EDragDropCategory::Audio },
		{ "EDragDropCategory::Custom", (int64)EDragDropCategory::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EDragDropCategory_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EDragDropCategory",
	"EDragDropCategory",
	Z_Construct_UEnum_SLT_EDragDropCategory_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDragDropCategory_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDragDropCategory_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EDragDropCategory_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EDragDropCategory()
{
	if (!Z_Registration_Info_UEnum_EDragDropCategory.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDragDropCategory.InnerSingleton, Z_Construct_UEnum_SLT_EDragDropCategory_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDragDropCategory.InnerSingleton;
}
// End Enum EDragDropCategory

// Begin Enum ESnapMode
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESnapMode;
static UEnum* ESnapMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESnapMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESnapMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ESnapMode, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ESnapMode"));
	}
	return Z_Registration_Info_UEnum_ESnapMode.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ESnapMode>()
{
	return ESnapMode_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ESnapMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Grid.DisplayName", "Grid" },
		{ "Grid.Name", "ESnapMode::Grid" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "ESnapMode::None" },
		{ "Object.DisplayName", "Object" },
		{ "Object.Name", "ESnapMode::Object" },
		{ "Smart.DisplayName", "Smart" },
		{ "Smart.Name", "ESnapMode::Smart" },
		{ "Surface.DisplayName", "Surface" },
		{ "Surface.Name", "ESnapMode::Surface" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESnapMode::None", (int64)ESnapMode::None },
		{ "ESnapMode::Grid", (int64)ESnapMode::Grid },
		{ "ESnapMode::Surface", (int64)ESnapMode::Surface },
		{ "ESnapMode::Object", (int64)ESnapMode::Object },
		{ "ESnapMode::Smart", (int64)ESnapMode::Smart },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ESnapMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ESnapMode",
	"ESnapMode",
	Z_Construct_UEnum_SLT_ESnapMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESnapMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESnapMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ESnapMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ESnapMode()
{
	if (!Z_Registration_Info_UEnum_ESnapMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESnapMode.InnerSingleton, Z_Construct_UEnum_SLT_ESnapMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESnapMode.InnerSingleton;
}
// End Enum ESnapMode

// Begin Enum EPlacementMode
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPlacementMode;
static UEnum* EPlacementMode_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPlacementMode.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPlacementMode.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EPlacementMode, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EPlacementMode"));
	}
	return Z_Registration_Info_UEnum_EPlacementMode.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EPlacementMode>()
{
	return EPlacementMode_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EPlacementMode_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Circle.DisplayName", "Circle" },
		{ "Circle.Name", "EPlacementMode::Circle" },
		{ "Line.DisplayName", "Line" },
		{ "Line.Name", "EPlacementMode::Line" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
		{ "Paint.DisplayName", "Paint" },
		{ "Paint.Name", "EPlacementMode::Paint" },
		{ "Random.DisplayName", "Random" },
		{ "Random.Name", "EPlacementMode::Random" },
		{ "Rectangle.DisplayName", "Rectangle" },
		{ "Rectangle.Name", "EPlacementMode::Rectangle" },
		{ "Single.DisplayName", "Single" },
		{ "Single.Name", "EPlacementMode::Single" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPlacementMode::Single", (int64)EPlacementMode::Single },
		{ "EPlacementMode::Paint", (int64)EPlacementMode::Paint },
		{ "EPlacementMode::Line", (int64)EPlacementMode::Line },
		{ "EPlacementMode::Circle", (int64)EPlacementMode::Circle },
		{ "EPlacementMode::Rectangle", (int64)EPlacementMode::Rectangle },
		{ "EPlacementMode::Random", (int64)EPlacementMode::Random },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EPlacementMode_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EPlacementMode",
	"EPlacementMode",
	Z_Construct_UEnum_SLT_EPlacementMode_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPlacementMode_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPlacementMode_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EPlacementMode_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EPlacementMode()
{
	if (!Z_Registration_Info_UEnum_EPlacementMode.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPlacementMode.InnerSingleton, Z_Construct_UEnum_SLT_EPlacementMode_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPlacementMode.InnerSingleton;
}
// End Enum EPlacementMode

// Begin ScriptStruct FDragDropAsset
static_assert(std::is_polymorphic<FDragDropAsset>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FDragDropAsset cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_DragDropAsset;
class UScriptStruct* FDragDropAsset::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_DragDropAsset.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_DragDropAsset.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDragDropAsset, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("DragDropAsset"));
	}
	return Z_Registration_Info_UScriptStruct_DragDropAsset.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FDragDropAsset>()
{
	return FDragDropAsset::StaticStruct();
}
struct Z_Construct_UScriptStruct_FDragDropAsset_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetID_MetaData[] = {
		{ "Category", "Asset" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetName_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetDescription_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Asset" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuiltIn_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresSetup_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementCost_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxInstances_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewMesh_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual assets\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewIcon_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewColor_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "Category", "Spawning" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Spawning data\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Spawning data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultScale_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeRotation_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeScale_MetaData[] = {
		{ "Category", "Spawning" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreferredSnapMode_MetaData[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Placement rules\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDistanceFromOthers_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConflictingCategories_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tags and metadata\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags and metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchKeywords_MetaData[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProperties_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AssetName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AssetDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static void NewProp_bIsBuiltIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuiltIn;
	static void NewProp_bRequiresSetup_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresSetup;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlacementCost;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxInstances;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewIcon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PreviewColor;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultScale;
	static void NewProp_bRandomizeRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeRotation;
	static void NewProp_bRandomizeScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeScale;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PreferredSnapMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PreferredSnapMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDistanceFromOthers;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ConflictingCategories_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ConflictingCategories_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConflictingCategories;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchKeywords_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SearchKeywords;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProperties;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDragDropAsset>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, AssetID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetID_MetaData), NewProp_AssetID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetName = { "AssetName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, AssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetName_MetaData), NewProp_AssetName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetDescription = { "AssetDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, AssetDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetDescription_MetaData), NewProp_AssetDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, Category), Z_Construct_UEnum_SLT_EDragDropCategory, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 817066345
void Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bIsBuiltIn_SetBit(void* Obj)
{
	((FDragDropAsset*)Obj)->bIsBuiltIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bIsBuiltIn = { "bIsBuiltIn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDragDropAsset), &Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bIsBuiltIn_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuiltIn_MetaData), NewProp_bIsBuiltIn_MetaData) };
void Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRequiresSetup_SetBit(void* Obj)
{
	((FDragDropAsset*)Obj)->bRequiresSetup = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRequiresSetup = { "bRequiresSetup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDragDropAsset), &Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRequiresSetup_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresSetup_MetaData), NewProp_bRequiresSetup_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PlacementCost = { "PlacementCost", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, PlacementCost), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementCost_MetaData), NewProp_PlacementCost_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_MaxInstances = { "MaxInstances", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, MaxInstances), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxInstances_MetaData), NewProp_MaxInstances_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewMesh = { "PreviewMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, PreviewMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewMesh_MetaData), NewProp_PreviewMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewIcon = { "PreviewIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, PreviewIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewIcon_MetaData), NewProp_PreviewIcon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewColor = { "PreviewColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, PreviewColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewColor_MetaData), NewProp_PreviewColor_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_DefaultScale = { "DefaultScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, DefaultScale), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultScale_MetaData), NewProp_DefaultScale_MetaData) };
void Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeRotation_SetBit(void* Obj)
{
	((FDragDropAsset*)Obj)->bRandomizeRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeRotation = { "bRandomizeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDragDropAsset), &Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeRotation_MetaData), NewProp_bRandomizeRotation_MetaData) };
void Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeScale_SetBit(void* Obj)
{
	((FDragDropAsset*)Obj)->bRandomizeScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeScale = { "bRandomizeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDragDropAsset), &Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeScale_MetaData), NewProp_bRandomizeScale_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreferredSnapMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreferredSnapMode = { "PreferredSnapMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, PreferredSnapMode), Z_Construct_UEnum_SLT_ESnapMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreferredSnapMode_MetaData), NewProp_PreferredSnapMode_MetaData) }; // 4106927359
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_MinDistanceFromOthers = { "MinDistanceFromOthers", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, MinDistanceFromOthers), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDistanceFromOthers_MetaData), NewProp_MinDistanceFromOthers_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories_Inner = { "ConflictingCategories", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EDragDropCategory, METADATA_PARAMS(0, nullptr) }; // 817066345
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories = { "ConflictingCategories", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, ConflictingCategories), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConflictingCategories_MetaData), NewProp_ConflictingCategories_MetaData) }; // 817066345
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetTags = { "AssetTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, AssetTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetTags_MetaData), NewProp_AssetTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_SearchKeywords_Inner = { "SearchKeywords", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_SearchKeywords = { "SearchKeywords", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, SearchKeywords), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchKeywords_MetaData), NewProp_SearchKeywords_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties_ValueProp = { "CustomProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties_Key_KeyProp = { "CustomProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties = { "CustomProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDragDropAsset, CustomProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProperties_MetaData), NewProp_CustomProperties_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDragDropAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bIsBuiltIn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRequiresSetup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PlacementCost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_MaxInstances,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreviewColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_DefaultScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_bRandomizeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreferredSnapMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_PreferredSnapMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_MinDistanceFromOthers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_ConflictingCategories,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_AssetTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_SearchKeywords_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_SearchKeywords,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewProp_CustomProperties,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragDropAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDragDropAsset_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"DragDropAsset",
	Z_Construct_UScriptStruct_FDragDropAsset_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragDropAsset_Statics::PropPointers),
	sizeof(FDragDropAsset),
	alignof(FDragDropAsset),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDragDropAsset_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDragDropAsset_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDragDropAsset()
{
	if (!Z_Registration_Info_UScriptStruct_DragDropAsset.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_DragDropAsset.InnerSingleton, Z_Construct_UScriptStruct_FDragDropAsset_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_DragDropAsset.InnerSingleton;
}
// End ScriptStruct FDragDropAsset

// Begin ScriptStruct FPlacementSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PlacementSettings;
class UScriptStruct* FPlacementSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PlacementSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PlacementSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlacementSettings, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PlacementSettings"));
	}
	return Z_Registration_Info_UScriptStruct_PlacementSettings.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPlacementSettings>()
{
	return FPlacementSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPlacementSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementMode_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SnapMode_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridSize_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaintRadius_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PaintDensity_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAlignToSurface_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeRotation_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRandomizeScale_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScaleVariation_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotationVariation_MetaData[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_PlacementMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PlacementMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_SnapMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SnapMode;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PaintRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PaintDensity;
	static void NewProp_bAlignToSurface_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAlignToSurface;
	static void NewProp_bRandomizeRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeRotation;
	static void NewProp_bRandomizeScale_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRandomizeScale;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScaleVariation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RotationVariation;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlacementSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PlacementMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PlacementMode = { "PlacementMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, PlacementMode), Z_Construct_UEnum_SLT_EPlacementMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementMode_MetaData), NewProp_PlacementMode_MetaData) }; // 4198537735
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_SnapMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_SnapMode = { "SnapMode", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, SnapMode), Z_Construct_UEnum_SLT_ESnapMode, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SnapMode_MetaData), NewProp_SnapMode_MetaData) }; // 4106927359
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, GridSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridSize_MetaData), NewProp_GridSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PaintRadius = { "PaintRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, PaintRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaintRadius_MetaData), NewProp_PaintRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PaintDensity = { "PaintDensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, PaintDensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PaintDensity_MetaData), NewProp_PaintDensity_MetaData) };
void Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bAlignToSurface_SetBit(void* Obj)
{
	((FPlacementSettings*)Obj)->bAlignToSurface = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bAlignToSurface = { "bAlignToSurface", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlacementSettings), &Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bAlignToSurface_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAlignToSurface_MetaData), NewProp_bAlignToSurface_MetaData) };
void Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeRotation_SetBit(void* Obj)
{
	((FPlacementSettings*)Obj)->bRandomizeRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeRotation = { "bRandomizeRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlacementSettings), &Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeRotation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeRotation_MetaData), NewProp_bRandomizeRotation_MetaData) };
void Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeScale_SetBit(void* Obj)
{
	((FPlacementSettings*)Obj)->bRandomizeScale = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeScale = { "bRandomizeScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlacementSettings), &Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeScale_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRandomizeScale_MetaData), NewProp_bRandomizeScale_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_ScaleVariation = { "ScaleVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, ScaleVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScaleVariation_MetaData), NewProp_ScaleVariation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_RotationVariation = { "RotationVariation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlacementSettings, RotationVariation), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotationVariation_MetaData), NewProp_RotationVariation_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlacementSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PlacementMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PlacementMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_SnapMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_SnapMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_GridSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PaintRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_PaintDensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bAlignToSurface,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_bRandomizeScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_ScaleVariation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewProp_RotationVariation,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlacementSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlacementSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PlacementSettings",
	Z_Construct_UScriptStruct_FPlacementSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlacementSettings_Statics::PropPointers),
	sizeof(FPlacementSettings),
	alignof(FPlacementSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlacementSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlacementSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlacementSettings()
{
	if (!Z_Registration_Info_UScriptStruct_PlacementSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PlacementSettings.InnerSingleton, Z_Construct_UScriptStruct_FPlacementSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PlacementSettings.InnerSingleton;
}
// End ScriptStruct FPlacementSettings

// Begin ScriptStruct FLevelDesignStats
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LevelDesignStats;
class UScriptStruct* FLevelDesignStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LevelDesignStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LevelDesignStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLevelDesignStats, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("LevelDesignStats"));
	}
	return Z_Registration_Info_UScriptStruct_LevelDesignStats.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FLevelDesignStats>()
{
	return FLevelDesignStats::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLevelDesignStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalActorsPlaced_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorsByCategory_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignTime_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastModified_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecentActions_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalActorsPlaced;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ActorsByCategory_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActorsByCategory_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActorsByCategory_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActorsByCategory;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DesignTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastModified;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RecentActions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RecentActions;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLevelDesignStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_TotalActorsPlaced = { "TotalActorsPlaced", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelDesignStats, TotalActorsPlaced), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalActorsPlaced_MetaData), NewProp_TotalActorsPlaced_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_ValueProp = { "ActorsByCategory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_Key_KeyProp = { "ActorsByCategory_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EDragDropCategory, METADATA_PARAMS(0, nullptr) }; // 817066345
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory = { "ActorsByCategory", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelDesignStats, ActorsByCategory), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorsByCategory_MetaData), NewProp_ActorsByCategory_MetaData) }; // 817066345
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_DesignTime = { "DesignTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelDesignStats, DesignTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignTime_MetaData), NewProp_DesignTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_LastModified = { "LastModified", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelDesignStats, LastModified), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastModified_MetaData), NewProp_LastModified_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_RecentActions_Inner = { "RecentActions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_RecentActions = { "RecentActions", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelDesignStats, RecentActions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecentActions_MetaData), NewProp_RecentActions_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLevelDesignStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_TotalActorsPlaced,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_ActorsByCategory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_DesignTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_LastModified,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_RecentActions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewProp_RecentActions,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelDesignStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLevelDesignStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"LevelDesignStats",
	Z_Construct_UScriptStruct_FLevelDesignStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelDesignStats_Statics::PropPointers),
	sizeof(FLevelDesignStats),
	alignof(FLevelDesignStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelDesignStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLevelDesignStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLevelDesignStats()
{
	if (!Z_Registration_Info_UScriptStruct_LevelDesignStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LevelDesignStats.InnerSingleton, Z_Construct_UScriptStruct_FLevelDesignStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LevelDesignStats.InnerSingleton;
}
// End ScriptStruct FLevelDesignStats

// Begin Delegate FOnActorPlaced
struct Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnActorPlaced_Parms
	{
		AActor* PlacedActor;
		FName AssetID;
		FVector Location;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlacedActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_PlacedActor = { "PlacedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorPlaced_Parms, PlacedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorPlaced_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorPlaced_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_PlacedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::NewProp_Location,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnActorPlaced__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::_Script_SLT_eventOnActorPlaced_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::_Script_SLT_eventOnActorPlaced_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnActorPlaced_DelegateWrapper(const FMulticastScriptDelegate& OnActorPlaced, AActor* PlacedActor, FName AssetID, FVector const& Location)
{
	struct _Script_SLT_eventOnActorPlaced_Parms
	{
		AActor* PlacedActor;
		FName AssetID;
		FVector Location;
	};
	_Script_SLT_eventOnActorPlaced_Parms Parms;
	Parms.PlacedActor=PlacedActor;
	Parms.AssetID=AssetID;
	Parms.Location=Location;
	OnActorPlaced.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnActorPlaced

// Begin Delegate FOnActorRemoved
struct Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnActorRemoved_Parms
	{
		AActor* RemovedActor;
		FName AssetID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RemovedActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::NewProp_RemovedActor = { "RemovedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorRemoved_Parms, RemovedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorRemoved_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::NewProp_RemovedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::NewProp_AssetID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnActorRemoved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::_Script_SLT_eventOnActorRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::_Script_SLT_eventOnActorRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnActorRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorRemoved, AActor* RemovedActor, FName AssetID)
{
	struct _Script_SLT_eventOnActorRemoved_Parms
	{
		AActor* RemovedActor;
		FName AssetID;
	};
	_Script_SLT_eventOnActorRemoved_Parms Parms;
	Parms.RemovedActor=RemovedActor;
	Parms.AssetID=AssetID;
	OnActorRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnActorRemoved

// Begin Delegate FOnSelectionChanged
struct Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnSelectionChanged_Parms
	{
		TArray<AActor*> SelectedActors;
		bool bMultiSelect;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedActors_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedActors;
	static void NewProp_bMultiSelect_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bMultiSelect;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_SelectedActors_Inner = { "SelectedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_SelectedActors = { "SelectedActors", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSelectionChanged_Parms, SelectedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedActors_MetaData), NewProp_SelectedActors_MetaData) };
void Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_bMultiSelect_SetBit(void* Obj)
{
	((_Script_SLT_eventOnSelectionChanged_Parms*)Obj)->bMultiSelect = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_bMultiSelect = { "bMultiSelect", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnSelectionChanged_Parms), &Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_bMultiSelect_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_SelectedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_SelectedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::NewProp_bMultiSelect,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnSelectionChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::_Script_SLT_eventOnSelectionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::_Script_SLT_eventOnSelectionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSelectionChanged, TArray<AActor*> const& SelectedActors, bool bMultiSelect)
{
	struct _Script_SLT_eventOnSelectionChanged_Parms
	{
		TArray<AActor*> SelectedActors;
		bool bMultiSelect;
	};
	_Script_SLT_eventOnSelectionChanged_Parms Parms;
	Parms.SelectedActors=SelectedActors;
	Parms.bMultiSelect=bMultiSelect ? true : false;
	OnSelectionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSelectionChanged

// Begin Delegate FOnPlacementModeChanged
struct Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPlacementModeChanged_Parms
	{
		EPlacementMode OldMode;
		EPlacementMode NewMode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldMode;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_OldMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_OldMode = { "OldMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPlacementModeChanged_Parms, OldMode), Z_Construct_UEnum_SLT_EPlacementMode, METADATA_PARAMS(0, nullptr) }; // 4198537735
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_NewMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_NewMode = { "NewMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPlacementModeChanged_Parms, NewMode), Z_Construct_UEnum_SLT_EPlacementMode, METADATA_PARAMS(0, nullptr) }; // 4198537735
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_OldMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_OldMode,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_NewMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::NewProp_NewMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPlacementModeChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::_Script_SLT_eventOnPlacementModeChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::_Script_SLT_eventOnPlacementModeChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPlacementModeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementModeChanged, EPlacementMode OldMode, EPlacementMode NewMode)
{
	struct _Script_SLT_eventOnPlacementModeChanged_Parms
	{
		EPlacementMode OldMode;
		EPlacementMode NewMode;
	};
	_Script_SLT_eventOnPlacementModeChanged_Parms Parms;
	Parms.OldMode=OldMode;
	Parms.NewMode=NewMode;
	OnPlacementModeChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPlacementModeChanged

// Begin Class UDragDropLevelDesigner Function CanPlaceAsset
struct Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics
{
	struct DragDropLevelDesigner_eventCanPlaceAsset_Parms
	{
		FName AssetID;
		FVector Location;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventCanPlaceAsset_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventCanPlaceAsset_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
void Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventCanPlaceAsset_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventCanPlaceAsset_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "CanPlaceAsset", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::DragDropLevelDesigner_eventCanPlaceAsset_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::DragDropLevelDesigner_eventCanPlaceAsset_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execCanPlaceAsset)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AssetID);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPlaceAsset(Z_Param_AssetID,Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function CanPlaceAsset

// Begin Class UDragDropLevelDesigner Function CanRedo
struct Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics
{
	struct DragDropLevelDesigner_eventCanRedo_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Undo" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventCanRedo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventCanRedo_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "CanRedo", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::DragDropLevelDesigner_eventCanRedo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::DragDropLevelDesigner_eventCanRedo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execCanRedo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanRedo();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function CanRedo

// Begin Class UDragDropLevelDesigner Function CanUndo
struct Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics
{
	struct DragDropLevelDesigner_eventCanUndo_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Undo" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventCanUndo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventCanUndo_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "CanUndo", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::DragDropLevelDesigner_eventCanUndo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::DragDropLevelDesigner_eventCanUndo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execCanUndo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUndo();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function CanUndo

// Begin Class UDragDropLevelDesigner Function ClearLevel
struct Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "ClearLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execClearLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearLevel();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function ClearLevel

// Begin Class UDragDropLevelDesigner Function ClearSelection
struct Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "ClearSelection", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execClearSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearSelection();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function ClearSelection

// Begin Class UDragDropLevelDesigner Function DuplicateSelectedActors
struct Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "DuplicateSelectedActors", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execDuplicateSelectedActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DuplicateSelectedActors();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function DuplicateSelectedActors

// Begin Class UDragDropLevelDesigner Function GetAssetData
struct Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics
{
	struct DragDropLevelDesigner_eventGetAssetData_Parms
	{
		FName AssetID;
		FDragDropAsset ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventGetAssetData_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventGetAssetData_Parms, ReturnValue), Z_Construct_UScriptStruct_FDragDropAsset, METADATA_PARAMS(0, nullptr) }; // 3463544237
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "GetAssetData", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::DragDropLevelDesigner_eventGetAssetData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::DragDropLevelDesigner_eventGetAssetData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execGetAssetData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AssetID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FDragDropAsset*)Z_Param__Result=P_THIS->GetAssetData(Z_Param_AssetID);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function GetAssetData

// Begin Class UDragDropLevelDesigner Function GetAssetsByCategory
struct Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics
{
	struct DragDropLevelDesigner_eventGetAssetsByCategory_Parms
	{
		EDragDropCategory Category;
		TArray<FDragDropAsset> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventGetAssetsByCategory_Parms, Category), Z_Construct_UEnum_SLT_EDragDropCategory, METADATA_PARAMS(0, nullptr) }; // 817066345
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDragDropAsset, METADATA_PARAMS(0, nullptr) }; // 3463544237
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventGetAssetsByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3463544237
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "GetAssetsByCategory", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::DragDropLevelDesigner_eventGetAssetsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::DragDropLevelDesigner_eventGetAssetsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execGetAssetsByCategory)
{
	P_GET_ENUM(EDragDropCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FDragDropAsset>*)Z_Param__Result=P_THIS->GetAssetsByCategory(EDragDropCategory(Z_Param_Category));
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function GetAssetsByCategory

// Begin Class UDragDropLevelDesigner Function GetDesignStatistics
struct Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics
{
	struct DragDropLevelDesigner_eventGetDesignStatistics_Parms
	{
		FLevelDesignStats ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventGetDesignStatistics_Parms, ReturnValue), Z_Construct_UScriptStruct_FLevelDesignStats, METADATA_PARAMS(0, nullptr) }; // 1591124716
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "GetDesignStatistics", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::DragDropLevelDesigner_eventGetDesignStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::DragDropLevelDesigner_eventGetDesignStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execGetDesignStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLevelDesignStats*)Z_Param__Result=P_THIS->GetDesignStatistics();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function GetDesignStatistics

// Begin Class UDragDropLevelDesigner Function GroupSelectedActors
struct Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "GroupSelectedActors", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execGroupSelectedActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GroupSelectedActors();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function GroupSelectedActors

// Begin Class UDragDropLevelDesigner Function InvertSelection
struct Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "InvertSelection", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execInvertSelection)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->InvertSelection();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function InvertSelection

// Begin Class UDragDropLevelDesigner Function LoadLevelLayout
struct Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics
{
	struct DragDropLevelDesigner_eventLoadLevelLayout_Parms
	{
		FString LayoutName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayoutName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayoutName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_LayoutName = { "LayoutName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventLoadLevelLayout_Parms, LayoutName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayoutName_MetaData), NewProp_LayoutName_MetaData) };
void Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventLoadLevelLayout_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventLoadLevelLayout_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_LayoutName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "LoadLevelLayout", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::DragDropLevelDesigner_eventLoadLevelLayout_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::DragDropLevelDesigner_eventLoadLevelLayout_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execLoadLevelLayout)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayoutName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLevelLayout(Z_Param_LayoutName);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function LoadLevelLayout

// Begin Class UDragDropLevelDesigner Function OnAssetSelected
struct DragDropLevelDesigner_eventOnAssetSelected_Parms
{
	FName AssetID;
	FDragDropAsset AssetData;
};
static const FName NAME_UDragDropLevelDesigner_OnAssetSelected = FName(TEXT("OnAssetSelected"));
void UDragDropLevelDesigner::OnAssetSelected(FName AssetID, FDragDropAsset const& AssetData)
{
	DragDropLevelDesigner_eventOnAssetSelected_Parms Parms;
	Parms.AssetID=AssetID;
	Parms.AssetData=AssetData;
	UFunction* Func = FindFunctionChecked(NAME_UDragDropLevelDesigner_OnAssetSelected);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventOnAssetSelected_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::NewProp_AssetData = { "AssetData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventOnAssetSelected_Parms, AssetData), Z_Construct_UScriptStruct_FDragDropAsset, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetData_MetaData), NewProp_AssetData_MetaData) }; // 3463544237
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::NewProp_AssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::NewProp_AssetData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "OnAssetSelected", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::PropPointers), sizeof(DragDropLevelDesigner_eventOnAssetSelected_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::Function_MetaDataParams) };
static_assert(sizeof(DragDropLevelDesigner_eventOnAssetSelected_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UDragDropLevelDesigner Function OnAssetSelected

// Begin Class UDragDropLevelDesigner Function OnDesignModeToggled
struct DragDropLevelDesigner_eventOnDesignModeToggled_Parms
{
	bool bInIsActive;
};
static const FName NAME_UDragDropLevelDesigner_OnDesignModeToggled = FName(TEXT("OnDesignModeToggled"));
void UDragDropLevelDesigner::OnDesignModeToggled(bool bInIsActive)
{
	DragDropLevelDesigner_eventOnDesignModeToggled_Parms Parms;
	Parms.bInIsActive=bInIsActive ? true : false;
	UFunction* Func = FindFunctionChecked(NAME_UDragDropLevelDesigner_OnDesignModeToggled);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bInIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bInIsActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::NewProp_bInIsActive_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventOnDesignModeToggled_Parms*)Obj)->bInIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::NewProp_bInIsActive = { "bInIsActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventOnDesignModeToggled_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::NewProp_bInIsActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::NewProp_bInIsActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "OnDesignModeToggled", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::PropPointers), sizeof(DragDropLevelDesigner_eventOnDesignModeToggled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::Function_MetaDataParams) };
static_assert(sizeof(DragDropLevelDesigner_eventOnDesignModeToggled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UDragDropLevelDesigner Function OnDesignModeToggled

// Begin Class UDragDropLevelDesigner Function OnLevelLayoutChanged
static const FName NAME_UDragDropLevelDesigner_OnLevelLayoutChanged = FName(TEXT("OnLevelLayoutChanged"));
void UDragDropLevelDesigner::OnLevelLayoutChanged()
{
	UFunction* Func = FindFunctionChecked(NAME_UDragDropLevelDesigner_OnLevelLayoutChanged);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "OnLevelLayoutChanged", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UDragDropLevelDesigner Function OnLevelLayoutChanged

// Begin Class UDragDropLevelDesigner Function OptimizeLevel
struct Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "OptimizeLevel", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execOptimizeLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeLevel();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function OptimizeLevel

// Begin Class UDragDropLevelDesigner Function PlaceActorAtLocation
struct Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics
{
	struct DragDropLevelDesigner_eventPlaceActorAtLocation_Parms
	{
		FVector Location;
		FRotator Rotation;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "CPP_Default_Rotation", "" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventPlaceActorAtLocation_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventPlaceActorAtLocation_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventPlaceActorAtLocation_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "PlaceActorAtLocation", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::DragDropLevelDesigner_eventPlaceActorAtLocation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::DragDropLevelDesigner_eventPlaceActorAtLocation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execPlaceActorAtLocation)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->PlaceActorAtLocation(Z_Param_Out_Location,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function PlaceActorAtLocation

// Begin Class UDragDropLevelDesigner Function Redo
struct Z_Construct_UFunction_UDragDropLevelDesigner_Redo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Undo" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_Redo_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "Redo", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_Redo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_Redo_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_Redo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_Redo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execRedo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Redo();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function Redo

// Begin Class UDragDropLevelDesigner Function RemoveSelectedActors
struct Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "RemoveSelectedActors", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execRemoveSelectedActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveSelectedActors();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function RemoveSelectedActors

// Begin Class UDragDropLevelDesigner Function SaveLevelLayout
struct Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics
{
	struct DragDropLevelDesigner_eventSaveLevelLayout_Parms
	{
		FString LayoutName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LayoutName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LayoutName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::NewProp_LayoutName = { "LayoutName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSaveLevelLayout_Parms, LayoutName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LayoutName_MetaData), NewProp_LayoutName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::NewProp_LayoutName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SaveLevelLayout", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::DragDropLevelDesigner_eventSaveLevelLayout_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::DragDropLevelDesigner_eventSaveLevelLayout_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSaveLevelLayout)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LayoutName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SaveLevelLayout(Z_Param_LayoutName);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SaveLevelLayout

// Begin Class UDragDropLevelDesigner Function SearchAssets
struct Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics
{
	struct DragDropLevelDesigner_eventSearchAssets_Parms
	{
		FString SearchTerm;
		TArray<FDragDropAsset> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchTerm_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchTerm;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_SearchTerm = { "SearchTerm", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSearchAssets_Parms, SearchTerm), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchTerm_MetaData), NewProp_SearchTerm_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDragDropAsset, METADATA_PARAMS(0, nullptr) }; // 3463544237
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSearchAssets_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3463544237
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_SearchTerm,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SearchAssets", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::DragDropLevelDesigner_eventSearchAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::DragDropLevelDesigner_eventSearchAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSearchAssets)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SearchTerm);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FDragDropAsset>*)Z_Param__Result=P_THIS->SearchAssets(Z_Param_SearchTerm);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SearchAssets

// Begin Class UDragDropLevelDesigner Function SelectActor
struct Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics
{
	struct DragDropLevelDesigner_eventSelectActor_Parms
	{
		AActor* Actor;
		bool bAddToSelection;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Selection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Selection functions\n" },
#endif
		{ "CPP_Default_bAddToSelection", "false" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Selection functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bAddToSelection_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAddToSelection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSelectActor_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_bAddToSelection_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventSelectActor_Parms*)Obj)->bAddToSelection = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_bAddToSelection = { "bAddToSelection", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventSelectActor_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_bAddToSelection_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::NewProp_bAddToSelection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SelectActor", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::DragDropLevelDesigner_eventSelectActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::DragDropLevelDesigner_eventSelectActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSelectActor)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_GET_UBOOL(Z_Param_bAddToSelection);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SelectActor(Z_Param_Actor,Z_Param_bAddToSelection);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SelectActor

// Begin Class UDragDropLevelDesigner Function SelectActorsByCategory
struct Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics
{
	struct DragDropLevelDesigner_eventSelectActorsByCategory_Parms
	{
		EDragDropCategory Category;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Category_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Category;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::NewProp_Category_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSelectActorsByCategory_Parms, Category), Z_Construct_UEnum_SLT_EDragDropCategory, METADATA_PARAMS(0, nullptr) }; // 817066345
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::NewProp_Category_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::NewProp_Category,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SelectActorsByCategory", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::DragDropLevelDesigner_eventSelectActorsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::DragDropLevelDesigner_eventSelectActorsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSelectActorsByCategory)
{
	P_GET_ENUM(EDragDropCategory,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SelectActorsByCategory(EDragDropCategory(Z_Param_Category));
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SelectActorsByCategory

// Begin Class UDragDropLevelDesigner Function SelectActorsInArea
struct Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics
{
	struct DragDropLevelDesigner_eventSelectActorsInArea_Parms
	{
		FVector Center;
		float Radius;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Selection" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Center_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Center;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Radius;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::NewProp_Center = { "Center", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSelectActorsInArea_Parms, Center), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Center_MetaData), NewProp_Center_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::NewProp_Radius = { "Radius", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSelectActorsInArea_Parms, Radius), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::NewProp_Center,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::NewProp_Radius,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SelectActorsInArea", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::DragDropLevelDesigner_eventSelectActorsInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::DragDropLevelDesigner_eventSelectActorsInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSelectActorsInArea)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Center);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Radius);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SelectActorsInArea(Z_Param_Out_Center,Z_Param_Radius);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SelectActorsInArea

// Begin Class UDragDropLevelDesigner Function SelectAssetForPlacement
struct Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics
{
	struct DragDropLevelDesigner_eventSelectAssetForPlacement_Parms
	{
		FName AssetID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AssetID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::NewProp_AssetID = { "AssetID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSelectAssetForPlacement_Parms, AssetID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::NewProp_AssetID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SelectAssetForPlacement", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::DragDropLevelDesigner_eventSelectAssetForPlacement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::DragDropLevelDesigner_eventSelectAssetForPlacement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSelectAssetForPlacement)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AssetID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SelectAssetForPlacement(Z_Param_AssetID);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SelectAssetForPlacement

// Begin Class UDragDropLevelDesigner Function SetDesignModeActive
struct Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics
{
	struct DragDropLevelDesigner_eventSetDesignModeActive_Parms
	{
		bool bActive;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main design functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main design functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bActive;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::NewProp_bActive_SetBit(void* Obj)
{
	((DragDropLevelDesigner_eventSetDesignModeActive_Parms*)Obj)->bActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::NewProp_bActive = { "bActive", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(DragDropLevelDesigner_eventSetDesignModeActive_Parms), &Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::NewProp_bActive_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::NewProp_bActive,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SetDesignModeActive", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::DragDropLevelDesigner_eventSetDesignModeActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::DragDropLevelDesigner_eventSetDesignModeActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSetDesignModeActive)
{
	P_GET_UBOOL(Z_Param_bActive);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDesignModeActive(Z_Param_bActive);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SetDesignModeActive

// Begin Class UDragDropLevelDesigner Function SetPlacementMode
struct Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics
{
	struct DragDropLevelDesigner_eventSetPlacementMode_Parms
	{
		EPlacementMode NewMode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Placement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Placement mode functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Placement mode functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::NewProp_NewMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::NewProp_NewMode = { "NewMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSetPlacementMode_Parms, NewMode), Z_Construct_UEnum_SLT_EPlacementMode, METADATA_PARAMS(0, nullptr) }; // 4198537735
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::NewProp_NewMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::NewProp_NewMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SetPlacementMode", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::DragDropLevelDesigner_eventSetPlacementMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::DragDropLevelDesigner_eventSetPlacementMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSetPlacementMode)
{
	P_GET_ENUM(EPlacementMode,Z_Param_NewMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlacementMode(EPlacementMode(Z_Param_NewMode));
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SetPlacementMode

// Begin Class UDragDropLevelDesigner Function SetSnapMode
struct Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics
{
	struct DragDropLevelDesigner_eventSetSnapMode_Parms
	{
		ESnapMode NewMode;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewMode_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewMode;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::NewProp_NewMode_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::NewProp_NewMode = { "NewMode", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSetSnapMode_Parms, NewMode), Z_Construct_UEnum_SLT_ESnapMode, METADATA_PARAMS(0, nullptr) }; // 4106927359
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::NewProp_NewMode_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::NewProp_NewMode,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SetSnapMode", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::DragDropLevelDesigner_eventSetSnapMode_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::DragDropLevelDesigner_eventSetSnapMode_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSetSnapMode)
{
	P_GET_ENUM(ESnapMode,Z_Param_NewMode);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetSnapMode(ESnapMode(Z_Param_NewMode));
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SetSnapMode

// Begin Class UDragDropLevelDesigner Function SnapLocationToGrid
struct Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics
{
	struct DragDropLevelDesigner_eventSnapLocationToGrid_Parms
	{
		FVector Location;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSnapLocationToGrid_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSnapLocationToGrid_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SnapLocationToGrid", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::DragDropLevelDesigner_eventSnapLocationToGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::DragDropLevelDesigner_eventSnapLocationToGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSnapLocationToGrid)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->SnapLocationToGrid(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SnapLocationToGrid

// Begin Class UDragDropLevelDesigner Function SnapLocationToSurface
struct Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics
{
	struct DragDropLevelDesigner_eventSnapLocationToSurface_Parms
	{
		FVector Location;
		FVector ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Placement" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSnapLocationToSurface_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(DragDropLevelDesigner_eventSnapLocationToSurface_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "SnapLocationToSurface", nullptr, nullptr, Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::PropPointers), sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::DragDropLevelDesigner_eventSnapLocationToSurface_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::DragDropLevelDesigner_eventSnapLocationToSurface_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execSnapLocationToSurface)
{
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector*)Z_Param__Result=P_THIS->SnapLocationToSurface(Z_Param_Out_Location);
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function SnapLocationToSurface

// Begin Class UDragDropLevelDesigner Function Undo
struct Z_Construct_UFunction_UDragDropLevelDesigner_Undo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Undo" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Undo/Redo functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Undo/Redo functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_Undo_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "Undo", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_Undo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_Undo_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_Undo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_Undo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execUndo)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->Undo();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function Undo

// Begin Class UDragDropLevelDesigner Function UngroupSelectedActors
struct Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Level Design" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UDragDropLevelDesigner, nullptr, "UngroupSelectedActors", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UDragDropLevelDesigner::execUngroupSelectedActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UngroupSelectedActors();
	P_NATIVE_END;
}
// End Class UDragDropLevelDesigner Function UngroupSelectedActors

// Begin Class UDragDropLevelDesigner
void UDragDropLevelDesigner::StaticRegisterNativesUDragDropLevelDesigner()
{
	UClass* Class = UDragDropLevelDesigner::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanPlaceAsset", &UDragDropLevelDesigner::execCanPlaceAsset },
		{ "CanRedo", &UDragDropLevelDesigner::execCanRedo },
		{ "CanUndo", &UDragDropLevelDesigner::execCanUndo },
		{ "ClearLevel", &UDragDropLevelDesigner::execClearLevel },
		{ "ClearSelection", &UDragDropLevelDesigner::execClearSelection },
		{ "DuplicateSelectedActors", &UDragDropLevelDesigner::execDuplicateSelectedActors },
		{ "GetAssetData", &UDragDropLevelDesigner::execGetAssetData },
		{ "GetAssetsByCategory", &UDragDropLevelDesigner::execGetAssetsByCategory },
		{ "GetDesignStatistics", &UDragDropLevelDesigner::execGetDesignStatistics },
		{ "GroupSelectedActors", &UDragDropLevelDesigner::execGroupSelectedActors },
		{ "InvertSelection", &UDragDropLevelDesigner::execInvertSelection },
		{ "LoadLevelLayout", &UDragDropLevelDesigner::execLoadLevelLayout },
		{ "OptimizeLevel", &UDragDropLevelDesigner::execOptimizeLevel },
		{ "PlaceActorAtLocation", &UDragDropLevelDesigner::execPlaceActorAtLocation },
		{ "Redo", &UDragDropLevelDesigner::execRedo },
		{ "RemoveSelectedActors", &UDragDropLevelDesigner::execRemoveSelectedActors },
		{ "SaveLevelLayout", &UDragDropLevelDesigner::execSaveLevelLayout },
		{ "SearchAssets", &UDragDropLevelDesigner::execSearchAssets },
		{ "SelectActor", &UDragDropLevelDesigner::execSelectActor },
		{ "SelectActorsByCategory", &UDragDropLevelDesigner::execSelectActorsByCategory },
		{ "SelectActorsInArea", &UDragDropLevelDesigner::execSelectActorsInArea },
		{ "SelectAssetForPlacement", &UDragDropLevelDesigner::execSelectAssetForPlacement },
		{ "SetDesignModeActive", &UDragDropLevelDesigner::execSetDesignModeActive },
		{ "SetPlacementMode", &UDragDropLevelDesigner::execSetPlacementMode },
		{ "SetSnapMode", &UDragDropLevelDesigner::execSetSnapMode },
		{ "SnapLocationToGrid", &UDragDropLevelDesigner::execSnapLocationToGrid },
		{ "SnapLocationToSurface", &UDragDropLevelDesigner::execSnapLocationToSurface },
		{ "Undo", &UDragDropLevelDesigner::execUndo },
		{ "UngroupSelectedActors", &UDragDropLevelDesigner::execUngroupSelectedActors },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UDragDropLevelDesigner);
UClass* Z_Construct_UClass_UDragDropLevelDesigner_NoRegister()
{
	return UDragDropLevelDesigner::StaticClass();
}
struct Z_Construct_UClass_UDragDropLevelDesigner_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "Core/Design/DragDropLevelDesigner.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetDatabase_MetaData[] = {
		{ "Category", "Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset database\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset database" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacementSettings_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bDesignModeActive_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowPreview_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowGrid_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableUndo_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAssetID_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SelectedActors_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignStats_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorPlaced_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSelectionChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPlacementModeChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedAssetDatabase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlacedActors_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewActor_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UndoStack_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Undo/Redo system\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Undo/Redo system" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RedoStack_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxUndoSteps_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/DragDropLevelDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AssetDatabase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlacementSettings;
	static void NewProp_bDesignModeActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bDesignModeActive;
	static void NewProp_bShowPreview_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowPreview;
	static void NewProp_bShowGrid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowGrid;
	static void NewProp_bEnableUndo_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableUndo;
	static const UECodeGen_Private::FNamePropertyParams NewProp_CurrentAssetID;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SelectedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SelectedActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesignStats;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorPlaced;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSelectionChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPlacementModeChanged;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedAssetDatabase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlacedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PlacedActors;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PreviewActor;
	static const UECodeGen_Private::FStrPropertyParams NewProp_UndoStack_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UndoStack;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RedoStack_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RedoStack;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxUndoSteps;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_CanPlaceAsset, "CanPlaceAsset" }, // 2837656063
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_CanRedo, "CanRedo" }, // 2249521247
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_CanUndo, "CanUndo" }, // 2771744607
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_ClearLevel, "ClearLevel" }, // 1850445505
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_ClearSelection, "ClearSelection" }, // 2166115447
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_DuplicateSelectedActors, "DuplicateSelectedActors" }, // 3377573466
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetData, "GetAssetData" }, // 3252472663
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_GetAssetsByCategory, "GetAssetsByCategory" }, // 3642969323
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_GetDesignStatistics, "GetDesignStatistics" }, // 264034326
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_GroupSelectedActors, "GroupSelectedActors" }, // 3109972818
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_InvertSelection, "InvertSelection" }, // 2215572719
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_LoadLevelLayout, "LoadLevelLayout" }, // 2053710919
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_OnAssetSelected, "OnAssetSelected" }, // 237358620
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_OnDesignModeToggled, "OnDesignModeToggled" }, // 285675981
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_OnLevelLayoutChanged, "OnLevelLayoutChanged" }, // 303308014
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_OptimizeLevel, "OptimizeLevel" }, // 847187534
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_PlaceActorAtLocation, "PlaceActorAtLocation" }, // 3164814633
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_Redo, "Redo" }, // 4233317921
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_RemoveSelectedActors, "RemoveSelectedActors" }, // 2361039480
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SaveLevelLayout, "SaveLevelLayout" }, // 3387087030
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SearchAssets, "SearchAssets" }, // 1034139982
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SelectActor, "SelectActor" }, // 1395536631
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsByCategory, "SelectActorsByCategory" }, // 3545223364
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SelectActorsInArea, "SelectActorsInArea" }, // 1277235461
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SelectAssetForPlacement, "SelectAssetForPlacement" }, // 121768361
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SetDesignModeActive, "SetDesignModeActive" }, // 1923213687
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SetPlacementMode, "SetPlacementMode" }, // 494113354
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SetSnapMode, "SetSnapMode" }, // 2889138310
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToGrid, "SnapLocationToGrid" }, // 116865372
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_SnapLocationToSurface, "SnapLocationToSurface" }, // 3364885128
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_Undo, "Undo" }, // 2288774588
		{ &Z_Construct_UFunction_UDragDropLevelDesigner_UngroupSelectedActors, "UngroupSelectedActors" }, // 1245913031
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UDragDropLevelDesigner>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_AssetDatabase = { "AssetDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, AssetDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetDatabase_MetaData), NewProp_AssetDatabase_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacementSettings = { "PlacementSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, PlacementSettings), Z_Construct_UScriptStruct_FPlacementSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacementSettings_MetaData), NewProp_PlacementSettings_MetaData) }; // 810508621
void Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bDesignModeActive_SetBit(void* Obj)
{
	((UDragDropLevelDesigner*)Obj)->bDesignModeActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bDesignModeActive = { "bDesignModeActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDragDropLevelDesigner), &Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bDesignModeActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bDesignModeActive_MetaData), NewProp_bDesignModeActive_MetaData) };
void Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowPreview_SetBit(void* Obj)
{
	((UDragDropLevelDesigner*)Obj)->bShowPreview = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowPreview = { "bShowPreview", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDragDropLevelDesigner), &Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowPreview_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowPreview_MetaData), NewProp_bShowPreview_MetaData) };
void Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowGrid_SetBit(void* Obj)
{
	((UDragDropLevelDesigner*)Obj)->bShowGrid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowGrid = { "bShowGrid", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDragDropLevelDesigner), &Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowGrid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowGrid_MetaData), NewProp_bShowGrid_MetaData) };
void Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bEnableUndo_SetBit(void* Obj)
{
	((UDragDropLevelDesigner*)Obj)->bEnableUndo = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bEnableUndo = { "bEnableUndo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UDragDropLevelDesigner), &Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bEnableUndo_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableUndo_MetaData), NewProp_bEnableUndo_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_CurrentAssetID = { "CurrentAssetID", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, CurrentAssetID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAssetID_MetaData), NewProp_CurrentAssetID_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_SelectedActors_Inner = { "SelectedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_SelectedActors = { "SelectedActors", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, SelectedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SelectedActors_MetaData), NewProp_SelectedActors_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_DesignStats = { "DesignStats", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, DesignStats), Z_Construct_UScriptStruct_FLevelDesignStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignStats_MetaData), NewProp_DesignStats_MetaData) }; // 1591124716
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnActorPlaced = { "OnActorPlaced", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, OnActorPlaced), Z_Construct_UDelegateFunction_SLT_OnActorPlaced__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorPlaced_MetaData), NewProp_OnActorPlaced_MetaData) }; // 3554787390
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnActorRemoved = { "OnActorRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, OnActorRemoved), Z_Construct_UDelegateFunction_SLT_OnActorRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorRemoved_MetaData), NewProp_OnActorRemoved_MetaData) }; // 2395854949
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnSelectionChanged = { "OnSelectionChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, OnSelectionChanged), Z_Construct_UDelegateFunction_SLT_OnSelectionChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSelectionChanged_MetaData), NewProp_OnSelectionChanged_MetaData) }; // 3473183597
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnPlacementModeChanged = { "OnPlacementModeChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, OnPlacementModeChanged), Z_Construct_UDelegateFunction_SLT_OnPlacementModeChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPlacementModeChanged_MetaData), NewProp_OnPlacementModeChanged_MetaData) }; // 1762621433
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_CachedAssetDatabase = { "CachedAssetDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, CachedAssetDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedAssetDatabase_MetaData), NewProp_CachedAssetDatabase_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacedActors_Inner = { "PlacedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacedActors = { "PlacedActors", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, PlacedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlacedActors_MetaData), NewProp_PlacedActors_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PreviewActor = { "PreviewActor", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, PreviewActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewActor_MetaData), NewProp_PreviewActor_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_UndoStack_Inner = { "UndoStack", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_UndoStack = { "UndoStack", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, UndoStack), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UndoStack_MetaData), NewProp_UndoStack_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_RedoStack_Inner = { "RedoStack", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_RedoStack = { "RedoStack", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, RedoStack), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RedoStack_MetaData), NewProp_RedoStack_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_MaxUndoSteps = { "MaxUndoSteps", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UDragDropLevelDesigner, MaxUndoSteps), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxUndoSteps_MetaData), NewProp_MaxUndoSteps_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UDragDropLevelDesigner_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_AssetDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacementSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bDesignModeActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowPreview,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bShowGrid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_bEnableUndo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_CurrentAssetID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_SelectedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_SelectedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_DesignStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnActorPlaced,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnActorRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnSelectionChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_OnPlacementModeChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_CachedAssetDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PlacedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_PreviewActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_UndoStack_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_UndoStack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_RedoStack_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_RedoStack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UDragDropLevelDesigner_Statics::NewProp_MaxUndoSteps,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDragDropLevelDesigner_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UDragDropLevelDesigner_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UDragDropLevelDesigner_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UDragDropLevelDesigner_Statics::ClassParams = {
	&UDragDropLevelDesigner::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UDragDropLevelDesigner_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UDragDropLevelDesigner_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UDragDropLevelDesigner_Statics::Class_MetaDataParams), Z_Construct_UClass_UDragDropLevelDesigner_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UDragDropLevelDesigner()
{
	if (!Z_Registration_Info_UClass_UDragDropLevelDesigner.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UDragDropLevelDesigner.OuterSingleton, Z_Construct_UClass_UDragDropLevelDesigner_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UDragDropLevelDesigner.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UDragDropLevelDesigner>()
{
	return UDragDropLevelDesigner::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UDragDropLevelDesigner);
UDragDropLevelDesigner::~UDragDropLevelDesigner() {}
// End Class UDragDropLevelDesigner

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EDragDropCategory_StaticEnum, TEXT("EDragDropCategory"), &Z_Registration_Info_UEnum_EDragDropCategory, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 817066345U) },
		{ ESnapMode_StaticEnum, TEXT("ESnapMode"), &Z_Registration_Info_UEnum_ESnapMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4106927359U) },
		{ EPlacementMode_StaticEnum, TEXT("EPlacementMode"), &Z_Registration_Info_UEnum_EPlacementMode, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4198537735U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FDragDropAsset::StaticStruct, Z_Construct_UScriptStruct_FDragDropAsset_Statics::NewStructOps, TEXT("DragDropAsset"), &Z_Registration_Info_UScriptStruct_DragDropAsset, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDragDropAsset), 3463544237U) },
		{ FPlacementSettings::StaticStruct, Z_Construct_UScriptStruct_FPlacementSettings_Statics::NewStructOps, TEXT("PlacementSettings"), &Z_Registration_Info_UScriptStruct_PlacementSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlacementSettings), 810508621U) },
		{ FLevelDesignStats::StaticStruct, Z_Construct_UScriptStruct_FLevelDesignStats_Statics::NewStructOps, TEXT("LevelDesignStats"), &Z_Registration_Info_UScriptStruct_LevelDesignStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLevelDesignStats), 1591124716U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UDragDropLevelDesigner, UDragDropLevelDesigner::StaticClass, TEXT("UDragDropLevelDesigner"), &Z_Registration_Info_UClass_UDragDropLevelDesigner, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UDragDropLevelDesigner), 1957879290U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_325498075(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
