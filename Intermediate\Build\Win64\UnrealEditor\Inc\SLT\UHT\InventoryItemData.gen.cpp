// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInventoryItemData() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UEnum* Z_Construct_UEnum_SLT_EItemRarity();
SLT_API UEnum* Z_Construct_UEnum_SLT_EItemType();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlot();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlotSaveData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EItemType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EItemType;
static UEnum* EItemType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EItemType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EItemType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EItemType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EItemType"));
	}
	return Z_Registration_Info_UEnum_EItemType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EItemType>()
{
	return EItemType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EItemType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ammo.DisplayName", "Ammo" },
		{ "Ammo.Name", "EItemType::Ammo" },
		{ "BlueprintType", "true" },
		{ "Consumable.DisplayName", "Consumable" },
		{ "Consumable.Name", "EItemType::Consumable" },
		{ "KeyItem.DisplayName", "Key Item" },
		{ "KeyItem.Name", "EItemType::KeyItem" },
		{ "Material.DisplayName", "Material" },
		{ "Material.Name", "EItemType::Material" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EItemType::None" },
		{ "Treasure.DisplayName", "Treasure" },
		{ "Treasure.Name", "EItemType::Treasure" },
		{ "Weapon.DisplayName", "Weapon" },
		{ "Weapon.Name", "EItemType::Weapon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EItemType::None", (int64)EItemType::None },
		{ "EItemType::Weapon", (int64)EItemType::Weapon },
		{ "EItemType::Ammo", (int64)EItemType::Ammo },
		{ "EItemType::Consumable", (int64)EItemType::Consumable },
		{ "EItemType::KeyItem", (int64)EItemType::KeyItem },
		{ "EItemType::Treasure", (int64)EItemType::Treasure },
		{ "EItemType::Material", (int64)EItemType::Material },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EItemType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EItemType",
	"EItemType",
	Z_Construct_UEnum_SLT_EItemType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EItemType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EItemType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EItemType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EItemType()
{
	if (!Z_Registration_Info_UEnum_EItemType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EItemType.InnerSingleton, Z_Construct_UEnum_SLT_EItemType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EItemType.InnerSingleton;
}
// End Enum EItemType

// Begin Enum EItemRarity
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EItemRarity;
static UEnum* EItemRarity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EItemRarity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EItemRarity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EItemRarity, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EItemRarity"));
	}
	return Z_Registration_Info_UEnum_EItemRarity.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EItemRarity>()
{
	return EItemRarity_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EItemRarity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Common.DisplayName", "Common" },
		{ "Common.Name", "EItemRarity::Common" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EItemRarity::Epic" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EItemRarity::Legendary" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EItemRarity::Rare" },
		{ "Uncommon.DisplayName", "Uncommon" },
		{ "Uncommon.Name", "EItemRarity::Uncommon" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EItemRarity::Common", (int64)EItemRarity::Common },
		{ "EItemRarity::Uncommon", (int64)EItemRarity::Uncommon },
		{ "EItemRarity::Rare", (int64)EItemRarity::Rare },
		{ "EItemRarity::Epic", (int64)EItemRarity::Epic },
		{ "EItemRarity::Legendary", (int64)EItemRarity::Legendary },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EItemRarity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EItemRarity",
	"EItemRarity",
	Z_Construct_UEnum_SLT_EItemRarity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EItemRarity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EItemRarity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EItemRarity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EItemRarity()
{
	if (!Z_Registration_Info_UEnum_EItemRarity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EItemRarity.InnerSingleton, Z_Construct_UEnum_SLT_EItemRarity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EItemRarity.InnerSingleton;
}
// End Enum EItemRarity

// Begin ScriptStruct FInventoryItemData
static_assert(std::is_polymorphic<FInventoryItemData>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FInventoryItemData cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_InventoryItemData;
class UScriptStruct* FInventoryItemData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_InventoryItemData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_InventoryItemData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FInventoryItemData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("InventoryItemData"));
	}
	return Z_Registration_Info_UScriptStruct_InventoryItemData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FInventoryItemData>()
{
	return FInventoryItemData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FInventoryItemData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ID_MetaData[] = {
		{ "Category", "Item Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier for this item\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for this item" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Name_MetaData[] = {
		{ "Category", "Item Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Display name of the item\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Display name of the item" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Description_MetaData[] = {
		{ "Category", "Item Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Description shown in tooltips\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Description shown in tooltips" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Icon_MetaData[] = {
		{ "Category", "Item Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Icon displayed in inventory\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Icon displayed in inventory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Width_MetaData[] = {
		{ "Category", "Grid" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid dimensions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid dimensions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Height_MetaData[] = {
		{ "Category", "Grid" },
		{ "ClampMax", "10" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRotate_MetaData[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Can this item be rotated in the grid?\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can this item be rotated in the grid?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanStack_MetaData[] = {
		{ "Category", "Stacking" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Stacking properties\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stacking properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxStackSize_MetaData[] = {
		{ "Category", "Stacking" },
		{ "ClampMax", "999" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PickupMesh_MetaData[] = {
		{ "Category", "World" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 3D mesh for world pickup\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "3D mesh for world pickup" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemClass_MetaData[] = {
		{ "Category", "World" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Actor class to spawn when using this item\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Actor class to spawn when using this item" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemType_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Item categorization\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Item categorization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemRarity_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemTags_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Gameplay tags for additional categorization\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Gameplay tags for additional categorization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "Category", "Properties" },
		{ "ClampMin", "0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Economic properties\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Economic properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Weight_MetaData[] = {
		{ "Category", "Properties" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsQuestItem_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Quest and interaction flags\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Quest and interaction flags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeDropped_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeSold_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProperties_MetaData[] = {
		{ "Category", "Custom Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom data for specific item types (weapons, consumables, etc.)\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom data for specific item types (weapons, consumables, etc.)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Name;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Description;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_Icon;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static void NewProp_bCanRotate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRotate;
	static void NewProp_bCanStack_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanStack;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxStackSize;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PickupMesh;
	static const UECodeGen_Private::FSoftClassPropertyParams NewProp_ItemClass;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ItemType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ItemType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ItemRarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ItemRarity;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemTags;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Value;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Weight;
	static void NewProp_bIsQuestItem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsQuestItem;
	static void NewProp_bCanBeDropped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeDropped;
	static void NewProp_bCanBeSold_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeSold;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProperties;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FInventoryItemData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ID = { "ID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, ID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ID_MetaData), NewProp_ID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Name = { "Name", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Name), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Name_MetaData), NewProp_Name_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Description = { "Description", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Description), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Description_MetaData), NewProp_Description_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Icon = { "Icon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Icon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Icon_MetaData), NewProp_Icon_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Width), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Width_MetaData), NewProp_Width_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Height), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Height_MetaData), NewProp_Height_MetaData) };
void Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanRotate_SetBit(void* Obj)
{
	((FInventoryItemData*)Obj)->bCanRotate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanRotate = { "bCanRotate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventoryItemData), &Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanRotate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRotate_MetaData), NewProp_bCanRotate_MetaData) };
void Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanStack_SetBit(void* Obj)
{
	((FInventoryItemData*)Obj)->bCanStack = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanStack = { "bCanStack", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventoryItemData), &Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanStack_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanStack_MetaData), NewProp_bCanStack_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_MaxStackSize = { "MaxStackSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, MaxStackSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxStackSize_MetaData), NewProp_MaxStackSize_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_PickupMesh = { "PickupMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, PickupMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PickupMesh_MetaData), NewProp_PickupMesh_MetaData) };
const UECodeGen_Private::FSoftClassPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemClass = { "ItemClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftClass, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, ItemClass), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemClass_MetaData), NewProp_ItemClass_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemType = { "ItemType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, ItemType), Z_Construct_UEnum_SLT_EItemType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemType_MetaData), NewProp_ItemType_MetaData) }; // 2941143027
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemRarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemRarity = { "ItemRarity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, ItemRarity), Z_Construct_UEnum_SLT_EItemRarity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemRarity_MetaData), NewProp_ItemRarity_MetaData) }; // 2495340207
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemTags = { "ItemTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, ItemTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemTags_MetaData), NewProp_ItemTags_MetaData) }; // 3352185621
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Weight = { "Weight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, Weight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Weight_MetaData), NewProp_Weight_MetaData) };
void Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bIsQuestItem_SetBit(void* Obj)
{
	((FInventoryItemData*)Obj)->bIsQuestItem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bIsQuestItem = { "bIsQuestItem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventoryItemData), &Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bIsQuestItem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsQuestItem_MetaData), NewProp_bIsQuestItem_MetaData) };
void Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeDropped_SetBit(void* Obj)
{
	((FInventoryItemData*)Obj)->bCanBeDropped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeDropped = { "bCanBeDropped", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventoryItemData), &Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeDropped_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeDropped_MetaData), NewProp_bCanBeDropped_MetaData) };
void Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeSold_SetBit(void* Obj)
{
	((FInventoryItemData*)Obj)->bCanBeSold = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeSold = { "bCanBeSold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventoryItemData), &Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeSold_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeSold_MetaData), NewProp_bCanBeSold_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties_ValueProp = { "CustomProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties_Key_KeyProp = { "CustomProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties = { "CustomProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventoryItemData, CustomProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProperties_MetaData), NewProp_CustomProperties_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FInventoryItemData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Name,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Description,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Icon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanRotate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanStack,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_MaxStackSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_PickupMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemRarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemRarity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_ItemTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_Weight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bIsQuestItem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeDropped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_bCanBeSold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewProp_CustomProperties,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventoryItemData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FInventoryItemData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"InventoryItemData",
	Z_Construct_UScriptStruct_FInventoryItemData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventoryItemData_Statics::PropPointers),
	sizeof(FInventoryItemData),
	alignof(FInventoryItemData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventoryItemData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FInventoryItemData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData()
{
	if (!Z_Registration_Info_UScriptStruct_InventoryItemData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_InventoryItemData.InnerSingleton, Z_Construct_UScriptStruct_FInventoryItemData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_InventoryItemData.InnerSingleton;
}
// End ScriptStruct FInventoryItemData

// Begin ScriptStruct FInventorySlot
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_InventorySlot;
class UScriptStruct* FInventorySlot::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_InventorySlot.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_InventorySlot.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FInventorySlot, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("InventorySlot"));
	}
	return Z_Registration_Info_UScriptStruct_InventorySlot.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FInventorySlot>()
{
	return FInventorySlot::StaticStruct();
}
struct Z_Construct_UScriptStruct_FInventorySlot_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// The item data\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "The item data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current quantity (for stackable items)\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current quantity (for stackable items)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRotated_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is the item rotated in the grid?\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is the item rotated in the grid?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridX_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid position\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid position" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridY_MetaData[] = {
		{ "Category", "Slot" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotID_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier for this slot instance\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for this slot instance" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuntimeData_MetaData[] = {
		{ "Category", "Slot" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom runtime data\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom runtime data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_bIsRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRotated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridY;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuntimeData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuntimeData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RuntimeData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FInventorySlot>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
void Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_bIsRotated_SetBit(void* Obj)
{
	((FInventorySlot*)Obj)->bIsRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_bIsRotated = { "bIsRotated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventorySlot), &Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_bIsRotated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRotated_MetaData), NewProp_bIsRotated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_GridX = { "GridX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, GridX), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridX_MetaData), NewProp_GridX_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_GridY = { "GridY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, GridY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridY_MetaData), NewProp_GridY_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_SlotID = { "SlotID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, SlotID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotID_MetaData), NewProp_SlotID_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData_ValueProp = { "RuntimeData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData_Key_KeyProp = { "RuntimeData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData = { "RuntimeData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlot, RuntimeData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuntimeData_MetaData), NewProp_RuntimeData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FInventorySlot_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_bIsRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_GridX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_GridY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_SlotID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlot_Statics::NewProp_RuntimeData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlot_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FInventorySlot_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"InventorySlot",
	Z_Construct_UScriptStruct_FInventorySlot_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlot_Statics::PropPointers),
	sizeof(FInventorySlot),
	alignof(FInventorySlot),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlot_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FInventorySlot_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FInventorySlot()
{
	if (!Z_Registration_Info_UScriptStruct_InventorySlot.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_InventorySlot.InnerSingleton, Z_Construct_UScriptStruct_FInventorySlot_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_InventorySlot.InnerSingleton;
}
// End ScriptStruct FInventorySlot

// Begin ScriptStruct FInventorySlotSaveData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_InventorySlotSaveData;
class UScriptStruct* FInventorySlotSaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_InventorySlotSaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_InventorySlotSaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FInventorySlotSaveData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("InventorySlotSaveData"));
	}
	return Z_Registration_Info_UScriptStruct_InventorySlotSaveData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FInventorySlotSaveData>()
{
	return FInventorySlotSaveData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemID_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Quantity_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRotated_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridX_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridY_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuntimeData_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "InventorySystem/Data/InventoryItemData.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_bIsRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRotated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridY;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuntimeData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RuntimeData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RuntimeData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FInventorySlotSaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlotSaveData, ItemID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemID_MetaData), NewProp_ItemID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlotSaveData, Quantity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Quantity_MetaData), NewProp_Quantity_MetaData) };
void Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_bIsRotated_SetBit(void* Obj)
{
	((FInventorySlotSaveData*)Obj)->bIsRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_bIsRotated = { "bIsRotated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FInventorySlotSaveData), &Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_bIsRotated_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRotated_MetaData), NewProp_bIsRotated_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_GridX = { "GridX", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlotSaveData, GridX), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridX_MetaData), NewProp_GridX_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_GridY = { "GridY", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlotSaveData, GridY), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridY_MetaData), NewProp_GridY_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData_ValueProp = { "RuntimeData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData_Key_KeyProp = { "RuntimeData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData = { "RuntimeData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FInventorySlotSaveData, RuntimeData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuntimeData_MetaData), NewProp_RuntimeData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_bIsRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_GridX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_GridY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewProp_RuntimeData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"InventorySlotSaveData",
	Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::PropPointers),
	sizeof(FInventorySlotSaveData),
	alignof(FInventorySlotSaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FInventorySlotSaveData()
{
	if (!Z_Registration_Info_UScriptStruct_InventorySlotSaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_InventorySlotSaveData.InnerSingleton, Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_InventorySlotSaveData.InnerSingleton;
}
// End ScriptStruct FInventorySlotSaveData

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EItemType_StaticEnum, TEXT("EItemType"), &Z_Registration_Info_UEnum_EItemType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2941143027U) },
		{ EItemRarity_StaticEnum, TEXT("EItemRarity"), &Z_Registration_Info_UEnum_EItemRarity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2495340207U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FInventoryItemData::StaticStruct, Z_Construct_UScriptStruct_FInventoryItemData_Statics::NewStructOps, TEXT("InventoryItemData"), &Z_Registration_Info_UScriptStruct_InventoryItemData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FInventoryItemData), 2976144554U) },
		{ FInventorySlot::StaticStruct, Z_Construct_UScriptStruct_FInventorySlot_Statics::NewStructOps, TEXT("InventorySlot"), &Z_Registration_Info_UScriptStruct_InventorySlot, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FInventorySlot), 2390674128U) },
		{ FInventorySlotSaveData::StaticStruct, Z_Construct_UScriptStruct_FInventorySlotSaveData_Statics::NewStructOps, TEXT("InventorySlotSaveData"), &Z_Registration_Info_UScriptStruct_InventorySlotSaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FInventorySlotSaveData), 2279301074U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_766738632(TEXT("/Script/SLT"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Data_InventoryItemData_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
