#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/World.h"
#include "Subsystems/WorldSubsystem.h"
#include "ObjectPoolManager.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorPooled, AActor*, Actor, bool, bWasReturned);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorSpawned, AActor*, Actor, TSubclassOf<AActor>, ActorClass);

USTRUCT(BlueprintType)
struct FObjectPoolSettings
{
	GENERATED_BODY()

	FObjectPoolSettings()
	{
		InitialPoolSize = 10;
		MaxPoolSize = 50;
		bAutoExpand = true;
		bPrewarmPool = true;
		PoolExpansionSize = 5;
		bEnablePooling = true;
		PoolCleanupInterval = 60.0f;
		MaxIdleTime = 120.0f;
	}

	// Initial number of objects to create
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings", meta = (ClampMin = "1"))
	int32 InitialPoolSize;

	// Maximum number of objects in pool
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings", meta = (ClampMin = "1"))
	int32 MaxPoolSize;

	// Can the pool expand beyond initial size?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings")
	bool bAutoExpand;

	// Should we create initial objects at startup?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings")
	bool bPrewarmPool;

	// How many objects to add when expanding
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings", meta = (ClampMin = "1"))
	int32 PoolExpansionSize;

	// Is pooling enabled for this class?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings")
	bool bEnablePooling;

	// How often to clean up unused objects (seconds)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings", meta = (ClampMin = "1.0"))
	float PoolCleanupInterval;

	// How long objects can be idle before cleanup (seconds)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Settings", meta = (ClampMin = "1.0"))
	float MaxIdleTime;
};

USTRUCT(BlueprintType)
struct FPooledActorInfo
{
	GENERATED_BODY()

	FPooledActorInfo()
	{
		Actor = nullptr;
		bIsActive = false;
		LastUsedTime = 0.0f;
		PooledTime = 0.0f;
		UsageCount = 0;
	}

	UPROPERTY()
	AActor* Actor;

	UPROPERTY()
	bool bIsActive;

	UPROPERTY()
	float LastUsedTime;

	UPROPERTY()
	float PooledTime;

	UPROPERTY()
	int32 UsageCount;
};

USTRUCT(BlueprintType)
struct FActorPool
{
	GENERATED_BODY()

	FActorPool()
	{
		ActorClass = nullptr;
		Settings = FObjectPoolSettings();
	}

	UPROPERTY()
	TSubclassOf<AActor> ActorClass;

	UPROPERTY()
	FObjectPoolSettings Settings;

	UPROPERTY()
	TArray<FPooledActorInfo> PooledActors;

	UPROPERTY()
	TArray<FPooledActorInfo> ActiveActors;
};

UINTERFACE(MinimalAPI, BlueprintType)
class UPoolableActor : public UInterface
{
	GENERATED_BODY()
};

/**
 * Interface for actors that can be pooled
 */
class SLT_API IPoolableActor
{
	GENERATED_BODY()

public:
	// Called when actor is retrieved from pool
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Object Pool")
	void OnActorPooled();
	virtual void OnActorPooled_Implementation() {}

	// Called when actor is returned to pool
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Object Pool")
	void OnActorReturned();
	virtual void OnActorReturned_Implementation() {}

	// Called to reset actor state for reuse
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Object Pool")
	void ResetActorState();
	virtual void ResetActorState_Implementation() {}

	// Check if actor can be returned to pool
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Object Pool")
	bool CanBePooled() const;
	virtual bool CanBePooled_Implementation() const { return true; }
};

UCLASS(BlueprintType, Blueprintable)
class SLT_API UObjectPoolManager : public UWorldSubsystem
{
	GENERATED_BODY()

public:
	UObjectPoolManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;
	virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

	// Pool configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Configuration")
	TMap<TSubclassOf<AActor>, FObjectPoolSettings> PoolConfigurations;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Configuration")
	bool bEnableGlobalPooling = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Pool Configuration")
	FObjectPoolSettings DefaultPoolSettings;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnActorPooled OnActorPooled;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnActorSpawned OnActorSpawned;

	// Main pool functions
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	AActor* GetPooledActor(TSubclassOf<AActor> ActorClass, const FVector& Location = FVector::ZeroVector, const FRotator& Rotation = FRotator::ZeroRotator);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	bool ReturnActorToPool(AActor* Actor);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void RegisterPoolConfiguration(TSubclassOf<AActor> ActorClass, const FObjectPoolSettings& Settings);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void PrewarmPool(TSubclassOf<AActor> ActorClass, int32 Count = -1);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ClearPool(TSubclassOf<AActor> ActorClass);

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void ClearAllPools();

	// Pool management
	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void CleanupIdleActors();

	UFUNCTION(BlueprintCallable, Category = "Object Pool")
	void SetPoolingEnabled(bool bEnabled);

	// Statistics
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Object Pool")
	int32 GetPoolSize(TSubclassOf<AActor> ActorClass) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Object Pool")
	int32 GetActiveActorCount(TSubclassOf<AActor> ActorClass) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Object Pool")
	float GetPoolEfficiency(TSubclassOf<AActor> ActorClass) const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Object Pool")
	TArray<FString> GetPoolStatistics() const;

protected:
	// Internal pool storage
	UPROPERTY()
	TMap<TSubclassOf<AActor>, FActorPool> ActorPools;

	// Cleanup timer
	FTimerHandle CleanupTimerHandle;

	// Internal functions
	FActorPool* GetOrCreatePool(TSubclassOf<AActor> ActorClass);
	AActor* CreatePooledActor(TSubclassOf<AActor> ActorClass);
	void ExpandPool(FActorPool* Pool);
	void OnCleanupTimer();
	FObjectPoolSettings GetPoolSettings(TSubclassOf<AActor> ActorClass) const;

	// Statistics tracking
	UPROPERTY()
	TMap<TSubclassOf<AActor>, int32> PoolHitCount;

	UPROPERTY()
	TMap<TSubclassOf<AActor>, int32> PoolMissCount;
};
