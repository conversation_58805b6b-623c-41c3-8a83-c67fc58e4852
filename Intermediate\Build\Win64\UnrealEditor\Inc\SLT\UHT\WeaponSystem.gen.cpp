// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/WeaponSystem.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeWeaponSystem() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UAnimMontage_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USkeletalMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USoundBase_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
NIAGARA_API UClass* Z_Construct_UClass_UNiagaraSystem_NoRegister();
SLT_API UClass* Z_Construct_UClass_UWeaponComponent();
SLT_API UClass* Z_Construct_UClass_UWeaponComponent_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAmmoType();
SLT_API UEnum* Z_Construct_UEnum_SLT_EWeaponCondition();
SLT_API UEnum* Z_Construct_UEnum_SLT_EWeaponType();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FWeaponData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FWeaponStats();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FWeaponUpgrade();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EWeaponType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWeaponType;
static UEnum* EWeaponType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWeaponType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWeaponType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EWeaponType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EWeaponType"));
	}
	return Z_Registration_Info_UEnum_EWeaponType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EWeaponType>()
{
	return EWeaponType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EWeaponType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Explosive.DisplayName", "Explosive" },
		{ "Explosive.Name", "EWeaponType::Explosive" },
		{ "Melee.DisplayName", "Melee" },
		{ "Melee.Name", "EWeaponType::Melee" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EWeaponType::None" },
		{ "Pistol.DisplayName", "Pistol" },
		{ "Pistol.Name", "EWeaponType::Pistol" },
		{ "Rifle.DisplayName", "Rifle" },
		{ "Rifle.Name", "EWeaponType::Rifle" },
		{ "Shotgun.DisplayName", "Shotgun" },
		{ "Shotgun.Name", "EWeaponType::Shotgun" },
		{ "Sniper.DisplayName", "Sniper" },
		{ "Sniper.Name", "EWeaponType::Sniper" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWeaponType::None", (int64)EWeaponType::None },
		{ "EWeaponType::Pistol", (int64)EWeaponType::Pistol },
		{ "EWeaponType::Rifle", (int64)EWeaponType::Rifle },
		{ "EWeaponType::Shotgun", (int64)EWeaponType::Shotgun },
		{ "EWeaponType::Sniper", (int64)EWeaponType::Sniper },
		{ "EWeaponType::Melee", (int64)EWeaponType::Melee },
		{ "EWeaponType::Explosive", (int64)EWeaponType::Explosive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EWeaponType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EWeaponType",
	"EWeaponType",
	Z_Construct_UEnum_SLT_EWeaponType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EWeaponType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EWeaponType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EWeaponType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EWeaponType()
{
	if (!Z_Registration_Info_UEnum_EWeaponType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWeaponType.InnerSingleton, Z_Construct_UEnum_SLT_EWeaponType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWeaponType.InnerSingleton;
}
// End Enum EWeaponType

// Begin Enum EWeaponCondition
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EWeaponCondition;
static UEnum* EWeaponCondition_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EWeaponCondition.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EWeaponCondition.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EWeaponCondition, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EWeaponCondition"));
	}
	return Z_Registration_Info_UEnum_EWeaponCondition.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EWeaponCondition>()
{
	return EWeaponCondition_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EWeaponCondition_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Broken.DisplayName", "Broken" },
		{ "Broken.Name", "EWeaponCondition::Broken" },
		{ "Excellent.DisplayName", "Excellent" },
		{ "Excellent.Name", "EWeaponCondition::Excellent" },
		{ "Fair.DisplayName", "Fair" },
		{ "Fair.Name", "EWeaponCondition::Fair" },
		{ "Good.DisplayName", "Good" },
		{ "Good.Name", "EWeaponCondition::Good" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
		{ "Perfect.DisplayName", "Perfect" },
		{ "Perfect.Name", "EWeaponCondition::Perfect" },
		{ "Poor.DisplayName", "Poor" },
		{ "Poor.Name", "EWeaponCondition::Poor" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EWeaponCondition::Broken", (int64)EWeaponCondition::Broken },
		{ "EWeaponCondition::Poor", (int64)EWeaponCondition::Poor },
		{ "EWeaponCondition::Fair", (int64)EWeaponCondition::Fair },
		{ "EWeaponCondition::Good", (int64)EWeaponCondition::Good },
		{ "EWeaponCondition::Excellent", (int64)EWeaponCondition::Excellent },
		{ "EWeaponCondition::Perfect", (int64)EWeaponCondition::Perfect },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EWeaponCondition_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EWeaponCondition",
	"EWeaponCondition",
	Z_Construct_UEnum_SLT_EWeaponCondition_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EWeaponCondition_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EWeaponCondition_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EWeaponCondition_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EWeaponCondition()
{
	if (!Z_Registration_Info_UEnum_EWeaponCondition.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EWeaponCondition.InnerSingleton, Z_Construct_UEnum_SLT_EWeaponCondition_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EWeaponCondition.InnerSingleton;
}
// End Enum EWeaponCondition

// Begin Enum EAmmoType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAmmoType;
static UEnum* EAmmoType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAmmoType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAmmoType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAmmoType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAmmoType"));
	}
	return Z_Registration_Info_UEnum_EAmmoType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAmmoType>()
{
	return EAmmoType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAmmoType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAmmoType::Custom" },
		{ "Explosive40mm.DisplayName", "40mm Explosive" },
		{ "Explosive40mm.Name", "EAmmoType::Explosive40mm" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EAmmoType::None" },
		{ "Pistol9mm.DisplayName", "9mm Pistol" },
		{ "Pistol9mm.Name", "EAmmoType::Pistol9mm" },
		{ "Rifle556.DisplayName", "5.56 Rifle" },
		{ "Rifle556.Name", "EAmmoType::Rifle556" },
		{ "Shotgun12g.DisplayName", "12 Gauge" },
		{ "Shotgun12g.Name", "EAmmoType::Shotgun12g" },
		{ "Sniper762.DisplayName", "7.62 Sniper" },
		{ "Sniper762.Name", "EAmmoType::Sniper762" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAmmoType::None", (int64)EAmmoType::None },
		{ "EAmmoType::Pistol9mm", (int64)EAmmoType::Pistol9mm },
		{ "EAmmoType::Rifle556", (int64)EAmmoType::Rifle556 },
		{ "EAmmoType::Shotgun12g", (int64)EAmmoType::Shotgun12g },
		{ "EAmmoType::Sniper762", (int64)EAmmoType::Sniper762 },
		{ "EAmmoType::Explosive40mm", (int64)EAmmoType::Explosive40mm },
		{ "EAmmoType::Custom", (int64)EAmmoType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAmmoType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAmmoType",
	"EAmmoType",
	Z_Construct_UEnum_SLT_EAmmoType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAmmoType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAmmoType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAmmoType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAmmoType()
{
	if (!Z_Registration_Info_UEnum_EAmmoType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAmmoType.InnerSingleton, Z_Construct_UEnum_SLT_EAmmoType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAmmoType.InnerSingleton;
}
// End Enum EAmmoType

// Begin ScriptStruct FWeaponUpgrade
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WeaponUpgrade;
class UScriptStruct* FWeaponUpgrade::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponUpgrade.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WeaponUpgrade.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWeaponUpgrade, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("WeaponUpgrade"));
	}
	return Z_Registration_Info_UScriptStruct_WeaponUpgrade.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FWeaponUpgrade>()
{
	return FWeaponUpgrade::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWeaponUpgrade_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeID_MetaData[] = {
		{ "Category", "Upgrade" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Upgrade identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeName_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeDescription_MetaData[] = {
		{ "Category", "Upgrade" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeLevel_MetaData[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Upgrade progression\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade progression" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxLevel_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CostPerLevel_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatModifier_MetaData[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Requirements\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredLevel_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeMesh_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual changes\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual changes" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UpgradeMaterial_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_UpgradeName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_UpgradeDescription;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UpgradeLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CostPerLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StatModifier;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UpgradeMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_UpgradeMaterial;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWeaponUpgrade>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeID_MetaData), NewProp_UpgradeID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeName = { "UpgradeName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeName_MetaData), NewProp_UpgradeName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeDescription = { "UpgradeDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeDescription_MetaData), NewProp_UpgradeDescription_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeLevel = { "UpgradeLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeLevel_MetaData), NewProp_UpgradeLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_MaxLevel = { "MaxLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, MaxLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxLevel_MetaData), NewProp_MaxLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_CostPerLevel = { "CostPerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, CostPerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CostPerLevel_MetaData), NewProp_CostPerLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_StatModifier = { "StatModifier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, StatModifier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatModifier_MetaData), NewProp_StatModifier_MetaData) };
void Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FWeaponUpgrade*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWeaponUpgrade), &Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_RequiredLevel = { "RequiredLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, RequiredLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredLevel_MetaData), NewProp_RequiredLevel_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeMesh = { "UpgradeMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeMesh_MetaData), NewProp_UpgradeMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeMaterial = { "UpgradeMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponUpgrade, UpgradeMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UpgradeMaterial_MetaData), NewProp_UpgradeMaterial_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_MaxLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_CostPerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_StatModifier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_RequiredLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewProp_UpgradeMaterial,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"WeaponUpgrade",
	Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::PropPointers),
	sizeof(FWeaponUpgrade),
	alignof(FWeaponUpgrade),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWeaponUpgrade()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponUpgrade.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WeaponUpgrade.InnerSingleton, Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WeaponUpgrade.InnerSingleton;
}
// End ScriptStruct FWeaponUpgrade

// Begin ScriptStruct FWeaponStats
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WeaponStats;
class UScriptStruct* FWeaponStats::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponStats.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WeaponStats.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWeaponStats, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("WeaponStats"));
	}
	return Z_Registration_Info_UScriptStruct_WeaponStats.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FWeaponStats>()
{
	return FWeaponStats::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWeaponStats_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseDamage_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat stats\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat stats" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Accuracy_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Range_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 to 1.0\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 to 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireRate_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadTime_MetaData[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Shots per second\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shots per second" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MagazineSize_MetaData[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxDurability_MetaData[] = {
		{ "Category", "Durability" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Durability\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Durability" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDurability_MetaData[] = {
		{ "Category", "Durability" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalChance_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced stats\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced stats" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalMultiplier_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// 0.0 to 1.0\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "0.0 to 1.0" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecoilStrength_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BaseDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Accuracy;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Range;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FireRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReloadTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MagazineSize;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxDurability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentDurability;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalChance;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalMultiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RecoilStrength;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWeaponStats>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_BaseDamage = { "BaseDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, BaseDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseDamage_MetaData), NewProp_BaseDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_Accuracy = { "Accuracy", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, Accuracy), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Accuracy_MetaData), NewProp_Accuracy_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_Range = { "Range", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, Range), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Range_MetaData), NewProp_Range_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_FireRate = { "FireRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, FireRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireRate_MetaData), NewProp_FireRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_ReloadTime = { "ReloadTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, ReloadTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadTime_MetaData), NewProp_ReloadTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_MagazineSize = { "MagazineSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, MagazineSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MagazineSize_MetaData), NewProp_MagazineSize_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_MaxDurability = { "MaxDurability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, MaxDurability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxDurability_MetaData), NewProp_MaxDurability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CurrentDurability = { "CurrentDurability", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, CurrentDurability), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDurability_MetaData), NewProp_CurrentDurability_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CriticalChance = { "CriticalChance", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, CriticalChance), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalChance_MetaData), NewProp_CriticalChance_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CriticalMultiplier = { "CriticalMultiplier", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, CriticalMultiplier), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalMultiplier_MetaData), NewProp_CriticalMultiplier_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_RecoilStrength = { "RecoilStrength", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponStats, RecoilStrength), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecoilStrength_MetaData), NewProp_RecoilStrength_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWeaponStats_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_BaseDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_Accuracy,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_Range,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_FireRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_ReloadTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_MagazineSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_MaxDurability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CurrentDurability,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CriticalChance,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_CriticalMultiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponStats_Statics::NewProp_RecoilStrength,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponStats_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWeaponStats_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"WeaponStats",
	Z_Construct_UScriptStruct_FWeaponStats_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponStats_Statics::PropPointers),
	sizeof(FWeaponStats),
	alignof(FWeaponStats),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponStats_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWeaponStats_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWeaponStats()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponStats.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WeaponStats.InnerSingleton, Z_Construct_UScriptStruct_FWeaponStats_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WeaponStats.InnerSingleton;
}
// End ScriptStruct FWeaponStats

// Begin ScriptStruct FWeaponData
static_assert(std::is_polymorphic<FWeaponData>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FWeaponData cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WeaponData;
class UScriptStruct* FWeaponData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WeaponData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWeaponData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("WeaponData"));
	}
	return Z_Registration_Info_UScriptStruct_WeaponData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FWeaponData>()
{
	return FWeaponData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWeaponData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponID_MetaData[] = {
		{ "Category", "Weapon" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Basic info\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Basic info" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponName_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponDescription_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponType_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AmmoType_MetaData[] = {
		{ "Category", "Weapon" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseStats_MetaData[] = {
		{ "Category", "Stats" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Stats and condition\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Stats and condition" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Condition_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAmmo_MetaData[] = {
		{ "Category", "Stats" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsAutomatic_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanBeUpgraded_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresTwoHands_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AvailableUpgrades_MetaData[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Upgrades\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrades" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponMesh_MetaData[] = {
		{ "Category", "Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Assets\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponIcon_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireAnimation_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadAnimation_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FireSound_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ReloadSound_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MuzzleFlash_MetaData[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProperties_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_WeaponName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_WeaponDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeaponType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeaponType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AmmoType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AmmoType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BaseStats;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Condition_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Condition;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentAmmo;
	static void NewProp_bIsAutomatic_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsAutomatic;
	static void NewProp_bCanBeUpgraded_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanBeUpgraded;
	static void NewProp_bRequiresTwoHands_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresTwoHands;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AvailableUpgrades_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AvailableUpgrades;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WeaponMesh;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WeaponIcon;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FireAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ReloadAnimation;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_FireSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ReloadSound;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_MuzzleFlash;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProperties;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWeaponData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponID_MetaData), NewProp_WeaponID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponName = { "WeaponName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponName_MetaData), NewProp_WeaponName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponDescription = { "WeaponDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponDescription_MetaData), NewProp_WeaponDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponType = { "WeaponType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponType), Z_Construct_UEnum_SLT_EWeaponType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponType_MetaData), NewProp_WeaponType_MetaData) }; // 502516317
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AmmoType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AmmoType = { "AmmoType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, AmmoType), Z_Construct_UEnum_SLT_EAmmoType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AmmoType_MetaData), NewProp_AmmoType_MetaData) }; // 793778902
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_BaseStats = { "BaseStats", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, BaseStats), Z_Construct_UScriptStruct_FWeaponStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseStats_MetaData), NewProp_BaseStats_MetaData) }; // 2665847082
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_Condition_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_Condition = { "Condition", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, Condition), Z_Construct_UEnum_SLT_EWeaponCondition, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Condition_MetaData), NewProp_Condition_MetaData) }; // 2945174532
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CurrentAmmo = { "CurrentAmmo", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, CurrentAmmo), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAmmo_MetaData), NewProp_CurrentAmmo_MetaData) };
void Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bIsAutomatic_SetBit(void* Obj)
{
	((FWeaponData*)Obj)->bIsAutomatic = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bIsAutomatic = { "bIsAutomatic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWeaponData), &Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bIsAutomatic_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsAutomatic_MetaData), NewProp_bIsAutomatic_MetaData) };
void Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bCanBeUpgraded_SetBit(void* Obj)
{
	((FWeaponData*)Obj)->bCanBeUpgraded = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bCanBeUpgraded = { "bCanBeUpgraded", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWeaponData), &Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bCanBeUpgraded_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanBeUpgraded_MetaData), NewProp_bCanBeUpgraded_MetaData) };
void Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bRequiresTwoHands_SetBit(void* Obj)
{
	((FWeaponData*)Obj)->bRequiresTwoHands = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bRequiresTwoHands = { "bRequiresTwoHands", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FWeaponData), &Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bRequiresTwoHands_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresTwoHands_MetaData), NewProp_bRequiresTwoHands_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AvailableUpgrades_Inner = { "AvailableUpgrades", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWeaponUpgrade, METADATA_PARAMS(0, nullptr) }; // 809889804
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AvailableUpgrades = { "AvailableUpgrades", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, AvailableUpgrades), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AvailableUpgrades_MetaData), NewProp_AvailableUpgrades_MetaData) }; // 809889804
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponMesh = { "WeaponMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponMesh), Z_Construct_UClass_USkeletalMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponMesh_MetaData), NewProp_WeaponMesh_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponIcon = { "WeaponIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, WeaponIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponIcon_MetaData), NewProp_WeaponIcon_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_FireAnimation = { "FireAnimation", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, FireAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireAnimation_MetaData), NewProp_FireAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_ReloadAnimation = { "ReloadAnimation", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, ReloadAnimation), Z_Construct_UClass_UAnimMontage_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadAnimation_MetaData), NewProp_ReloadAnimation_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_FireSound = { "FireSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, FireSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FireSound_MetaData), NewProp_FireSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_ReloadSound = { "ReloadSound", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, ReloadSound), Z_Construct_UClass_USoundBase_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ReloadSound_MetaData), NewProp_ReloadSound_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_MuzzleFlash = { "MuzzleFlash", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, MuzzleFlash), Z_Construct_UClass_UNiagaraSystem_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MuzzleFlash_MetaData), NewProp_MuzzleFlash_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties_ValueProp = { "CustomProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties_Key_KeyProp = { "CustomProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties = { "CustomProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWeaponData, CustomProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProperties_MetaData), NewProp_CustomProperties_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWeaponData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AmmoType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AmmoType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_BaseStats,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_Condition_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_Condition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CurrentAmmo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bIsAutomatic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bCanBeUpgraded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_bRequiresTwoHands,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AvailableUpgrades_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_AvailableUpgrades,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_WeaponIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_FireAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_ReloadAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_FireSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_ReloadSound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_MuzzleFlash,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWeaponData_Statics::NewProp_CustomProperties,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWeaponData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"WeaponData",
	Z_Construct_UScriptStruct_FWeaponData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponData_Statics::PropPointers),
	sizeof(FWeaponData),
	alignof(FWeaponData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWeaponData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWeaponData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWeaponData()
{
	if (!Z_Registration_Info_UScriptStruct_WeaponData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WeaponData.InnerSingleton, Z_Construct_UScriptStruct_FWeaponData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WeaponData.InnerSingleton;
}
// End ScriptStruct FWeaponData

// Begin Delegate FOnWeaponFired
struct Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnWeaponFired_Parms
	{
		FName WeaponID;
		int32 RemainingAmmo;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RemainingAmmo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponFired_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::NewProp_RemainingAmmo = { "RemainingAmmo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponFired_Parms, RemainingAmmo), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::NewProp_RemainingAmmo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnWeaponFired__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::_Script_SLT_eventOnWeaponFired_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::_Script_SLT_eventOnWeaponFired_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponFired_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponFired, FName WeaponID, int32 RemainingAmmo)
{
	struct _Script_SLT_eventOnWeaponFired_Parms
	{
		FName WeaponID;
		int32 RemainingAmmo;
	};
	_Script_SLT_eventOnWeaponFired_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.RemainingAmmo=RemainingAmmo;
	OnWeaponFired.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponFired

// Begin Delegate FOnWeaponReloaded
struct Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnWeaponReloaded_Parms
	{
		FName WeaponID;
		int32 NewAmmoCount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewAmmoCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponReloaded_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::NewProp_NewAmmoCount = { "NewAmmoCount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponReloaded_Parms, NewAmmoCount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::NewProp_NewAmmoCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnWeaponReloaded__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::_Script_SLT_eventOnWeaponReloaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::_Script_SLT_eventOnWeaponReloaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponReloaded_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponReloaded, FName WeaponID, int32 NewAmmoCount)
{
	struct _Script_SLT_eventOnWeaponReloaded_Parms
	{
		FName WeaponID;
		int32 NewAmmoCount;
	};
	_Script_SLT_eventOnWeaponReloaded_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.NewAmmoCount=NewAmmoCount;
	OnWeaponReloaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponReloaded

// Begin Delegate FOnWeaponUpgraded
struct Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnWeaponUpgraded_Parms
	{
		FName WeaponID;
		FName UpgradeID;
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponUpgraded_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponUpgraded_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponUpgraded_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnWeaponUpgraded__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::_Script_SLT_eventOnWeaponUpgraded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::_Script_SLT_eventOnWeaponUpgraded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponUpgraded_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponUpgraded, FName WeaponID, FName UpgradeID, int32 NewLevel)
{
	struct _Script_SLT_eventOnWeaponUpgraded_Parms
	{
		FName WeaponID;
		FName UpgradeID;
		int32 NewLevel;
	};
	_Script_SLT_eventOnWeaponUpgraded_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.UpgradeID=UpgradeID;
	Parms.NewLevel=NewLevel;
	OnWeaponUpgraded.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponUpgraded

// Begin Delegate FOnWeaponConditionChanged
struct Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnWeaponConditionChanged_Parms
	{
		FName WeaponID;
		EWeaponCondition NewCondition;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewCondition_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewCondition;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponConditionChanged_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_NewCondition_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_NewCondition = { "NewCondition", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponConditionChanged_Parms, NewCondition), Z_Construct_UEnum_SLT_EWeaponCondition, METADATA_PARAMS(0, nullptr) }; // 2945174532
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_NewCondition_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::NewProp_NewCondition,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnWeaponConditionChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::_Script_SLT_eventOnWeaponConditionChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::_Script_SLT_eventOnWeaponConditionChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponConditionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponConditionChanged, FName WeaponID, EWeaponCondition NewCondition)
{
	struct _Script_SLT_eventOnWeaponConditionChanged_Parms
	{
		FName WeaponID;
		EWeaponCondition NewCondition;
	};
	_Script_SLT_eventOnWeaponConditionChanged_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.NewCondition=NewCondition;
	OnWeaponConditionChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponConditionChanged

// Begin Delegate FOnWeaponEquipped
struct Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnWeaponEquipped_Parms
	{
		FName WeaponID;
		bool bIsEquipped;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_bIsEquipped_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEquipped;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnWeaponEquipped_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_bIsEquipped_SetBit(void* Obj)
{
	((_Script_SLT_eventOnWeaponEquipped_Parms*)Obj)->bIsEquipped = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_bIsEquipped = { "bIsEquipped", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnWeaponEquipped_Parms), &Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_bIsEquipped_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::NewProp_bIsEquipped,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnWeaponEquipped__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::_Script_SLT_eventOnWeaponEquipped_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::_Script_SLT_eventOnWeaponEquipped_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnWeaponEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponEquipped, FName WeaponID, bool bIsEquipped)
{
	struct _Script_SLT_eventOnWeaponEquipped_Parms
	{
		FName WeaponID;
		bool bIsEquipped;
	};
	_Script_SLT_eventOnWeaponEquipped_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.bIsEquipped=bIsEquipped ? true : false;
	OnWeaponEquipped.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnWeaponEquipped

// Begin Class UWeaponComponent Function AddWeapon
struct Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics
{
	struct WeaponComponent_eventAddWeapon_Parms
	{
		FName WeaponID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon management\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventAddWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventAddWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventAddWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "AddWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::WeaponComponent_eventAddWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::WeaponComponent_eventAddWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_AddWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_AddWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execAddWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function AddWeapon

// Begin Class UWeaponComponent Function CanFire
struct Z_Construct_UFunction_UWeaponComponent_CanFire_Statics
{
	struct WeaponComponent_eventCanFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Combat functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Combat functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventCanFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventCanFire_Parms), &Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "CanFire", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::WeaponComponent_eventCanFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::WeaponComponent_eventCanFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_CanFire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_CanFire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execCanFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanFire();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function CanFire

// Begin Class UWeaponComponent Function CanUpgradeWeapon
struct Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics
{
	struct WeaponComponent_eventCanUpgradeWeapon_Parms
	{
		FName WeaponID;
		FName UpgradeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Upgrade functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Upgrade functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventCanUpgradeWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventCanUpgradeWeapon_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventCanUpgradeWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventCanUpgradeWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "CanUpgradeWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::WeaponComponent_eventCanUpgradeWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::WeaponComponent_eventCanUpgradeWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execCanUpgradeWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanUpgradeWeapon(Z_Param_WeaponID,Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function CanUpgradeWeapon

// Begin Class UWeaponComponent Function ConsumeAmmo
struct Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics
{
	struct WeaponComponent_eventConsumeAmmo_Parms
	{
		EAmmoType AmmoType;
		int32 Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AmmoType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AmmoType;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_AmmoType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_AmmoType = { "AmmoType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventConsumeAmmo_Parms, AmmoType), Z_Construct_UEnum_SLT_EAmmoType, METADATA_PARAMS(0, nullptr) }; // 793778902
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventConsumeAmmo_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventConsumeAmmo_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventConsumeAmmo_Parms), &Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_AmmoType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_AmmoType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "ConsumeAmmo", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::WeaponComponent_eventConsumeAmmo_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::WeaponComponent_eventConsumeAmmo_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execConsumeAmmo)
{
	P_GET_ENUM(EAmmoType,Z_Param_AmmoType);
	P_GET_PROPERTY(FIntProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConsumeAmmo(EAmmoType(Z_Param_AmmoType),Z_Param_Amount);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function ConsumeAmmo

// Begin Class UWeaponComponent Function DamageWeapon
struct Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics
{
	struct WeaponComponent_eventDamageWeapon_Parms
	{
		FName WeaponID;
		float DamageAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Condition" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Condition functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Condition functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DamageAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventDamageWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::NewProp_DamageAmount = { "DamageAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventDamageWeapon_Parms, DamageAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::NewProp_DamageAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "DamageWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::WeaponComponent_eventDamageWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::WeaponComponent_eventDamageWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_DamageWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_DamageWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execDamageWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_DamageAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DamageWeapon(Z_Param_WeaponID,Z_Param_DamageAmount);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function DamageWeapon

// Begin Class UWeaponComponent Function EquipWeapon
struct Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics
{
	struct WeaponComponent_eventEquipWeapon_Parms
	{
		FName WeaponID;
		FString SlotName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Equipment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Equipment functions\n" },
#endif
		{ "CPP_Default_SlotName", "Primary" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipment functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SlotName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventEquipWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_SlotName = { "SlotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventEquipWeapon_Parms, SlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotName_MetaData), NewProp_SlotName_MetaData) };
void Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventEquipWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventEquipWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_SlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "EquipWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::WeaponComponent_eventEquipWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::WeaponComponent_eventEquipWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_EquipWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_EquipWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execEquipWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FStrProperty,Z_Param_SlotName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->EquipWeapon(Z_Param_WeaponID,Z_Param_SlotName);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function EquipWeapon

// Begin Class UWeaponComponent Function Fire
struct Z_Construct_UFunction_UWeaponComponent_Fire_Statics
{
	struct WeaponComponent_eventFire_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UWeaponComponent_Fire_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventFire_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_Fire_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventFire_Parms), &Z_Construct_UFunction_UWeaponComponent_Fire_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_Fire_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_Fire_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Fire_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_Fire_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "Fire", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_Fire_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Fire_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_Fire_Statics::WeaponComponent_eventFire_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Fire_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_Fire_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_Fire_Statics::WeaponComponent_eventFire_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_Fire()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_Fire_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execFire)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Fire();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function Fire

// Begin Class UWeaponComponent Function GetAccuracy
struct Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics
{
	struct WeaponComponent_eventGetAccuracy_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetAccuracy_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetAccuracy", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::WeaponComponent_eventGetAccuracy_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::WeaponComponent_eventGetAccuracy_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetAccuracy()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetAccuracy_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetAccuracy)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAccuracy();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetAccuracy

// Begin Class UWeaponComponent Function GetAvailableUpgrades
struct Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics
{
	struct WeaponComponent_eventGetAvailableUpgrades_Parms
	{
		FName WeaponID;
		TArray<FWeaponUpgrade> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetAvailableUpgrades_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FWeaponUpgrade, METADATA_PARAMS(0, nullptr) }; // 809889804
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetAvailableUpgrades_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 809889804
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetAvailableUpgrades", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::WeaponComponent_eventGetAvailableUpgrades_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::WeaponComponent_eventGetAvailableUpgrades_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetAvailableUpgrades)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FWeaponUpgrade>*)Z_Param__Result=P_THIS->GetAvailableUpgrades(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetAvailableUpgrades

// Begin Class UWeaponComponent Function GetDamage
struct Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics
{
	struct WeaponComponent_eventGetDamage_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetDamage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetDamage", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::WeaponComponent_eventGetDamage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::WeaponComponent_eventGetDamage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetDamage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetDamage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetDamage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetDamage();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetDamage

// Begin Class UWeaponComponent Function GetEquippedWeapon
struct Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics
{
	struct WeaponComponent_eventGetEquippedWeapon_Parms
	{
		FString SlotName;
		FName ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Equipment" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SlotName;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::NewProp_SlotName = { "SlotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetEquippedWeapon_Parms, SlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotName_MetaData), NewProp_SlotName_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetEquippedWeapon_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::NewProp_SlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetEquippedWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::WeaponComponent_eventGetEquippedWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::WeaponComponent_eventGetEquippedWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetEquippedWeapon)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SlotName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FName*)Z_Param__Result=P_THIS->GetEquippedWeapon(Z_Param_SlotName);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetEquippedWeapon

// Begin Class UWeaponComponent Function GetOwnedWeapons
struct Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics
{
	struct WeaponComponent_eventGetOwnedWeapons_Parms
	{
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetOwnedWeapons_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetOwnedWeapons", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::WeaponComponent_eventGetOwnedWeapons_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::WeaponComponent_eventGetOwnedWeapons_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetOwnedWeapons)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetOwnedWeapons();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetOwnedWeapons

// Begin Class UWeaponComponent Function GetUpgradeCost
struct Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics
{
	struct WeaponComponent_eventGetUpgradeCost_Parms
	{
		FName WeaponID;
		FName UpgradeID;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetUpgradeCost_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetUpgradeCost_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetUpgradeCost_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetUpgradeCost", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::WeaponComponent_eventGetUpgradeCost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::WeaponComponent_eventGetUpgradeCost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetUpgradeCost)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetUpgradeCost(Z_Param_WeaponID,Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetUpgradeCost

// Begin Class UWeaponComponent Function GetWeaponCondition
struct Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics
{
	struct WeaponComponent_eventGetWeaponCondition_Parms
	{
		FName WeaponID;
		EWeaponCondition ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponCondition_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponCondition_Parms, ReturnValue), Z_Construct_UEnum_SLT_EWeaponCondition, METADATA_PARAMS(0, nullptr) }; // 2945174532
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetWeaponCondition", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::WeaponComponent_eventGetWeaponCondition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::WeaponComponent_eventGetWeaponCondition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetWeaponCondition)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EWeaponCondition*)Z_Param__Result=P_THIS->GetWeaponCondition(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetWeaponCondition

// Begin Class UWeaponComponent Function GetWeaponData
struct Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics
{
	struct WeaponComponent_eventGetWeaponData_Parms
	{
		FName WeaponID;
		FWeaponData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponData_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponData_Parms, ReturnValue), Z_Construct_UScriptStruct_FWeaponData, METADATA_PARAMS(0, nullptr) }; // 112663130
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetWeaponData", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::WeaponComponent_eventGetWeaponData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::WeaponComponent_eventGetWeaponData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetWeaponData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetWeaponData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetWeaponData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FWeaponData*)Z_Param__Result=P_THIS->GetWeaponData(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetWeaponData

// Begin Class UWeaponComponent Function GetWeaponsByType
struct Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics
{
	struct WeaponComponent_eventGetWeaponsByType_Parms
	{
		EWeaponType WeaponType;
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_WeaponType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_WeaponType;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_WeaponType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_WeaponType = { "WeaponType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponsByType_Parms, WeaponType), Z_Construct_UEnum_SLT_EWeaponType, METADATA_PARAMS(0, nullptr) }; // 502516317
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventGetWeaponsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_WeaponType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_WeaponType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "GetWeaponsByType", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::WeaponComponent_eventGetWeaponsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::WeaponComponent_eventGetWeaponsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execGetWeaponsByType)
{
	P_GET_ENUM(EWeaponType,Z_Param_WeaponType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetWeaponsByType(EWeaponType(Z_Param_WeaponType));
	P_NATIVE_END;
}
// End Class UWeaponComponent Function GetWeaponsByType

// Begin Class UWeaponComponent Function HasWeapon
struct Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics
{
	struct WeaponComponent_eventHasWeapon_Parms
	{
		FName WeaponID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventHasWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventHasWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventHasWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "HasWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::WeaponComponent_eventHasWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::WeaponComponent_eventHasWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_HasWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_HasWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execHasWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function HasWeapon

// Begin Class UWeaponComponent Function OnAmmoChanged
struct WeaponComponent_eventOnAmmoChanged_Parms
{
	FName WeaponID;
	int32 CurrentAmmo;
	int32 MaxAmmo;
};
static const FName NAME_UWeaponComponent_OnAmmoChanged = FName(TEXT("OnAmmoChanged"));
void UWeaponComponent::OnAmmoChanged(FName WeaponID, int32 CurrentAmmo, int32 MaxAmmo)
{
	WeaponComponent_eventOnAmmoChanged_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.CurrentAmmo=CurrentAmmo;
	Parms.MaxAmmo=MaxAmmo;
	UFunction* Func = FindFunctionChecked(NAME_UWeaponComponent_OnAmmoChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentAmmo;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxAmmo;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventOnAmmoChanged_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_CurrentAmmo = { "CurrentAmmo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventOnAmmoChanged_Parms, CurrentAmmo), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_MaxAmmo = { "MaxAmmo", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventOnAmmoChanged_Parms, MaxAmmo), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_CurrentAmmo,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::NewProp_MaxAmmo,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "OnAmmoChanged", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::PropPointers), sizeof(WeaponComponent_eventOnAmmoChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(WeaponComponent_eventOnAmmoChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UWeaponComponent Function OnAmmoChanged

// Begin Class UWeaponComponent Function OnWeaponStatsChanged
struct WeaponComponent_eventOnWeaponStatsChanged_Parms
{
	FName WeaponID;
	FWeaponStats NewStats;
};
static const FName NAME_UWeaponComponent_OnWeaponStatsChanged = FName(TEXT("OnWeaponStatsChanged"));
void UWeaponComponent::OnWeaponStatsChanged(FName WeaponID, FWeaponStats const& NewStats)
{
	WeaponComponent_eventOnWeaponStatsChanged_Parms Parms;
	Parms.WeaponID=WeaponID;
	Parms.NewStats=NewStats;
	UFunction* Func = FindFunctionChecked(NAME_UWeaponComponent_OnWeaponStatsChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NewStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_NewStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventOnWeaponStatsChanged_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::NewProp_NewStats = { "NewStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventOnWeaponStatsChanged_Parms, NewStats), Z_Construct_UScriptStruct_FWeaponStats, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NewStats_MetaData), NewProp_NewStats_MetaData) }; // 2665847082
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::NewProp_NewStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "OnWeaponStatsChanged", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::PropPointers), sizeof(WeaponComponent_eventOnWeaponStatsChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(WeaponComponent_eventOnWeaponStatsChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UWeaponComponent Function OnWeaponStatsChanged

// Begin Class UWeaponComponent Function Reload
struct Z_Construct_UFunction_UWeaponComponent_Reload_Statics
{
	struct WeaponComponent_eventReload_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Combat" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UWeaponComponent_Reload_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventReload_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_Reload_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventReload_Parms), &Z_Construct_UFunction_UWeaponComponent_Reload_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_Reload_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_Reload_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Reload_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_Reload_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "Reload", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_Reload_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Reload_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_Reload_Statics::WeaponComponent_eventReload_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_Reload_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_Reload_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_Reload_Statics::WeaponComponent_eventReload_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_Reload()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_Reload_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execReload)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->Reload();
	P_NATIVE_END;
}
// End Class UWeaponComponent Function Reload

// Begin Class UWeaponComponent Function RemoveWeapon
struct Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics
{
	struct WeaponComponent_eventRemoveWeapon_Parms
	{
		FName WeaponID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Weapons" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventRemoveWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventRemoveWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventRemoveWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "RemoveWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::WeaponComponent_eventRemoveWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::WeaponComponent_eventRemoveWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_RemoveWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_RemoveWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execRemoveWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function RemoveWeapon

// Begin Class UWeaponComponent Function RepairWeapon
struct Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics
{
	struct WeaponComponent_eventRepairWeapon_Parms
	{
		FName WeaponID;
		float RepairAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Condition" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RepairAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventRepairWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::NewProp_RepairAmount = { "RepairAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventRepairWeapon_Parms, RepairAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::NewProp_RepairAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "RepairWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::WeaponComponent_eventRepairWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::WeaponComponent_eventRepairWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_RepairWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_RepairWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execRepairWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_RepairAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RepairWeapon(Z_Param_WeaponID,Z_Param_RepairAmount);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function RepairWeapon

// Begin Class UWeaponComponent Function SwitchToWeapon
struct Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics
{
	struct WeaponComponent_eventSwitchToWeapon_Parms
	{
		FName WeaponID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Equipment" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventSwitchToWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventSwitchToWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventSwitchToWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "SwitchToWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::WeaponComponent_eventSwitchToWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::WeaponComponent_eventSwitchToWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execSwitchToWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SwitchToWeapon(Z_Param_WeaponID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function SwitchToWeapon

// Begin Class UWeaponComponent Function UnequipWeapon
struct Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics
{
	struct WeaponComponent_eventUnequipWeapon_Parms
	{
		FString SlotName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Equipment" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SlotName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_SlotName = { "SlotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventUnequipWeapon_Parms, SlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotName_MetaData), NewProp_SlotName_MetaData) };
void Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventUnequipWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventUnequipWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_SlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "UnequipWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::WeaponComponent_eventUnequipWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::WeaponComponent_eventUnequipWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_UnequipWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_UnequipWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execUnequipWeapon)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_SlotName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UnequipWeapon(Z_Param_SlotName);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function UnequipWeapon

// Begin Class UWeaponComponent Function UpgradeWeapon
struct Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics
{
	struct WeaponComponent_eventUpgradeWeapon_Parms
	{
		FName WeaponID;
		FName UpgradeID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Upgrades" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_WeaponID;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UpgradeID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_WeaponID = { "WeaponID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventUpgradeWeapon_Parms, WeaponID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_UpgradeID = { "UpgradeID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(WeaponComponent_eventUpgradeWeapon_Parms, UpgradeID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((WeaponComponent_eventUpgradeWeapon_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(WeaponComponent_eventUpgradeWeapon_Parms), &Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_WeaponID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_UpgradeID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UWeaponComponent, nullptr, "UpgradeWeapon", nullptr, nullptr, Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::PropPointers), sizeof(Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::WeaponComponent_eventUpgradeWeapon_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::Function_MetaDataParams), Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::WeaponComponent_eventUpgradeWeapon_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UWeaponComponent::execUpgradeWeapon)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_WeaponID);
	P_GET_PROPERTY(FNameProperty,Z_Param_UpgradeID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->UpgradeWeapon(Z_Param_WeaponID,Z_Param_UpgradeID);
	P_NATIVE_END;
}
// End Class UWeaponComponent Function UpgradeWeapon

// Begin Class UWeaponComponent
void UWeaponComponent::StaticRegisterNativesUWeaponComponent()
{
	UClass* Class = UWeaponComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddWeapon", &UWeaponComponent::execAddWeapon },
		{ "CanFire", &UWeaponComponent::execCanFire },
		{ "CanUpgradeWeapon", &UWeaponComponent::execCanUpgradeWeapon },
		{ "ConsumeAmmo", &UWeaponComponent::execConsumeAmmo },
		{ "DamageWeapon", &UWeaponComponent::execDamageWeapon },
		{ "EquipWeapon", &UWeaponComponent::execEquipWeapon },
		{ "Fire", &UWeaponComponent::execFire },
		{ "GetAccuracy", &UWeaponComponent::execGetAccuracy },
		{ "GetAvailableUpgrades", &UWeaponComponent::execGetAvailableUpgrades },
		{ "GetDamage", &UWeaponComponent::execGetDamage },
		{ "GetEquippedWeapon", &UWeaponComponent::execGetEquippedWeapon },
		{ "GetOwnedWeapons", &UWeaponComponent::execGetOwnedWeapons },
		{ "GetUpgradeCost", &UWeaponComponent::execGetUpgradeCost },
		{ "GetWeaponCondition", &UWeaponComponent::execGetWeaponCondition },
		{ "GetWeaponData", &UWeaponComponent::execGetWeaponData },
		{ "GetWeaponsByType", &UWeaponComponent::execGetWeaponsByType },
		{ "HasWeapon", &UWeaponComponent::execHasWeapon },
		{ "Reload", &UWeaponComponent::execReload },
		{ "RemoveWeapon", &UWeaponComponent::execRemoveWeapon },
		{ "RepairWeapon", &UWeaponComponent::execRepairWeapon },
		{ "SwitchToWeapon", &UWeaponComponent::execSwitchToWeapon },
		{ "UnequipWeapon", &UWeaponComponent::execUnequipWeapon },
		{ "UpgradeWeapon", &UWeaponComponent::execUpgradeWeapon },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UWeaponComponent);
UClass* Z_Construct_UClass_UWeaponComponent_NoRegister()
{
	return UWeaponComponent::StaticClass();
}
struct Z_Construct_UClass_UWeaponComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "Core/Systems/WeaponSystem.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponDatabase_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon database\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon database" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedWeapons_MetaData[] = {
		{ "Category", "Equipment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current equipped weapons\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current equipped weapons" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentWeapon_MetaData[] = {
		{ "Category", "Equipment" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Slot -> WeaponID\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Slot -> WeaponID" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OwnedWeapons_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weapon inventory\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weapon inventory" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponFired_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponReloaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponUpgraded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponConditionChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnWeaponEquipped_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedWeaponDatabase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached database\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/WeaponSystem.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached database" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_WeaponDatabase;
	static const UECodeGen_Private::FNamePropertyParams NewProp_EquippedWeapons_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EquippedWeapons_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EquippedWeapons;
	static const UECodeGen_Private::FNamePropertyParams NewProp_CurrentWeapon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OwnedWeapons_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_OwnedWeapons_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_OwnedWeapons;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponFired;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponReloaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponUpgraded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponConditionChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnWeaponEquipped;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedWeaponDatabase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UWeaponComponent_AddWeapon, "AddWeapon" }, // 1295175173
		{ &Z_Construct_UFunction_UWeaponComponent_CanFire, "CanFire" }, // 1167830245
		{ &Z_Construct_UFunction_UWeaponComponent_CanUpgradeWeapon, "CanUpgradeWeapon" }, // 1546506937
		{ &Z_Construct_UFunction_UWeaponComponent_ConsumeAmmo, "ConsumeAmmo" }, // 3615315799
		{ &Z_Construct_UFunction_UWeaponComponent_DamageWeapon, "DamageWeapon" }, // 905924660
		{ &Z_Construct_UFunction_UWeaponComponent_EquipWeapon, "EquipWeapon" }, // 936140056
		{ &Z_Construct_UFunction_UWeaponComponent_Fire, "Fire" }, // 3940226230
		{ &Z_Construct_UFunction_UWeaponComponent_GetAccuracy, "GetAccuracy" }, // 3186913444
		{ &Z_Construct_UFunction_UWeaponComponent_GetAvailableUpgrades, "GetAvailableUpgrades" }, // 907770553
		{ &Z_Construct_UFunction_UWeaponComponent_GetDamage, "GetDamage" }, // 3755946179
		{ &Z_Construct_UFunction_UWeaponComponent_GetEquippedWeapon, "GetEquippedWeapon" }, // 1520302741
		{ &Z_Construct_UFunction_UWeaponComponent_GetOwnedWeapons, "GetOwnedWeapons" }, // 49954354
		{ &Z_Construct_UFunction_UWeaponComponent_GetUpgradeCost, "GetUpgradeCost" }, // 3557147709
		{ &Z_Construct_UFunction_UWeaponComponent_GetWeaponCondition, "GetWeaponCondition" }, // 3032209004
		{ &Z_Construct_UFunction_UWeaponComponent_GetWeaponData, "GetWeaponData" }, // 4049652007
		{ &Z_Construct_UFunction_UWeaponComponent_GetWeaponsByType, "GetWeaponsByType" }, // 2422385168
		{ &Z_Construct_UFunction_UWeaponComponent_HasWeapon, "HasWeapon" }, // 1592696747
		{ &Z_Construct_UFunction_UWeaponComponent_OnAmmoChanged, "OnAmmoChanged" }, // 256460794
		{ &Z_Construct_UFunction_UWeaponComponent_OnWeaponStatsChanged, "OnWeaponStatsChanged" }, // 3034812318
		{ &Z_Construct_UFunction_UWeaponComponent_Reload, "Reload" }, // 380363996
		{ &Z_Construct_UFunction_UWeaponComponent_RemoveWeapon, "RemoveWeapon" }, // 1276935843
		{ &Z_Construct_UFunction_UWeaponComponent_RepairWeapon, "RepairWeapon" }, // 1909402772
		{ &Z_Construct_UFunction_UWeaponComponent_SwitchToWeapon, "SwitchToWeapon" }, // 362696462
		{ &Z_Construct_UFunction_UWeaponComponent_UnequipWeapon, "UnequipWeapon" }, // 3687100251
		{ &Z_Construct_UFunction_UWeaponComponent_UpgradeWeapon, "UpgradeWeapon" }, // 1003434750
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UWeaponComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_WeaponDatabase = { "WeaponDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, WeaponDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponDatabase_MetaData), NewProp_WeaponDatabase_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons_ValueProp = { "EquippedWeapons", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons_Key_KeyProp = { "EquippedWeapons_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons = { "EquippedWeapons", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, EquippedWeapons), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedWeapons_MetaData), NewProp_EquippedWeapons_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_CurrentWeapon = { "CurrentWeapon", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, CurrentWeapon), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentWeapon_MetaData), NewProp_CurrentWeapon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons_ValueProp = { "OwnedWeapons", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FWeaponData, METADATA_PARAMS(0, nullptr) }; // 112663130
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons_Key_KeyProp = { "OwnedWeapons_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons = { "OwnedWeapons", nullptr, (EPropertyFlags)0x0010000001020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OwnedWeapons), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OwnedWeapons_MetaData), NewProp_OwnedWeapons_MetaData) }; // 112663130
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponFired = { "OnWeaponFired", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OnWeaponFired), Z_Construct_UDelegateFunction_SLT_OnWeaponFired__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponFired_MetaData), NewProp_OnWeaponFired_MetaData) }; // 2840153664
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponReloaded = { "OnWeaponReloaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OnWeaponReloaded), Z_Construct_UDelegateFunction_SLT_OnWeaponReloaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponReloaded_MetaData), NewProp_OnWeaponReloaded_MetaData) }; // 2290355728
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponUpgraded = { "OnWeaponUpgraded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OnWeaponUpgraded), Z_Construct_UDelegateFunction_SLT_OnWeaponUpgraded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponUpgraded_MetaData), NewProp_OnWeaponUpgraded_MetaData) }; // 580875210
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponConditionChanged = { "OnWeaponConditionChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OnWeaponConditionChanged), Z_Construct_UDelegateFunction_SLT_OnWeaponConditionChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponConditionChanged_MetaData), NewProp_OnWeaponConditionChanged_MetaData) }; // 3440452361
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponEquipped = { "OnWeaponEquipped", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, OnWeaponEquipped), Z_Construct_UDelegateFunction_SLT_OnWeaponEquipped__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnWeaponEquipped_MetaData), NewProp_OnWeaponEquipped_MetaData) }; // 3069593907
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UWeaponComponent_Statics::NewProp_CachedWeaponDatabase = { "CachedWeaponDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UWeaponComponent, CachedWeaponDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedWeaponDatabase_MetaData), NewProp_CachedWeaponDatabase_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UWeaponComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_WeaponDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_EquippedWeapons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_CurrentWeapon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OwnedWeapons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponFired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponReloaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponUpgraded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponConditionChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_OnWeaponEquipped,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UWeaponComponent_Statics::NewProp_CachedWeaponDatabase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UWeaponComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UWeaponComponent_Statics::ClassParams = {
	&UWeaponComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UWeaponComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UWeaponComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UWeaponComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UWeaponComponent()
{
	if (!Z_Registration_Info_UClass_UWeaponComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UWeaponComponent.OuterSingleton, Z_Construct_UClass_UWeaponComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UWeaponComponent.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UWeaponComponent>()
{
	return UWeaponComponent::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UWeaponComponent);
UWeaponComponent::~UWeaponComponent() {}
// End Class UWeaponComponent

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EWeaponType_StaticEnum, TEXT("EWeaponType"), &Z_Registration_Info_UEnum_EWeaponType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 502516317U) },
		{ EWeaponCondition_StaticEnum, TEXT("EWeaponCondition"), &Z_Registration_Info_UEnum_EWeaponCondition, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2945174532U) },
		{ EAmmoType_StaticEnum, TEXT("EAmmoType"), &Z_Registration_Info_UEnum_EAmmoType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 793778902U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FWeaponUpgrade::StaticStruct, Z_Construct_UScriptStruct_FWeaponUpgrade_Statics::NewStructOps, TEXT("WeaponUpgrade"), &Z_Registration_Info_UScriptStruct_WeaponUpgrade, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWeaponUpgrade), 809889804U) },
		{ FWeaponStats::StaticStruct, Z_Construct_UScriptStruct_FWeaponStats_Statics::NewStructOps, TEXT("WeaponStats"), &Z_Registration_Info_UScriptStruct_WeaponStats, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWeaponStats), 2665847082U) },
		{ FWeaponData::StaticStruct, Z_Construct_UScriptStruct_FWeaponData_Statics::NewStructOps, TEXT("WeaponData"), &Z_Registration_Info_UScriptStruct_WeaponData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWeaponData), 112663130U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UWeaponComponent, UWeaponComponent::StaticClass, TEXT("UWeaponComponent"), &Z_Registration_Info_UClass_UWeaponComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UWeaponComponent), 510220151U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_3133506627(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
