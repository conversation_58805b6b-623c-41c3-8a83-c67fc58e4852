// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/LoadingManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class EAssetPriority : uint8;
enum class ELoadingState : uint8;
struct FLoadingProgress;
struct FLoadingTip;
struct FSoftObjectPath;
#ifdef SLT_LoadingManager_generated_h
#error "LoadingManager.generated.h already included, missing '#pragma once' in LoadingManager.h"
#endif
#define SLT_LoadingManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_38_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLoadingTip_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FLoadingTip>();

#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_71_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAssetLoadRequest_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FAssetLoadRequest>();

#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_107_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLoadingProgress_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FLoadingProgress>();

#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_153_DELEGATE \
SLT_API void FOnLoadingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingStateChanged, ELoadingState NewState);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_154_DELEGATE \
SLT_API void FOnLoadingProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingProgressUpdated, FLoadingProgress const& Progress);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_155_DELEGATE \
SLT_API void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, const FString& AssetPath, bool bSuccess);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_156_DELEGATE \
SLT_API void FOnLoadingComplete_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingComplete, bool bSuccess);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_157_DELEGATE \
SLT_API void FOnLoadingError_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingError, const FString& ErrorMessage, const FString& AssetPath);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOptimizeMemory); \
	DECLARE_FUNCTION(execGetMemoryUsage); \
	DECLARE_FUNCTION(execClearAssetCache); \
	DECLARE_FUNCTION(execSetAssetPriority); \
	DECLARE_FUNCTION(execGetEstimatedTimeRemaining); \
	DECLARE_FUNCTION(execAddLoadingMessage); \
	DECLARE_FUNCTION(execUpdateProgress); \
	DECLARE_FUNCTION(execGetRandomLoadingTip); \
	DECLARE_FUNCTION(execHideLoadingScreen); \
	DECLARE_FUNCTION(execShowLoadingScreen); \
	DECLARE_FUNCTION(execIsLevelStreamed); \
	DECLARE_FUNCTION(execUnstreamLevel); \
	DECLARE_FUNCTION(execStreamLevel); \
	DECLARE_FUNCTION(execUnloadAssets); \
	DECLARE_FUNCTION(execPreloadAssets); \
	DECLARE_FUNCTION(execLoadAssetAsync); \
	DECLARE_FUNCTION(execLoadAssets); \
	DECLARE_FUNCTION(execLoadAssetBundle); \
	DECLARE_FUNCTION(execIsCurrentlyLoading); \
	DECLARE_FUNCTION(execStopLoading); \
	DECLARE_FUNCTION(execStartLoading);


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesULoadingManager(); \
	friend struct Z_Construct_UClass_ULoadingManager_Statics; \
public: \
	DECLARE_CLASS(ULoadingManager, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(ULoadingManager)


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ULoadingManager(ULoadingManager&&); \
	ULoadingManager(const ULoadingManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ULoadingManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULoadingManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ULoadingManager) \
	NO_API virtual ~ULoadingManager();


#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_159_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_162_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class ULoadingManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_LoadingManager_h


#define FOREACH_ENUM_ELOADINGSTATE(op) \
	op(ELoadingState::Idle) \
	op(ELoadingState::Preparing) \
	op(ELoadingState::Loading) \
	op(ELoadingState::Streaming) \
	op(ELoadingState::Finalizing) \
	op(ELoadingState::Complete) \
	op(ELoadingState::Failed) 

enum class ELoadingState : uint8;
template<> struct TIsUEnumClass<ELoadingState> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ELoadingState>();

#define FOREACH_ENUM_EASSETPRIORITY(op) \
	op(EAssetPriority::Critical) \
	op(EAssetPriority::High) \
	op(EAssetPriority::Medium) \
	op(EAssetPriority::Low) \
	op(EAssetPriority::Background) 

enum class EAssetPriority : uint8;
template<> struct TIsUEnumClass<EAssetPriority> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAssetPriority>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
