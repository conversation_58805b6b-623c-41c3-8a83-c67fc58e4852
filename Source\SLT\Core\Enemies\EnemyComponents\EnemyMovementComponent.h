#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../BaseEnemyCharacter.h"
#include "EnemyMovementComponent.generated.h"

class UCharacterMovementComponent;
class UNavigationSystemV1;

UENUM(BlueprintType)
enum class EEnemyMovementType : uint8
{
	Walking			UMETA(DisplayName = "Walking"),
	Running			UMETA(DisplayName = "Running"),
	Charging		UMETA(DisplayName = "Charging"),
	Sneaking		UMETA(DisplayName = "Sneaking"),
	Flying			UMETA(DisplayName = "Flying"),
	Swimming		UMETA(DisplayName = "Swimming"),
	Climbing		UMETA(DisplayName = "Climbing"),
	Teleporting		UMETA(DisplayName = "Teleporting"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EMovementBehavior : uint8
{
	Direct			UMETA(DisplayName = "Direct"),
	Flanking		UMETA(DisplayName = "Flanking"),
	Circling		UMETA(DisplayName = "Circling"),
	Zigzag			UMETA(DisplayName = "Zigzag"),
	Cautious		UMETA(DisplayName = "Cautious"),
	Aggressive		UMETA(DisplayName = "Aggressive"),
	Evasive			UMETA(DisplayName = "Evasive"),
	Predictive		UMETA(DisplayName = "Predictive")
};

USTRUCT(BlueprintType)
struct FMovementSettings
{
	GENERATED_BODY()

	FMovementSettings()
	{
		MovementType = EEnemyMovementType::Walking;
		MovementBehavior = EMovementBehavior::Direct;
		BaseSpeed = 300.0f;
		MaxSpeed = 600.0f;
		Acceleration = 1000.0f;
		Deceleration = 2000.0f;
		TurnRate = 180.0f;
		StoppingDistance = 100.0f;
		AvoidanceRadius = 150.0f;
		bCanJump = true;
		JumpHeight = 400.0f;
		bCanClimb = false;
		ClimbSpeed = 200.0f;
		bCanSwim = false;
		SwimSpeed = 250.0f;
		bCanFly = false;
		FlySpeed = 400.0f;
		FlightHeight = 500.0f;
		bUseNavMesh = true;
		bAvoidOtherEnemies = true;
		bAvoidObstacles = true;
		PathfindingAccuracy = 50.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Type")
	EEnemyMovementType MovementType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Behavior")
	EMovementBehavior MovementBehavior;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float BaseSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float MaxSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float Acceleration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Speed")
	float Deceleration;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Rotation")
	float TurnRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float StoppingDistance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Avoidance")
	float AvoidanceRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanJump;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanJump"))
	float JumpHeight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanClimb;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanClimb"))
	float ClimbSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanSwim;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanSwim"))
	float SwimSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities")
	bool bCanFly;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanFly"))
	float FlySpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Abilities", meta = (EditCondition = "bCanFly"))
	float FlightHeight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	bool bUseNavMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Avoidance")
	bool bAvoidOtherEnemies;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Avoidance")
	bool bAvoidObstacles;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Navigation")
	float PathfindingAccuracy;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, float> CustomMovementParameters;
};

USTRUCT(BlueprintType)
struct FMovementTarget
{
	GENERATED_BODY()

	FMovementTarget()
	{
		TargetLocation = FVector::ZeroVector;
		TargetActor = nullptr;
		AcceptanceRadius = 100.0f;
		bUseActorLocation = false;
		bRequireLineOfSight = false;
		Priority = 1;
		MovementBehavior = EMovementBehavior::Direct;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	FVector TargetLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	AActor* TargetActor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	float AcceptanceRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	bool bUseActorLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	bool bRequireLineOfSight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	int32 Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	EMovementBehavior MovementBehavior;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Target")
	FGameplayTagContainer TargetTags;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMovementTargetReached, const FVector&, ReachedLocation, AActor*, TargetActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMovementBlocked, const FVector&, BlockedLocation);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMovementTypeChanged, EEnemyMovementType, OldType, EEnemyMovementType, NewType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnMovementBehaviorChanged, EMovementBehavior, OldBehavior, EMovementBehavior, NewBehavior);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UEnemyMovementComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UEnemyMovementComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Movement Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
	FMovementSettings MovementSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Configuration")
	TSoftObjectPtr<UDataTable> MovementDatabase;

	// Current Movement State
	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	FMovementTarget CurrentTarget;

	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	bool bIsMoving = false;

	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	bool bIsBlocked = false;

	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	FVector CurrentVelocity = FVector::ZeroVector;

	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	float CurrentSpeed = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Movement State")
	FVector LastValidLocation = FVector::ZeroVector;

	// Movement Modifiers
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Modifiers")
	float SpeedMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Modifiers")
	float AccelerationMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Modifiers")
	float TurnRateMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement Modifiers")
	bool bMovementDisabled = false;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnMovementTargetReached OnMovementTargetReached;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnMovementBlocked OnMovementBlocked;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnMovementTypeChanged OnMovementTypeChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnMovementBehaviorChanged OnMovementBehaviorChanged;

	// Main Movement Functions
	UFUNCTION(BlueprintCallable, Category = "Movement")
	void InitializeMovement(ABaseEnemyCharacter* OwnerEnemy);

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void SetMovementTarget(const FMovementTarget& Target);

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void MoveToLocation(const FVector& Location, float AcceptanceRadius = 100.0f);

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void MoveToActor(AActor* TargetActor, float AcceptanceRadius = 100.0f);

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void StopMovement();

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void PauseMovement();

	UFUNCTION(BlueprintCallable, Category = "Movement")
	void ResumeMovement();

	// Movement Type Functions
	UFUNCTION(BlueprintCallable, Category = "Movement Type")
	void SetMovementType(EEnemyMovementType NewType);

	UFUNCTION(BlueprintCallable, Category = "Movement Type")
	void SetMovementBehavior(EMovementBehavior NewBehavior);

	UFUNCTION(BlueprintCallable, Category = "Movement Type")
	void SetMovementSpeed(float NewSpeed);

	UFUNCTION(BlueprintCallable, Category = "Movement Type")
	void ApplySpeedModifier(float Multiplier, float Duration = -1.0f);

	// Advanced Movement Functions
	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void PerformJump(const FVector& JumpTarget);

	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void StartClimbing(AActor* ClimbTarget);

	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void StartFlying(float TargetHeight = -1.0f);

	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void StopFlying();

	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void PerformDash(const FVector& DashDirection, float DashDistance = 500.0f);

	UFUNCTION(BlueprintCallable, Category = "Advanced Movement")
	void PerformTeleport(const FVector& TeleportLocation);

	// Pathfinding Functions
	UFUNCTION(BlueprintCallable, Category = "Pathfinding")
	bool FindPathToLocation(const FVector& TargetLocation, TArray<FVector>& OutPath);

	UFUNCTION(BlueprintCallable, Category = "Pathfinding")
	FVector GetNextPathPoint() const;

	UFUNCTION(BlueprintCallable, Category = "Pathfinding")
	bool IsPathBlocked() const;

	UFUNCTION(BlueprintCallable, Category = "Pathfinding")
	void RecalculatePath();

	// Avoidance Functions
	UFUNCTION(BlueprintCallable, Category = "Avoidance")
	FVector CalculateAvoidanceVector() const;

	UFUNCTION(BlueprintCallable, Category = "Avoidance")
	TArray<AActor*> GetNearbyObstacles(float Radius = 300.0f) const;

	UFUNCTION(BlueprintCallable, Category = "Avoidance")
	bool ShouldAvoidActor(AActor* Actor) const;

	// Utility Functions
	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	float GetDistanceToTarget() const;

	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	bool HasReachedTarget() const;

	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	FVector GetMovementDirection() const;

	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	float GetCurrentSpeedPercentage() const;

	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	bool CanReachLocation(const FVector& Location) const;

	UFUNCTION(BlueprintCallable, Category = "Movement Utility")
	FVector PredictTargetLocation(AActor* Target, float PredictionTime = 1.0f) const;

	// Configuration Functions
	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void LoadMovementSettingsFromDatabase();

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	void ApplyMovementSettings(const FMovementSettings& Settings);

	UFUNCTION(BlueprintCallable, Category = "Configuration")
	FMovementSettings GetCurrentMovementSettings() const;

	// Blueprint Events
	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnMovementInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnMovementStarted(const FVector& TargetLocation);

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnMovementStopped();

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnTargetReached(const FVector& ReachedLocation);

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnPathBlocked(const FVector& BlockedLocation);

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnJumpStarted(const FVector& JumpTarget);

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnClimbStarted(AActor* ClimbTarget);

	UFUNCTION(BlueprintImplementableEvent, Category = "Movement Events")
	void OnFlightStarted(float FlightHeight);

protected:
	// Internal references
	UPROPERTY()
	ABaseEnemyCharacter* OwnerEnemyCharacter;

	UPROPERTY()
	UCharacterMovementComponent* CharacterMovement;

	UPROPERTY()
	UNavigationSystemV1* NavigationSystem;

	// Internal state
	UPROPERTY()
	TArray<FVector> CurrentPath;

	UPROPERTY()
	int32 CurrentPathIndex = 0;

	UPROPERTY()
	TMap<FString, float> SpeedModifierTimers;

	UPROPERTY()
	bool bIsPaused = false;

	// Timer handles
	FTimerHandle MovementUpdateTimerHandle;
	FTimerHandle PathRecalculationTimerHandle;

	// Internal functions
	void UpdateMovement(float DeltaTime);
	void UpdatePathfinding();
	void ProcessMovementBehavior();
	void ApplyMovementModifiers();
	void HandleMovementBlocked();
	void UpdateCharacterMovementSettings();
	FVector CalculateFlankingPosition(const FVector& TargetLocation) const;
	FVector CalculateCirclingPosition(const FVector& TargetLocation) const;
	FVector CalculateZigzagPosition(const FVector& TargetLocation) const;
	FVector CalculateEvasivePosition(const FVector& TargetLocation) const;
	bool IsLocationReachable(const FVector& Location) const;
	void CleanupMovement();
};
