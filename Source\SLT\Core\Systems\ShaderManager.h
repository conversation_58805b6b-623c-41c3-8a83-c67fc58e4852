#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "../InventorySystem/Data/InventoryItemData.h"
#include "ShaderManager.generated.h"

UENUM(BlueprintType)
enum class EShaderVariant : uint8
{
	Default			UMETA(DisplayName = "Default"),
	Highlighted		UMETA(DisplayName = "Highlighted"),
	Selected		UMETA(DisplayName = "Selected"),
	Disabled		UMETA(DisplayName = "Disabled"),
	Damaged			UMETA(DisplayName = "Damaged"),
	Rare			UMETA(DisplayName = "Rare"),
	Epic			UMETA(DisplayName = "Epic"),
	Legendary		UMETA(DisplayName = "Legendary"),
	Interactive		UMETA(DisplayName = "Interactive"),
	Locked			UMETA(DisplayName = "Locked")
};

UENUM(BlueprintType)
enum class EPostProcessType : uint8
{
	None			UMETA(DisplayName = "None"),
	Interaction		UMETA(DisplayName = "Interaction"),
	Inventory		UMETA(DisplayName = "Inventory"),
	Combat			UMETA(DisplayName = "Combat"),
	Puzzle			UMETA(DisplayName = "Puzzle"),
	Transition		UMETA(DisplayName = "Transition"),
	Death			UMETA(DisplayName = "Death")
};

USTRUCT(BlueprintType)
struct FShaderVariantData
{
	GENERATED_BODY()

	FShaderVariantData()
	{
		VariantType = EShaderVariant::Default;
		BaseMaterial = nullptr;
		OverrideMaterial = nullptr;
		EmissiveColor = FLinearColor::White;
		EmissiveIntensity = 1.0f;
		Metallic = 0.0f;
		Roughness = 0.5f;
		Opacity = 1.0f;
		bUseCustomParameters = false;
		AnimationSpeed = 1.0f;
		PulseFrequency = 1.0f;
		bEnableAnimation = false;
	}

	// Variant identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variant")
	EShaderVariant VariantType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Variant")
	FText VariantName;

	// Material references
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
	TSoftObjectPtr<UMaterialInterface> BaseMaterial;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Materials")
	TSoftObjectPtr<UMaterialInterface> OverrideMaterial;

	// Material parameters
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	FLinearColor EmissiveColor;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float EmissiveIntensity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float Metallic;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float Roughness;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float Opacity;

	// Custom parameters
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	bool bUseCustomParameters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, float> ScalarParameters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FLinearColor> VectorParameters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, TSoftObjectPtr<UTexture>> TextureParameters;

	// Animation properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	bool bEnableAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float AnimationSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
	float PulseFrequency;
};

USTRUCT(BlueprintType)
struct FShaderPostProcessSettings
{
	GENERATED_BODY()

	FShaderPostProcessSettings()
	{
		ProcessType = EPostProcessType::None;
		BlendWeight = 1.0f;
		FadeInTime = 0.5f;
		FadeOutTime = 0.5f;
		Duration = -1.0f;
		bAutoRemove = false;
		Priority = 0;
	}

	// Post process identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Post Process")
	EPostProcessType ProcessType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Post Process")
	FText ProcessName;

	// Material reference
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material")
	TSoftObjectPtr<UMaterialInterface> PostProcessMaterial;

	// Blend settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend")
	float BlendWeight;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend")
	float FadeInTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blend")
	float FadeOutTime;

	// Duration settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration")
	float Duration; // -1 for infinite

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Duration")
	bool bAutoRemove;

	// Priority for multiple effects
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Priority")
	int32 Priority;

	// Custom parameters
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	TMap<FString, float> ScalarParameters;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	TMap<FString, FLinearColor> VectorParameters;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnShaderVariantApplied, UPrimitiveComponent*, Component, EShaderVariant, VariantType);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPostProcessApplied, EPostProcessType, ProcessType, float, BlendWeight);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPostProcessRemoved, EPostProcessType, ProcessType);

UCLASS(BlueprintType, Blueprintable)
class SLT_API UShaderManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UShaderManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Shader variant configurations
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Shader Variants")
	TMap<EShaderVariant, FShaderVariantData> ShaderVariants;

	// Post process configurations
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Post Process")
	TMap<EPostProcessType, FShaderPostProcessSettings> PostProcessSettings;

	// Material parameter collection for global effects
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Global")
	TSoftObjectPtr<UMaterialParameterCollection> GlobalParameterCollection;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnShaderVariantApplied OnShaderVariantApplied;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPostProcessApplied OnPostProcessApplied;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPostProcessRemoved OnPostProcessRemoved;

	// Shader variant functions
	UFUNCTION(BlueprintCallable, Category = "Shader Variants")
	bool ApplyShaderVariant(UPrimitiveComponent* Component, EShaderVariant VariantType);

	UFUNCTION(BlueprintCallable, Category = "Shader Variants")
	bool RemoveShaderVariant(UPrimitiveComponent* Component);

	UFUNCTION(BlueprintCallable, Category = "Shader Variants")
	UMaterialInstanceDynamic* CreateDynamicMaterial(UPrimitiveComponent* Component, EShaderVariant VariantType);

	UFUNCTION(BlueprintCallable, Category = "Shader Variants")
	void ApplyItemRarityShader(UPrimitiveComponent* Component, EItemRarity Rarity);

	// Post process functions
	UFUNCTION(BlueprintCallable, Category = "Post Process")
	bool ApplyPostProcess(EPostProcessType ProcessType, float BlendWeight = 1.0f, float Duration = -1.0f);

	UFUNCTION(BlueprintCallable, Category = "Post Process")
	bool RemovePostProcess(EPostProcessType ProcessType);

	UFUNCTION(BlueprintCallable, Category = "Post Process")
	void ClearAllPostProcesses();

	UFUNCTION(BlueprintCallable, Category = "Post Process")
	bool IsPostProcessActive(EPostProcessType ProcessType) const;

	// Global parameter functions
	UFUNCTION(BlueprintCallable, Category = "Global Parameters")
	void SetGlobalScalarParameter(const FString& ParameterName, float Value);

	UFUNCTION(BlueprintCallable, Category = "Global Parameters")
	void SetGlobalVectorParameter(const FString& ParameterName, const FLinearColor& Value);

	UFUNCTION(BlueprintCallable, Category = "Global Parameters")
	float GetGlobalScalarParameter(const FString& ParameterName) const;

	UFUNCTION(BlueprintCallable, Category = "Global Parameters")
	FLinearColor GetGlobalVectorParameter(const FString& ParameterName) const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void PrecompileShaderVariants();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void CacheShaderVariants();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<EShaderVariant> GetAvailableVariants() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SetShaderQuality(int32 QualityLevel);

	// Animation functions
	UFUNCTION(BlueprintCallable, Category = "Animation")
	void StartShaderAnimation(UPrimitiveComponent* Component, EShaderVariant VariantType);

	UFUNCTION(BlueprintCallable, Category = "Animation")
	void StopShaderAnimation(UPrimitiveComponent* Component);

	UFUNCTION(BlueprintCallable, Category = "Animation")
	void UpdateShaderAnimations(float DeltaTime);

protected:
	// Internal storage
	UPROPERTY()
	TMap<UPrimitiveComponent*, UMaterialInstanceDynamic*> ComponentMaterials;

	UPROPERTY()
	TMap<UPrimitiveComponent*, EShaderVariant> ComponentVariants;

	UPROPERTY()
	TSet<EPostProcessType> ActivePostProcesses;

	// Animation tracking
	UPROPERTY()
	TMap<UPrimitiveComponent*, float> AnimationTimers;

	// Cached parameter collection
	UPROPERTY()
	UMaterialParameterCollection* CachedParameterCollection;

	// Internal functions
	void LoadParameterCollection();
	FLinearColor GetRarityColor(EItemRarity Rarity) const;
	EShaderVariant GetRarityVariant(EItemRarity Rarity) const;
	void UpdateAnimatedMaterial(UPrimitiveComponent* Component, float DeltaTime);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnShaderSystemInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnMaterialParametersChanged(UPrimitiveComponent* Component, EShaderVariant VariantType);
};
