/NOLOGO
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/DEF
/NAME:"UnrealEditor-SLT.dll"
/IGNORE:4221
/NODEFAULTLIB
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\SLTEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.1.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.2.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.3.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.4.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.5.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.6.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.7.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\MyClass.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\SLT.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\AIBehaviorDesigner.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\AutomatedSetupWizard.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\DragDropLevelDesigner.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\VisualDesignTools.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\EnemyCharacter.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Interactable.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\SLTPlayerCharacter.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InventorySaveGame.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\LevelProgressionManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\LoadingManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ObjectPoolManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ResourceManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ShaderManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\StatisticsManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\WeaponSystem.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ItemActor.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InteractionComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InventoryGridComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\PuzzleActor.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\PuzzleComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Default.rc2.res"
/OUT:"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\UnrealEditor-SLT.lib"