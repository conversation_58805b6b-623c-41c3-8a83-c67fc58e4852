// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/WeaponSystem.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class EAmmoType : uint8;
enum class EWeaponCondition : uint8;
enum class EWeaponType : uint8;
struct FWeaponData;
struct FWeaponStats;
struct FWeaponUpgrade;
#ifdef SLT_WeaponSystem_generated_h
#error "WeaponSystem.generated.h already included, missing '#pragma once' in WeaponSystem.h"
#endif
#define SLT_WeaponSystem_generated_h

#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_56_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWeaponUpgrade_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FWeaponUpgrade>();

#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_114_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWeaponStats_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FWeaponStats>();

#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_171_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FWeaponData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FWeaponData>();

#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_256_DELEGATE \
SLT_API void FOnWeaponFired_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponFired, FName WeaponID, int32 RemainingAmmo);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_257_DELEGATE \
SLT_API void FOnWeaponReloaded_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponReloaded, FName WeaponID, int32 NewAmmoCount);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_258_DELEGATE \
SLT_API void FOnWeaponUpgraded_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponUpgraded, FName WeaponID, FName UpgradeID, int32 NewLevel);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_259_DELEGATE \
SLT_API void FOnWeaponConditionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponConditionChanged, FName WeaponID, EWeaponCondition NewCondition);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_260_DELEGATE \
SLT_API void FOnWeaponEquipped_DelegateWrapper(const FMulticastScriptDelegate& OnWeaponEquipped, FName WeaponID, bool bIsEquipped);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execConsumeAmmo); \
	DECLARE_FUNCTION(execGetWeaponsByType); \
	DECLARE_FUNCTION(execGetOwnedWeapons); \
	DECLARE_FUNCTION(execGetWeaponCondition); \
	DECLARE_FUNCTION(execRepairWeapon); \
	DECLARE_FUNCTION(execDamageWeapon); \
	DECLARE_FUNCTION(execGetAvailableUpgrades); \
	DECLARE_FUNCTION(execGetUpgradeCost); \
	DECLARE_FUNCTION(execUpgradeWeapon); \
	DECLARE_FUNCTION(execCanUpgradeWeapon); \
	DECLARE_FUNCTION(execGetAccuracy); \
	DECLARE_FUNCTION(execGetDamage); \
	DECLARE_FUNCTION(execReload); \
	DECLARE_FUNCTION(execFire); \
	DECLARE_FUNCTION(execCanFire); \
	DECLARE_FUNCTION(execSwitchToWeapon); \
	DECLARE_FUNCTION(execGetEquippedWeapon); \
	DECLARE_FUNCTION(execUnequipWeapon); \
	DECLARE_FUNCTION(execEquipWeapon); \
	DECLARE_FUNCTION(execGetWeaponData); \
	DECLARE_FUNCTION(execHasWeapon); \
	DECLARE_FUNCTION(execRemoveWeapon); \
	DECLARE_FUNCTION(execAddWeapon);


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUWeaponComponent(); \
	friend struct Z_Construct_UClass_UWeaponComponent_Statics; \
public: \
	DECLARE_CLASS(UWeaponComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UWeaponComponent)


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UWeaponComponent(UWeaponComponent&&); \
	UWeaponComponent(const UWeaponComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UWeaponComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UWeaponComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UWeaponComponent) \
	NO_API virtual ~UWeaponComponent();


#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_262_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h_265_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UWeaponComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_WeaponSystem_h


#define FOREACH_ENUM_EWEAPONTYPE(op) \
	op(EWeaponType::None) \
	op(EWeaponType::Pistol) \
	op(EWeaponType::Rifle) \
	op(EWeaponType::Shotgun) \
	op(EWeaponType::Sniper) \
	op(EWeaponType::Melee) \
	op(EWeaponType::Explosive) 

enum class EWeaponType : uint8;
template<> struct TIsUEnumClass<EWeaponType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EWeaponType>();

#define FOREACH_ENUM_EWEAPONCONDITION(op) \
	op(EWeaponCondition::Broken) \
	op(EWeaponCondition::Poor) \
	op(EWeaponCondition::Fair) \
	op(EWeaponCondition::Good) \
	op(EWeaponCondition::Excellent) \
	op(EWeaponCondition::Perfect) 

enum class EWeaponCondition : uint8;
template<> struct TIsUEnumClass<EWeaponCondition> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EWeaponCondition>();

#define FOREACH_ENUM_EAMMOTYPE(op) \
	op(EAmmoType::None) \
	op(EAmmoType::Pistol9mm) \
	op(EAmmoType::Rifle556) \
	op(EAmmoType::Shotgun12g) \
	op(EAmmoType::Sniper762) \
	op(EAmmoType::Explosive40mm) \
	op(EAmmoType::Custom) 

enum class EAmmoType : uint8;
template<> struct TIsUEnumClass<EAmmoType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAmmoType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
