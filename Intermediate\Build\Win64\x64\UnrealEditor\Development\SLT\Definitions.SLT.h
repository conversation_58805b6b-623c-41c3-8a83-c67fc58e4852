// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for SLT
#pragma once
#include "G:/Gamedev/SLT/Intermediate/Build/Win64/x64/SLTEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef SLT_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME SLT
#define UE_TARGET_NAME SLTEditor
#define UE_MODULE_NAME "SLT"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORSTYLE_API DLLIMPORT
#define EDITORWIDGETS_API DLLIMPORT
#define LEVELSEQUENCE_API DLLIMPORT
#define SIGNIFICANCEMANAGER_API DLLIMPORT
#define BEHAVIORTREEEDITOR_API DLLIMPORT
#define VECTORVM_SUPPORTS_EXPERIMENTAL 1
#define VECTORVM_SUPPORTS_LEGACY 1
#define NIAGARA_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_SUPPORTS_SERIALIZATION 0
#define VECTORVM_DEBUG_PRINTF 0
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define SLT_API DLLEXPORT
#define ENHANCEDINPUT_API DLLIMPORT
#define COMMONUI_API DLLIMPORT
#define WIDGETCAROUSEL_API DLLIMPORT
#define UE_COMMONINPUT_PLATFORM_TYPE PC
#define COMMONINPUT_API DLLIMPORT
#define MEDIAASSETS_API DLLIMPORT
#define MEDIA_API DLLIMPORT
#define MEDIAUTILS_API DLLIMPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define AIMODULE_API DLLIMPORT
