// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for SLT
#pragma once
#include "G:/Gamedev/SLT/Intermediate/Build/Win64/x64/SLTEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef SLT_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME SLT
#define UE_TARGET_NAME SLTEditor
#define UE_MODULE_NAME "SLT"
#define UE_PLUGIN_NAME ""
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define SLT_API DLLEXPORT
