// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Design/AutomatedSetupWizard.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class ESetupWizardStep : uint8;
struct FSetupValidationResult;
struct FSetupWizardConfiguration;
struct FSetupWizardStepData;
#ifdef SLT_AutomatedSetupWizard_generated_h
#error "AutomatedSetupWizard.generated.h already included, missing '#pragma once' in AutomatedSetupWizard.h"
#endif
#define SLT_AutomatedSetupWizard_generated_h

#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_43_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FSetupWizardConfiguration>();

#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_126_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSetupWizardStepData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FSetupWizardStepData>();

#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_175_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FSetupValidationResult_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FSetupValidationResult>();

#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_207_DELEGATE \
SLT_API void FOnSetupStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSetupStepCompleted, ESetupWizardStep CompletedStep, float Progress);


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_208_DELEGATE \
SLT_API void FOnSetupWizardCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSetupWizardCompleted, bool bSuccess);


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_209_DELEGATE \
SLT_API void FOnSetupValidated_DelegateWrapper(const FMulticastScriptDelegate& OnSetupValidated, FSetupValidationResult const& Result, ESetupWizardStep CurrentStep);


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_210_DELEGATE \
SLT_API void FOnSetupError_DelegateWrapper(const FMulticastScriptDelegate& OnSetupError, const FString& ErrorMessage, ESetupWizardStep FailedStep);


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execImportSetupConfiguration); \
	DECLARE_FUNCTION(execExportSetupConfiguration); \
	DECLARE_FUNCTION(execResetWizardProgress); \
	DECLARE_FUNCTION(execLoadWizardProgress); \
	DECLARE_FUNCTION(execSaveWizardProgress); \
	DECLARE_FUNCTION(execGetCurrentStepInstructions); \
	DECLARE_FUNCTION(execGetEstimatedRemainingTime); \
	DECLARE_FUNCTION(execGetAllSteps); \
	DECLARE_FUNCTION(execGetCurrentStepData); \
	DECLARE_FUNCTION(execAutoFixCommonIssues); \
	DECLARE_FUNCTION(execValidateStep); \
	DECLARE_FUNCTION(execValidateCurrentSetup); \
	DECLARE_FUNCTION(execCreateSampleContent); \
	DECLARE_FUNCTION(execSetupUI); \
	DECLARE_FUNCTION(execSetupInputMappings); \
	DECLARE_FUNCTION(execSetupPlayerCharacter); \
	DECLARE_FUNCTION(execSetupPuzzleSystem); \
	DECLARE_FUNCTION(execSetupEnemyAI); \
	DECLARE_FUNCTION(execSetupWeaponSystem); \
	DECLARE_FUNCTION(execSetupInventorySystem); \
	DECLARE_FUNCTION(execRunFullAutomatedSetup); \
	DECLARE_FUNCTION(execRunQuickSetup); \
	DECLARE_FUNCTION(execSkipCurrentStep); \
	DECLARE_FUNCTION(execCompleteCurrentStep); \
	DECLARE_FUNCTION(execGoToStep); \
	DECLARE_FUNCTION(execPreviousStep); \
	DECLARE_FUNCTION(execNextStep); \
	DECLARE_FUNCTION(execStopSetupWizard); \
	DECLARE_FUNCTION(execStartSetupWizard);


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAutomatedSetupWizard(); \
	friend struct Z_Construct_UClass_UAutomatedSetupWizard_Statics; \
public: \
	DECLARE_CLASS(UAutomatedSetupWizard, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UAutomatedSetupWizard)


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UAutomatedSetupWizard(UAutomatedSetupWizard&&); \
	UAutomatedSetupWizard(const UAutomatedSetupWizard&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAutomatedSetupWizard); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAutomatedSetupWizard); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAutomatedSetupWizard) \
	NO_API virtual ~UAutomatedSetupWizard();


#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_212_PROLOG
#define FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_215_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UAutomatedSetupWizard>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h


#define FOREACH_ENUM_ESETUPWIZARDSTEP(op) \
	op(ESetupWizardStep::Welcome) \
	op(ESetupWizardStep::ProjectSetup) \
	op(ESetupWizardStep::LevelSetup) \
	op(ESetupWizardStep::PlayerSetup) \
	op(ESetupWizardStep::InventorySetup) \
	op(ESetupWizardStep::WeaponSetup) \
	op(ESetupWizardStep::EnemySetup) \
	op(ESetupWizardStep::PuzzleSetup) \
	op(ESetupWizardStep::UISetup) \
	op(ESetupWizardStep::Testing) \
	op(ESetupWizardStep::Finalization) \
	op(ESetupWizardStep::Complete) 

enum class ESetupWizardStep : uint8;
template<> struct TIsUEnumClass<ESetupWizardStep> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ESetupWizardStep>();

#define FOREACH_ENUM_ESETUPCOMPLEXITY(op) \
	op(ESetupComplexity::QuickStart) \
	op(ESetupComplexity::Basic) \
	op(ESetupComplexity::Standard) \
	op(ESetupComplexity::Advanced) \
	op(ESetupComplexity::Expert) 

enum class ESetupComplexity : uint8;
template<> struct TIsUEnumClass<ESetupComplexity> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ESetupComplexity>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
