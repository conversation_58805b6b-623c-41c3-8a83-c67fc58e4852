#include "PuzzleActor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Engine.h"

APuzzleActor::APuzzleActor()
{
	PrimaryActorTick.bCanEverTick = false;

	// Create root component
	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

	// Create mesh component
	MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
	MeshComponent->SetupAttachment(RootComponent);
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
	MeshComponent->SetCollisionObjectType(ECollisionChannel::ECC_WorldStatic);

	// Create interaction box
	InteractionBox = CreateDefaultSubobject<UBoxComponent>(TEXT("InteractionBox"));
	InteractionBox->SetupAttachment(RootComponent);
	InteractionBox->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
	InteractionBox->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	InteractionBox->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
	InteractionBox->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	InteractionBox->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
	InteractionBox->SetGenerateOverlapEvents(true);

	// Create puzzle component
	PuzzleComponent = CreateDefaultSubobject<UPuzzleComponent>(TEXT("PuzzleComponent"));

	// Default settings
	bUseCustomPrompt = false;
	bShowPuzzleProgress = true;
	bRequireInteractionToStart = true;
	bChangeColorOnSolved = true;
	SolvedColor = FLinearColor::Green;
	UnsolvedColor = FLinearColor::White;
	FailedColor = FLinearColor::Red;
}

void APuzzleActor::BeginPlay()
{
	Super::BeginPlay();
	
	// Bind to puzzle component events
	if (PuzzleComponent)
	{
		PuzzleComponent->OnPuzzleSolved.AddDynamic(this, &APuzzleActor::OnPuzzleSolved);
		PuzzleComponent->OnPuzzleFailed.AddDynamic(this, &APuzzleActor::OnPuzzleFailed);
		PuzzleComponent->OnPuzzleStateChanged.AddDynamic(this, &APuzzleActor::OnPuzzleStateChanged);
		PuzzleComponent->OnPuzzleReset.AddDynamic(this, &APuzzleActor::OnPuzzleReset);
	}

	// Create dynamic material instance
	if (MeshComponent && MeshComponent->GetMaterial(0))
	{
		DynamicMaterial = MeshComponent->CreateDynamicMaterialInstance(0);
	}

	UpdateVisualState();
}

FInteractionData APuzzleActor::GetInteractionData_Implementation() const
{
	FInteractionData InteractionData;
	InteractionData.InteractionType = EInteractionType::Use;
	InteractionData.InteractionDuration = 0.0f;
	InteractionData.bCanInteractMultipleTimes = true;
	InteractionData.InteractionRange = 200.0f;
	InteractionData.bShowInteractionPrompt = true;

	// Set interaction text
	if (bUseCustomPrompt && !InteractionPrompt.IsEmpty())
	{
		InteractionData.InteractionText = InteractionPrompt;
	}
	else if (PuzzleComponent)
	{
		if (PuzzleComponent->IsPuzzleSolved())
		{
			InteractionData.InteractionText = FText::FromString(TEXT("Puzzle Solved"));
			InteractionData.bCanInteractMultipleTimes = false;
		}
		else
		{
			FString BaseText = TEXT("Interact with puzzle");
			
			if (bShowPuzzleProgress)
			{
				float Progress = PuzzleComponent->GetCompletionPercentage() * 100.0f;
				BaseText += FString::Printf(TEXT(" (%.0f%% complete)"), Progress);
			}

			InteractionData.InteractionText = FText::FromString(BaseText);
		}
	}
	else
	{
		InteractionData.InteractionText = FText::FromString(TEXT("Interact"));
	}

	return InteractionData;
}

bool APuzzleActor::CanInteract_Implementation(APawn* InteractingPawn) const
{
	if (!PuzzleComponent)
	{
		return false;
	}

	// Can't interact if puzzle is solved (unless it allows multiple interactions)
	if (PuzzleComponent->IsPuzzleSolved())
	{
		return false;
	}

	// Can't interact if puzzle can't be attempted
	return PuzzleComponent->CanAttemptPuzzle();
}

void APuzzleActor::OnInteractionStart_Implementation(APawn* InteractingPawn)
{
	// Activate puzzle if required
	if (bRequireInteractionToStart && PuzzleComponent && !PuzzleComponent->IsPuzzleActive())
	{
		PuzzleComponent->ActivatePuzzle();
	}
}

void APuzzleActor::OnInteractionComplete_Implementation(APawn* InteractingPawn)
{
	OnPuzzleInteracted(InteractingPawn);
}

void APuzzleActor::OnInteractionCancel_Implementation(APawn* InteractingPawn)
{
	// Nothing to do for puzzle interaction cancellation
}

FVector APuzzleActor::GetInteractionLocation_Implementation() const
{
	return GetActorLocation();
}

bool APuzzleActor::TryInputSolution(const FString& Solution, APawn* InteractingPawn)
{
	if (!PuzzleComponent)
	{
		return false;
	}

	FName SolutionName(*Solution);
	bool bSuccess = PuzzleComponent->TrySolvePuzzle(SolutionName, InteractingPawn);
	
	OnSolutionAttempted(Solution, bSuccess);
	
	return bSuccess;
}

bool APuzzleActor::TryInputStep(const FString& StepValue, APawn* InteractingPawn)
{
	if (!PuzzleComponent)
	{
		return false;
	}

	return PuzzleComponent->TryCompleteCurrentStep(StepValue, InteractingPawn);
}

void APuzzleActor::ResetPuzzle()
{
	if (PuzzleComponent)
	{
		PuzzleComponent->ResetPuzzle(true);
	}
}

bool APuzzleActor::IsPuzzleSolved() const
{
	return PuzzleComponent ? PuzzleComponent->IsPuzzleSolved() : false;
}

float APuzzleActor::GetPuzzleProgress() const
{
	return PuzzleComponent ? PuzzleComponent->GetCompletionPercentage() : 0.0f;
}

void APuzzleActor::OnPuzzleSolved(APawn* SolvingPawn)
{
	UpdateVisualState();
	OnPuzzleActorSolved.Broadcast(this, SolvingPawn);
}

void APuzzleActor::OnPuzzleFailed(APawn* FailingPawn)
{
	UpdateVisualState();
	OnPuzzleActorFailed.Broadcast(this, FailingPawn);
}

void APuzzleActor::OnPuzzleStateChanged(EPuzzleState OldState, EPuzzleState NewState)
{
	UpdateVisualState();
	OnPuzzleVisualUpdate(NewState);
}

void APuzzleActor::OnPuzzleReset(bool bWasForced)
{
	UpdateVisualState();
	OnPuzzleActorReset.Broadcast(this);
}

void APuzzleActor::UpdateVisualState()
{
	if (!bChangeColorOnSolved || !PuzzleComponent)
	{
		return;
	}

	FLinearColor TargetColor = UnsolvedColor;

	switch (PuzzleComponent->CurrentState)
	{
		case EPuzzleState::Solved:
			TargetColor = SolvedColor;
			break;
		case EPuzzleState::Failed:
			TargetColor = FailedColor;
			break;
		default:
			TargetColor = UnsolvedColor;
			break;
	}

	SetMeshColor(TargetColor);
}

void APuzzleActor::SetMeshColor(const FLinearColor& Color)
{
	if (DynamicMaterial)
	{
		// Try common parameter names for base color
		DynamicMaterial->SetVectorParameterValue(TEXT("BaseColor"), Color);
		DynamicMaterial->SetVectorParameterValue(TEXT("Color"), Color);
		DynamicMaterial->SetVectorParameterValue(TEXT("Albedo"), Color);
	}
}
