// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/InventorySaveGame.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInventorySaveGame() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_USaveGame();
SLT_API UClass* Z_Construct_UClass_ASLTPlayerCharacter_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventoryGridComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventorySaveGame();
SLT_API UClass* Z_Construct_UClass_UInventorySaveGame_NoRegister();
SLT_API UClass* Z_Construct_UClass_UPuzzleComponent_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPuzzleState();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlotSaveData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerSaveData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPuzzleSaveData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FWorldSaveData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin ScriptStruct FPuzzleSaveData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PuzzleSaveData;
class UScriptStruct* FPuzzleSaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PuzzleSaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PuzzleSaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPuzzleSaveData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PuzzleSaveData"));
	}
	return Z_Registration_Info_UScriptStruct_PuzzleSaveData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPuzzleSaveData>()
{
	return FPuzzleSaveData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPuzzleSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleID_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier for the puzzle\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier for the puzzle" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsSolved_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Puzzle completion state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Puzzle completion state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStepIndex_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current progress\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current progress" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttemptCount_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Attempt tracking\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Attempt tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleState_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedSteps_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Completed steps\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Completed steps" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomSaveData_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom save data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom save data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_PuzzleID;
	static void NewProp_bIsSolved_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsSolved;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CurrentStepIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_AttemptCount;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PuzzleState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PuzzleState;
	static const UECodeGen_Private::FBoolPropertyParams NewProp_CompletedSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CompletedSteps;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSaveData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSaveData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomSaveData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPuzzleSaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleID = { "PuzzleID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, PuzzleID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleID_MetaData), NewProp_PuzzleID_MetaData) };
void Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_bIsSolved_SetBit(void* Obj)
{
	((FPuzzleSaveData*)Obj)->bIsSolved = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_bIsSolved = { "bIsSolved", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPuzzleSaveData), &Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_bIsSolved_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsSolved_MetaData), NewProp_bIsSolved_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CurrentStepIndex = { "CurrentStepIndex", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, CurrentStepIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStepIndex_MetaData), NewProp_CurrentStepIndex_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_AttemptCount = { "AttemptCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, AttemptCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttemptCount_MetaData), NewProp_AttemptCount_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleState = { "PuzzleState", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, PuzzleState), Z_Construct_UEnum_SLT_EPuzzleState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleState_MetaData), NewProp_PuzzleState_MetaData) }; // 3710896167
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CompletedSteps_Inner = { "CompletedSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CompletedSteps = { "CompletedSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, CompletedSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedSteps_MetaData), NewProp_CompletedSteps_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData_ValueProp = { "CustomSaveData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData_Key_KeyProp = { "CustomSaveData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData = { "CustomSaveData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPuzzleSaveData, CustomSaveData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomSaveData_MetaData), NewProp_CustomSaveData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_bIsSolved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CurrentStepIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_AttemptCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_PuzzleState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CompletedSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CompletedSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewProp_CustomSaveData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PuzzleSaveData",
	Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::PropPointers),
	sizeof(FPuzzleSaveData),
	alignof(FPuzzleSaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPuzzleSaveData()
{
	if (!Z_Registration_Info_UScriptStruct_PuzzleSaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PuzzleSaveData.InnerSingleton, Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PuzzleSaveData.InnerSingleton;
}
// End ScriptStruct FPuzzleSaveData

// Begin ScriptStruct FPlayerSaveData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PlayerSaveData;
class UScriptStruct* FPlayerSaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerSaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PlayerSaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerSaveData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PlayerSaveData"));
	}
	return Z_Registration_Info_UScriptStruct_PlayerSaveData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPlayerSaveData>()
{
	return FPlayerSaveData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPlayerSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player transform\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player transform" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRotation_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Health_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player stats\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player stats" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxHealth_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Experience_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Level_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EquippedItems_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Equipped items\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Equipped items" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomPlayerData_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom player data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom player data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRotation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Health;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxHealth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Experience;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Level;
	static const UECodeGen_Private::FNamePropertyParams NewProp_EquippedItems_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EquippedItems_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EquippedItems;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomPlayerData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomPlayerData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomPlayerData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerSaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_PlayerRotation = { "PlayerRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, PlayerRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRotation_MetaData), NewProp_PlayerRotation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Health = { "Health", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, Health), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Health_MetaData), NewProp_Health_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_MaxHealth = { "MaxHealth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, MaxHealth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxHealth_MetaData), NewProp_MaxHealth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Experience = { "Experience", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, Experience), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Experience_MetaData), NewProp_Experience_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Level = { "Level", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, Level), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Level_MetaData), NewProp_Level_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems_ValueProp = { "EquippedItems", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems_Key_KeyProp = { "EquippedItems_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems = { "EquippedItems", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, EquippedItems), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EquippedItems_MetaData), NewProp_EquippedItems_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData_ValueProp = { "CustomPlayerData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData_Key_KeyProp = { "CustomPlayerData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData = { "CustomPlayerData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerSaveData, CustomPlayerData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomPlayerData_MetaData), NewProp_CustomPlayerData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_PlayerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_PlayerRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Health,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_MaxHealth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Experience,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_Level,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_EquippedItems,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewProp_CustomPlayerData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerSaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PlayerSaveData",
	Z_Construct_UScriptStruct_FPlayerSaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerSaveData_Statics::PropPointers),
	sizeof(FPlayerSaveData),
	alignof(FPlayerSaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerSaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerSaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerSaveData()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerSaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PlayerSaveData.InnerSingleton, Z_Construct_UScriptStruct_FPlayerSaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PlayerSaveData.InnerSingleton;
}
// End ScriptStruct FPlayerSaveData

// Begin ScriptStruct FWorldSaveData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_WorldSaveData;
class UScriptStruct* FWorldSaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_WorldSaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_WorldSaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FWorldSaveData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("WorldSaveData"));
	}
	return Z_Registration_Info_UScriptStruct_WorldSaveData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FWorldSaveData>()
{
	return FWorldSaveData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FWorldSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameTime_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayTime_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CollectedWorldItems_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Collected items in the world (by actor name or ID)\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Collected items in the world (by actor name or ID)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DestroyedActors_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Destroyed actors (by name or ID)\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Destroyed actors (by name or ID)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TriggeredEvents_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Triggered events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Triggered events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomWorldData_MetaData[] = {
		{ "Category", "Save Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom world data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom world data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GameTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CollectedWorldItems_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CollectedWorldItems;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DestroyedActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DestroyedActors;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TriggeredEvents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TriggeredEvents;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomWorldData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomWorldData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomWorldData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FWorldSaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_GameTime = { "GameTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, GameTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameTime_MetaData), NewProp_GameTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_PlayTime = { "PlayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, PlayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayTime_MetaData), NewProp_PlayTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CollectedWorldItems_Inner = { "CollectedWorldItems", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CollectedWorldItems = { "CollectedWorldItems", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, CollectedWorldItems), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CollectedWorldItems_MetaData), NewProp_CollectedWorldItems_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_DestroyedActors_Inner = { "DestroyedActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_DestroyedActors = { "DestroyedActors", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, DestroyedActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DestroyedActors_MetaData), NewProp_DestroyedActors_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_TriggeredEvents_Inner = { "TriggeredEvents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_TriggeredEvents = { "TriggeredEvents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, TriggeredEvents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TriggeredEvents_MetaData), NewProp_TriggeredEvents_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData_ValueProp = { "CustomWorldData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData_Key_KeyProp = { "CustomWorldData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData = { "CustomWorldData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FWorldSaveData, CustomWorldData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomWorldData_MetaData), NewProp_CustomWorldData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FWorldSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_GameTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_PlayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CollectedWorldItems_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CollectedWorldItems,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_DestroyedActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_DestroyedActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_TriggeredEvents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_TriggeredEvents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewProp_CustomWorldData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWorldSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FWorldSaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"WorldSaveData",
	Z_Construct_UScriptStruct_FWorldSaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWorldSaveData_Statics::PropPointers),
	sizeof(FWorldSaveData),
	alignof(FWorldSaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FWorldSaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FWorldSaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FWorldSaveData()
{
	if (!Z_Registration_Info_UScriptStruct_WorldSaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_WorldSaveData.InnerSingleton, Z_Construct_UScriptStruct_FWorldSaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_WorldSaveData.InnerSingleton;
}
// End ScriptStruct FWorldSaveData

// Begin Class UInventorySaveGame Function ClearAllData
struct Z_Construct_UFunction_UInventorySaveGame_ClearAllData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_ClearAllData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "ClearAllData", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_ClearAllData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_ClearAllData_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UInventorySaveGame_ClearAllData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_ClearAllData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execClearAllData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllData();
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function ClearAllData

// Begin Class UInventorySaveGame Function GetCustomData
struct Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics
{
	struct InventorySaveGame_eventGetCustomData_Parms
	{
		FString Key;
		FString DefaultValue;
		FString ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "CPP_Default_DefaultValue", "" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultValue_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultValue;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventGetCustomData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_DefaultValue = { "DefaultValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventGetCustomData_Parms, DefaultValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultValue_MetaData), NewProp_DefaultValue_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventGetCustomData_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_DefaultValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "GetCustomData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::InventorySaveGame_eventGetCustomData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::InventorySaveGame_eventGetCustomData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_GetCustomData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_GetCustomData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execGetCustomData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_DefaultValue);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FString*)Z_Param__Result=P_THIS->GetCustomData(Z_Param_Key,Z_Param_DefaultValue);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function GetCustomData

// Begin Class UInventorySaveGame Function HasCustomData
struct Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics
{
	struct InventorySaveGame_eventHasCustomData_Parms
	{
		FString Key;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventHasCustomData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
void Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventHasCustomData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventHasCustomData_Parms), &Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "HasCustomData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::InventorySaveGame_eventHasCustomData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::InventorySaveGame_eventHasCustomData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_HasCustomData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_HasCustomData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execHasCustomData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasCustomData(Z_Param_Key);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function HasCustomData

// Begin Class UInventorySaveGame Function IsValidSaveData
struct Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics
{
	struct InventorySaveGame_eventIsValidSaveData_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventIsValidSaveData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventIsValidSaveData_Parms), &Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "IsValidSaveData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::InventorySaveGame_eventIsValidSaveData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::InventorySaveGame_eventIsValidSaveData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execIsValidSaveData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidSaveData();
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function IsValidSaveData

// Begin Class UInventorySaveGame Function LoadInventoryData
struct Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics
{
	struct InventorySaveGame_eventLoadInventoryData_Parms
	{
		UInventoryGridComponent* InventoryComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_InventoryComponent = { "InventoryComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventLoadInventoryData_Parms, InventoryComponent), Z_Construct_UClass_UInventoryGridComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryComponent_MetaData), NewProp_InventoryComponent_MetaData) };
void Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventLoadInventoryData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventLoadInventoryData_Parms), &Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_InventoryComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "LoadInventoryData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::InventorySaveGame_eventLoadInventoryData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::InventorySaveGame_eventLoadInventoryData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execLoadInventoryData)
{
	P_GET_OBJECT(UInventoryGridComponent,Z_Param_InventoryComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadInventoryData(Z_Param_InventoryComponent);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function LoadInventoryData

// Begin Class UInventorySaveGame Function LoadPlayerData
struct Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics
{
	struct InventorySaveGame_eventLoadPlayerData_Parms
	{
		ASLTPlayerCharacter* PlayerCharacter;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventLoadPlayerData_Parms, PlayerCharacter), Z_Construct_UClass_ASLTPlayerCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventLoadPlayerData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventLoadPlayerData_Parms), &Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_PlayerCharacter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "LoadPlayerData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::InventorySaveGame_eventLoadPlayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::InventorySaveGame_eventLoadPlayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execLoadPlayerData)
{
	P_GET_OBJECT(ASLTPlayerCharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadPlayerData(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function LoadPlayerData

// Begin Class UInventorySaveGame Function LoadPuzzleData
struct Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics
{
	struct InventorySaveGame_eventLoadPuzzleData_Parms
	{
		UPuzzleComponent* PuzzleComponent;
		FName PuzzleID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleComponent;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PuzzleID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_PuzzleComponent = { "PuzzleComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventLoadPuzzleData_Parms, PuzzleComponent), Z_Construct_UClass_UPuzzleComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleComponent_MetaData), NewProp_PuzzleComponent_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_PuzzleID = { "PuzzleID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventLoadPuzzleData_Parms, PuzzleID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventLoadPuzzleData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventLoadPuzzleData_Parms), &Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_PuzzleComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_PuzzleID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "LoadPuzzleData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::InventorySaveGame_eventLoadPuzzleData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::InventorySaveGame_eventLoadPuzzleData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execLoadPuzzleData)
{
	P_GET_OBJECT(UPuzzleComponent,Z_Param_PuzzleComponent);
	P_GET_PROPERTY(FNameProperty,Z_Param_PuzzleID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadPuzzleData(Z_Param_PuzzleComponent,Z_Param_PuzzleID);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function LoadPuzzleData

// Begin Class UInventorySaveGame Function RemoveCustomData
struct Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics
{
	struct InventorySaveGame_eventRemoveCustomData_Parms
	{
		FString Key;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventRemoveCustomData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::NewProp_Key,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "RemoveCustomData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::InventorySaveGame_eventRemoveCustomData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::InventorySaveGame_eventRemoveCustomData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execRemoveCustomData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveCustomData(Z_Param_Key);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function RemoveCustomData

// Begin Class UInventorySaveGame Function SaveInventoryData
struct Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics
{
	struct InventorySaveGame_eventSaveInventoryData_Parms
	{
		UInventoryGridComponent* InventoryComponent;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Save/Load functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save/Load functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryComponent;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_InventoryComponent = { "InventoryComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSaveInventoryData_Parms, InventoryComponent), Z_Construct_UClass_UInventoryGridComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryComponent_MetaData), NewProp_InventoryComponent_MetaData) };
void Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventSaveInventoryData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventSaveInventoryData_Parms), &Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_InventoryComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "SaveInventoryData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::InventorySaveGame_eventSaveInventoryData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::InventorySaveGame_eventSaveInventoryData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execSaveInventoryData)
{
	P_GET_OBJECT(UInventoryGridComponent,Z_Param_InventoryComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveInventoryData(Z_Param_InventoryComponent);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function SaveInventoryData

// Begin Class UInventorySaveGame Function SavePlayerData
struct Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics
{
	struct InventorySaveGame_eventSavePlayerData_Parms
	{
		ASLTPlayerCharacter* PlayerCharacter;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PlayerCharacter;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_PlayerCharacter = { "PlayerCharacter", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSavePlayerData_Parms, PlayerCharacter), Z_Construct_UClass_ASLTPlayerCharacter_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventSavePlayerData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventSavePlayerData_Parms), &Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_PlayerCharacter,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "SavePlayerData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::InventorySaveGame_eventSavePlayerData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::InventorySaveGame_eventSavePlayerData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_SavePlayerData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_SavePlayerData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execSavePlayerData)
{
	P_GET_OBJECT(ASLTPlayerCharacter,Z_Param_PlayerCharacter);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SavePlayerData(Z_Param_PlayerCharacter);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function SavePlayerData

// Begin Class UInventorySaveGame Function SavePuzzleData
struct Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics
{
	struct InventorySaveGame_eventSavePuzzleData_Parms
	{
		UPuzzleComponent* PuzzleComponent;
		FName PuzzleID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_PuzzleComponent;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PuzzleID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_PuzzleComponent = { "PuzzleComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSavePuzzleData_Parms, PuzzleComponent), Z_Construct_UClass_UPuzzleComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleComponent_MetaData), NewProp_PuzzleComponent_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_PuzzleID = { "PuzzleID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSavePuzzleData_Parms, PuzzleID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventorySaveGame_eventSavePuzzleData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventorySaveGame_eventSavePuzzleData_Parms), &Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_PuzzleComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_PuzzleID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "SavePuzzleData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::InventorySaveGame_eventSavePuzzleData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::InventorySaveGame_eventSavePuzzleData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execSavePuzzleData)
{
	P_GET_OBJECT(UPuzzleComponent,Z_Param_PuzzleComponent);
	P_GET_PROPERTY(FNameProperty,Z_Param_PuzzleID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SavePuzzleData(Z_Param_PuzzleComponent,Z_Param_PuzzleID);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function SavePuzzleData

// Begin Class UInventorySaveGame Function SetCustomData
struct Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics
{
	struct InventorySaveGame_eventSetCustomData_Parms
	{
		FString Key;
		FString Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Key_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Key;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::NewProp_Key = { "Key", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSetCustomData_Parms, Key), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Key_MetaData), NewProp_Key_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventSetCustomData_Parms, Value), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::NewProp_Key,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "SetCustomData", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::InventorySaveGame_eventSetCustomData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::InventorySaveGame_eventSetCustomData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_SetCustomData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_SetCustomData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execSetCustomData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Key);
	P_GET_PROPERTY(FStrProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetCustomData(Z_Param_Key,Z_Param_Value);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function SetCustomData

// Begin Class UInventorySaveGame Function UpdateMetadata
struct Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics
{
	struct InventorySaveGame_eventUpdateMetadata_Parms
	{
		FString InSaveSlotName;
		int32 InUserIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InSaveSlotName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_InSaveSlotName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_InUserIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::NewProp_InSaveSlotName = { "InSaveSlotName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventUpdateMetadata_Parms, InSaveSlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InSaveSlotName_MetaData), NewProp_InSaveSlotName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::NewProp_InUserIndex = { "InUserIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventorySaveGame_eventUpdateMetadata_Parms, InUserIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::NewProp_InSaveSlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::NewProp_InUserIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventorySaveGame, nullptr, "UpdateMetadata", nullptr, nullptr, Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::InventorySaveGame_eventUpdateMetadata_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::InventorySaveGame_eventUpdateMetadata_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventorySaveGame::execUpdateMetadata)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_InSaveSlotName);
	P_GET_PROPERTY(FIntProperty,Z_Param_InUserIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateMetadata(Z_Param_InSaveSlotName,Z_Param_InUserIndex);
	P_NATIVE_END;
}
// End Class UInventorySaveGame Function UpdateMetadata

// Begin Class UInventorySaveGame
void UInventorySaveGame::StaticRegisterNativesUInventorySaveGame()
{
	UClass* Class = UInventorySaveGame::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearAllData", &UInventorySaveGame::execClearAllData },
		{ "GetCustomData", &UInventorySaveGame::execGetCustomData },
		{ "HasCustomData", &UInventorySaveGame::execHasCustomData },
		{ "IsValidSaveData", &UInventorySaveGame::execIsValidSaveData },
		{ "LoadInventoryData", &UInventorySaveGame::execLoadInventoryData },
		{ "LoadPlayerData", &UInventorySaveGame::execLoadPlayerData },
		{ "LoadPuzzleData", &UInventorySaveGame::execLoadPuzzleData },
		{ "RemoveCustomData", &UInventorySaveGame::execRemoveCustomData },
		{ "SaveInventoryData", &UInventorySaveGame::execSaveInventoryData },
		{ "SavePlayerData", &UInventorySaveGame::execSavePlayerData },
		{ "SavePuzzleData", &UInventorySaveGame::execSavePuzzleData },
		{ "SetCustomData", &UInventorySaveGame::execSetCustomData },
		{ "UpdateMetadata", &UInventorySaveGame::execUpdateMetadata },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UInventorySaveGame);
UClass* Z_Construct_UClass_UInventorySaveGame_NoRegister()
{
	return UInventorySaveGame::StaticClass();
}
struct Z_Construct_UClass_UInventorySaveGame_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/InventorySaveGame.h" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveSlotName_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Save file metadata\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save file metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UserIndex_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SaveDateTime_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GameVersion_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventorySlots_MetaData[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Inventory data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inventory data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridWidth_MetaData[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridHeight_MetaData[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWeight_MetaData[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PuzzleData_MetaData[] = {
		{ "Category", "Puzzles" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Puzzle data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Puzzle data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerData_MetaData[] = {
		{ "Category", "Player" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WorldData_MetaData[] = {
		{ "Category", "World" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// World data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "World data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomSaveData_MetaData[] = {
		{ "Category", "Custom Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom data storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/InventorySaveGame.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom data storage" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_SaveSlotName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UserIndex;
	static const UECodeGen_Private::FStructPropertyParams NewProp_SaveDateTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_GameVersion;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InventorySlots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InventorySlots;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWeight;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PuzzleData_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PuzzleData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerData;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WorldData;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSaveData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSaveData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomSaveData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UInventorySaveGame_ClearAllData, "ClearAllData" }, // 2508210240
		{ &Z_Construct_UFunction_UInventorySaveGame_GetCustomData, "GetCustomData" }, // 403136352
		{ &Z_Construct_UFunction_UInventorySaveGame_HasCustomData, "HasCustomData" }, // 929281407
		{ &Z_Construct_UFunction_UInventorySaveGame_IsValidSaveData, "IsValidSaveData" }, // 198490552
		{ &Z_Construct_UFunction_UInventorySaveGame_LoadInventoryData, "LoadInventoryData" }, // 1653205543
		{ &Z_Construct_UFunction_UInventorySaveGame_LoadPlayerData, "LoadPlayerData" }, // 3052065148
		{ &Z_Construct_UFunction_UInventorySaveGame_LoadPuzzleData, "LoadPuzzleData" }, // 74283387
		{ &Z_Construct_UFunction_UInventorySaveGame_RemoveCustomData, "RemoveCustomData" }, // 3351068981
		{ &Z_Construct_UFunction_UInventorySaveGame_SaveInventoryData, "SaveInventoryData" }, // 1480142113
		{ &Z_Construct_UFunction_UInventorySaveGame_SavePlayerData, "SavePlayerData" }, // 3995648336
		{ &Z_Construct_UFunction_UInventorySaveGame_SavePuzzleData, "SavePuzzleData" }, // 3225188177
		{ &Z_Construct_UFunction_UInventorySaveGame_SetCustomData, "SetCustomData" }, // 1352685830
		{ &Z_Construct_UFunction_UInventorySaveGame_UpdateMetadata, "UpdateMetadata" }, // 3881504715
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UInventorySaveGame>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_SaveSlotName = { "SaveSlotName", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, SaveSlotName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveSlotName_MetaData), NewProp_SaveSlotName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_UserIndex = { "UserIndex", nullptr, (EPropertyFlags)0x0010000000020001, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, UserIndex), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UserIndex_MetaData), NewProp_UserIndex_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_SaveDateTime = { "SaveDateTime", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, SaveDateTime), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SaveDateTime_MetaData), NewProp_SaveDateTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GameVersion = { "GameVersion", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, GameVersion), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GameVersion_MetaData), NewProp_GameVersion_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_InventorySlots_Inner = { "InventorySlots", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FInventorySlotSaveData, METADATA_PARAMS(0, nullptr) }; // 2279301074
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_InventorySlots = { "InventorySlots", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, InventorySlots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventorySlots_MetaData), NewProp_InventorySlots_MetaData) }; // 2279301074
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GridWidth = { "GridWidth", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, GridWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridWidth_MetaData), NewProp_GridWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GridHeight = { "GridHeight", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, GridHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridHeight_MetaData), NewProp_GridHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_MaxWeight = { "MaxWeight", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, MaxWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWeight_MetaData), NewProp_MaxWeight_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PuzzleData_Inner = { "PuzzleData", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPuzzleSaveData, METADATA_PARAMS(0, nullptr) }; // 1440889550
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PuzzleData = { "PuzzleData", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, PuzzleData), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PuzzleData_MetaData), NewProp_PuzzleData_MetaData) }; // 1440889550
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PlayerData = { "PlayerData", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, PlayerData), Z_Construct_UScriptStruct_FPlayerSaveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerData_MetaData), NewProp_PlayerData_MetaData) }; // 3329062140
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_WorldData = { "WorldData", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, WorldData), Z_Construct_UScriptStruct_FWorldSaveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WorldData_MetaData), NewProp_WorldData_MetaData) }; // 191440310
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData_ValueProp = { "CustomSaveData", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData_Key_KeyProp = { "CustomSaveData_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData = { "CustomSaveData", nullptr, (EPropertyFlags)0x0020080000020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventorySaveGame, CustomSaveData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomSaveData_MetaData), NewProp_CustomSaveData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UInventorySaveGame_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_SaveSlotName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_UserIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_SaveDateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GameVersion,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_InventorySlots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_InventorySlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GridWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_GridHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_MaxWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PuzzleData_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PuzzleData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_PlayerData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_WorldData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventorySaveGame_Statics::NewProp_CustomSaveData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInventorySaveGame_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UInventorySaveGame_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_USaveGame,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInventorySaveGame_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UInventorySaveGame_Statics::ClassParams = {
	&UInventorySaveGame::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UInventorySaveGame_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UInventorySaveGame_Statics::PropPointers),
	0,
	0x001000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UInventorySaveGame_Statics::Class_MetaDataParams), Z_Construct_UClass_UInventorySaveGame_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UInventorySaveGame()
{
	if (!Z_Registration_Info_UClass_UInventorySaveGame.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UInventorySaveGame.OuterSingleton, Z_Construct_UClass_UInventorySaveGame_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UInventorySaveGame.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UInventorySaveGame>()
{
	return UInventorySaveGame::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UInventorySaveGame);
UInventorySaveGame::~UInventorySaveGame() {}
// End Class UInventorySaveGame

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPuzzleSaveData::StaticStruct, Z_Construct_UScriptStruct_FPuzzleSaveData_Statics::NewStructOps, TEXT("PuzzleSaveData"), &Z_Registration_Info_UScriptStruct_PuzzleSaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPuzzleSaveData), 1440889550U) },
		{ FPlayerSaveData::StaticStruct, Z_Construct_UScriptStruct_FPlayerSaveData_Statics::NewStructOps, TEXT("PlayerSaveData"), &Z_Registration_Info_UScriptStruct_PlayerSaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerSaveData), 3329062140U) },
		{ FWorldSaveData::StaticStruct, Z_Construct_UScriptStruct_FWorldSaveData_Statics::NewStructOps, TEXT("WorldSaveData"), &Z_Registration_Info_UScriptStruct_WorldSaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FWorldSaveData), 191440310U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UInventorySaveGame, UInventorySaveGame::StaticClass, TEXT("UInventorySaveGame"), &Z_Registration_Info_UClass_UInventorySaveGame, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UInventorySaveGame), 1838475422U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_1339492429(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_InventorySaveGame_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
