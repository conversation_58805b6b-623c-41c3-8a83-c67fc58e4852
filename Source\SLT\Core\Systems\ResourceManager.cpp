#include "ResourceManager.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

UResourceManager::UResourceManager()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.1f; // Update 10 times per second

	// Default scarcity thresholds
	AbundantThreshold = 0.8f;
	NormalThreshold = 0.5f;
	ScarceThreshold = 0.2f;
	CriticalThreshold = 0.05f;
}

void UResourceManager::BeginPlay()
{
	Super::BeginPlay();

	// Initialize all resources
	for (auto& ResourcePair : Resources)
	{
		ResourcePair.Value.LastUpdateTime = GetWorld()->GetTimeSeconds();
		UpdateResourceScarcity(ResourcePair.Key);
	}
}

void UResourceManager::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	RegenerateAllResources(DeltaTime);
}

bool UResourceManager::AddResource(FName ResourceID, const FResourceData& ResourceData)
{
	if (ResourceID == NAME_None)
	{
		return false;
	}

	FResourceData NewResourceData = ResourceData;
	NewResourceData.LastUpdateTime = GetWorld()->GetTimeSeconds();
	
	Resources.Add(ResourceID, NewResourceData);
	UpdateResourceScarcity(ResourceID);
	
	OnResourceChanged.Broadcast(ResourceID, NewResourceData.CurrentAmount, NewResourceData.MaxAmount);
	OnResourceUpdated(ResourceID, NewResourceData);
	
	return true;
}

bool UResourceManager::RemoveResource(FName ResourceID)
{
	if (Resources.Contains(ResourceID))
	{
		Resources.Remove(ResourceID);
		return true;
	}
	return false;
}

bool UResourceManager::HasResource(FName ResourceID) const
{
	return Resources.Contains(ResourceID);
}

FResourceData UResourceManager::GetResourceData(FName ResourceID) const
{
	if (const FResourceData* Data = Resources.Find(ResourceID))
	{
		return *Data;
	}
	return FResourceData();
}

float UResourceManager::GetResourceAmount(FName ResourceID) const
{
	if (const FResourceData* Data = Resources.Find(ResourceID))
	{
		return Data->CurrentAmount;
	}
	return 0.0f;
}

float UResourceManager::GetResourcePercentage(FName ResourceID) const
{
	if (const FResourceData* Data = Resources.Find(ResourceID))
	{
		if (Data->MaxAmount > 0.0f)
		{
			return Data->CurrentAmount / Data->MaxAmount;
		}
	}
	return 0.0f;
}

bool UResourceManager::SetResourceAmount(FName ResourceID, float NewAmount)
{
	if (FResourceData* Data = Resources.Find(ResourceID))
	{
		float OldAmount = Data->CurrentAmount;
		Data->CurrentAmount = FMath::Clamp(NewAmount, Data->MinAmount, Data->bHasMaxLimit ? Data->MaxAmount : NewAmount);
		
		if (OldAmount != Data->CurrentAmount)
		{
			UpdateResourceScarcity(ResourceID);
			OnResourceChanged.Broadcast(ResourceID, Data->CurrentAmount, Data->MaxAmount);
			OnResourceUpdated(ResourceID, *Data);
			
			// Check for depletion
			if (Data->CurrentAmount <= Data->MinAmount)
			{
				OnResourceDepleted.Broadcast(ResourceID, Data->ResourceType);
			}
		}
		
		return true;
	}
	return false;
}

bool UResourceManager::ModifyResource(FName ResourceID, float Amount)
{
	if (const FResourceData* Data = Resources.Find(ResourceID))
	{
		return SetResourceAmount(ResourceID, Data->CurrentAmount + Amount);
	}
	return false;
}

bool UResourceManager::SetResourceMax(FName ResourceID, float NewMax)
{
	if (FResourceData* Data = Resources.Find(ResourceID))
	{
		Data->MaxAmount = FMath::Max(0.0f, NewMax);
		
		// Clamp current amount if necessary
		if (Data->bHasMaxLimit && Data->CurrentAmount > Data->MaxAmount)
		{
			Data->CurrentAmount = Data->MaxAmount;
		}
		
		UpdateResourceScarcity(ResourceID);
		OnResourceChanged.Broadcast(ResourceID, Data->CurrentAmount, Data->MaxAmount);
		OnResourceUpdated(ResourceID, *Data);
		
		return true;
	}
	return false;
}

bool UResourceManager::CanPerformAction(const FGameplayTag& ActionTag) const
{
	// Check all consumption rules for this action
	for (const FResourceConsumptionRule& Rule : ConsumptionRules)
	{
		if (Rule.ActionTag == ActionTag)
		{
			const FResourceData* ResourceData = Resources.Find(Rule.ResourceID);
			if (!ResourceData)
			{
				return false; // Resource doesn't exist
			}

			float RequiredAmount = Rule.ConsumptionAmount;
			if (Rule.bIsPercentage)
			{
				RequiredAmount = ResourceData->MaxAmount * (Rule.ConsumptionAmount / 100.0f);
			}

			if (Rule.bRequireFullAmount && ResourceData->CurrentAmount < RequiredAmount)
			{
				return false;
			}
		}
	}

	return true;
}

bool UResourceManager::ConsumeForAction(const FGameplayTag& ActionTag)
{
	if (!CanPerformAction(ActionTag))
	{
		// Find which resource is insufficient
		for (const FResourceConsumptionRule& Rule : ConsumptionRules)
		{
			if (Rule.ActionTag == ActionTag)
			{
				const FResourceData* ResourceData = Resources.Find(Rule.ResourceID);
				if (ResourceData)
				{
					float RequiredAmount = Rule.ConsumptionAmount;
					if (Rule.bIsPercentage)
					{
						RequiredAmount = ResourceData->MaxAmount * (Rule.ConsumptionAmount / 100.0f);
					}

					if (ResourceData->CurrentAmount < RequiredAmount)
					{
						OnActionBlocked(ActionTag, Rule.ResourceID);
						break;
					}
				}
			}
		}
		return false;
	}

	// Consume resources
	bool bSuccess = true;
	for (const FResourceConsumptionRule& Rule : ConsumptionRules)
	{
		if (Rule.ActionTag == ActionTag)
		{
			float ConsumeAmount = Rule.ConsumptionAmount;
			if (Rule.bIsPercentage)
			{
				const FResourceData* ResourceData = Resources.Find(Rule.ResourceID);
				if (ResourceData)
				{
					ConsumeAmount = ResourceData->MaxAmount * (Rule.ConsumptionAmount / 100.0f);
				}
			}

			if (ModifyResource(Rule.ResourceID, -ConsumeAmount))
			{
				OnResourceConsumed.Broadcast(Rule.ResourceID, ConsumeAmount, ActionTag);
			}
			else
			{
				bSuccess = false;
			}
		}
	}

	return bSuccess;
}

float UResourceManager::GetActionCost(const FGameplayTag& ActionTag, FName ResourceID) const
{
	for (const FResourceConsumptionRule& Rule : ConsumptionRules)
	{
		if (Rule.ActionTag == ActionTag && Rule.ResourceID == ResourceID)
		{
			float Cost = Rule.ConsumptionAmount;
			if (Rule.bIsPercentage)
			{
				const FResourceData* ResourceData = Resources.Find(ResourceID);
				if (ResourceData)
				{
					Cost = ResourceData->MaxAmount * (Rule.ConsumptionAmount / 100.0f);
				}
			}
			return Cost;
		}
	}
	return 0.0f;
}

EResourceScarcityLevel UResourceManager::GetResourceScarcity(FName ResourceID) const
{
	if (const FResourceData* Data = Resources.Find(ResourceID))
	{
		return Data->ScarcityLevel;
	}
	return EResourceScarcityLevel::Empty;
}

TArray<FName> UResourceManager::GetResourcesByScarcity(EResourceScarcityLevel ScarcityLevel) const
{
	TArray<FName> FilteredResources;
	
	for (const auto& ResourcePair : Resources)
	{
		if (ResourcePair.Value.ScarcityLevel == ScarcityLevel)
		{
			FilteredResources.Add(ResourcePair.Key);
		}
	}
	
	return FilteredResources;
}

bool UResourceManager::IsResourceCritical(FName ResourceID) const
{
	EResourceScarcityLevel Scarcity = GetResourceScarcity(ResourceID);
	return Scarcity == EResourceScarcityLevel::Critical || Scarcity == EResourceScarcityLevel::Empty;
}

TArray<FName> UResourceManager::GetAllResourceIDs() const
{
	TArray<FName> ResourceIDs;
	Resources.GetKeys(ResourceIDs);
	return ResourceIDs;
}

TArray<FName> UResourceManager::GetResourcesByType(EResourceType ResourceType) const
{
	TArray<FName> FilteredResources;
	
	for (const auto& ResourcePair : Resources)
	{
		if (ResourcePair.Value.ResourceType == ResourceType)
		{
			FilteredResources.Add(ResourcePair.Key);
		}
	}
	
	return FilteredResources;
}

void UResourceManager::RegenerateAllResources(float DeltaTime)
{
	for (auto& ResourcePair : Resources)
	{
		ProcessResourceRegeneration(ResourcePair.Key, DeltaTime);
		ProcessResourceDecay(ResourcePair.Key, DeltaTime);
	}
}

void UResourceManager::ResetAllResources()
{
	for (auto& ResourcePair : Resources)
	{
		FResourceData& Data = ResourcePair.Value;
		Data.CurrentAmount = Data.MaxAmount;
		UpdateResourceScarcity(ResourcePair.Key);
		OnResourceChanged.Broadcast(ResourcePair.Key, Data.CurrentAmount, Data.MaxAmount);
		OnResourceUpdated(ResourcePair.Key, Data);
	}
}

bool UResourceManager::ConsumeItem(const FInventoryItemData& ItemData, int32 Quantity)
{
	// Check if item has resource restoration properties
	for (const auto& PropertyPair : ItemData.CustomProperties)
	{
		if (PropertyPair.Key.StartsWith(TEXT("Resource_")))
		{
			FString ResourceIDString = PropertyPair.Key.RightChop(9); // Remove "Resource_" prefix
			FName ResourceID = FName(*ResourceIDString);
			
			float RestoreAmount = FCString::Atof(*PropertyPair.Value) * Quantity;
			ModifyResource(ResourceID, RestoreAmount);
		}
	}
	
	return true;
}

float UResourceManager::GetItemResourceValue(const FInventoryItemData& ItemData, FName ResourceID) const
{
	FString PropertyKey = FString::Printf(TEXT("Resource_%s"), *ResourceID.ToString());
	if (const FString* Value = ItemData.CustomProperties.Find(PropertyKey))
	{
		return FCString::Atof(**Value);
	}
	return 0.0f;
}

void UResourceManager::UpdateResourceScarcity(FName ResourceID)
{
	if (FResourceData* Data = Resources.Find(ResourceID))
	{
		float Percentage = GetResourcePercentage(ResourceID);
		EResourceScarcityLevel NewLevel = CalculateScarcityLevel(Percentage);
		
		if (NewLevel != Data->ScarcityLevel)
		{
			EResourceScarcityLevel OldLevel = Data->ScarcityLevel;
			Data->ScarcityLevel = NewLevel;
			OnResourceScarcityChanged.Broadcast(ResourceID, NewLevel);
		}
	}
}

void UResourceManager::ProcessResourceRegeneration(FName ResourceID, float DeltaTime)
{
	if (FResourceData* Data = Resources.Find(ResourceID))
	{
		if (Data->bCanRegenerate && Data->RegenerationRate > 0.0f)
		{
			float RegenAmount = Data->RegenerationRate * DeltaTime;
			float NewAmount = FMath::Min(Data->CurrentAmount + RegenAmount, Data->MaxAmount);
			
			if (NewAmount != Data->CurrentAmount)
			{
				SetResourceAmount(ResourceID, NewAmount);
			}
		}
	}
}

void UResourceManager::ProcessResourceDecay(FName ResourceID, float DeltaTime)
{
	if (FResourceData* Data = Resources.Find(ResourceID))
	{
		if (Data->bCanDecay && Data->DecayRate > 0.0f)
		{
			float DecayAmount = Data->DecayRate * DeltaTime;
			float NewAmount = FMath::Max(Data->CurrentAmount - DecayAmount, Data->MinAmount);
			
			if (NewAmount != Data->CurrentAmount)
			{
				SetResourceAmount(ResourceID, NewAmount);
			}
		}
	}
}

EResourceScarcityLevel UResourceManager::CalculateScarcityLevel(float Percentage) const
{
	if (Percentage <= 0.0f)
	{
		return EResourceScarcityLevel::Empty;
	}
	else if (Percentage <= CriticalThreshold)
	{
		return EResourceScarcityLevel::Critical;
	}
	else if (Percentage <= ScarceThreshold)
	{
		return EResourceScarcityLevel::Scarce;
	}
	else if (Percentage <= NormalThreshold)
	{
		return EResourceScarcityLevel::Normal;
	}
	else
	{
		return EResourceScarcityLevel::Abundant;
	}
}
