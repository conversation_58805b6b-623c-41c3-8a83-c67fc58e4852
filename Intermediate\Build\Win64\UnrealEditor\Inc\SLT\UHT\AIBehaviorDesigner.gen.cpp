// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Design/AIBehaviorDesigner.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAIBehaviorDesigner() {}

// Begin Cross Module References
AIMODULE_API UClass* Z_Construct_UClass_AAIController_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTree_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UBehaviorTreeComponent_NoRegister();
AIMODULE_API UClass* Z_Construct_UClass_UBlackboardComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UAIBehaviorDesigner();
SLT_API UClass* Z_Construct_UClass_UAIBehaviorDesigner_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAIBehaviorType();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAIDifficultyLevel();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAIPersonality();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FAIBehaviorState();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FAIBehaviorTemplate();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EAIBehaviorType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAIBehaviorType;
static UEnum* EAIBehaviorType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAIBehaviorType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAIBehaviorType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAIBehaviorType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAIBehaviorType"));
	}
	return Z_Registration_Info_UEnum_EAIBehaviorType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAIBehaviorType>()
{
	return EAIBehaviorType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAIBehaviorType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ambush.DisplayName", "Ambush" },
		{ "Ambush.Name", "EAIBehaviorType::Ambush" },
		{ "BlueprintType", "true" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EAIBehaviorType::Custom" },
		{ "Flee.DisplayName", "Flee" },
		{ "Flee.Name", "EAIBehaviorType::Flee" },
		{ "Follow.DisplayName", "Follow" },
		{ "Follow.Name", "EAIBehaviorType::Follow" },
		{ "Guard.DisplayName", "Guard" },
		{ "Guard.Name", "EAIBehaviorType::Guard" },
		{ "Hunt.DisplayName", "Hunt" },
		{ "Hunt.Name", "EAIBehaviorType::Hunt" },
		{ "Investigate.DisplayName", "Investigate" },
		{ "Investigate.Name", "EAIBehaviorType::Investigate" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
		{ "Patrol.DisplayName", "Patrol" },
		{ "Patrol.Name", "EAIBehaviorType::Patrol" },
		{ "Swarm.DisplayName", "Swarm" },
		{ "Swarm.Name", "EAIBehaviorType::Swarm" },
		{ "Wander.DisplayName", "Wander" },
		{ "Wander.Name", "EAIBehaviorType::Wander" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAIBehaviorType::Patrol", (int64)EAIBehaviorType::Patrol },
		{ "EAIBehaviorType::Guard", (int64)EAIBehaviorType::Guard },
		{ "EAIBehaviorType::Hunt", (int64)EAIBehaviorType::Hunt },
		{ "EAIBehaviorType::Flee", (int64)EAIBehaviorType::Flee },
		{ "EAIBehaviorType::Investigate", (int64)EAIBehaviorType::Investigate },
		{ "EAIBehaviorType::Follow", (int64)EAIBehaviorType::Follow },
		{ "EAIBehaviorType::Wander", (int64)EAIBehaviorType::Wander },
		{ "EAIBehaviorType::Ambush", (int64)EAIBehaviorType::Ambush },
		{ "EAIBehaviorType::Swarm", (int64)EAIBehaviorType::Swarm },
		{ "EAIBehaviorType::Custom", (int64)EAIBehaviorType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAIBehaviorType",
	"EAIBehaviorType",
	Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAIBehaviorType()
{
	if (!Z_Registration_Info_UEnum_EAIBehaviorType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAIBehaviorType.InnerSingleton, Z_Construct_UEnum_SLT_EAIBehaviorType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAIBehaviorType.InnerSingleton;
}
// End Enum EAIBehaviorType

// Begin Enum EAIPersonality
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAIPersonality;
static UEnum* EAIPersonality_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAIPersonality.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAIPersonality.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAIPersonality, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAIPersonality"));
	}
	return Z_Registration_Info_UEnum_EAIPersonality.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAIPersonality>()
{
	return EAIPersonality_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAIPersonality_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAIPersonality::Adaptive" },
		{ "Aggressive.DisplayName", "Aggressive" },
		{ "Aggressive.Name", "EAIPersonality::Aggressive" },
		{ "BlueprintType", "true" },
		{ "Cautious.DisplayName", "Cautious" },
		{ "Cautious.Name", "EAIPersonality::Cautious" },
		{ "Cowardly.DisplayName", "Cowardly" },
		{ "Cowardly.Name", "EAIPersonality::Cowardly" },
		{ "Defensive.DisplayName", "Defensive" },
		{ "Defensive.Name", "EAIPersonality::Defensive" },
		{ "Intelligent.DisplayName", "Intelligent" },
		{ "Intelligent.Name", "EAIPersonality::Intelligent" },
		{ "Lone.DisplayName", "Lone" },
		{ "Lone.Name", "EAIPersonality::Lone" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
		{ "Pack.DisplayName", "Pack" },
		{ "Pack.Name", "EAIPersonality::Pack" },
		{ "Reckless.DisplayName", "Reckless" },
		{ "Reckless.Name", "EAIPersonality::Reckless" },
		{ "Territorial.DisplayName", "Territorial" },
		{ "Territorial.Name", "EAIPersonality::Territorial" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAIPersonality::Aggressive", (int64)EAIPersonality::Aggressive },
		{ "EAIPersonality::Defensive", (int64)EAIPersonality::Defensive },
		{ "EAIPersonality::Cautious", (int64)EAIPersonality::Cautious },
		{ "EAIPersonality::Reckless", (int64)EAIPersonality::Reckless },
		{ "EAIPersonality::Intelligent", (int64)EAIPersonality::Intelligent },
		{ "EAIPersonality::Cowardly", (int64)EAIPersonality::Cowardly },
		{ "EAIPersonality::Territorial", (int64)EAIPersonality::Territorial },
		{ "EAIPersonality::Pack", (int64)EAIPersonality::Pack },
		{ "EAIPersonality::Lone", (int64)EAIPersonality::Lone },
		{ "EAIPersonality::Adaptive", (int64)EAIPersonality::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAIPersonality_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAIPersonality",
	"EAIPersonality",
	Z_Construct_UEnum_SLT_EAIPersonality_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIPersonality_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIPersonality_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAIPersonality_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAIPersonality()
{
	if (!Z_Registration_Info_UEnum_EAIPersonality.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAIPersonality.InnerSingleton, Z_Construct_UEnum_SLT_EAIPersonality_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAIPersonality.InnerSingleton;
}
// End Enum EAIPersonality

// Begin Enum EAIDifficultyLevel
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAIDifficultyLevel;
static UEnum* EAIDifficultyLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAIDifficultyLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAIDifficultyLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAIDifficultyLevel, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAIDifficultyLevel"));
	}
	return Z_Registration_Info_UEnum_EAIDifficultyLevel.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAIDifficultyLevel>()
{
	return EAIDifficultyLevel_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Adaptive.DisplayName", "Adaptive" },
		{ "Adaptive.Name", "EAIDifficultyLevel::Adaptive" },
		{ "Beginner.DisplayName", "Beginner" },
		{ "Beginner.Name", "EAIDifficultyLevel::Beginner" },
		{ "BlueprintType", "true" },
		{ "Easy.DisplayName", "Easy" },
		{ "Easy.Name", "EAIDifficultyLevel::Easy" },
		{ "Expert.DisplayName", "Expert" },
		{ "Expert.Name", "EAIDifficultyLevel::Expert" },
		{ "Hard.DisplayName", "Hard" },
		{ "Hard.Name", "EAIDifficultyLevel::Hard" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
		{ "Nightmare.DisplayName", "Nightmare" },
		{ "Nightmare.Name", "EAIDifficultyLevel::Nightmare" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EAIDifficultyLevel::Normal" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAIDifficultyLevel::Beginner", (int64)EAIDifficultyLevel::Beginner },
		{ "EAIDifficultyLevel::Easy", (int64)EAIDifficultyLevel::Easy },
		{ "EAIDifficultyLevel::Normal", (int64)EAIDifficultyLevel::Normal },
		{ "EAIDifficultyLevel::Hard", (int64)EAIDifficultyLevel::Hard },
		{ "EAIDifficultyLevel::Expert", (int64)EAIDifficultyLevel::Expert },
		{ "EAIDifficultyLevel::Nightmare", (int64)EAIDifficultyLevel::Nightmare },
		{ "EAIDifficultyLevel::Adaptive", (int64)EAIDifficultyLevel::Adaptive },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAIDifficultyLevel",
	"EAIDifficultyLevel",
	Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAIDifficultyLevel()
{
	if (!Z_Registration_Info_UEnum_EAIDifficultyLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAIDifficultyLevel.InnerSingleton, Z_Construct_UEnum_SLT_EAIDifficultyLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAIDifficultyLevel.InnerSingleton;
}
// End Enum EAIDifficultyLevel

// Begin ScriptStruct FAIBehaviorTemplate
static_assert(std::is_polymorphic<FAIBehaviorTemplate>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FAIBehaviorTemplate cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_AIBehaviorTemplate;
class UScriptStruct* FAIBehaviorTemplate::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAIBehaviorTemplate, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("AIBehaviorTemplate"));
	}
	return Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FAIBehaviorTemplate>()
{
	return FAIBehaviorTemplate::StaticStruct();
}
struct Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateID_MetaData[] = {
		{ "Category", "Template" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateDescription_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorType_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Personality_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuiltIn_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresSetup_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedSetupTime_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTree_MetaData[] = {
		{ "Category", "AI Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// AI assets\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "AI assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SightRadius_MetaData[] = {
		{ "Category", "Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior parameters\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_HearingRadius_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovementSpeed_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackRange_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackDamage_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AttackCooldown_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PatrolRadius_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AlertDuration_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InvestigationTime_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanCallForHelp_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanUseWeapons_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanTakeCover_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanFlank_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRetreat_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLearnsFromPlayer_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSupportsGroupBehavior_MetaData[] = {
		{ "Category", "Group" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Group behavior\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Group behavior" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GroupCommunicationRange_MetaData[] = {
		{ "Category", "Group" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxGroupSize_MetaData[] = {
		{ "Category", "Group" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tags and categorization\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags and categorization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchKeywords_MetaData[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomParameters_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TemplateDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Personality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Personality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DifficultyLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DifficultyLevel;
	static void NewProp_bIsBuiltIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuiltIn;
	static void NewProp_bRequiresSetup_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresSetup;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedSetupTime;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BehaviorTree;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_SightRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_HearingRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MovementSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackDamage;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AttackCooldown;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PatrolRadius;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AlertDuration;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InvestigationTime;
	static void NewProp_bCanCallForHelp_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanCallForHelp;
	static void NewProp_bCanUseWeapons_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanUseWeapons;
	static void NewProp_bCanTakeCover_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanTakeCover;
	static void NewProp_bCanFlank_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanFlank;
	static void NewProp_bCanRetreat_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRetreat;
	static void NewProp_bLearnsFromPlayer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLearnsFromPlayer;
	static void NewProp_bSupportsGroupBehavior_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSupportsGroupBehavior;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GroupCommunicationRange;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxGroupSize;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchKeywords_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SearchKeywords;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAIBehaviorTemplate>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, TemplateID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateID_MetaData), NewProp_TemplateID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateDescription = { "TemplateDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, TemplateDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateDescription_MetaData), NewProp_TemplateDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, BehaviorType), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorType_MetaData), NewProp_BehaviorType_MetaData) }; // 269887658
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_Personality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_Personality = { "Personality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, Personality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Personality_MetaData), NewProp_Personality_MetaData) }; // 1411235553
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_DifficultyLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, DifficultyLevel), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) }; // 3987173687
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bIsBuiltIn_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bIsBuiltIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bIsBuiltIn = { "bIsBuiltIn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bIsBuiltIn_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuiltIn_MetaData), NewProp_bIsBuiltIn_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bRequiresSetup_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bRequiresSetup = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bRequiresSetup = { "bRequiresSetup", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bRequiresSetup_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresSetup_MetaData), NewProp_bRequiresSetup_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_EstimatedSetupTime = { "EstimatedSetupTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, EstimatedSetupTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedSetupTime_MetaData), NewProp_EstimatedSetupTime_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorTree = { "BehaviorTree", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, BehaviorTree), Z_Construct_UClass_UBehaviorTree_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTree_MetaData), NewProp_BehaviorTree_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SightRadius = { "SightRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, SightRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SightRadius_MetaData), NewProp_SightRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_HearingRadius = { "HearingRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, HearingRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_HearingRadius_MetaData), NewProp_HearingRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_MovementSpeed = { "MovementSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, MovementSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovementSpeed_MetaData), NewProp_MovementSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackRange = { "AttackRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, AttackRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackRange_MetaData), NewProp_AttackRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackDamage = { "AttackDamage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, AttackDamage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackDamage_MetaData), NewProp_AttackDamage_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackCooldown = { "AttackCooldown", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, AttackCooldown), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AttackCooldown_MetaData), NewProp_AttackCooldown_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_PatrolRadius = { "PatrolRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, PatrolRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PatrolRadius_MetaData), NewProp_PatrolRadius_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AlertDuration = { "AlertDuration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, AlertDuration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AlertDuration_MetaData), NewProp_AlertDuration_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_InvestigationTime = { "InvestigationTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, InvestigationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InvestigationTime_MetaData), NewProp_InvestigationTime_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanCallForHelp_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bCanCallForHelp = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanCallForHelp = { "bCanCallForHelp", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanCallForHelp_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanCallForHelp_MetaData), NewProp_bCanCallForHelp_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanUseWeapons_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bCanUseWeapons = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanUseWeapons = { "bCanUseWeapons", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanUseWeapons_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanUseWeapons_MetaData), NewProp_bCanUseWeapons_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanTakeCover_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bCanTakeCover = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanTakeCover = { "bCanTakeCover", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanTakeCover_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanTakeCover_MetaData), NewProp_bCanTakeCover_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanFlank_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bCanFlank = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanFlank = { "bCanFlank", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanFlank_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanFlank_MetaData), NewProp_bCanFlank_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanRetreat_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bCanRetreat = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanRetreat = { "bCanRetreat", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanRetreat_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRetreat_MetaData), NewProp_bCanRetreat_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bLearnsFromPlayer_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bLearnsFromPlayer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bLearnsFromPlayer = { "bLearnsFromPlayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bLearnsFromPlayer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLearnsFromPlayer_MetaData), NewProp_bLearnsFromPlayer_MetaData) };
void Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bSupportsGroupBehavior_SetBit(void* Obj)
{
	((FAIBehaviorTemplate*)Obj)->bSupportsGroupBehavior = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bSupportsGroupBehavior = { "bSupportsGroupBehavior", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorTemplate), &Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bSupportsGroupBehavior_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSupportsGroupBehavior_MetaData), NewProp_bSupportsGroupBehavior_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_GroupCommunicationRange = { "GroupCommunicationRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, GroupCommunicationRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GroupCommunicationRange_MetaData), NewProp_GroupCommunicationRange_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_MaxGroupSize = { "MaxGroupSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, MaxGroupSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxGroupSize_MetaData), NewProp_MaxGroupSize_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorTags = { "BehaviorTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, BehaviorTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTags_MetaData), NewProp_BehaviorTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SearchKeywords_Inner = { "SearchKeywords", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SearchKeywords = { "SearchKeywords", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, SearchKeywords), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchKeywords_MetaData), NewProp_SearchKeywords_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters_ValueProp = { "CustomParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters_Key_KeyProp = { "CustomParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters = { "CustomParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorTemplate, CustomParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomParameters_MetaData), NewProp_CustomParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_TemplateDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_Personality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_Personality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_DifficultyLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bIsBuiltIn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bRequiresSetup,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_EstimatedSetupTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorTree,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SightRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_HearingRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_MovementSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackDamage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AttackCooldown,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_PatrolRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_AlertDuration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_InvestigationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanCallForHelp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanUseWeapons,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanTakeCover,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanFlank,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bCanRetreat,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bLearnsFromPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_bSupportsGroupBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_GroupCommunicationRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_MaxGroupSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_BehaviorTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SearchKeywords_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_SearchKeywords,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewProp_CustomParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"AIBehaviorTemplate",
	Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::PropPointers),
	sizeof(FAIBehaviorTemplate),
	alignof(FAIBehaviorTemplate),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAIBehaviorTemplate()
{
	if (!Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.InnerSingleton, Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_AIBehaviorTemplate.InnerSingleton;
}
// End ScriptStruct FAIBehaviorTemplate

// Begin ScriptStruct FAIBehaviorState
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_AIBehaviorState;
class UScriptStruct* FAIBehaviorState::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_AIBehaviorState.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_AIBehaviorState.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAIBehaviorState, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("AIBehaviorState"));
	}
	return Z_Registration_Info_UScriptStruct_AIBehaviorState.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FAIBehaviorState>()
{
	return FAIBehaviorState::StaticStruct();
}
struct Z_Construct_UScriptStruct_FAIBehaviorState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentBehavior_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentPersonality_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentDifficulty_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StateStartTime_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastStateChange_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StateChangeCount_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorParameters_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveModifiers_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentBehavior;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentPersonality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentPersonality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentDifficulty_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentDifficulty;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StateStartTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastStateChange;
	static const UECodeGen_Private::FIntPropertyParams NewProp_StateChangeCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BehaviorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_BehaviorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_BehaviorParameters;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ActiveModifiers_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveModifiers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAIBehaviorState>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentBehavior = { "CurrentBehavior", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, CurrentBehavior), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentBehavior_MetaData), NewProp_CurrentBehavior_MetaData) }; // 269887658
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentPersonality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentPersonality = { "CurrentPersonality", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, CurrentPersonality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentPersonality_MetaData), NewProp_CurrentPersonality_MetaData) }; // 1411235553
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentDifficulty_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentDifficulty = { "CurrentDifficulty", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, CurrentDifficulty), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentDifficulty_MetaData), NewProp_CurrentDifficulty_MetaData) }; // 3987173687
void Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FAIBehaviorState*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAIBehaviorState), &Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_StateStartTime = { "StateStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, StateStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StateStartTime_MetaData), NewProp_StateStartTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_LastStateChange = { "LastStateChange", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, LastStateChange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastStateChange_MetaData), NewProp_LastStateChange_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_StateChangeCount = { "StateChangeCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, StateChangeCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StateChangeCount_MetaData), NewProp_StateChangeCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters_ValueProp = { "BehaviorParameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters_Key_KeyProp = { "BehaviorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters = { "BehaviorParameters", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, BehaviorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorParameters_MetaData), NewProp_BehaviorParameters_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_ActiveModifiers_Inner = { "ActiveModifiers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_ActiveModifiers = { "ActiveModifiers", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAIBehaviorState, ActiveModifiers), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveModifiers_MetaData), NewProp_ActiveModifiers_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAIBehaviorState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentPersonality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentPersonality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentDifficulty_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_CurrentDifficulty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_StateStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_LastStateChange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_StateChangeCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_BehaviorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_ActiveModifiers_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewProp_ActiveModifiers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAIBehaviorState_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"AIBehaviorState",
	Z_Construct_UScriptStruct_FAIBehaviorState_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorState_Statics::PropPointers),
	sizeof(FAIBehaviorState),
	alignof(FAIBehaviorState),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAIBehaviorState_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAIBehaviorState_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAIBehaviorState()
{
	if (!Z_Registration_Info_UScriptStruct_AIBehaviorState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_AIBehaviorState.InnerSingleton, Z_Construct_UScriptStruct_FAIBehaviorState_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_AIBehaviorState.InnerSingleton;
}
// End ScriptStruct FAIBehaviorState

// Begin Delegate FOnBehaviorChanged
struct Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnBehaviorChanged_Parms
	{
		EAIBehaviorType OldBehavior;
		EAIBehaviorType NewBehavior;
		AActor* AIActor;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldBehavior;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewBehavior;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AIActor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_OldBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_OldBehavior = { "OldBehavior", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnBehaviorChanged_Parms, OldBehavior), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_NewBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_NewBehavior = { "NewBehavior", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnBehaviorChanged_Parms, NewBehavior), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_AIActor = { "AIActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnBehaviorChanged_Parms, AIActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_OldBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_OldBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_NewBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_NewBehavior,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::NewProp_AIActor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnBehaviorChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::_Script_SLT_eventOnBehaviorChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::_Script_SLT_eventOnBehaviorChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBehaviorChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorChanged, EAIBehaviorType OldBehavior, EAIBehaviorType NewBehavior, AActor* AIActor)
{
	struct _Script_SLT_eventOnBehaviorChanged_Parms
	{
		EAIBehaviorType OldBehavior;
		EAIBehaviorType NewBehavior;
		AActor* AIActor;
	};
	_Script_SLT_eventOnBehaviorChanged_Parms Parms;
	Parms.OldBehavior=OldBehavior;
	Parms.NewBehavior=NewBehavior;
	Parms.AIActor=AIActor;
	OnBehaviorChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnBehaviorChanged

// Begin Delegate FOnPersonalityChanged
struct Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPersonalityChanged_Parms
	{
		EAIPersonality OldPersonality;
		EAIPersonality NewPersonality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldPersonality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldPersonality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPersonality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPersonality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_OldPersonality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_OldPersonality = { "OldPersonality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPersonalityChanged_Parms, OldPersonality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(0, nullptr) }; // 1411235553
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_NewPersonality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_NewPersonality = { "NewPersonality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPersonalityChanged_Parms, NewPersonality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(0, nullptr) }; // 1411235553
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_OldPersonality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_OldPersonality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_NewPersonality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::NewProp_NewPersonality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPersonalityChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::_Script_SLT_eventOnPersonalityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::_Script_SLT_eventOnPersonalityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPersonalityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPersonalityChanged, EAIPersonality OldPersonality, EAIPersonality NewPersonality)
{
	struct _Script_SLT_eventOnPersonalityChanged_Parms
	{
		EAIPersonality OldPersonality;
		EAIPersonality NewPersonality;
	};
	_Script_SLT_eventOnPersonalityChanged_Parms Parms;
	Parms.OldPersonality=OldPersonality;
	Parms.NewPersonality=NewPersonality;
	OnPersonalityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPersonalityChanged

// Begin Delegate FOnDifficultyChanged
struct Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnDifficultyChanged_Parms
	{
		EAIDifficultyLevel OldDifficulty;
		EAIDifficultyLevel NewDifficulty;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldDifficulty_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldDifficulty;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewDifficulty_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewDifficulty;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_OldDifficulty_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_OldDifficulty = { "OldDifficulty", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnDifficultyChanged_Parms, OldDifficulty), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(0, nullptr) }; // 3987173687
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_NewDifficulty_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_NewDifficulty = { "NewDifficulty", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnDifficultyChanged_Parms, NewDifficulty), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(0, nullptr) }; // 3987173687
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_OldDifficulty_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_OldDifficulty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_NewDifficulty_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::NewProp_NewDifficulty,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnDifficultyChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::_Script_SLT_eventOnDifficultyChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::_Script_SLT_eventOnDifficultyChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDifficultyChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDifficultyChanged, EAIDifficultyLevel OldDifficulty, EAIDifficultyLevel NewDifficulty)
{
	struct _Script_SLT_eventOnDifficultyChanged_Parms
	{
		EAIDifficultyLevel OldDifficulty;
		EAIDifficultyLevel NewDifficulty;
	};
	_Script_SLT_eventOnDifficultyChanged_Parms Parms;
	Parms.OldDifficulty=OldDifficulty;
	Parms.NewDifficulty=NewDifficulty;
	OnDifficultyChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnDifficultyChanged

// Begin Delegate FOnBehaviorTemplateApplied
struct Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnBehaviorTemplateApplied_Parms
	{
		FName TemplateID;
		FAIBehaviorTemplate Template;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnBehaviorTemplateApplied_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnBehaviorTemplateApplied_Parms, Template), Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 319305316
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::NewProp_TemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::NewProp_Template,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnBehaviorTemplateApplied__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::_Script_SLT_eventOnBehaviorTemplateApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::_Script_SLT_eventOnBehaviorTemplateApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnBehaviorTemplateApplied_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorTemplateApplied, FName TemplateID, FAIBehaviorTemplate const& Template)
{
	struct _Script_SLT_eventOnBehaviorTemplateApplied_Parms
	{
		FName TemplateID;
		FAIBehaviorTemplate Template;
	};
	_Script_SLT_eventOnBehaviorTemplateApplied_Parms Parms;
	Parms.TemplateID=TemplateID;
	Parms.Template=Template;
	OnBehaviorTemplateApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnBehaviorTemplateApplied

// Begin Class UAIBehaviorDesigner Function ActivateBehavior
struct Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ActivateBehavior", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execActivateBehavior)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ActivateBehavior();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ActivateBehavior

// Begin Class UAIBehaviorDesigner Function AdaptToPlayerBehavior
struct Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics
{
	struct AIBehaviorDesigner_eventAdaptToPlayerBehavior_Parms
	{
		TMap<FString,float> PlayerStats;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerStats_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayerStats_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerStats_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerStats;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats_ValueProp = { "PlayerStats", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats_Key_KeyProp = { "PlayerStats_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats = { "PlayerStats", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventAdaptToPlayerBehavior_Parms, PlayerStats), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerStats_MetaData), NewProp_PlayerStats_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::NewProp_PlayerStats,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "AdaptToPlayerBehavior", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::AIBehaviorDesigner_eventAdaptToPlayerBehavior_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::AIBehaviorDesigner_eventAdaptToPlayerBehavior_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execAdaptToPlayerBehavior)
{
	P_GET_TMAP_REF(FString,float,Z_Param_Out_PlayerStats);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AdaptToPlayerBehavior(Z_Param_Out_PlayerStats);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function AdaptToPlayerBehavior

// Begin Class UAIBehaviorDesigner Function ApplyBehaviorTemplate
struct Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics
{
	struct AIBehaviorDesigner_eventApplyBehaviorTemplate_Parms
	{
		FName TemplateID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main behavior functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main behavior functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventApplyBehaviorTemplate_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::NewProp_TemplateID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ApplyBehaviorTemplate", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::AIBehaviorDesigner_eventApplyBehaviorTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::AIBehaviorDesigner_eventApplyBehaviorTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execApplyBehaviorTemplate)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_TemplateID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyBehaviorTemplate(Z_Param_TemplateID);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ApplyBehaviorTemplate

// Begin Class UAIBehaviorDesigner Function ApplyParameterModifier
struct Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics
{
	struct AIBehaviorDesigner_eventApplyParameterModifier_Parms
	{
		FString ModifierName;
		float Multiplier;
		float Duration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Parameters" },
		{ "CPP_Default_Duration", "-1.000000" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModifierName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModifierName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Multiplier;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_ModifierName = { "ModifierName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventApplyParameterModifier_Parms, ModifierName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModifierName_MetaData), NewProp_ModifierName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_Multiplier = { "Multiplier", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventApplyParameterModifier_Parms, Multiplier), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventApplyParameterModifier_Parms, Duration), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_ModifierName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_Multiplier,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::NewProp_Duration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ApplyParameterModifier", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::AIBehaviorDesigner_eventApplyParameterModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::AIBehaviorDesigner_eventApplyParameterModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execApplyParameterModifier)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModifierName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Multiplier);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyParameterModifier(Z_Param_ModifierName,Z_Param_Multiplier,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ApplyParameterModifier

// Begin Class UAIBehaviorDesigner Function AutoConfigureForDifficulty
struct Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics
{
	struct AIBehaviorDesigner_eventAutoConfigureForDifficulty_Parms
	{
		EAIDifficultyLevel TargetDifficulty;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auto Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auto-configuration functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-configuration functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetDifficulty_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetDifficulty;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::NewProp_TargetDifficulty_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::NewProp_TargetDifficulty = { "TargetDifficulty", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventAutoConfigureForDifficulty_Parms, TargetDifficulty), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(0, nullptr) }; // 3987173687
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::NewProp_TargetDifficulty_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::NewProp_TargetDifficulty,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "AutoConfigureForDifficulty", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::AIBehaviorDesigner_eventAutoConfigureForDifficulty_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::AIBehaviorDesigner_eventAutoConfigureForDifficulty_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execAutoConfigureForDifficulty)
{
	P_GET_ENUM(EAIDifficultyLevel,Z_Param_TargetDifficulty);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoConfigureForDifficulty(EAIDifficultyLevel(Z_Param_TargetDifficulty));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function AutoConfigureForDifficulty

// Begin Class UAIBehaviorDesigner Function AutoConfigureForScenario
struct Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics
{
	struct AIBehaviorDesigner_eventAutoConfigureForScenario_Parms
	{
		FString ScenarioName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScenarioName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScenarioName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::NewProp_ScenarioName = { "ScenarioName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventAutoConfigureForScenario_Parms, ScenarioName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScenarioName_MetaData), NewProp_ScenarioName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::NewProp_ScenarioName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "AutoConfigureForScenario", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::AIBehaviorDesigner_eventAutoConfigureForScenario_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::AIBehaviorDesigner_eventAutoConfigureForScenario_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execAutoConfigureForScenario)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ScenarioName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoConfigureForScenario(Z_Param_ScenarioName);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function AutoConfigureForScenario

// Begin Class UAIBehaviorDesigner Function AutoFixCommonIssues
struct Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "AutoFixCommonIssues", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execAutoFixCommonIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoFixCommonIssues();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function AutoFixCommonIssues

// Begin Class UAIBehaviorDesigner Function CreateCustomTemplate
struct Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics
{
	struct AIBehaviorDesigner_eventCreateCustomTemplate_Parms
	{
		FAIBehaviorTemplate Template;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventCreateCustomTemplate_Parms, Template), Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 319305316
void Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AIBehaviorDesigner_eventCreateCustomTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AIBehaviorDesigner_eventCreateCustomTemplate_Parms), &Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "CreateCustomTemplate", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::AIBehaviorDesigner_eventCreateCustomTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::AIBehaviorDesigner_eventCreateCustomTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execCreateCustomTemplate)
{
	P_GET_STRUCT_REF(FAIBehaviorTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateCustomTemplate(Z_Param_Out_Template);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function CreateCustomTemplate

// Begin Class UAIBehaviorDesigner Function DeactivateBehavior
struct Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "DeactivateBehavior", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execDeactivateBehavior)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->DeactivateBehavior();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function DeactivateBehavior

// Begin Class UAIBehaviorDesigner Function ExportBehaviorConfiguration
struct Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics
{
	struct AIBehaviorDesigner_eventExportBehaviorConfiguration_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventExportBehaviorConfiguration_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ExportBehaviorConfiguration", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::AIBehaviorDesigner_eventExportBehaviorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::AIBehaviorDesigner_eventExportBehaviorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execExportBehaviorConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportBehaviorConfiguration(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ExportBehaviorConfiguration

// Begin Class UAIBehaviorDesigner Function GetAvailableTemplates
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics
{
	struct AIBehaviorDesigner_eventGetAvailableTemplates_Parms
	{
		TArray<FAIBehaviorTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetAvailableTemplates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetAvailableTemplates", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::AIBehaviorDesigner_eventGetAvailableTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::AIBehaviorDesigner_eventGetAvailableTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetAvailableTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAIBehaviorTemplate>*)Z_Param__Result=P_THIS->GetAvailableTemplates();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetAvailableTemplates

// Begin Class UAIBehaviorDesigner Function GetBehaviorParameter
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics
{
	struct AIBehaviorDesigner_eventGetBehaviorParameter_Parms
	{
		FString ParameterName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetBehaviorParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetBehaviorParameter_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetBehaviorParameter", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::AIBehaviorDesigner_eventGetBehaviorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::AIBehaviorDesigner_eventGetBehaviorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetBehaviorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetBehaviorParameter(Z_Param_ParameterName);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetBehaviorParameter

// Begin Class UAIBehaviorDesigner Function GetBehaviorTemplate
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics
{
	struct AIBehaviorDesigner_eventGetBehaviorTemplate_Parms
	{
		FName TemplateID;
		FAIBehaviorTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetBehaviorTemplate_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetBehaviorTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::NewProp_TemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetBehaviorTemplate", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::AIBehaviorDesigner_eventGetBehaviorTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::AIBehaviorDesigner_eventGetBehaviorTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetBehaviorTemplate)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_TemplateID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FAIBehaviorTemplate*)Z_Param__Result=P_THIS->GetBehaviorTemplate(Z_Param_TemplateID);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetBehaviorTemplate

// Begin Class UAIBehaviorDesigner Function GetSetupIssues
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics
{
	struct AIBehaviorDesigner_eventGetSetupIssues_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetSetupIssues_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetSetupIssues", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::AIBehaviorDesigner_eventGetSetupIssues_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::AIBehaviorDesigner_eventGetSetupIssues_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetSetupIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetSetupIssues();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetSetupIssues

// Begin Class UAIBehaviorDesigner Function GetTemplatesByPersonality
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics
{
	struct AIBehaviorDesigner_eventGetTemplatesByPersonality_Parms
	{
		EAIPersonality InPersonality;
		TArray<FAIBehaviorTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InPersonality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InPersonality;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_InPersonality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_InPersonality = { "InPersonality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetTemplatesByPersonality_Parms, InPersonality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(0, nullptr) }; // 1411235553
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetTemplatesByPersonality_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_InPersonality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_InPersonality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetTemplatesByPersonality", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::AIBehaviorDesigner_eventGetTemplatesByPersonality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::AIBehaviorDesigner_eventGetTemplatesByPersonality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetTemplatesByPersonality)
{
	P_GET_ENUM(EAIPersonality,Z_Param_InPersonality);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAIBehaviorTemplate>*)Z_Param__Result=P_THIS->GetTemplatesByPersonality(EAIPersonality(Z_Param_InPersonality));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetTemplatesByPersonality

// Begin Class UAIBehaviorDesigner Function GetTemplatesByType
struct Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics
{
	struct AIBehaviorDesigner_eventGetTemplatesByType_Parms
	{
		EAIBehaviorType InBehaviorType;
		TArray<FAIBehaviorTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InBehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InBehaviorType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_InBehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_InBehaviorType = { "InBehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetTemplatesByType_Parms, InBehaviorType), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAIBehaviorTemplate, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventGetTemplatesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 319305316
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_InBehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_InBehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "GetTemplatesByType", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::AIBehaviorDesigner_eventGetTemplatesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::AIBehaviorDesigner_eventGetTemplatesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execGetTemplatesByType)
{
	P_GET_ENUM(EAIBehaviorType,Z_Param_InBehaviorType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAIBehaviorTemplate>*)Z_Param__Result=P_THIS->GetTemplatesByType(EAIBehaviorType(Z_Param_InBehaviorType));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function GetTemplatesByType

// Begin Class UAIBehaviorDesigner Function ImportBehaviorConfiguration
struct Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics
{
	struct AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms), &Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ImportBehaviorConfiguration", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::AIBehaviorDesigner_eventImportBehaviorConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execImportBehaviorConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportBehaviorConfiguration(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ImportBehaviorConfiguration

// Begin Class UAIBehaviorDesigner Function OnBehaviorActivated
struct AIBehaviorDesigner_eventOnBehaviorActivated_Parms
{
	EAIBehaviorType InBehaviorType;
};
static const FName NAME_UAIBehaviorDesigner_OnBehaviorActivated = FName(TEXT("OnBehaviorActivated"));
void UAIBehaviorDesigner::OnBehaviorActivated(EAIBehaviorType InBehaviorType)
{
	AIBehaviorDesigner_eventOnBehaviorActivated_Parms Parms;
	Parms.InBehaviorType=InBehaviorType;
	UFunction* Func = FindFunctionChecked(NAME_UAIBehaviorDesigner_OnBehaviorActivated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InBehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InBehaviorType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::NewProp_InBehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::NewProp_InBehaviorType = { "InBehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventOnBehaviorActivated_Parms, InBehaviorType), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::NewProp_InBehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::NewProp_InBehaviorType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "OnBehaviorActivated", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::PropPointers), sizeof(AIBehaviorDesigner_eventOnBehaviorActivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::Function_MetaDataParams) };
static_assert(sizeof(AIBehaviorDesigner_eventOnBehaviorActivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAIBehaviorDesigner Function OnBehaviorActivated

// Begin Class UAIBehaviorDesigner Function OnBehaviorDeactivated
struct AIBehaviorDesigner_eventOnBehaviorDeactivated_Parms
{
	EAIBehaviorType InBehaviorType;
};
static const FName NAME_UAIBehaviorDesigner_OnBehaviorDeactivated = FName(TEXT("OnBehaviorDeactivated"));
void UAIBehaviorDesigner::OnBehaviorDeactivated(EAIBehaviorType InBehaviorType)
{
	AIBehaviorDesigner_eventOnBehaviorDeactivated_Parms Parms;
	Parms.InBehaviorType=InBehaviorType;
	UFunction* Func = FindFunctionChecked(NAME_UAIBehaviorDesigner_OnBehaviorDeactivated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_InBehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InBehaviorType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::NewProp_InBehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::NewProp_InBehaviorType = { "InBehaviorType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventOnBehaviorDeactivated_Parms, InBehaviorType), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::NewProp_InBehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::NewProp_InBehaviorType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "OnBehaviorDeactivated", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::PropPointers), sizeof(AIBehaviorDesigner_eventOnBehaviorDeactivated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::Function_MetaDataParams) };
static_assert(sizeof(AIBehaviorDesigner_eventOnBehaviorDeactivated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAIBehaviorDesigner Function OnBehaviorDeactivated

// Begin Class UAIBehaviorDesigner Function OnParameterChanged
struct AIBehaviorDesigner_eventOnParameterChanged_Parms
{
	FString ParameterName;
	float OldValue;
	float NewValue;
};
static const FName NAME_UAIBehaviorDesigner_OnParameterChanged = FName(TEXT("OnParameterChanged"));
void UAIBehaviorDesigner::OnParameterChanged(const FString& ParameterName, float OldValue, float NewValue)
{
	AIBehaviorDesigner_eventOnParameterChanged_Parms Parms;
	Parms.ParameterName=ParameterName;
	Parms.OldValue=OldValue;
	Parms.NewValue=NewValue;
	UFunction* Func = FindFunctionChecked(NAME_UAIBehaviorDesigner_OnParameterChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OldValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventOnParameterChanged_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_OldValue = { "OldValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventOnParameterChanged_Parms, OldValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventOnParameterChanged_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_OldValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::NewProp_NewValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "OnParameterChanged", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::PropPointers), sizeof(AIBehaviorDesigner_eventOnParameterChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(AIBehaviorDesigner_eventOnParameterChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAIBehaviorDesigner Function OnParameterChanged

// Begin Class UAIBehaviorDesigner Function RemoveParameterModifier
struct Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics
{
	struct AIBehaviorDesigner_eventRemoveParameterModifier_Parms
	{
		FString ModifierName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModifierName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModifierName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::NewProp_ModifierName = { "ModifierName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventRemoveParameterModifier_Parms, ModifierName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModifierName_MetaData), NewProp_ModifierName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::NewProp_ModifierName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "RemoveParameterModifier", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::AIBehaviorDesigner_eventRemoveParameterModifier_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::AIBehaviorDesigner_eventRemoveParameterModifier_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execRemoveParameterModifier)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ModifierName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveParameterModifier(Z_Param_ModifierName);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function RemoveParameterModifier

// Begin Class UAIBehaviorDesigner Function ResetToDefaults
struct Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ResetToDefaults", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execResetToDefaults)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetToDefaults();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ResetToDefaults

// Begin Class UAIBehaviorDesigner Function SetBehaviorParameter
struct Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics
{
	struct AIBehaviorDesigner_eventSetBehaviorParameter_Parms
	{
		FString ParameterName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Parameter functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Parameter functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventSetBehaviorParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventSetBehaviorParameter_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "SetBehaviorParameter", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::AIBehaviorDesigner_eventSetBehaviorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::AIBehaviorDesigner_eventSetBehaviorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execSetBehaviorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBehaviorParameter(Z_Param_ParameterName,Z_Param_Value);
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function SetBehaviorParameter

// Begin Class UAIBehaviorDesigner Function SetBehaviorType
struct Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics
{
	struct AIBehaviorDesigner_eventSetBehaviorType_Parms
	{
		EAIBehaviorType NewBehavior;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewBehavior_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewBehavior;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::NewProp_NewBehavior_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::NewProp_NewBehavior = { "NewBehavior", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventSetBehaviorType_Parms, NewBehavior), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(0, nullptr) }; // 269887658
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::NewProp_NewBehavior_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::NewProp_NewBehavior,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "SetBehaviorType", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::AIBehaviorDesigner_eventSetBehaviorType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::AIBehaviorDesigner_eventSetBehaviorType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execSetBehaviorType)
{
	P_GET_ENUM(EAIBehaviorType,Z_Param_NewBehavior);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetBehaviorType(EAIBehaviorType(Z_Param_NewBehavior));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function SetBehaviorType

// Begin Class UAIBehaviorDesigner Function SetDifficultyLevel
struct Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics
{
	struct AIBehaviorDesigner_eventSetDifficultyLevel_Parms
	{
		EAIDifficultyLevel NewDifficulty;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewDifficulty_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewDifficulty;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::NewProp_NewDifficulty_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::NewProp_NewDifficulty = { "NewDifficulty", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventSetDifficultyLevel_Parms, NewDifficulty), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(0, nullptr) }; // 3987173687
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::NewProp_NewDifficulty_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::NewProp_NewDifficulty,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "SetDifficultyLevel", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::AIBehaviorDesigner_eventSetDifficultyLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::AIBehaviorDesigner_eventSetDifficultyLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execSetDifficultyLevel)
{
	P_GET_ENUM(EAIDifficultyLevel,Z_Param_NewDifficulty);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetDifficultyLevel(EAIDifficultyLevel(Z_Param_NewDifficulty));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function SetDifficultyLevel

// Begin Class UAIBehaviorDesigner Function SetPersonality
struct Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics
{
	struct AIBehaviorDesigner_eventSetPersonality_Parms
	{
		EAIPersonality NewPersonality;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "AI Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewPersonality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewPersonality;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::NewProp_NewPersonality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::NewProp_NewPersonality = { "NewPersonality", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AIBehaviorDesigner_eventSetPersonality_Parms, NewPersonality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(0, nullptr) }; // 1411235553
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::NewProp_NewPersonality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::NewProp_NewPersonality,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "SetPersonality", nullptr, nullptr, Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::AIBehaviorDesigner_eventSetPersonality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::AIBehaviorDesigner_eventSetPersonality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execSetPersonality)
{
	P_GET_ENUM(EAIPersonality,Z_Param_NewPersonality);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPersonality(EAIPersonality(Z_Param_NewPersonality));
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function SetPersonality

// Begin Class UAIBehaviorDesigner Function ValidateBehaviorSetup
struct Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAIBehaviorDesigner, nullptr, "ValidateBehaviorSetup", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAIBehaviorDesigner::execValidateBehaviorSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ValidateBehaviorSetup();
	P_NATIVE_END;
}
// End Class UAIBehaviorDesigner Function ValidateBehaviorSetup

// Begin Class UAIBehaviorDesigner
void UAIBehaviorDesigner::StaticRegisterNativesUAIBehaviorDesigner()
{
	UClass* Class = UAIBehaviorDesigner::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateBehavior", &UAIBehaviorDesigner::execActivateBehavior },
		{ "AdaptToPlayerBehavior", &UAIBehaviorDesigner::execAdaptToPlayerBehavior },
		{ "ApplyBehaviorTemplate", &UAIBehaviorDesigner::execApplyBehaviorTemplate },
		{ "ApplyParameterModifier", &UAIBehaviorDesigner::execApplyParameterModifier },
		{ "AutoConfigureForDifficulty", &UAIBehaviorDesigner::execAutoConfigureForDifficulty },
		{ "AutoConfigureForScenario", &UAIBehaviorDesigner::execAutoConfigureForScenario },
		{ "AutoFixCommonIssues", &UAIBehaviorDesigner::execAutoFixCommonIssues },
		{ "CreateCustomTemplate", &UAIBehaviorDesigner::execCreateCustomTemplate },
		{ "DeactivateBehavior", &UAIBehaviorDesigner::execDeactivateBehavior },
		{ "ExportBehaviorConfiguration", &UAIBehaviorDesigner::execExportBehaviorConfiguration },
		{ "GetAvailableTemplates", &UAIBehaviorDesigner::execGetAvailableTemplates },
		{ "GetBehaviorParameter", &UAIBehaviorDesigner::execGetBehaviorParameter },
		{ "GetBehaviorTemplate", &UAIBehaviorDesigner::execGetBehaviorTemplate },
		{ "GetSetupIssues", &UAIBehaviorDesigner::execGetSetupIssues },
		{ "GetTemplatesByPersonality", &UAIBehaviorDesigner::execGetTemplatesByPersonality },
		{ "GetTemplatesByType", &UAIBehaviorDesigner::execGetTemplatesByType },
		{ "ImportBehaviorConfiguration", &UAIBehaviorDesigner::execImportBehaviorConfiguration },
		{ "RemoveParameterModifier", &UAIBehaviorDesigner::execRemoveParameterModifier },
		{ "ResetToDefaults", &UAIBehaviorDesigner::execResetToDefaults },
		{ "SetBehaviorParameter", &UAIBehaviorDesigner::execSetBehaviorParameter },
		{ "SetBehaviorType", &UAIBehaviorDesigner::execSetBehaviorType },
		{ "SetDifficultyLevel", &UAIBehaviorDesigner::execSetDifficultyLevel },
		{ "SetPersonality", &UAIBehaviorDesigner::execSetPersonality },
		{ "ValidateBehaviorSetup", &UAIBehaviorDesigner::execValidateBehaviorSetup },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UAIBehaviorDesigner);
UClass* Z_Construct_UClass_UAIBehaviorDesigner_NoRegister()
{
	return UAIBehaviorDesigner::StaticClass();
}
struct Z_Construct_UClass_UAIBehaviorDesigner_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "Core/Design/AIBehaviorDesigner.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorTemplateDatabase_MetaData[] = {
		{ "Category", "Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template database\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template database" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTemplateID_MetaData[] = {
		{ "Category", "Behavior" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current behavior configuration\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current behavior configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorType_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Personality_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DifficultyLevel_MetaData[] = {
		{ "Category", "Behavior" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BehaviorState_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Behavior state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Behavior state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoConfigureFromTemplate_MetaData[] = {
		{ "Category", "Auto Config" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Auto-configuration settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Auto-configuration settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAdaptToDifficulty_MetaData[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bLearnFromPlayer_MetaData[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdaptationRate_MetaData[] = {
		{ "Category", "Auto Config" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBehaviorChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPersonalityChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDifficultyChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnBehaviorTemplateApplied_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedTemplateDatabase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedAIController_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedBehaviorTreeComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedBlackboardComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterModifiers_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ModifierTimers_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/AIBehaviorDesigner.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BehaviorTemplateDatabase;
	static const UECodeGen_Private::FNamePropertyParams NewProp_CurrentTemplateID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_BehaviorType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_BehaviorType;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Personality_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Personality;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DifficultyLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DifficultyLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_BehaviorState;
	static void NewProp_bAutoConfigureFromTemplate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoConfigureFromTemplate;
	static void NewProp_bAdaptToDifficulty_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAdaptToDifficulty;
	static void NewProp_bLearnFromPlayer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bLearnFromPlayer;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AdaptationRate;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBehaviorChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPersonalityChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDifficultyChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnBehaviorTemplateApplied;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedTemplateDatabase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedAIController;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedBehaviorTreeComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedBlackboardComponent;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ParameterModifiers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterModifiers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ParameterModifiers;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ModifierTimers_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ModifierTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ModifierTimers;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ActivateBehavior, "ActivateBehavior" }, // 4067425643
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_AdaptToPlayerBehavior, "AdaptToPlayerBehavior" }, // 2213914060
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ApplyBehaviorTemplate, "ApplyBehaviorTemplate" }, // 3256468655
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ApplyParameterModifier, "ApplyParameterModifier" }, // 2599292660
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForDifficulty, "AutoConfigureForDifficulty" }, // 4101435434
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_AutoConfigureForScenario, "AutoConfigureForScenario" }, // 651097911
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_AutoFixCommonIssues, "AutoFixCommonIssues" }, // 2912655515
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_CreateCustomTemplate, "CreateCustomTemplate" }, // 4144194384
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_DeactivateBehavior, "DeactivateBehavior" }, // 239337439
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ExportBehaviorConfiguration, "ExportBehaviorConfiguration" }, // 907094960
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetAvailableTemplates, "GetAvailableTemplates" }, // 2275613705
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorParameter, "GetBehaviorParameter" }, // 2566281484
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetBehaviorTemplate, "GetBehaviorTemplate" }, // 2682623937
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetSetupIssues, "GetSetupIssues" }, // 318913153
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByPersonality, "GetTemplatesByPersonality" }, // 817347695
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_GetTemplatesByType, "GetTemplatesByType" }, // 1223302353
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ImportBehaviorConfiguration, "ImportBehaviorConfiguration" }, // 1945031009
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorActivated, "OnBehaviorActivated" }, // 885000017
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_OnBehaviorDeactivated, "OnBehaviorDeactivated" }, // 2861611772
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_OnParameterChanged, "OnParameterChanged" }, // 2461958553
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_RemoveParameterModifier, "RemoveParameterModifier" }, // 2972495352
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ResetToDefaults, "ResetToDefaults" }, // 3484432135
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorParameter, "SetBehaviorParameter" }, // 2219819575
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_SetBehaviorType, "SetBehaviorType" }, // 3373350355
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_SetDifficultyLevel, "SetDifficultyLevel" }, // 2254407873
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_SetPersonality, "SetPersonality" }, // 4190795362
		{ &Z_Construct_UFunction_UAIBehaviorDesigner_ValidateBehaviorSetup, "ValidateBehaviorSetup" }, // 1690491694
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAIBehaviorDesigner>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorTemplateDatabase = { "BehaviorTemplateDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, BehaviorTemplateDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorTemplateDatabase_MetaData), NewProp_BehaviorTemplateDatabase_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CurrentTemplateID = { "CurrentTemplateID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, CurrentTemplateID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTemplateID_MetaData), NewProp_CurrentTemplateID_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorType = { "BehaviorType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, BehaviorType), Z_Construct_UEnum_SLT_EAIBehaviorType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorType_MetaData), NewProp_BehaviorType_MetaData) }; // 269887658
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_Personality_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_Personality = { "Personality", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, Personality), Z_Construct_UEnum_SLT_EAIPersonality, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Personality_MetaData), NewProp_Personality_MetaData) }; // 1411235553
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_DifficultyLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_DifficultyLevel = { "DifficultyLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, DifficultyLevel), Z_Construct_UEnum_SLT_EAIDifficultyLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DifficultyLevel_MetaData), NewProp_DifficultyLevel_MetaData) }; // 3987173687
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorState = { "BehaviorState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, BehaviorState), Z_Construct_UScriptStruct_FAIBehaviorState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BehaviorState_MetaData), NewProp_BehaviorState_MetaData) }; // 3348408789
void Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAutoConfigureFromTemplate_SetBit(void* Obj)
{
	((UAIBehaviorDesigner*)Obj)->bAutoConfigureFromTemplate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAutoConfigureFromTemplate = { "bAutoConfigureFromTemplate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAIBehaviorDesigner), &Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAutoConfigureFromTemplate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoConfigureFromTemplate_MetaData), NewProp_bAutoConfigureFromTemplate_MetaData) };
void Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAdaptToDifficulty_SetBit(void* Obj)
{
	((UAIBehaviorDesigner*)Obj)->bAdaptToDifficulty = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAdaptToDifficulty = { "bAdaptToDifficulty", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAIBehaviorDesigner), &Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAdaptToDifficulty_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAdaptToDifficulty_MetaData), NewProp_bAdaptToDifficulty_MetaData) };
void Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bLearnFromPlayer_SetBit(void* Obj)
{
	((UAIBehaviorDesigner*)Obj)->bLearnFromPlayer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bLearnFromPlayer = { "bLearnFromPlayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAIBehaviorDesigner), &Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bLearnFromPlayer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bLearnFromPlayer_MetaData), NewProp_bLearnFromPlayer_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_AdaptationRate = { "AdaptationRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, AdaptationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdaptationRate_MetaData), NewProp_AdaptationRate_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnBehaviorChanged = { "OnBehaviorChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, OnBehaviorChanged), Z_Construct_UDelegateFunction_SLT_OnBehaviorChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBehaviorChanged_MetaData), NewProp_OnBehaviorChanged_MetaData) }; // 3035169296
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnPersonalityChanged = { "OnPersonalityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, OnPersonalityChanged), Z_Construct_UDelegateFunction_SLT_OnPersonalityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPersonalityChanged_MetaData), NewProp_OnPersonalityChanged_MetaData) }; // 800829004
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnDifficultyChanged = { "OnDifficultyChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, OnDifficultyChanged), Z_Construct_UDelegateFunction_SLT_OnDifficultyChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDifficultyChanged_MetaData), NewProp_OnDifficultyChanged_MetaData) }; // 241603396
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnBehaviorTemplateApplied = { "OnBehaviorTemplateApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, OnBehaviorTemplateApplied), Z_Construct_UDelegateFunction_SLT_OnBehaviorTemplateApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnBehaviorTemplateApplied_MetaData), NewProp_OnBehaviorTemplateApplied_MetaData) }; // 639465764
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedTemplateDatabase = { "CachedTemplateDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, CachedTemplateDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedTemplateDatabase_MetaData), NewProp_CachedTemplateDatabase_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedAIController = { "CachedAIController", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, CachedAIController), Z_Construct_UClass_AAIController_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedAIController_MetaData), NewProp_CachedAIController_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedBehaviorTreeComponent = { "CachedBehaviorTreeComponent", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, CachedBehaviorTreeComponent), Z_Construct_UClass_UBehaviorTreeComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedBehaviorTreeComponent_MetaData), NewProp_CachedBehaviorTreeComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedBlackboardComponent = { "CachedBlackboardComponent", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, CachedBlackboardComponent), Z_Construct_UClass_UBlackboardComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedBlackboardComponent_MetaData), NewProp_CachedBlackboardComponent_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers_ValueProp = { "ParameterModifiers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers_Key_KeyProp = { "ParameterModifiers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers = { "ParameterModifiers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, ParameterModifiers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterModifiers_MetaData), NewProp_ParameterModifiers_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers_ValueProp = { "ModifierTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers_Key_KeyProp = { "ModifierTimers_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers = { "ModifierTimers", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAIBehaviorDesigner, ModifierTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ModifierTimers_MetaData), NewProp_ModifierTimers_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAIBehaviorDesigner_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorTemplateDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CurrentTemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_Personality_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_Personality,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_DifficultyLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_DifficultyLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_BehaviorState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAutoConfigureFromTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bAdaptToDifficulty,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_bLearnFromPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_AdaptationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnBehaviorChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnPersonalityChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnDifficultyChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_OnBehaviorTemplateApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedTemplateDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedAIController,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedBehaviorTreeComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_CachedBlackboardComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ParameterModifiers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAIBehaviorDesigner_Statics::NewProp_ModifierTimers,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAIBehaviorDesigner_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAIBehaviorDesigner_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAIBehaviorDesigner_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAIBehaviorDesigner_Statics::ClassParams = {
	&UAIBehaviorDesigner::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAIBehaviorDesigner_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAIBehaviorDesigner_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAIBehaviorDesigner_Statics::Class_MetaDataParams), Z_Construct_UClass_UAIBehaviorDesigner_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAIBehaviorDesigner()
{
	if (!Z_Registration_Info_UClass_UAIBehaviorDesigner.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAIBehaviorDesigner.OuterSingleton, Z_Construct_UClass_UAIBehaviorDesigner_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAIBehaviorDesigner.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UAIBehaviorDesigner>()
{
	return UAIBehaviorDesigner::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAIBehaviorDesigner);
UAIBehaviorDesigner::~UAIBehaviorDesigner() {}
// End Class UAIBehaviorDesigner

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EAIBehaviorType_StaticEnum, TEXT("EAIBehaviorType"), &Z_Registration_Info_UEnum_EAIBehaviorType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 269887658U) },
		{ EAIPersonality_StaticEnum, TEXT("EAIPersonality"), &Z_Registration_Info_UEnum_EAIPersonality, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1411235553U) },
		{ EAIDifficultyLevel_StaticEnum, TEXT("EAIDifficultyLevel"), &Z_Registration_Info_UEnum_EAIDifficultyLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3987173687U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FAIBehaviorTemplate::StaticStruct, Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics::NewStructOps, TEXT("AIBehaviorTemplate"), &Z_Registration_Info_UScriptStruct_AIBehaviorTemplate, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAIBehaviorTemplate), 319305316U) },
		{ FAIBehaviorState::StaticStruct, Z_Construct_UScriptStruct_FAIBehaviorState_Statics::NewStructOps, TEXT("AIBehaviorState"), &Z_Registration_Info_UScriptStruct_AIBehaviorState, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAIBehaviorState), 3348408789U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAIBehaviorDesigner, UAIBehaviorDesigner::StaticClass, TEXT("UAIBehaviorDesigner"), &Z_Registration_Info_UClass_UAIBehaviorDesigner, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAIBehaviorDesigner), 1071542428U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_3491884669(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
