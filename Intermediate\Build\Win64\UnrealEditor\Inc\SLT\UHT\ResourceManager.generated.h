// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/ResourceManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
enum class EResourceScarcityLevel : uint8;
enum class EResourceType : uint8;
struct FGameplayTag;
struct FInventoryItemData;
struct FResourceData;
#ifdef SLT_ResourceManager_generated_h
#error "ResourceManager.generated.h already included, missing '#pragma once' in ResourceManager.h"
#endif
#define SLT_ResourceManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FResourceData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FResourceData>();

#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_103_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FResourceConsumptionRule>();

#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_140_DELEGATE \
SLT_API void FOnResourceChanged_DelegateWrapper(const FMulticastScriptDelegate& OnResourceChanged, FName ResourceID, float NewAmount, float MaxAmount);


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_141_DELEGATE \
SLT_API void FOnResourceDepleted_DelegateWrapper(const FMulticastScriptDelegate& OnResourceDepleted, FName ResourceID, EResourceType ResourceType);


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_142_DELEGATE \
SLT_API void FOnResourceScarcityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnResourceScarcityChanged, FName ResourceID, EResourceScarcityLevel NewLevel);


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_143_DELEGATE \
SLT_API void FOnResourceConsumed_DelegateWrapper(const FMulticastScriptDelegate& OnResourceConsumed, FName ResourceID, float Amount, FGameplayTag ActionTag);


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetItemResourceValue); \
	DECLARE_FUNCTION(execConsumeItem); \
	DECLARE_FUNCTION(execResetAllResources); \
	DECLARE_FUNCTION(execRegenerateAllResources); \
	DECLARE_FUNCTION(execGetResourcesByType); \
	DECLARE_FUNCTION(execGetAllResourceIDs); \
	DECLARE_FUNCTION(execIsResourceCritical); \
	DECLARE_FUNCTION(execGetResourcesByScarcity); \
	DECLARE_FUNCTION(execGetResourceScarcity); \
	DECLARE_FUNCTION(execGetActionCost); \
	DECLARE_FUNCTION(execConsumeForAction); \
	DECLARE_FUNCTION(execCanPerformAction); \
	DECLARE_FUNCTION(execSetResourceMax); \
	DECLARE_FUNCTION(execModifyResource); \
	DECLARE_FUNCTION(execSetResourceAmount); \
	DECLARE_FUNCTION(execGetResourcePercentage); \
	DECLARE_FUNCTION(execGetResourceAmount); \
	DECLARE_FUNCTION(execGetResourceData); \
	DECLARE_FUNCTION(execHasResource); \
	DECLARE_FUNCTION(execRemoveResource); \
	DECLARE_FUNCTION(execAddResource);


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUResourceManager(); \
	friend struct Z_Construct_UClass_UResourceManager_Statics; \
public: \
	DECLARE_CLASS(UResourceManager, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UResourceManager)


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UResourceManager(UResourceManager&&); \
	UResourceManager(const UResourceManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UResourceManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UResourceManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UResourceManager) \
	NO_API virtual ~UResourceManager();


#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_145_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_148_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UResourceManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_ResourceManager_h


#define FOREACH_ENUM_ERESOURCETYPE(op) \
	op(EResourceType::Health) \
	op(EResourceType::Ammo) \
	op(EResourceType::Currency) \
	op(EResourceType::Energy) \
	op(EResourceType::Experience) \
	op(EResourceType::Custom) 

enum class EResourceType : uint8;
template<> struct TIsUEnumClass<EResourceType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EResourceType>();

#define FOREACH_ENUM_ERESOURCESCARCITYLEVEL(op) \
	op(EResourceScarcityLevel::Abundant) \
	op(EResourceScarcityLevel::Normal) \
	op(EResourceScarcityLevel::Scarce) \
	op(EResourceScarcityLevel::Critical) \
	op(EResourceScarcityLevel::Empty) 

enum class EResourceScarcityLevel : uint8;
template<> struct TIsUEnumClass<EResourceScarcityLevel> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EResourceScarcityLevel>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
