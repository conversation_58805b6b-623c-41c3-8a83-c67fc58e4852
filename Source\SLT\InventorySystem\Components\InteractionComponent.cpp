#include "InteractionComponent.h"
#include "Core/Interfaces/Interactable.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/KismetSystemLibrary.h"
#include "EnhancedInputComponent.h"
#include "InputAction.h"
#include "TimerManager.h"

UInteractionComponent::UInteractionComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.1f; // Check for interactables 10 times per second

	InteractionRange = 200.0f;
	InteractionSphereRadius = 50.0f;
	bUseLineTrace = true;
	bRequireLineOfSight = true;

	// Default object types for interaction
	InteractionObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_WorldStatic));
	InteractionObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_WorldDynamic));
	InteractionObjectTypes.Add(UEngineTypes::ConvertToObjectType(ECollisionChannel::ECC_Pawn));
}

void UInteractionComponent::BeginPlay()
{
	Super::BeginPlay();
}

void UInteractionComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (!bIsInteracting)
	{
		UpdateInteractableDetection();
	}
}

void UInteractionComponent::UpdateInteractableDetection()
{
	AActor* BestInteractable = FindBestInteractable();
	
	if (BestInteractable != CurrentInteractable.Get())
	{
		SetCurrentInteractable(BestInteractable);
	}
}

void UInteractionComponent::SetCurrentInteractable(AActor* NewInteractable)
{
	AActor* PreviousInteractable = CurrentInteractable.Get();
	
	if (PreviousInteractable == NewInteractable)
	{
		return;
	}

	// Clear previous interactable
	if (PreviousInteractable)
	{
		CurrentInteractable.Reset();
		CurrentInteractionData = FInteractionData();
		OnInteractableLost.Broadcast();
	}

	// Set new interactable
	if (NewInteractable && CanInteractWith(NewInteractable))
	{
		CurrentInteractable = NewInteractable;
		
		// Get interaction data
		if (NewInteractable->Implements<UInteractable>())
		{
			CurrentInteractionData = IInteractable::Execute_GetInteractionData(NewInteractable);
			OnInteractableFound.Broadcast(NewInteractable, CurrentInteractionData);
		}
	}
}

TArray<AActor*> UInteractionComponent::FindInteractablesInRange() const
{
	TArray<AActor*> FoundActors;
	
	if (!GetOwner())
	{
		return FoundActors;
	}

	FVector StartLocation = GetInteractionStartLocation();
	
	// Sphere overlap to find potential interactables
	TArray<FOverlapResult> OverlapResults;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(GetOwner());

	bool bHit = GetWorld()->OverlapMultiByObjectType(
		OverlapResults,
		StartLocation,
		FQuat::Identity,
		FCollisionObjectQueryParams(InteractionObjectTypes),
		FCollisionShape::MakeSphere(InteractionRange),
		QueryParams
	);

	if (bHit)
	{
		for (const FOverlapResult& Result : OverlapResults)
		{
			AActor* Actor = Result.GetActor();
			if (Actor && Actor->Implements<UInteractable>())
			{
				// Check distance
				float Distance = GetDistanceToActor(Actor);
				if (Distance <= InteractionRange)
				{
					// Check line of sight if required
					if (!bRequireLineOfSight || HasLineOfSight(Actor))
					{
						FoundActors.Add(Actor);
					}
				}
			}
		}
	}

	return FoundActors;
}

AActor* UInteractionComponent::FindBestInteractable() const
{
	TArray<AActor*> Interactables = FindInteractablesInRange();
	
	if (Interactables.Num() == 0)
	{
		return nullptr;
	}

	// Find the closest interactable
	AActor* BestInteractable = nullptr;
	float BestDistance = FLT_MAX;

	for (AActor* Actor : Interactables)
	{
		float Distance = GetDistanceToActor(Actor);
		if (Distance < BestDistance)
		{
			BestDistance = Distance;
			BestInteractable = Actor;
		}
	}

	return BestInteractable;
}

bool UInteractionComponent::CanInteractWith(AActor* Actor) const
{
	if (!Actor || !Actor->Implements<UInteractable>())
	{
		return false;
	}

	APawn* OwnerPawn = Cast<APawn>(GetOwner());
	if (!OwnerPawn)
	{
		return false;
	}

	// Check if the actor allows interaction
	bool bCanInteract = IInteractable::Execute_CanInteract(Actor, OwnerPawn);
	if (!bCanInteract)
	{
		return false;
	}

	// Check interaction requirements
	bool bRequirementsMet = IInteractable::Execute_CheckInteractionRequirements(Actor, OwnerPawn);
	return bRequirementsMet;
}

void UInteractionComponent::StartInteraction()
{
	if (bIsInteracting || !CurrentInteractable.IsValid())
	{
		return;
	}

	APawn* OwnerPawn = Cast<APawn>(GetOwner());
	if (!OwnerPawn)
	{
		return;
	}

	AActor* InteractableActor = CurrentInteractable.Get();
	
	// Double-check we can still interact
	if (!CanInteractWith(InteractableActor))
	{
		return;
	}

	bIsInteracting = true;
	InteractionStartTime = GetWorld()->GetTimeSeconds();

	// Start interaction on the interactable
	IInteractable::Execute_OnInteractionStart(InteractableActor, OwnerPawn);
	OnInteractionStarted.Broadcast(InteractableActor, OwnerPawn);

	// Handle interaction duration
	if (CurrentInteractionData.InteractionDuration > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			InteractionTimerHandle,
			this,
			&UInteractionComponent::OnInteractionTimerComplete,
			CurrentInteractionData.InteractionDuration,
			false
		);
	}
	else
	{
		// Instant interaction
		CompleteInteraction();
	}
}

void UInteractionComponent::CancelInteraction()
{
	if (!bIsInteracting)
	{
		return;
	}

	APawn* OwnerPawn = Cast<APawn>(GetOwner());
	AActor* InteractableActor = CurrentInteractable.Get();

	// Clear timer
	if (InteractionTimerHandle.IsValid())
	{
		GetWorld()->GetTimerManager().ClearTimer(InteractionTimerHandle);
		InteractionTimerHandle.Invalidate();
	}

	bIsInteracting = false;

	// Notify interactable
	if (InteractableActor)
	{
		IInteractable::Execute_OnInteractionCancel(InteractableActor, OwnerPawn);
	}

	OnInteractionCancelled.Broadcast(InteractableActor, OwnerPawn);
}

void UInteractionComponent::CompleteInteraction()
{
	if (!bIsInteracting)
	{
		return;
	}

	APawn* OwnerPawn = Cast<APawn>(GetOwner());
	AActor* InteractableActor = CurrentInteractable.Get();

	bIsInteracting = false;

	// Clear timer
	if (InteractionTimerHandle.IsValid())
	{
		GetWorld()->GetTimerManager().ClearTimer(InteractionTimerHandle);
		InteractionTimerHandle.Invalidate();
	}

	// Complete interaction on the interactable
	if (InteractableActor)
	{
		IInteractable::Execute_OnInteractionComplete(InteractableActor, OwnerPawn);
	}

	OnInteractionCompleted.Broadcast(InteractableActor, OwnerPawn);

	// Check if this was a one-time interaction
	if (!CurrentInteractionData.bCanInteractMultipleTimes)
	{
		SetCurrentInteractable(nullptr);
	}
}

void UInteractionComponent::OnInteractionTimerComplete()
{
	CompleteInteraction();
}

bool UInteractionComponent::HasLineOfSight(AActor* Actor) const
{
	if (!Actor || !bRequireLineOfSight)
	{
		return true;
	}

	FVector StartLocation = GetInteractionStartLocation();
	FVector EndLocation = IInteractable::Execute_GetInteractionLocation(Actor);

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(GetOwner());
	QueryParams.AddIgnoredActor(Actor);

	bool bHit = GetWorld()->LineTraceSingleByChannel(
		HitResult,
		StartLocation,
		EndLocation,
		ECollisionChannel::ECC_Visibility,
		QueryParams
	);

	return !bHit; // No hit means clear line of sight
}

float UInteractionComponent::GetDistanceToActor(AActor* Actor) const
{
	if (!Actor)
	{
		return FLT_MAX;
	}

	FVector StartLocation = GetInteractionStartLocation();
	FVector ActorLocation = IInteractable::Execute_GetInteractionLocation(Actor);
	
	return FVector::Dist(StartLocation, ActorLocation);
}

FVector UInteractionComponent::GetInteractionStartLocation() const
{
	if (APawn* OwnerPawn = Cast<APawn>(GetOwner()))
	{
		// Use pawn's eye location if available
		FVector EyeLocation;
		FRotator EyeRotation;
		OwnerPawn->GetActorEyesViewPoint(EyeLocation, EyeRotation);
		return EyeLocation;
	}

	return GetOwner() ? GetOwner()->GetActorLocation() : FVector::ZeroVector;
}

void UInteractionComponent::SetupInputComponent(UEnhancedInputComponent* EnhancedInputComponent)
{
	if (!EnhancedInputComponent || !InteractAction.IsValid())
	{
		return;
	}

	UInputAction* LoadedInteractAction = InteractAction.LoadSynchronous();
	if (LoadedInteractAction)
	{
		EnhancedInputComponent->BindAction(LoadedInteractAction, ETriggerEvent::Started, this, &UInteractionComponent::OnInteractPressed);
		EnhancedInputComponent->BindAction(LoadedInteractAction, ETriggerEvent::Completed, this, &UInteractionComponent::OnInteractReleased);
	}
}

void UInteractionComponent::OnInteractPressed()
{
	StartInteraction();
}

void UInteractionComponent::OnInteractReleased()
{
	// For now, we don't cancel on release
	// This could be used for hold-to-interact mechanics
}
