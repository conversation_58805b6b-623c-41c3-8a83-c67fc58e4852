// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/LevelProgressionManager.h"
#include "Runtime/Engine/Classes/Engine/GameInstance.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeLevelProgressionManager() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTableRowBase();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_ULevelProgressionManager();
SLT_API UClass* Z_Construct_UClass_ULevelProgressionManager_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_ECheckpointType();
SLT_API UEnum* Z_Construct_UEnum_SLT_ELevelState();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FCheckpointData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FLevelData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FProgressionSaveData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum ECheckpointType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ECheckpointType;
static UEnum* ECheckpointType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ECheckpointType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ECheckpointType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ECheckpointType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ECheckpointType"));
	}
	return Z_Registration_Info_UEnum_ECheckpointType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ECheckpointType>()
{
	return ECheckpointType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ECheckpointType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AreaCleared.DisplayName", "Area Cleared" },
		{ "AreaCleared.Name", "ECheckpointType::AreaCleared" },
		{ "Automatic.DisplayName", "Automatic" },
		{ "Automatic.Name", "ECheckpointType::Automatic" },
		{ "BlueprintType", "true" },
		{ "BossDefeated.DisplayName", "Boss Defeated" },
		{ "BossDefeated.Name", "ECheckpointType::BossDefeated" },
		{ "LevelTransition.DisplayName", "Level Transition" },
		{ "LevelTransition.Name", "ECheckpointType::LevelTransition" },
		{ "Manual.DisplayName", "Manual" },
		{ "Manual.Name", "ECheckpointType::Manual" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
		{ "PuzzleSolved.DisplayName", "Puzzle Solved" },
		{ "PuzzleSolved.Name", "ECheckpointType::PuzzleSolved" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ECheckpointType::Manual", (int64)ECheckpointType::Manual },
		{ "ECheckpointType::Automatic", (int64)ECheckpointType::Automatic },
		{ "ECheckpointType::LevelTransition", (int64)ECheckpointType::LevelTransition },
		{ "ECheckpointType::BossDefeated", (int64)ECheckpointType::BossDefeated },
		{ "ECheckpointType::PuzzleSolved", (int64)ECheckpointType::PuzzleSolved },
		{ "ECheckpointType::AreaCleared", (int64)ECheckpointType::AreaCleared },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ECheckpointType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ECheckpointType",
	"ECheckpointType",
	Z_Construct_UEnum_SLT_ECheckpointType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ECheckpointType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ECheckpointType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ECheckpointType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ECheckpointType()
{
	if (!Z_Registration_Info_UEnum_ECheckpointType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ECheckpointType.InnerSingleton, Z_Construct_UEnum_SLT_ECheckpointType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ECheckpointType.InnerSingleton;
}
// End Enum ECheckpointType

// Begin Enum ELevelState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELevelState;
static UEnum* ELevelState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELevelState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELevelState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ELevelState, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ELevelState"));
	}
	return Z_Registration_Info_UEnum_ELevelState.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ELevelState>()
{
	return ELevelState_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ELevelState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Available.DisplayName", "Available" },
		{ "Available.Name", "ELevelState::Available" },
		{ "BlueprintType", "true" },
		{ "Completed.DisplayName", "Completed" },
		{ "Completed.Name", "ELevelState::Completed" },
		{ "InProgress.DisplayName", "In Progress" },
		{ "InProgress.Name", "ELevelState::InProgress" },
		{ "Locked.DisplayName", "Locked" },
		{ "Locked.Name", "ELevelState::Locked" },
		{ "Mastered.DisplayName", "Mastered" },
		{ "Mastered.Name", "ELevelState::Mastered" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELevelState::Locked", (int64)ELevelState::Locked },
		{ "ELevelState::Available", (int64)ELevelState::Available },
		{ "ELevelState::InProgress", (int64)ELevelState::InProgress },
		{ "ELevelState::Completed", (int64)ELevelState::Completed },
		{ "ELevelState::Mastered", (int64)ELevelState::Mastered },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ELevelState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ELevelState",
	"ELevelState",
	Z_Construct_UEnum_SLT_ELevelState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ELevelState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ELevelState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ELevelState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ELevelState()
{
	if (!Z_Registration_Info_UEnum_ELevelState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELevelState.InnerSingleton, Z_Construct_UEnum_SLT_ELevelState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELevelState.InnerSingleton;
}
// End Enum ELevelState

// Begin ScriptStruct FCheckpointData
static_assert(std::is_polymorphic<FCheckpointData>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FCheckpointData cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_CheckpointData;
class UScriptStruct* FCheckpointData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_CheckpointData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_CheckpointData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FCheckpointData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("CheckpointData"));
	}
	return Z_Registration_Info_UScriptStruct_CheckpointData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FCheckpointData>()
{
	return FCheckpointData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FCheckpointData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckpointID_MetaData[] = {
		{ "Category", "Checkpoint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckpointName_MetaData[] = {
		{ "Category", "Checkpoint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Display name\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Display name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckpointType_MetaData[] = {
		{ "Category", "Checkpoint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Type of checkpoint\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Type of checkpoint" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "Category", "Checkpoint" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level this checkpoint belongs to\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level this checkpoint belongs to" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLocation_MetaData[] = {
		{ "Category", "Transform" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Player spawn location\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Player spawn location" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerRotation_MetaData[] = {
		{ "Category", "Transform" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Checkpoint settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Checkpoint settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSaveInventory_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSaveProgress_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredObjectives_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Requirements to unlock this checkpoint\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requirements to unlock this checkpoint" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredCheckpoints_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockTags_MetaData[] = {
		{ "Category", "Unlocks" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// What this checkpoint unlocks\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "What this checkpoint unlocks" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockCheckpoints_MetaData[] = {
		{ "Category", "Unlocks" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CheckpointID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_CheckpointName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CheckpointType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CheckpointType;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerLocation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerRotation;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static void NewProp_bSaveInventory_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSaveInventory;
	static void NewProp_bSaveProgress_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSaveProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredObjectives;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredCheckpoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredCheckpoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockTags;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockCheckpoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockCheckpoints;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FCheckpointData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointID = { "CheckpointID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, CheckpointID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckpointID_MetaData), NewProp_CheckpointID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointName = { "CheckpointName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, CheckpointName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckpointName_MetaData), NewProp_CheckpointName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointType = { "CheckpointType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, CheckpointType), Z_Construct_UEnum_SLT_ECheckpointType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckpointType_MetaData), NewProp_CheckpointType_MetaData) }; // 3947953594
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_PlayerLocation = { "PlayerLocation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, PlayerLocation), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLocation_MetaData), NewProp_PlayerLocation_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_PlayerRotation = { "PlayerRotation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, PlayerRotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerRotation_MetaData), NewProp_PlayerRotation_MetaData) };
void Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FCheckpointData*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCheckpointData), &Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
void Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveInventory_SetBit(void* Obj)
{
	((FCheckpointData*)Obj)->bSaveInventory = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveInventory = { "bSaveInventory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCheckpointData), &Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveInventory_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSaveInventory_MetaData), NewProp_bSaveInventory_MetaData) };
void Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveProgress_SetBit(void* Obj)
{
	((FCheckpointData*)Obj)->bSaveProgress = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveProgress = { "bSaveProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FCheckpointData), &Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveProgress_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSaveProgress_MetaData), NewProp_bSaveProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredObjectives = { "RequiredObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, RequiredObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredObjectives_MetaData), NewProp_RequiredObjectives_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredCheckpoints_Inner = { "RequiredCheckpoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredCheckpoints = { "RequiredCheckpoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, RequiredCheckpoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredCheckpoints_MetaData), NewProp_RequiredCheckpoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockTags = { "UnlockTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, UnlockTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockTags_MetaData), NewProp_UnlockTags_MetaData) }; // 3352185621
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockCheckpoints_Inner = { "UnlockCheckpoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockCheckpoints = { "UnlockCheckpoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, UnlockCheckpoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockCheckpoints_MetaData), NewProp_UnlockCheckpoints_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FCheckpointData, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FCheckpointData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CheckpointType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_LevelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_PlayerLocation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_PlayerRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveInventory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_bSaveProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredCheckpoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_RequiredCheckpoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockCheckpoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_UnlockCheckpoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FCheckpointData_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCheckpointData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FCheckpointData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"CheckpointData",
	Z_Construct_UScriptStruct_FCheckpointData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCheckpointData_Statics::PropPointers),
	sizeof(FCheckpointData),
	alignof(FCheckpointData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FCheckpointData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FCheckpointData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FCheckpointData()
{
	if (!Z_Registration_Info_UScriptStruct_CheckpointData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_CheckpointData.InnerSingleton, Z_Construct_UScriptStruct_FCheckpointData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_CheckpointData.InnerSingleton;
}
// End ScriptStruct FCheckpointData

// Begin ScriptStruct FLevelData
static_assert(std::is_polymorphic<FLevelData>() == std::is_polymorphic<FTableRowBase>(), "USTRUCT FLevelData cannot be polymorphic unless super FTableRowBase is polymorphic");
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LevelData;
class UScriptStruct* FLevelData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LevelData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LevelData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLevelData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("LevelData"));
	}
	return Z_Registration_Info_UScriptStruct_LevelData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FLevelData>()
{
	return FLevelData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLevelData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelID_MetaData[] = {
		{ "Category", "Level" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Unique identifier\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Unique identifier" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "Category", "Level" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Display name\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Display name" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelPath_MetaData[] = {
		{ "Category", "Level" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level asset path\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level asset path" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_State_MetaData[] = {
		{ "Category", "Level" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredLevel_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level requirements\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RecommendedLevel_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredLevels_MetaData[] = {
		{ "Category", "Requirements" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxObjectives_MetaData[] = {
		{ "Category", "Info" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level info\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level info" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsMainLevel_MetaData[] = {
		{ "Category", "Info" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAllowReplay_MetaData[] = {
		{ "Category", "Info" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingScreenTexture_MetaData[] = {
		{ "Category", "UI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// UI\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelDescription_MetaData[] = {
		{ "Category", "UI" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom data\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom data" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LevelName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelPath;
	static const UECodeGen_Private::FBytePropertyParams NewProp_State_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_State;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RequiredLevel;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RecommendedLevel;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredLevels_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredLevels;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxObjectives;
	static void NewProp_bIsMainLevel_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsMainLevel;
	static void NewProp_bAllowReplay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowReplay;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LoadingScreenTexture;
	static const UECodeGen_Private::FTextPropertyParams NewProp_LevelDescription;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLevelData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, LevelID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelID_MetaData), NewProp_LevelID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelPath = { "LevelPath", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, LevelPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelPath_MetaData), NewProp_LevelPath_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_State_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_State = { "State", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, State), Z_Construct_UEnum_SLT_ELevelState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_State_MetaData), NewProp_State_MetaData) }; // 1083325619
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevel = { "RequiredLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, RequiredLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredLevel_MetaData), NewProp_RequiredLevel_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RecommendedLevel = { "RecommendedLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, RecommendedLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RecommendedLevel_MetaData), NewProp_RecommendedLevel_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevels_Inner = { "RequiredLevels", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevels = { "RequiredLevels", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, RequiredLevels), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredLevels_MetaData), NewProp_RequiredLevels_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_MaxObjectives = { "MaxObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, MaxObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxObjectives_MetaData), NewProp_MaxObjectives_MetaData) };
void Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bIsMainLevel_SetBit(void* Obj)
{
	((FLevelData*)Obj)->bIsMainLevel = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bIsMainLevel = { "bIsMainLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLevelData), &Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bIsMainLevel_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsMainLevel_MetaData), NewProp_bIsMainLevel_MetaData) };
void Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bAllowReplay_SetBit(void* Obj)
{
	((FLevelData*)Obj)->bAllowReplay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bAllowReplay = { "bAllowReplay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLevelData), &Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bAllowReplay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAllowReplay_MetaData), NewProp_bAllowReplay_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LoadingScreenTexture = { "LoadingScreenTexture", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, LoadingScreenTexture), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingScreenTexture_MetaData), NewProp_LoadingScreenTexture_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelDescription = { "LevelDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, LevelDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelDescription_MetaData), NewProp_LevelDescription_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLevelData, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLevelData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_State_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_State,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RecommendedLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevels_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_RequiredLevels,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_MaxObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bIsMainLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_bAllowReplay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LoadingScreenTexture,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_LevelDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLevelData_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLevelData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	Z_Construct_UScriptStruct_FTableRowBase,
	&NewStructOps,
	"LevelData",
	Z_Construct_UScriptStruct_FLevelData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelData_Statics::PropPointers),
	sizeof(FLevelData),
	alignof(FLevelData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLevelData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLevelData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLevelData()
{
	if (!Z_Registration_Info_UScriptStruct_LevelData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LevelData.InnerSingleton, Z_Construct_UScriptStruct_FLevelData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LevelData.InnerSingleton;
}
// End ScriptStruct FLevelData

// Begin ScriptStruct FProgressionSaveData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ProgressionSaveData;
class UScriptStruct* FProgressionSaveData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ProgressionSaveData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ProgressionSaveData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FProgressionSaveData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ProgressionSaveData"));
	}
	return Z_Registration_Info_UScriptStruct_ProgressionSaveData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FProgressionSaveData>()
{
	return FProgressionSaveData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FProgressionSaveData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentLevel_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastCheckpoint_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerLevel_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalPlayTime_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedObjectives_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalObjectives_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelStates_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedCheckpoints_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockedTags_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProgressData_MetaData[] = {
		{ "Category", "Save Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CurrentLevel;
	static const UECodeGen_Private::FNamePropertyParams NewProp_LastCheckpoint;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PlayerLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalPlayTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_CompletedObjectives;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalObjectives;
	static const UECodeGen_Private::FBytePropertyParams NewProp_LevelStates_ValueProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_LevelStates_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelStates_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_LevelStates;
	static const UECodeGen_Private::FNamePropertyParams NewProp_UnlockedCheckpoints_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_UnlockedCheckpoints;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockedTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProgressData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProgressData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProgressData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FProgressionSaveData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CurrentLevel = { "CurrentLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, CurrentLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentLevel_MetaData), NewProp_CurrentLevel_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LastCheckpoint = { "LastCheckpoint", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, LastCheckpoint), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastCheckpoint_MetaData), NewProp_LastCheckpoint_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_PlayerLevel = { "PlayerLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, PlayerLevel), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerLevel_MetaData), NewProp_PlayerLevel_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_TotalPlayTime = { "TotalPlayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, TotalPlayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalPlayTime_MetaData), NewProp_TotalPlayTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CompletedObjectives = { "CompletedObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, CompletedObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedObjectives_MetaData), NewProp_CompletedObjectives_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_TotalObjectives = { "TotalObjectives", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, TotalObjectives), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalObjectives_MetaData), NewProp_TotalObjectives_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_ValueProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_ValueProp = { "LevelStates", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UEnum_SLT_ELevelState, METADATA_PARAMS(0, nullptr) }; // 1083325619
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_Key_KeyProp = { "LevelStates_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates = { "LevelStates", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, LevelStates), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelStates_MetaData), NewProp_LevelStates_MetaData) }; // 1083325619
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedCheckpoints_Inner = { "UnlockedCheckpoints", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedCheckpoints = { "UnlockedCheckpoints", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, UnlockedCheckpoints), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedCheckpoints_MetaData), NewProp_UnlockedCheckpoints_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedTags = { "UnlockedTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, UnlockedTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockedTags_MetaData), NewProp_UnlockedTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData_ValueProp = { "CustomProgressData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData_Key_KeyProp = { "CustomProgressData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData = { "CustomProgressData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FProgressionSaveData, CustomProgressData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProgressData_MetaData), NewProp_CustomProgressData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FProgressionSaveData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CurrentLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LastCheckpoint,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_PlayerLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_TotalPlayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CompletedObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_TotalObjectives,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_ValueProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_LevelStates,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedCheckpoints_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedCheckpoints,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_UnlockedTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewProp_CustomProgressData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProgressionSaveData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FProgressionSaveData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ProgressionSaveData",
	Z_Construct_UScriptStruct_FProgressionSaveData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProgressionSaveData_Statics::PropPointers),
	sizeof(FProgressionSaveData),
	alignof(FProgressionSaveData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FProgressionSaveData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FProgressionSaveData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FProgressionSaveData()
{
	if (!Z_Registration_Info_UScriptStruct_ProgressionSaveData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ProgressionSaveData.InnerSingleton, Z_Construct_UScriptStruct_FProgressionSaveData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ProgressionSaveData.InnerSingleton;
}
// End ScriptStruct FProgressionSaveData

// Begin Delegate FOnCheckpointReached
struct Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnCheckpointReached_Parms
	{
		FName CheckpointID;
		FCheckpointData CheckpointData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckpointData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CheckpointID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CheckpointData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::NewProp_CheckpointID = { "CheckpointID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnCheckpointReached_Parms, CheckpointID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::NewProp_CheckpointData = { "CheckpointData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnCheckpointReached_Parms, CheckpointData), Z_Construct_UScriptStruct_FCheckpointData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckpointData_MetaData), NewProp_CheckpointData_MetaData) }; // 1489733810
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::NewProp_CheckpointID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::NewProp_CheckpointData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnCheckpointReached__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::_Script_SLT_eventOnCheckpointReached_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::_Script_SLT_eventOnCheckpointReached_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnCheckpointReached_DelegateWrapper(const FMulticastScriptDelegate& OnCheckpointReached, FName CheckpointID, FCheckpointData const& CheckpointData)
{
	struct _Script_SLT_eventOnCheckpointReached_Parms
	{
		FName CheckpointID;
		FCheckpointData CheckpointData;
	};
	_Script_SLT_eventOnCheckpointReached_Parms Parms;
	Parms.CheckpointID=CheckpointID;
	Parms.CheckpointData=CheckpointData;
	OnCheckpointReached.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnCheckpointReached

// Begin Delegate FOnLevelStateChanged
struct Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLevelStateChanged_Parms
	{
		FName LevelID;
		ELevelState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLevelStateChanged_Parms, LevelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLevelStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_ELevelState, METADATA_PARAMS(0, nullptr) }; // 1083325619
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLevelStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnLevelStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnLevelStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLevelStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLevelStateChanged, FName LevelID, ELevelState NewState)
{
	struct _Script_SLT_eventOnLevelStateChanged_Parms
	{
		FName LevelID;
		ELevelState NewState;
	};
	_Script_SLT_eventOnLevelStateChanged_Parms Parms;
	Parms.LevelID=LevelID;
	Parms.NewState=NewState;
	OnLevelStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLevelStateChanged

// Begin Delegate FOnLevelTransition
struct Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLevelTransition_Parms
	{
		FName FromLevel;
		FName ToLevel;
		bool bIsLoading;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_FromLevel;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ToLevel;
	static void NewProp_bIsLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoading;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_FromLevel = { "FromLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLevelTransition_Parms, FromLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_ToLevel = { "ToLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLevelTransition_Parms, ToLevel), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_bIsLoading_SetBit(void* Obj)
{
	((_Script_SLT_eventOnLevelTransition_Parms*)Obj)->bIsLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_bIsLoading = { "bIsLoading", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnLevelTransition_Parms), &Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_bIsLoading_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_FromLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_ToLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::NewProp_bIsLoading,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLevelTransition__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::_Script_SLT_eventOnLevelTransition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::_Script_SLT_eventOnLevelTransition_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLevelTransition_DelegateWrapper(const FMulticastScriptDelegate& OnLevelTransition, FName FromLevel, FName ToLevel, bool bIsLoading)
{
	struct _Script_SLT_eventOnLevelTransition_Parms
	{
		FName FromLevel;
		FName ToLevel;
		bool bIsLoading;
	};
	_Script_SLT_eventOnLevelTransition_Parms Parms;
	Parms.FromLevel=FromLevel;
	Parms.ToLevel=ToLevel;
	Parms.bIsLoading=bIsLoading ? true : false;
	OnLevelTransition.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLevelTransition

// Begin Delegate FOnObjectiveCompleted
struct Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnObjectiveCompleted_Parms
	{
		int32 ObjectiveIndex;
		int32 TotalCompleted;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalCompleted;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnObjectiveCompleted_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::NewProp_TotalCompleted = { "TotalCompleted", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnObjectiveCompleted_Parms, TotalCompleted), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::NewProp_ObjectiveIndex,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::NewProp_TotalCompleted,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnObjectiveCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::_Script_SLT_eventOnObjectiveCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::_Script_SLT_eventOnObjectiveCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnObjectiveCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnObjectiveCompleted, int32 ObjectiveIndex, int32 TotalCompleted)
{
	struct _Script_SLT_eventOnObjectiveCompleted_Parms
	{
		int32 ObjectiveIndex;
		int32 TotalCompleted;
	};
	_Script_SLT_eventOnObjectiveCompleted_Parms Parms;
	Parms.ObjectiveIndex=ObjectiveIndex;
	Parms.TotalCompleted=TotalCompleted;
	OnObjectiveCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnObjectiveCompleted

// Begin Class ULevelProgressionManager Function ActivateCheckpoint
struct Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics
{
	struct LevelProgressionManager_eventActivateCheckpoint_Parms
	{
		FName CheckpointID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Checkpoints" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Checkpoint functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Checkpoint functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CheckpointID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_CheckpointID = { "CheckpointID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventActivateCheckpoint_Parms, CheckpointID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventActivateCheckpoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventActivateCheckpoint_Parms), &Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_CheckpointID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "ActivateCheckpoint", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::LevelProgressionManager_eventActivateCheckpoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::LevelProgressionManager_eventActivateCheckpoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execActivateCheckpoint)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_CheckpointID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ActivateCheckpoint(Z_Param_CheckpointID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function ActivateCheckpoint

// Begin Class ULevelProgressionManager Function CompleteObjective
struct Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics
{
	struct LevelProgressionManager_eventCompleteObjective_Parms
	{
		int32 ObjectiveIndex;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objectives" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Objective functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Objective functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ObjectiveIndex;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::NewProp_ObjectiveIndex = { "ObjectiveIndex", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventCompleteObjective_Parms, ObjectiveIndex), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::NewProp_ObjectiveIndex,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "CompleteObjective", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::LevelProgressionManager_eventCompleteObjective_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::LevelProgressionManager_eventCompleteObjective_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execCompleteObjective)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_ObjectiveIndex);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteObjective(Z_Param_ObjectiveIndex);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function CompleteObjective

// Begin Class ULevelProgressionManager Function GetAvailableCheckpoints
struct Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics
{
	struct LevelProgressionManager_eventGetAvailableCheckpoints_Parms
	{
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Checkpoints" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetAvailableCheckpoints_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetAvailableCheckpoints", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::LevelProgressionManager_eventGetAvailableCheckpoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::LevelProgressionManager_eventGetAvailableCheckpoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetAvailableCheckpoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetAvailableCheckpoints();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetAvailableCheckpoints

// Begin Class ULevelProgressionManager Function GetCheckpointData
struct Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics
{
	struct LevelProgressionManager_eventGetCheckpointData_Parms
	{
		FName CheckpointID;
		FCheckpointData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Checkpoints" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CheckpointID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::NewProp_CheckpointID = { "CheckpointID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetCheckpointData_Parms, CheckpointID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetCheckpointData_Parms, ReturnValue), Z_Construct_UScriptStruct_FCheckpointData, METADATA_PARAMS(0, nullptr) }; // 1489733810
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::NewProp_CheckpointID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetCheckpointData", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::LevelProgressionManager_eventGetCheckpointData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::LevelProgressionManager_eventGetCheckpointData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetCheckpointData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_CheckpointID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FCheckpointData*)Z_Param__Result=P_THIS->GetCheckpointData(Z_Param_CheckpointID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetCheckpointData

// Begin Class ULevelProgressionManager Function GetLevelData
struct Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics
{
	struct LevelProgressionManager_eventGetLevelData_Parms
	{
		FName LevelID;
		FLevelData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Levels" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetLevelData_Parms, LevelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetLevelData_Parms, ReturnValue), Z_Construct_UScriptStruct_FLevelData, METADATA_PARAMS(0, nullptr) }; // 3953523680
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetLevelData", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::LevelProgressionManager_eventGetLevelData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::LevelProgressionManager_eventGetLevelData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetLevelData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetLevelData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetLevelData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_LevelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLevelData*)Z_Param__Result=P_THIS->GetLevelData(Z_Param_LevelID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetLevelData

// Begin Class ULevelProgressionManager Function GetLevelState
struct Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics
{
	struct LevelProgressionManager_eventGetLevelState_Parms
	{
		FName LevelID;
		ELevelState ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Levels" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetLevelState_Parms, LevelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetLevelState_Parms, ReturnValue), Z_Construct_UEnum_SLT_ELevelState, METADATA_PARAMS(0, nullptr) }; // 1083325619
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetLevelState", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::LevelProgressionManager_eventGetLevelState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::LevelProgressionManager_eventGetLevelState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetLevelState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetLevelState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetLevelState)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_LevelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(ELevelState*)Z_Param__Result=P_THIS->GetLevelState(Z_Param_LevelID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetLevelState

// Begin Class ULevelProgressionManager Function GetObjectiveProgress
struct Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics
{
	struct LevelProgressionManager_eventGetObjectiveProgress_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objectives" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetObjectiveProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetObjectiveProgress", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::LevelProgressionManager_eventGetObjectiveProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::LevelProgressionManager_eventGetObjectiveProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetObjectiveProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetObjectiveProgress();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetObjectiveProgress

// Begin Class ULevelProgressionManager Function GetPlayerLevel
struct Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics
{
	struct LevelProgressionManager_eventGetPlayerLevel_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetPlayerLevel_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetPlayerLevel", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::LevelProgressionManager_eventGetPlayerLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::LevelProgressionManager_eventGetPlayerLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetPlayerLevel)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayerLevel();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetPlayerLevel

// Begin Class ULevelProgressionManager Function GetUnlockedLevels
struct Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics
{
	struct LevelProgressionManager_eventGetUnlockedLevels_Parms
	{
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Levels" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventGetUnlockedLevels_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "GetUnlockedLevels", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::LevelProgressionManager_eventGetUnlockedLevels_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::LevelProgressionManager_eventGetUnlockedLevels_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execGetUnlockedLevels)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetUnlockedLevels();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function GetUnlockedLevels

// Begin Class ULevelProgressionManager Function HasUnlockedTag
struct Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics
{
	struct LevelProgressionManager_eventHasUnlockedTag_Parms
	{
		FGameplayTag Tag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventHasUnlockedTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
void Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventHasUnlockedTag_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventHasUnlockedTag_Parms), &Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_Tag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "HasUnlockedTag", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::LevelProgressionManager_eventHasUnlockedTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::LevelProgressionManager_eventHasUnlockedTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execHasUnlockedTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasUnlockedTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function HasUnlockedTag

// Begin Class ULevelProgressionManager Function LoadCheckpoint
struct Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics
{
	struct LevelProgressionManager_eventLoadCheckpoint_Parms
	{
		FName CheckpointID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Checkpoints" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_CheckpointID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_CheckpointID = { "CheckpointID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventLoadCheckpoint_Parms, CheckpointID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventLoadCheckpoint_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventLoadCheckpoint_Parms), &Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_CheckpointID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "LoadCheckpoint", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::LevelProgressionManager_eventLoadCheckpoint_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::LevelProgressionManager_eventLoadCheckpoint_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execLoadCheckpoint)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_CheckpointID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadCheckpoint(Z_Param_CheckpointID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function LoadCheckpoint

// Begin Class ULevelProgressionManager Function LoadLevel
struct Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics
{
	struct LevelProgressionManager_eventLoadLevel_Parms
	{
		FName LevelID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Levels" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventLoadLevel_Parms, LevelID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventLoadLevel_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventLoadLevel_Parms), &Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "LoadLevel", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::LevelProgressionManager_eventLoadLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::LevelProgressionManager_eventLoadLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_LoadLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_LoadLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execLoadLevel)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_LevelID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadLevel(Z_Param_LevelID);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function LoadLevel

// Begin Class ULevelProgressionManager Function LoadProgression
struct Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics
{
	struct LevelProgressionManager_eventLoadProgression_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventLoadProgression_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventLoadProgression_Parms), &Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "LoadProgression", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::LevelProgressionManager_eventLoadProgression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::LevelProgressionManager_eventLoadProgression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_LoadProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_LoadProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execLoadProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadProgression();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function LoadProgression

// Begin Class ULevelProgressionManager Function ResetProgression
struct Z_Construct_UFunction_ULevelProgressionManager_ResetProgression_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_ResetProgression_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "ResetProgression", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_ResetProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_ResetProgression_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULevelProgressionManager_ResetProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_ResetProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execResetProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetProgression();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function ResetProgression

// Begin Class ULevelProgressionManager Function SaveProgression
struct Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics
{
	struct LevelProgressionManager_eventSaveProgression_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Save/Load\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save/Load" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LevelProgressionManager_eventSaveProgression_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LevelProgressionManager_eventSaveProgression_Parms), &Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "SaveProgression", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::LevelProgressionManager_eventSaveProgression_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::LevelProgressionManager_eventSaveProgression_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_SaveProgression()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_SaveProgression_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execSaveProgression)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveProgression();
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function SaveProgression

// Begin Class ULevelProgressionManager Function SetLevelState
struct Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics
{
	struct LevelProgressionManager_eventSetLevelState_Parms
	{
		FName LevelID;
		ELevelState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Levels" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_LevelID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_LevelID = { "LevelID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventSetLevelState_Parms, LevelID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventSetLevelState_Parms, NewState), Z_Construct_UEnum_SLT_ELevelState, METADATA_PARAMS(0, nullptr) }; // 1083325619
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_LevelID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "SetLevelState", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::LevelProgressionManager_eventSetLevelState_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::LevelProgressionManager_eventSetLevelState_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_SetLevelState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_SetLevelState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execSetLevelState)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_LevelID);
	P_GET_ENUM(ELevelState,Z_Param_NewState);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetLevelState(Z_Param_LevelID,ELevelState(Z_Param_NewState));
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function SetLevelState

// Begin Class ULevelProgressionManager Function SetPlayerLevel
struct Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics
{
	struct LevelProgressionManager_eventSetPlayerLevel_Parms
	{
		int32 NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventSetPlayerLevel_Parms, NewLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "SetPlayerLevel", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::LevelProgressionManager_eventSetPlayerLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::LevelProgressionManager_eventSetPlayerLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execSetPlayerLevel)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_NewLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPlayerLevel(Z_Param_NewLevel);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function SetPlayerLevel

// Begin Class ULevelProgressionManager Function SetTotalObjectives
struct Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics
{
	struct LevelProgressionManager_eventSetTotalObjectives_Parms
	{
		int32 Total;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Objectives" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_Total;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::NewProp_Total = { "Total", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventSetTotalObjectives_Parms, Total), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::NewProp_Total,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "SetTotalObjectives", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::LevelProgressionManager_eventSetTotalObjectives_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::LevelProgressionManager_eventSetTotalObjectives_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execSetTotalObjectives)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_Total);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetTotalObjectives(Z_Param_Total);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function SetTotalObjectives

// Begin Class ULevelProgressionManager Function UnlockTag
struct Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics
{
	struct LevelProgressionManager_eventUnlockTag_Parms
	{
		FGameplayTag Tag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progression" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Progression functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progression functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Tag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::NewProp_Tag = { "Tag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LevelProgressionManager_eventUnlockTag_Parms, Tag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tag_MetaData), NewProp_Tag_MetaData) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::NewProp_Tag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULevelProgressionManager, nullptr, "UnlockTag", nullptr, nullptr, Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::LevelProgressionManager_eventUnlockTag_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::LevelProgressionManager_eventUnlockTag_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULevelProgressionManager_UnlockTag()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULevelProgressionManager_UnlockTag_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULevelProgressionManager::execUnlockTag)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Tag);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockTag(Z_Param_Out_Tag);
	P_NATIVE_END;
}
// End Class ULevelProgressionManager Function UnlockTag

// Begin Class ULevelProgressionManager
void ULevelProgressionManager::StaticRegisterNativesULevelProgressionManager()
{
	UClass* Class = ULevelProgressionManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ActivateCheckpoint", &ULevelProgressionManager::execActivateCheckpoint },
		{ "CompleteObjective", &ULevelProgressionManager::execCompleteObjective },
		{ "GetAvailableCheckpoints", &ULevelProgressionManager::execGetAvailableCheckpoints },
		{ "GetCheckpointData", &ULevelProgressionManager::execGetCheckpointData },
		{ "GetLevelData", &ULevelProgressionManager::execGetLevelData },
		{ "GetLevelState", &ULevelProgressionManager::execGetLevelState },
		{ "GetObjectiveProgress", &ULevelProgressionManager::execGetObjectiveProgress },
		{ "GetPlayerLevel", &ULevelProgressionManager::execGetPlayerLevel },
		{ "GetUnlockedLevels", &ULevelProgressionManager::execGetUnlockedLevels },
		{ "HasUnlockedTag", &ULevelProgressionManager::execHasUnlockedTag },
		{ "LoadCheckpoint", &ULevelProgressionManager::execLoadCheckpoint },
		{ "LoadLevel", &ULevelProgressionManager::execLoadLevel },
		{ "LoadProgression", &ULevelProgressionManager::execLoadProgression },
		{ "ResetProgression", &ULevelProgressionManager::execResetProgression },
		{ "SaveProgression", &ULevelProgressionManager::execSaveProgression },
		{ "SetLevelState", &ULevelProgressionManager::execSetLevelState },
		{ "SetPlayerLevel", &ULevelProgressionManager::execSetPlayerLevel },
		{ "SetTotalObjectives", &ULevelProgressionManager::execSetTotalObjectives },
		{ "UnlockTag", &ULevelProgressionManager::execUnlockTag },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ULevelProgressionManager);
UClass* Z_Construct_UClass_ULevelProgressionManager_NoRegister()
{
	return ULevelProgressionManager::StaticClass();
}
struct Z_Construct_UClass_ULevelProgressionManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/LevelProgressionManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CheckpointDatabase_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Data tables\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Data tables" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelDatabase_MetaData[] = {
		{ "Category", "Data" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnCheckpointReached_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLevelStateChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLevelTransition_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnObjectiveCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedCheckpointDatabase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached databases\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached databases" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedLevelDatabase_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/LevelProgressionManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_CheckpointDatabase;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_LevelDatabase;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnCheckpointReached;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLevelStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLevelTransition;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnObjectiveCompleted;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedCheckpointDatabase;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedLevelDatabase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ULevelProgressionManager_ActivateCheckpoint, "ActivateCheckpoint" }, // 2186259326
		{ &Z_Construct_UFunction_ULevelProgressionManager_CompleteObjective, "CompleteObjective" }, // 1107026862
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetAvailableCheckpoints, "GetAvailableCheckpoints" }, // 2102306713
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetCheckpointData, "GetCheckpointData" }, // 2780722681
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetLevelData, "GetLevelData" }, // 4278419769
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetLevelState, "GetLevelState" }, // 262489318
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetObjectiveProgress, "GetObjectiveProgress" }, // 3981058187
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetPlayerLevel, "GetPlayerLevel" }, // 4134268746
		{ &Z_Construct_UFunction_ULevelProgressionManager_GetUnlockedLevels, "GetUnlockedLevels" }, // 1439163037
		{ &Z_Construct_UFunction_ULevelProgressionManager_HasUnlockedTag, "HasUnlockedTag" }, // 2175903802
		{ &Z_Construct_UFunction_ULevelProgressionManager_LoadCheckpoint, "LoadCheckpoint" }, // 165527446
		{ &Z_Construct_UFunction_ULevelProgressionManager_LoadLevel, "LoadLevel" }, // 2704875171
		{ &Z_Construct_UFunction_ULevelProgressionManager_LoadProgression, "LoadProgression" }, // 2427399961
		{ &Z_Construct_UFunction_ULevelProgressionManager_ResetProgression, "ResetProgression" }, // 2507376545
		{ &Z_Construct_UFunction_ULevelProgressionManager_SaveProgression, "SaveProgression" }, // 2494625831
		{ &Z_Construct_UFunction_ULevelProgressionManager_SetLevelState, "SetLevelState" }, // 2168972584
		{ &Z_Construct_UFunction_ULevelProgressionManager_SetPlayerLevel, "SetPlayerLevel" }, // 2684510071
		{ &Z_Construct_UFunction_ULevelProgressionManager_SetTotalObjectives, "SetTotalObjectives" }, // 3097748357
		{ &Z_Construct_UFunction_ULevelProgressionManager_UnlockTag, "UnlockTag" }, // 19359446
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ULevelProgressionManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CheckpointDatabase = { "CheckpointDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, CheckpointDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CheckpointDatabase_MetaData), NewProp_CheckpointDatabase_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_LevelDatabase = { "LevelDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, LevelDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelDatabase_MetaData), NewProp_LevelDatabase_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CurrentProgress = { "CurrentProgress", nullptr, (EPropertyFlags)0x0010000000020015, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, CurrentProgress), Z_Construct_UScriptStruct_FProgressionSaveData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentProgress_MetaData), NewProp_CurrentProgress_MetaData) }; // 2218347908
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnCheckpointReached = { "OnCheckpointReached", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, OnCheckpointReached), Z_Construct_UDelegateFunction_SLT_OnCheckpointReached__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnCheckpointReached_MetaData), NewProp_OnCheckpointReached_MetaData) }; // 2493054301
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnLevelStateChanged = { "OnLevelStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, OnLevelStateChanged), Z_Construct_UDelegateFunction_SLT_OnLevelStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLevelStateChanged_MetaData), NewProp_OnLevelStateChanged_MetaData) }; // 2362648982
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnLevelTransition = { "OnLevelTransition", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, OnLevelTransition), Z_Construct_UDelegateFunction_SLT_OnLevelTransition__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLevelTransition_MetaData), NewProp_OnLevelTransition_MetaData) }; // 406171616
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnObjectiveCompleted = { "OnObjectiveCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, OnObjectiveCompleted), Z_Construct_UDelegateFunction_SLT_OnObjectiveCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnObjectiveCompleted_MetaData), NewProp_OnObjectiveCompleted_MetaData) }; // 511602408
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CachedCheckpointDatabase = { "CachedCheckpointDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, CachedCheckpointDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedCheckpointDatabase_MetaData), NewProp_CachedCheckpointDatabase_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CachedLevelDatabase = { "CachedLevelDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULevelProgressionManager, CachedLevelDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedLevelDatabase_MetaData), NewProp_CachedLevelDatabase_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ULevelProgressionManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CheckpointDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_LevelDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CurrentProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnCheckpointReached,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnLevelStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnLevelTransition,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_OnObjectiveCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CachedCheckpointDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULevelProgressionManager_Statics::NewProp_CachedLevelDatabase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ULevelProgressionManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ULevelProgressionManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ULevelProgressionManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ULevelProgressionManager_Statics::ClassParams = {
	&ULevelProgressionManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ULevelProgressionManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ULevelProgressionManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ULevelProgressionManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ULevelProgressionManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ULevelProgressionManager()
{
	if (!Z_Registration_Info_UClass_ULevelProgressionManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ULevelProgressionManager.OuterSingleton, Z_Construct_UClass_ULevelProgressionManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ULevelProgressionManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<ULevelProgressionManager>()
{
	return ULevelProgressionManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ULevelProgressionManager);
ULevelProgressionManager::~ULevelProgressionManager() {}
// End Class ULevelProgressionManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ECheckpointType_StaticEnum, TEXT("ECheckpointType"), &Z_Registration_Info_UEnum_ECheckpointType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3947953594U) },
		{ ELevelState_StaticEnum, TEXT("ELevelState"), &Z_Registration_Info_UEnum_ELevelState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1083325619U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FCheckpointData::StaticStruct, Z_Construct_UScriptStruct_FCheckpointData_Statics::NewStructOps, TEXT("CheckpointData"), &Z_Registration_Info_UScriptStruct_CheckpointData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FCheckpointData), 1489733810U) },
		{ FLevelData::StaticStruct, Z_Construct_UScriptStruct_FLevelData_Statics::NewStructOps, TEXT("LevelData"), &Z_Registration_Info_UScriptStruct_LevelData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLevelData), 3953523680U) },
		{ FProgressionSaveData::StaticStruct, Z_Construct_UScriptStruct_FProgressionSaveData_Statics::NewStructOps, TEXT("ProgressionSaveData"), &Z_Registration_Info_UScriptStruct_ProgressionSaveData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FProgressionSaveData), 2218347908U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ULevelProgressionManager, ULevelProgressionManager::StaticClass, TEXT("ULevelProgressionManager"), &Z_Registration_Info_UClass_ULevelProgressionManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ULevelProgressionManager), 2597509325U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_683954972(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LevelProgressionManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
