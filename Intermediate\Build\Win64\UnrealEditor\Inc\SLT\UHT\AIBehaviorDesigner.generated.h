// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Design/AIBehaviorDesigner.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
class AActor;
enum class EAIBehaviorType : uint8;
enum class EAIDifficultyLevel : uint8;
enum class EAIPersonality : uint8;
struct FAIBehaviorTemplate;
#ifdef SLT_AIBehaviorDesigner_generated_h
#error "AIBehaviorDesigner.generated.h already included, missing '#pragma once' in AIBehaviorDesigner.h"
#endif
#define SLT_AIBehaviorDesigner_generated_h

#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_60_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAIBehaviorTemplate_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FAIBehaviorTemplate>();

#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_184_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAIBehaviorState_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FAIBehaviorState>();

#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_225_DELEGATE \
SLT_API void FOnBehaviorChanged_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorChanged, EAIBehaviorType OldBehavior, EAIBehaviorType NewBehavior, AActor* AIActor);


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_226_DELEGATE \
SLT_API void FOnPersonalityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPersonalityChanged, EAIPersonality OldPersonality, EAIPersonality NewPersonality);


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_227_DELEGATE \
SLT_API void FOnDifficultyChanged_DelegateWrapper(const FMulticastScriptDelegate& OnDifficultyChanged, EAIDifficultyLevel OldDifficulty, EAIDifficultyLevel NewDifficulty);


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_228_DELEGATE \
SLT_API void FOnBehaviorTemplateApplied_DelegateWrapper(const FMulticastScriptDelegate& OnBehaviorTemplateApplied, FName TemplateID, FAIBehaviorTemplate const& Template);


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execImportBehaviorConfiguration); \
	DECLARE_FUNCTION(execExportBehaviorConfiguration); \
	DECLARE_FUNCTION(execAutoFixCommonIssues); \
	DECLARE_FUNCTION(execGetSetupIssues); \
	DECLARE_FUNCTION(execValidateBehaviorSetup); \
	DECLARE_FUNCTION(execRemoveParameterModifier); \
	DECLARE_FUNCTION(execApplyParameterModifier); \
	DECLARE_FUNCTION(execGetBehaviorParameter); \
	DECLARE_FUNCTION(execSetBehaviorParameter); \
	DECLARE_FUNCTION(execResetToDefaults); \
	DECLARE_FUNCTION(execAdaptToPlayerBehavior); \
	DECLARE_FUNCTION(execAutoConfigureForScenario); \
	DECLARE_FUNCTION(execAutoConfigureForDifficulty); \
	DECLARE_FUNCTION(execCreateCustomTemplate); \
	DECLARE_FUNCTION(execGetTemplatesByPersonality); \
	DECLARE_FUNCTION(execGetTemplatesByType); \
	DECLARE_FUNCTION(execGetBehaviorTemplate); \
	DECLARE_FUNCTION(execGetAvailableTemplates); \
	DECLARE_FUNCTION(execDeactivateBehavior); \
	DECLARE_FUNCTION(execActivateBehavior); \
	DECLARE_FUNCTION(execSetDifficultyLevel); \
	DECLARE_FUNCTION(execSetPersonality); \
	DECLARE_FUNCTION(execSetBehaviorType); \
	DECLARE_FUNCTION(execApplyBehaviorTemplate);


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUAIBehaviorDesigner(); \
	friend struct Z_Construct_UClass_UAIBehaviorDesigner_Statics; \
public: \
	DECLARE_CLASS(UAIBehaviorDesigner, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UAIBehaviorDesigner)


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UAIBehaviorDesigner(UAIBehaviorDesigner&&); \
	UAIBehaviorDesigner(const UAIBehaviorDesigner&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UAIBehaviorDesigner); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAIBehaviorDesigner); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UAIBehaviorDesigner) \
	NO_API virtual ~UAIBehaviorDesigner();


#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_230_PROLOG
#define FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h_233_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UAIBehaviorDesigner>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Design_AIBehaviorDesigner_h


#define FOREACH_ENUM_EAIBEHAVIORTYPE(op) \
	op(EAIBehaviorType::Patrol) \
	op(EAIBehaviorType::Guard) \
	op(EAIBehaviorType::Hunt) \
	op(EAIBehaviorType::Flee) \
	op(EAIBehaviorType::Investigate) \
	op(EAIBehaviorType::Follow) \
	op(EAIBehaviorType::Wander) \
	op(EAIBehaviorType::Ambush) \
	op(EAIBehaviorType::Swarm) \
	op(EAIBehaviorType::Custom) 

enum class EAIBehaviorType : uint8;
template<> struct TIsUEnumClass<EAIBehaviorType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAIBehaviorType>();

#define FOREACH_ENUM_EAIPERSONALITY(op) \
	op(EAIPersonality::Aggressive) \
	op(EAIPersonality::Defensive) \
	op(EAIPersonality::Cautious) \
	op(EAIPersonality::Reckless) \
	op(EAIPersonality::Intelligent) \
	op(EAIPersonality::Cowardly) \
	op(EAIPersonality::Territorial) \
	op(EAIPersonality::Pack) \
	op(EAIPersonality::Lone) \
	op(EAIPersonality::Adaptive) 

enum class EAIPersonality : uint8;
template<> struct TIsUEnumClass<EAIPersonality> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAIPersonality>();

#define FOREACH_ENUM_EAIDIFFICULTYLEVEL(op) \
	op(EAIDifficultyLevel::Beginner) \
	op(EAIDifficultyLevel::Easy) \
	op(EAIDifficultyLevel::Normal) \
	op(EAIDifficultyLevel::Hard) \
	op(EAIDifficultyLevel::Expert) \
	op(EAIDifficultyLevel::Nightmare) \
	op(EAIDifficultyLevel::Adaptive) 

enum class EAIDifficultyLevel : uint8;
template<> struct TIsUEnumClass<EAIDifficultyLevel> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAIDifficultyLevel>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
