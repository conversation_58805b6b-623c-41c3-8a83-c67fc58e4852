#include "AutomatedSetupWizard.h"
#include "Engine/World.h"
#include "Engine/DataTable.h"
#include "Kismet/GameplayStatics.h"
#include "Core/Player/SLTPlayerCharacter.h"
#include "InventorySystem/Actors/ItemActor.h"
#include "Core/Enemies/EnemyCharacter.h"
#include "PuzzleSystem/Actors/PuzzleActor.h"

UAutomatedSetupWizard::UAutomatedSetupWizard()
{
	CurrentStep = ESetupWizardStep::Welcome;
	bIsWizardActive = false;
	OverallProgress = 0.0f;
	WizardStartTime = 0.0f;
}

void UAutomatedSetupWizard::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	InitializeWizardSteps();
}

void UAutomatedSetupWizard::Deinitialize()
{
	if (bIsWizardActive)
	{
		SaveWizardProgress();
	}
	Super::Deinitialize();
}

void UAutomatedSetupWizard::StartSetupWizard(const FSetupWizardConfiguration& Configuration)
{
	if (bIsWizardActive)
	{
		UE_LOG(LogTemp, Warning, TEXT("Setup wizard is already active"));
		return;
	}

	WizardConfiguration = Configuration;
	bIsWizardActive = true;
	CurrentStep = ESetupWizardStep::Welcome;
	OverallProgress = 0.0f;
	WizardStartTime = FPlatformTime::Seconds();
	CompletedSteps.Empty();

	InitializeWizardSteps();
	OnWizardStarted(Configuration);

	UE_LOG(LogTemp, Log, TEXT("Setup wizard started with complexity: %s"), 
		*UEnum::GetValueAsString(Configuration.SetupComplexity));
}

void UAutomatedSetupWizard::StopSetupWizard()
{
	if (!bIsWizardActive)
	{
		return;
	}

	SaveWizardProgress();
	bIsWizardActive = false;
	OnSetupWizardCompleted.Broadcast(false);

	UE_LOG(LogTemp, Log, TEXT("Setup wizard stopped"));
}

void UAutomatedSetupWizard::NextStep()
{
	if (!bIsWizardActive)
	{
		return;
	}

	ESetupWizardStep OldStep = CurrentStep;
	int32 CurrentStepIndex = (int32)CurrentStep;
	
	if (CurrentStepIndex < (int32)ESetupWizardStep::Complete)
	{
		CurrentStep = (ESetupWizardStep)(CurrentStepIndex + 1);
		OnStepChanged(OldStep, CurrentStep);
		UpdateProgress();
	}
	else
	{
		// Wizard complete
		bIsWizardActive = false;
		OnSetupWizardCompleted.Broadcast(true);
	}
}

void UAutomatedSetupWizard::PreviousStep()
{
	if (!bIsWizardActive)
	{
		return;
	}

	ESetupWizardStep OldStep = CurrentStep;
	int32 CurrentStepIndex = (int32)CurrentStep;
	
	if (CurrentStepIndex > 0)
	{
		CurrentStep = (ESetupWizardStep)(CurrentStepIndex - 1);
		OnStepChanged(OldStep, CurrentStep);
		UpdateProgress();
	}
}

void UAutomatedSetupWizard::GoToStep(ESetupWizardStep TargetStep)
{
	if (!bIsWizardActive)
	{
		return;
	}

	ESetupWizardStep OldStep = CurrentStep;
	CurrentStep = TargetStep;
	OnStepChanged(OldStep, CurrentStep);
	UpdateProgress();
}

void UAutomatedSetupWizard::CompleteCurrentStep()
{
	if (!bIsWizardActive)
	{
		return;
	}

	// Execute the setup for this step
	bool bSuccess = ExecuteStepSetup(CurrentStep);
	
	if (bSuccess)
	{
		CompletedSteps.Add(CurrentStep, true);
		
		// Find step data and mark as completed
		for (FSetupWizardStepData& StepData : WizardSteps)
		{
			if (StepData.StepType == CurrentStep)
			{
				StepData.bIsCompleted = true;
				StepData.Progress = 1.0f;
				break;
			}
		}

		OnSetupStepCompleted.Broadcast(CurrentStep, OverallProgress);
		NextStep();
	}
	else
	{
		OnSetupError.Broadcast(TEXT("Failed to complete current step"), CurrentStep);
	}
}

void UAutomatedSetupWizard::SkipCurrentStep()
{
	if (!bIsWizardActive)
	{
		return;
	}

	UE_LOG(LogTemp, Warning, TEXT("Skipping step: %s"), *UEnum::GetValueAsString(CurrentStep));
	NextStep();
}

void UAutomatedSetupWizard::RunQuickSetup()
{
	FSetupWizardConfiguration QuickConfig;
	QuickConfig.SetupComplexity = ESetupComplexity::QuickStart;
	QuickConfig.bSetupSampleContent = true;
	QuickConfig.bCreateTestLevel = true;
	QuickConfig.bSetupInputMappings = true;
	
	StartSetupWizard(QuickConfig);
	
	// Auto-complete all steps for quick setup
	while (bIsWizardActive && CurrentStep != ESetupWizardStep::Complete)
	{
		CompleteCurrentStep();
	}
}

void UAutomatedSetupWizard::RunFullAutomatedSetup()
{
	FSetupWizardConfiguration FullConfig;
	FullConfig.SetupComplexity = ESetupComplexity::Standard;
	
	StartSetupWizard(FullConfig);
	
	// Auto-complete all steps
	while (bIsWizardActive && CurrentStep != ESetupWizardStep::Complete)
	{
		CompleteCurrentStep();
	}
}

void UAutomatedSetupWizard::SetupInventorySystem()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up inventory system..."));
	
	// This would create default inventory data tables, configure grid settings, etc.
	CreateDefaultDataTables();
	
	UE_LOG(LogTemp, Log, TEXT("Inventory system setup complete"));
}

void UAutomatedSetupWizard::SetupWeaponSystem()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up weapon system..."));
	
	// This would create weapon data tables, configure upgrade trees, etc.
	
	UE_LOG(LogTemp, Log, TEXT("Weapon system setup complete"));
}

void UAutomatedSetupWizard::SetupEnemyAI()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up enemy AI system..."));
	
	// This would create AI behavior trees, configure enemy types, etc.
	
	UE_LOG(LogTemp, Log, TEXT("Enemy AI system setup complete"));
}

void UAutomatedSetupWizard::SetupPuzzleSystem()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up puzzle system..."));
	
	// This would create puzzle templates, configure puzzle types, etc.
	
	UE_LOG(LogTemp, Log, TEXT("Puzzle system setup complete"));
}

void UAutomatedSetupWizard::SetupPlayerCharacter()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up player character..."));
	
	// This would configure the player character with all components
	if (UWorld* World = GetWorld())
	{
		// Set default player character class if not specified
		if (!WizardConfiguration.PlayerCharacterClass)
		{
			WizardConfiguration.PlayerCharacterClass = ASLTPlayerCharacter::StaticClass();
		}
	}
	
	UE_LOG(LogTemp, Log, TEXT("Player character setup complete"));
}

void UAutomatedSetupWizard::SetupInputMappings()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up input mappings..."));
	
	// This would create and configure Enhanced Input mappings
	
	UE_LOG(LogTemp, Log, TEXT("Input mappings setup complete"));
}

void UAutomatedSetupWizard::SetupUI()
{
	UE_LOG(LogTemp, Log, TEXT("Setting up UI system..."));
	
	// This would create UI widgets, configure Common UI, etc.
	
	UE_LOG(LogTemp, Log, TEXT("UI system setup complete"));
}

void UAutomatedSetupWizard::CreateSampleContent()
{
	UE_LOG(LogTemp, Log, TEXT("Creating sample content..."));
	
	if (UWorld* World = GetWorld())
	{
		// Spawn sample items (using basic actors for now)
		FVector ItemLocation(0, 0, 100);
		for (int32 i = 0; i < 5; i++)
		{
			FVector SpawnLocation = ItemLocation + FVector(i * 200, 0, 0);
			AActor* ItemActor = SpawnActorWithComponents(AActor::StaticClass(), SpawnLocation);
			if (ItemActor)
			{
				UE_LOG(LogTemp, Log, TEXT("Created sample item at %s"), *SpawnLocation.ToString());
			}
		}

		// Spawn sample enemies (using basic actors for now)
		FVector EnemyLocation(500, 500, 100);
		for (int32 i = 0; i < 3; i++)
		{
			FVector SpawnLocation = EnemyLocation + FVector(i * 300, 0, 0);
			AActor* Enemy = SpawnActorWithComponents(AActor::StaticClass(), SpawnLocation);
			if (Enemy)
			{
				UE_LOG(LogTemp, Log, TEXT("Created sample enemy at %s"), *SpawnLocation.ToString());
			}
		}

		// Spawn sample puzzles (using basic actors for now)
		FVector PuzzleLocation(-500, 0, 100);
		AActor* Puzzle = SpawnActorWithComponents(AActor::StaticClass(), PuzzleLocation);
		if (Puzzle)
		{
			UE_LOG(LogTemp, Log, TEXT("Created sample puzzle at %s"), *PuzzleLocation.ToString());
		}
	}
	
	UE_LOG(LogTemp, Log, TEXT("Sample content creation complete"));
}

FSetupValidationResult UAutomatedSetupWizard::ValidateCurrentSetup()
{
	FSetupValidationResult Result;
	Result.bIsValid = true;
	Result.ErrorCount = 0;
	Result.WarningCount = 0;

	// Calculate completion percentage
	int32 CompletedCount = 0;
	for (const auto& StepPair : CompletedSteps)
	{
		if (StepPair.Value)
		{
			CompletedCount++;
		}
	}
	Result.CompletionPercentage = WizardSteps.Num() > 0 ? (float)CompletedCount / WizardSteps.Num() : 0.0f;

	// Validate each completed step
	for (const auto& StepPair : CompletedSteps)
	{
		if (StepPair.Value && !ValidateStep(StepPair.Key))
		{
			Result.bIsValid = false;
			Result.ErrorCount++;
			Result.ErrorMessages.Add(FText::FromString(FString::Printf(TEXT("Step %s validation failed"), 
				*UEnum::GetValueAsString(StepPair.Key))));
		}
	}

	OnSetupValidated.Broadcast(Result, CurrentStep);
	return Result;
}

bool UAutomatedSetupWizard::ValidateStep(ESetupWizardStep Step)
{
	switch (Step)
	{
		case ESetupWizardStep::PlayerSetup:
			return WizardConfiguration.PlayerCharacterClass != nullptr;
		
		case ESetupWizardStep::InventorySetup:
			return WizardConfiguration.bIncludeInventorySystem;
		
		case ESetupWizardStep::WeaponSetup:
			return WizardConfiguration.bIncludeWeaponSystem;
		
		default:
			return true;
	}
}

void UAutomatedSetupWizard::AutoFixCommonIssues()
{
	UE_LOG(LogTemp, Log, TEXT("Auto-fixing common setup issues..."));
	
	// Fix missing player character class
	if (!WizardConfiguration.PlayerCharacterClass)
	{
		WizardConfiguration.PlayerCharacterClass = ASLTPlayerCharacter::StaticClass();
		UE_LOG(LogTemp, Log, TEXT("Fixed: Set default player character class"));
	}
	
	// Enable required systems for selected complexity
	if (WizardConfiguration.SetupComplexity >= ESetupComplexity::Standard)
	{
		WizardConfiguration.bIncludeInventorySystem = true;
		WizardConfiguration.bIncludeWeaponSystem = true;
		WizardConfiguration.bIncludeResourceManagement = true;
	}
	
	UE_LOG(LogTemp, Log, TEXT("Auto-fix complete"));
}

FSetupWizardStepData UAutomatedSetupWizard::GetCurrentStepData() const
{
	for (const FSetupWizardStepData& StepData : WizardSteps)
	{
		if (StepData.StepType == CurrentStep)
		{
			return StepData;
		}
	}
	return FSetupWizardStepData();
}

TArray<FSetupWizardStepData> UAutomatedSetupWizard::GetAllSteps() const
{
	return WizardSteps;
}

float UAutomatedSetupWizard::GetEstimatedRemainingTime() const
{
	float RemainingTime = 0.0f;
	
	for (const FSetupWizardStepData& StepData : WizardSteps)
	{
		if (!StepData.bIsCompleted && (int32)StepData.StepType >= (int32)CurrentStep)
		{
			RemainingTime += StepData.EstimatedTime;
		}
	}
	
	return RemainingTime;
}

TArray<FText> UAutomatedSetupWizard::GetCurrentStepInstructions() const
{
	FSetupWizardStepData CurrentStepData = GetCurrentStepData();
	return CurrentStepData.Instructions;
}

void UAutomatedSetupWizard::SaveWizardProgress()
{
	// This would save wizard progress to a file or game save
	UE_LOG(LogTemp, Log, TEXT("Saving wizard progress..."));
}

bool UAutomatedSetupWizard::LoadWizardProgress()
{
	// This would load wizard progress from a file or game save
	UE_LOG(LogTemp, Log, TEXT("Loading wizard progress..."));
	return true;
}

void UAutomatedSetupWizard::ResetWizardProgress()
{
	CompletedSteps.Empty();
	CurrentStep = ESetupWizardStep::Welcome;
	OverallProgress = 0.0f;
	
	for (FSetupWizardStepData& StepData : WizardSteps)
	{
		StepData.bIsCompleted = false;
		StepData.Progress = 0.0f;
	}
	
	UE_LOG(LogTemp, Log, TEXT("Wizard progress reset"));
}

void UAutomatedSetupWizard::ExportSetupConfiguration(const FString& FilePath) const
{
	// This would export the configuration to JSON or other format
	UE_LOG(LogTemp, Log, TEXT("Exporting setup configuration to: %s"), *FilePath);
}

bool UAutomatedSetupWizard::ImportSetupConfiguration(const FString& FilePath)
{
	// This would import configuration from file
	UE_LOG(LogTemp, Log, TEXT("Importing setup configuration from: %s"), *FilePath);
	return true;
}

void UAutomatedSetupWizard::InitializeWizardSteps()
{
	WizardSteps.Empty();

	// Create step data for each step
	TArray<ESetupWizardStep> AllSteps = {
		ESetupWizardStep::Welcome,
		ESetupWizardStep::ProjectSetup,
		ESetupWizardStep::LevelSetup,
		ESetupWizardStep::PlayerSetup,
		ESetupWizardStep::InventorySetup,
		ESetupWizardStep::WeaponSetup,
		ESetupWizardStep::EnemySetup,
		ESetupWizardStep::PuzzleSetup,
		ESetupWizardStep::UISetup,
		ESetupWizardStep::Testing,
		ESetupWizardStep::Finalization,
		ESetupWizardStep::Complete
	};

	for (ESetupWizardStep Step : AllSteps)
	{
		FSetupWizardStepData StepData;
		StepData.StepType = Step;
		StepData.StepName = FText::FromString(UEnum::GetValueAsString(Step));
		StepData.bIsRequired = true;
		StepData.EstimatedTime = 5.0f; // Default 5 minutes per step
		
		// Customize based on complexity
		switch (WizardConfiguration.SetupComplexity)
		{
			case ESetupComplexity::QuickStart:
				StepData.EstimatedTime = 1.0f;
				break;
			case ESetupComplexity::Advanced:
				StepData.EstimatedTime = 10.0f;
				break;
			case ESetupComplexity::Expert:
				StepData.EstimatedTime = 15.0f;
				break;
		}
		
		WizardSteps.Add(StepData);
	}
}

void UAutomatedSetupWizard::UpdateProgress()
{
	int32 CompletedCount = CompletedSteps.Num();
	int32 TotalSteps = WizardSteps.Num();
	
	OverallProgress = TotalSteps > 0 ? (float)CompletedCount / TotalSteps : 0.0f;
	OnProgressUpdated(OverallProgress, CurrentStep);
}

bool UAutomatedSetupWizard::ExecuteStepSetup(ESetupWizardStep Step)
{
	switch (Step)
	{
		case ESetupWizardStep::Welcome:
			return true; // Always succeeds
		
		case ESetupWizardStep::ProjectSetup:
			SetupProjectSettings();
			return true;
		
		case ESetupWizardStep::PlayerSetup:
			SetupPlayerCharacter();
			return true;
		
		case ESetupWizardStep::InventorySetup:
			if (WizardConfiguration.bIncludeInventorySystem)
			{
				SetupInventorySystem();
			}
			return true;
		
		case ESetupWizardStep::WeaponSetup:
			if (WizardConfiguration.bIncludeWeaponSystem)
			{
				SetupWeaponSystem();
			}
			return true;
		
		case ESetupWizardStep::EnemySetup:
			if (WizardConfiguration.bIncludeEnemyAI)
			{
				SetupEnemyAI();
			}
			return true;
		
		case ESetupWizardStep::PuzzleSetup:
			if (WizardConfiguration.bIncludePuzzleSystem)
			{
				SetupPuzzleSystem();
			}
			return true;
		
		case ESetupWizardStep::UISetup:
			if (WizardConfiguration.bConfigureUI)
			{
				SetupUI();
			}
			return true;
		
		case ESetupWizardStep::Testing:
			if (WizardConfiguration.bSetupSampleContent)
			{
				CreateSampleContent();
			}
			return true;
		
		default:
			return true;
	}
}

void UAutomatedSetupWizard::CreateDefaultDataTables()
{
	// This would create and populate default data tables
	UE_LOG(LogTemp, Log, TEXT("Creating default data tables..."));
}

void UAutomatedSetupWizard::SetupProjectSettings()
{
	// This would configure project settings, input mappings, etc.
	UE_LOG(LogTemp, Log, TEXT("Configuring project settings..."));
}

void UAutomatedSetupWizard::CreateTestLevel()
{
	// This would create a test level with sample content
	UE_LOG(LogTemp, Log, TEXT("Creating test level..."));
}

AActor* UAutomatedSetupWizard::SpawnActorWithComponents(TSubclassOf<AActor> ActorClass, const FVector& Location)
{
	if (!ActorClass || !GetWorld())
	{
		return nullptr;
	}

	FActorSpawnParameters SpawnParams;
	SpawnParams.SpawnCollisionHandlingOverride = ESpawnActorCollisionHandlingMethod::AdjustIfPossibleButAlwaysSpawn;
	
	AActor* SpawnedActor = GetWorld()->SpawnActor<AActor>(ActorClass, Location, FRotator::ZeroRotator, SpawnParams);
	return SpawnedActor;
}
