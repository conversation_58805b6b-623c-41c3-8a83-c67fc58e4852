// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Interfaces/InventoryInterface.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FGuid;
struct FInventoryItemData;
struct FInventorySlot;
#ifdef SLT_InventoryInterface_generated_h
#error "InventoryInterface.generated.h already included, missing '#pragma once' in InventoryInterface.h"
#endif
#define SLT_InventoryInterface_generated_h

#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_13_DELEGATE \
SLT_API void FOnInventoryChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryChanged, FInventorySlot const& ChangedSlot, bool bWasAdded);


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_14_DELEGATE \
SLT_API void FOnInventoryOpened_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryOpened, bool bIsOpen);


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDropItem); \
	DECLARE_FUNCTION(execUseItem); \
	DECLARE_FUNCTION(execGetMaxWeight); \
	DECLARE_FUNCTION(execGetCurrentWeight); \
	DECLARE_FUNCTION(execGetInventoryCapacity); \
	DECLARE_FUNCTION(execClearInventory); \
	DECLARE_FUNCTION(execIsInventoryFull); \
	DECLARE_FUNCTION(execGetItemQuantity); \
	DECLARE_FUNCTION(execHasItem); \
	DECLARE_FUNCTION(execRemoveItemBySlotID); \
	DECLARE_FUNCTION(execRemoveItem); \
	DECLARE_FUNCTION(execAddItem);


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	SLT_API UInventoryInterface(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UInventoryInterface(UInventoryInterface&&); \
	UInventoryInterface(const UInventoryInterface&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(SLT_API, UInventoryInterface); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UInventoryInterface); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UInventoryInterface) \
	SLT_API virtual ~UInventoryInterface();


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesUInventoryInterface(); \
	friend struct Z_Construct_UClass_UInventoryInterface_Statics; \
public: \
	DECLARE_CLASS(UInventoryInterface, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/SLT"), SLT_API) \
	DECLARE_SERIALIZER(UInventoryInterface)


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_GENERATED_UINTERFACE_BODY() \
	FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IInventoryInterface() {} \
public: \
	typedef UInventoryInterface UClassType; \
	typedef IInventoryInterface ThisClass; \
	static bool Execute_AddItem(UObject* O, FInventoryItemData const& ItemData, int32 Quantity, FInventorySlot& OutSlot); \
	static void Execute_ClearInventory(UObject* O); \
	static bool Execute_DropItem(UObject* O, FName ItemID, int32 Quantity, FVector const& DropLocation); \
	static float Execute_GetCurrentWeight(const UObject* O); \
	static int32 Execute_GetInventoryCapacity(const UObject* O); \
	static int32 Execute_GetItemQuantity(const UObject* O, FName ItemID); \
	static float Execute_GetMaxWeight(const UObject* O); \
	static bool Execute_HasItem(const UObject* O, FName ItemID); \
	static bool Execute_IsInventoryFull(const UObject* O); \
	static bool Execute_RemoveItem(UObject* O, FName ItemID, int32 Quantity); \
	static bool Execute_RemoveItemBySlotID(UObject* O, FGuid const& SlotID); \
	static bool Execute_UseItem(UObject* O, FName ItemID, int32 Quantity); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_16_PROLOG
#define FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_27_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h_19_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UInventoryInterface>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Interfaces_InventoryInterface_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
