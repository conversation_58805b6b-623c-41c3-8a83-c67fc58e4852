# Resident Evil 4-Style Inventory System Implementation Guide

## Overview

This implementation provides a comprehensive, modular inventory and gameplay system inspired by Resident Evil 4, built for Unreal Engine 5 using C++, Enhanced Input, and Common UI frameworks.

## System Architecture

### Core Components

#### 1. Data Structures (`InventorySystem/Data/`)
- **`FInventoryItemData`**: Core item data structure with grid dimensions, properties, and metadata
- **`FInventorySlot`**: Runtime slot data with position, rotation, and quantity
- **`FInventorySlotSaveData`**: Serializable save data for persistence

#### 2. Interfaces (`Core/Interfaces/`)
- **`IInteractable`**: Interface for world objects that can be interacted with
- **`IInventoryInterface`**: Interface for inventory operations (add, remove, query items)

#### 3. Components (`InventorySystem/Components/`)
- **`UInventoryGridComponent`**: Main inventory management with grid-based placement
- **`UInteractionComponent`**: Handles world object interaction detection and execution

#### 4. Actors (`InventorySystem/Actors/`)
- **`AItemActor`**: Pickup items in the world with floating/rotation animations

#### 5. Player System (`Core/Player/`)
- **`ASLTPlayerCharacter`**: Enhanced Third Person Character with inventory and interaction

#### 6. Puzzle System (`PuzzleSystem/`)
- **`UPuzzleComponent`**: Configurable puzzle system with steps and requirements
- **`APuzzleActor`**: World puzzle objects with visual feedback

#### 7. Enemy System (`Core/Enemies/`)
- **`AEnemyCharacter`**: AI-driven enemy with states, combat, and loot dropping

#### 8. Save System (`Core/Systems/`)
- **`UInventorySaveGame`**: Comprehensive save/load system for all game data

## Key Features

### Grid-Based Inventory
- **Tetris-like placement**: Items occupy multiple grid cells based on dimensions
- **Item rotation**: Items can be rotated to fit in different orientations
- **Drag and drop**: Full support for moving items within the grid
- **Stacking**: Stackable items automatically combine when possible
- **Weight system**: Optional weight limits for realistic inventory management

### Interaction System
- **Range-based detection**: Configurable interaction ranges and line-of-sight
- **Context-sensitive prompts**: Dynamic interaction text based on object type
- **Requirements checking**: Items can require specific inventory items or conditions
- **Multi-step interactions**: Support for timed or complex interactions

### Puzzle System
- **Flexible configuration**: Support for sequence, combination, and pattern puzzles
- **Step-based progression**: Individual puzzle steps with requirements
- **Visual feedback**: Automatic color changes and state updates
- **Save/load support**: Puzzle progress is automatically saved

### Enhanced Input Integration
- **Modern input system**: Uses UE5's Enhanced Input for all controls
- **Remappable controls**: All inputs can be reconfigured
- **Context-aware**: Different input contexts for gameplay vs UI

## Designer-Friendly Features

### Blueprint Integration
All major components expose their functionality to Blueprint:
- **Events and delegates** for system communication
- **Blueprint-callable functions** for custom logic
- **Blueprint-implementable events** for visual feedback
- **Exposed properties** for easy configuration

### Data-Driven Configuration
- **Data Tables**: Item definitions stored in easily editable data tables
- **Asset references**: Soft references prevent loading issues
- **Gameplay Tags**: Flexible categorization and filtering system
- **Custom properties**: Extensible key-value storage for special cases

### Visual Tools
- **Grid visualization**: Debug overlays show grid state and item placement
- **Interaction feedback**: Visual prompts and highlights for interactable objects
- **State indicators**: Color-coded feedback for puzzle and enemy states

## Setup Instructions

### 1. Project Configuration
The system requires these plugins (already configured in .uproject):
- Enhanced Input
- Common UI
- Gameplay Tags
- UMG
- Gameplay Abilities

### 2. Input Setup
Create these Input Actions in your project:
- `IA_Move` (Vector2D)
- `IA_Look` (Vector2D)
- `IA_Jump` (Boolean)
- `IA_Interact` (Boolean)
- `IA_OpenInventory` (Boolean)
- `IA_RotateItem` (Boolean)

### 3. Data Table Setup
Create a Data Table using `FInventoryItemData` as the row structure:
1. Right-click in Content Browser → Miscellaneous → Data Table
2. Select `FInventoryItemData` as Row Structure
3. Name it `DT_ItemDatabase`
4. Add your item definitions

### 4. Character Setup
Replace your default character with `ASLTPlayerCharacter` or:
1. Add `UInventoryGridComponent` to your character
2. Add `UInteractionComponent` to your character
3. Set up Enhanced Input bindings
4. Configure the inventory UI widget class

## Usage Examples

### Adding Items to Inventory
```cpp
// In C++
FInventoryItemData ItemData;
if (InventoryComponent->GetItemDataByID(TEXT("HealthPotion"), ItemData))
{
    FInventorySlot OutSlot;
    bool bSuccess = InventoryComponent->AddItem(ItemData, 1, OutSlot);
}
```

### Creating Interactive Objects
```cpp
// Implement IInteractable interface
class MYGAME_API AMyInteractable : public AActor, public IInteractable
{
    virtual void OnInteractionComplete_Implementation(APawn* InteractingPawn) override
    {
        // Custom interaction logic
    }
};
```

### Setting Up Puzzles
```cpp
// Configure puzzle in Blueprint or C++
PuzzleComponent->PuzzleType = EPuzzleType::Sequence;
PuzzleComponent->PuzzleSteps.Add(FPuzzleStep{TEXT("Step1"), TEXT("Red"), false});
PuzzleComponent->PuzzleSteps.Add(FPuzzleStep{TEXT("Step2"), TEXT("Blue"), false});
```

## Extension Points

### Custom Item Types
Add new item types by extending `EItemType` enum and implementing custom logic in:
- `UInventoryGridComponent::UseItem_Implementation()`
- Item-specific actor classes
- UI widgets for special item interactions

### Custom Puzzle Types
Create new puzzle mechanics by:
- Adding to `EPuzzleType` enum
- Implementing custom logic in `UPuzzleComponent`
- Creating specialized puzzle actor classes

### AI Behavior
Extend enemy AI by:
- Creating custom Behavior Trees
- Adding new states to `EEnemyState`
- Implementing custom sensing and combat logic

## Performance Considerations

- **Soft references**: All asset references use soft pointers to prevent loading issues
- **Event-driven updates**: UI only updates when inventory changes
- **Efficient grid operations**: Grid state is cached for fast collision detection
- **LOD system**: Health bars and UI elements have distance-based visibility

## Troubleshooting

### Common Issues
1. **Items not appearing**: Check that ItemDatabase is set on InventoryGridComponent
2. **Interaction not working**: Verify Enhanced Input setup and InteractionComponent configuration
3. **Save/Load issues**: Ensure all data structures are properly marked with UPROPERTY()
4. **Blueprint compilation errors**: Check that all required modules are included in Build.cs

### Debug Features
- Enable inventory grid visualization in development builds
- Use console commands for adding/removing items
- Check interaction component debug overlays
- Monitor AI blackboard values in Behavior Tree debugger

## Next Steps

This system provides a solid foundation that can be extended with:
- **Weapon upgrading system** (RE4-style weapon modification)
- **Merchant/trading system** (buy/sell items)
- **Crafting system** (combine items to create new ones)
- **Equipment system** (equippable weapons and armor)
- **Advanced UI** (Common UI-based inventory screens)

The modular design ensures that new features can be added without breaking existing functionality.
