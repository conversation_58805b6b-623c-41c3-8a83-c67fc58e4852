// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Design/AutomatedSetupWizard.h"
#include "Runtime/Engine/Classes/Engine/GameInstance.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeAutomatedSetupWizard() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UAutomatedSetupWizard();
SLT_API UClass* Z_Construct_UClass_UAutomatedSetupWizard_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_ESetupComplexity();
SLT_API UEnum* Z_Construct_UEnum_SLT_ESetupWizardStep();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FSetupValidationResult();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FSetupWizardConfiguration();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FSetupWizardStepData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum ESetupWizardStep
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESetupWizardStep;
static UEnum* ESetupWizardStep_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESetupWizardStep.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESetupWizardStep.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ESetupWizardStep, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ESetupWizardStep"));
	}
	return Z_Registration_Info_UEnum_ESetupWizardStep.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ESetupWizardStep>()
{
	return ESetupWizardStep_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ESetupWizardStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Complete.DisplayName", "Complete" },
		{ "Complete.Name", "ESetupWizardStep::Complete" },
		{ "EnemySetup.DisplayName", "Enemy Setup" },
		{ "EnemySetup.Name", "ESetupWizardStep::EnemySetup" },
		{ "Finalization.DisplayName", "Finalization" },
		{ "Finalization.Name", "ESetupWizardStep::Finalization" },
		{ "InventorySetup.DisplayName", "Inventory Setup" },
		{ "InventorySetup.Name", "ESetupWizardStep::InventorySetup" },
		{ "LevelSetup.DisplayName", "Level Setup" },
		{ "LevelSetup.Name", "ESetupWizardStep::LevelSetup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
		{ "PlayerSetup.DisplayName", "Player Setup" },
		{ "PlayerSetup.Name", "ESetupWizardStep::PlayerSetup" },
		{ "ProjectSetup.DisplayName", "Project Setup" },
		{ "ProjectSetup.Name", "ESetupWizardStep::ProjectSetup" },
		{ "PuzzleSetup.DisplayName", "Puzzle Setup" },
		{ "PuzzleSetup.Name", "ESetupWizardStep::PuzzleSetup" },
		{ "Testing.DisplayName", "Testing" },
		{ "Testing.Name", "ESetupWizardStep::Testing" },
		{ "UISetup.DisplayName", "UI Setup" },
		{ "UISetup.Name", "ESetupWizardStep::UISetup" },
		{ "WeaponSetup.DisplayName", "Weapon Setup" },
		{ "WeaponSetup.Name", "ESetupWizardStep::WeaponSetup" },
		{ "Welcome.DisplayName", "Welcome" },
		{ "Welcome.Name", "ESetupWizardStep::Welcome" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESetupWizardStep::Welcome", (int64)ESetupWizardStep::Welcome },
		{ "ESetupWizardStep::ProjectSetup", (int64)ESetupWizardStep::ProjectSetup },
		{ "ESetupWizardStep::LevelSetup", (int64)ESetupWizardStep::LevelSetup },
		{ "ESetupWizardStep::PlayerSetup", (int64)ESetupWizardStep::PlayerSetup },
		{ "ESetupWizardStep::InventorySetup", (int64)ESetupWizardStep::InventorySetup },
		{ "ESetupWizardStep::WeaponSetup", (int64)ESetupWizardStep::WeaponSetup },
		{ "ESetupWizardStep::EnemySetup", (int64)ESetupWizardStep::EnemySetup },
		{ "ESetupWizardStep::PuzzleSetup", (int64)ESetupWizardStep::PuzzleSetup },
		{ "ESetupWizardStep::UISetup", (int64)ESetupWizardStep::UISetup },
		{ "ESetupWizardStep::Testing", (int64)ESetupWizardStep::Testing },
		{ "ESetupWizardStep::Finalization", (int64)ESetupWizardStep::Finalization },
		{ "ESetupWizardStep::Complete", (int64)ESetupWizardStep::Complete },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ESetupWizardStep",
	"ESetupWizardStep",
	Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ESetupWizardStep()
{
	if (!Z_Registration_Info_UEnum_ESetupWizardStep.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESetupWizardStep.InnerSingleton, Z_Construct_UEnum_SLT_ESetupWizardStep_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESetupWizardStep.InnerSingleton;
}
// End Enum ESetupWizardStep

// Begin Enum ESetupComplexity
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ESetupComplexity;
static UEnum* ESetupComplexity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ESetupComplexity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ESetupComplexity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ESetupComplexity, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ESetupComplexity"));
	}
	return Z_Registration_Info_UEnum_ESetupComplexity.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ESetupComplexity>()
{
	return ESetupComplexity_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ESetupComplexity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced Setup (60 min)" },
		{ "Advanced.Name", "ESetupComplexity::Advanced" },
		{ "Basic.DisplayName", "Basic Setup (15 min)" },
		{ "Basic.Name", "ESetupComplexity::Basic" },
		{ "BlueprintType", "true" },
		{ "Expert.DisplayName", "Expert Setup (120+ min)" },
		{ "Expert.Name", "ESetupComplexity::Expert" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
		{ "QuickStart.DisplayName", "Quick Start (5 min)" },
		{ "QuickStart.Name", "ESetupComplexity::QuickStart" },
		{ "Standard.DisplayName", "Standard Setup (30 min)" },
		{ "Standard.Name", "ESetupComplexity::Standard" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ESetupComplexity::QuickStart", (int64)ESetupComplexity::QuickStart },
		{ "ESetupComplexity::Basic", (int64)ESetupComplexity::Basic },
		{ "ESetupComplexity::Standard", (int64)ESetupComplexity::Standard },
		{ "ESetupComplexity::Advanced", (int64)ESetupComplexity::Advanced },
		{ "ESetupComplexity::Expert", (int64)ESetupComplexity::Expert },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ESetupComplexity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ESetupComplexity",
	"ESetupComplexity",
	Z_Construct_UEnum_SLT_ESetupComplexity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESetupComplexity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ESetupComplexity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ESetupComplexity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ESetupComplexity()
{
	if (!Z_Registration_Info_UEnum_ESetupComplexity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ESetupComplexity.InnerSingleton, Z_Construct_UEnum_SLT_ESetupComplexity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ESetupComplexity.InnerSingleton;
}
// End Enum ESetupComplexity

// Begin ScriptStruct FSetupWizardConfiguration
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_SetupWizardConfiguration;
class UScriptStruct* FSetupWizardConfiguration::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSetupWizardConfiguration, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("SetupWizardConfiguration"));
	}
	return Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FSetupWizardConfiguration>()
{
	return FSetupWizardConfiguration::StaticStruct();
}
struct Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SetupComplexity_MetaData[] = {
		{ "Category", "Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Basic configuration\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Basic configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProjectName_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerCharacterClass_MetaData[] = {
		{ "Category", "Configuration" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeInventorySystem_MetaData[] = {
		{ "Category", "Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// System inclusion flags\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System inclusion flags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeWeaponSystem_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeEnemyAI_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludePuzzleSystem_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeResourceManagement_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeStatistics_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIncludeLoadingSystem_MetaData[] = {
		{ "Category", "Systems" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSetupSampleContent_MetaData[] = {
		{ "Category", "Content" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Content setup flags\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Content setup flags" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCreateTestLevel_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bSetupInputMappings_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bConfigureUI_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableDebugTools_MetaData[] = {
		{ "Category", "Content" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomSettings_MetaData[] = {
		{ "Category", "Advanced" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Advanced settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Advanced settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AdditionalPlugins_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FeatureTags_MetaData[] = {
		{ "Category", "Advanced" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_SetupComplexity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_SetupComplexity;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ProjectName;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PlayerCharacterClass;
	static void NewProp_bIncludeInventorySystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeInventorySystem;
	static void NewProp_bIncludeWeaponSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeWeaponSystem;
	static void NewProp_bIncludeEnemyAI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeEnemyAI;
	static void NewProp_bIncludePuzzleSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludePuzzleSystem;
	static void NewProp_bIncludeResourceManagement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeResourceManagement;
	static void NewProp_bIncludeStatistics_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeStatistics;
	static void NewProp_bIncludeLoadingSystem_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIncludeLoadingSystem;
	static void NewProp_bSetupSampleContent_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSetupSampleContent;
	static void NewProp_bCreateTestLevel_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCreateTestLevel;
	static void NewProp_bSetupInputMappings_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSetupInputMappings;
	static void NewProp_bConfigureUI_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bConfigureUI;
	static void NewProp_bEnableDebugTools_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableDebugTools;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSettings_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomSettings;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AdditionalPlugins_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AdditionalPlugins;
	static const UECodeGen_Private::FStructPropertyParams NewProp_FeatureTags;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSetupWizardConfiguration>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_SetupComplexity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_SetupComplexity = { "SetupComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, SetupComplexity), Z_Construct_UEnum_SLT_ESetupComplexity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SetupComplexity_MetaData), NewProp_SetupComplexity_MetaData) }; // 988790327
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_ProjectName = { "ProjectName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, ProjectName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProjectName_MetaData), NewProp_ProjectName_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_PlayerCharacterClass = { "PlayerCharacterClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, PlayerCharacterClass), Z_Construct_UClass_UClass, Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerCharacterClass_MetaData), NewProp_PlayerCharacterClass_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeInventorySystem_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeInventorySystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeInventorySystem = { "bIncludeInventorySystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeInventorySystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeInventorySystem_MetaData), NewProp_bIncludeInventorySystem_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeWeaponSystem_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeWeaponSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeWeaponSystem = { "bIncludeWeaponSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeWeaponSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeWeaponSystem_MetaData), NewProp_bIncludeWeaponSystem_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeEnemyAI_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeEnemyAI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeEnemyAI = { "bIncludeEnemyAI", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeEnemyAI_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeEnemyAI_MetaData), NewProp_bIncludeEnemyAI_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludePuzzleSystem_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludePuzzleSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludePuzzleSystem = { "bIncludePuzzleSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludePuzzleSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludePuzzleSystem_MetaData), NewProp_bIncludePuzzleSystem_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeResourceManagement_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeResourceManagement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeResourceManagement = { "bIncludeResourceManagement", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeResourceManagement_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeResourceManagement_MetaData), NewProp_bIncludeResourceManagement_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeStatistics_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeStatistics = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeStatistics = { "bIncludeStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeStatistics_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeStatistics_MetaData), NewProp_bIncludeStatistics_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeLoadingSystem_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bIncludeLoadingSystem = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeLoadingSystem = { "bIncludeLoadingSystem", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeLoadingSystem_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIncludeLoadingSystem_MetaData), NewProp_bIncludeLoadingSystem_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupSampleContent_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bSetupSampleContent = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupSampleContent = { "bSetupSampleContent", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupSampleContent_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSetupSampleContent_MetaData), NewProp_bSetupSampleContent_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bCreateTestLevel_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bCreateTestLevel = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bCreateTestLevel = { "bCreateTestLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bCreateTestLevel_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCreateTestLevel_MetaData), NewProp_bCreateTestLevel_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupInputMappings_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bSetupInputMappings = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupInputMappings = { "bSetupInputMappings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupInputMappings_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bSetupInputMappings_MetaData), NewProp_bSetupInputMappings_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bConfigureUI_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bConfigureUI = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bConfigureUI = { "bConfigureUI", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bConfigureUI_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bConfigureUI_MetaData), NewProp_bConfigureUI_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bEnableDebugTools_SetBit(void* Obj)
{
	((FSetupWizardConfiguration*)Obj)->bEnableDebugTools = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bEnableDebugTools = { "bEnableDebugTools", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardConfiguration), &Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bEnableDebugTools_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableDebugTools_MetaData), NewProp_bEnableDebugTools_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings_ValueProp = { "CustomSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings_Key_KeyProp = { "CustomSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings = { "CustomSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, CustomSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomSettings_MetaData), NewProp_CustomSettings_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_AdditionalPlugins_Inner = { "AdditionalPlugins", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_AdditionalPlugins = { "AdditionalPlugins", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, AdditionalPlugins), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AdditionalPlugins_MetaData), NewProp_AdditionalPlugins_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_FeatureTags = { "FeatureTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardConfiguration, FeatureTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FeatureTags_MetaData), NewProp_FeatureTags_MetaData) }; // 3352185621
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_SetupComplexity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_SetupComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_ProjectName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_PlayerCharacterClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeInventorySystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeWeaponSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeEnemyAI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludePuzzleSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeResourceManagement,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bIncludeLoadingSystem,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupSampleContent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bCreateTestLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bSetupInputMappings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bConfigureUI,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_bEnableDebugTools,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_CustomSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_AdditionalPlugins_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_AdditionalPlugins,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewProp_FeatureTags,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"SetupWizardConfiguration",
	Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::PropPointers),
	sizeof(FSetupWizardConfiguration),
	alignof(FSetupWizardConfiguration),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSetupWizardConfiguration()
{
	if (!Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.InnerSingleton, Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_SetupWizardConfiguration.InnerSingleton;
}
// End ScriptStruct FSetupWizardConfiguration

// Begin ScriptStruct FSetupWizardStepData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_SetupWizardStepData;
class UScriptStruct* FSetupWizardStepData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_SetupWizardStepData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_SetupWizardStepData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSetupWizardStepData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("SetupWizardStepData"));
	}
	return Z_Registration_Info_UScriptStruct_SetupWizardStepData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FSetupWizardStepData>()
{
	return FSetupWizardStepData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FSetupWizardStepData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepType_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepName_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepDescription_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsRequired_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsCompleted_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedTime_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Instructions_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Tips_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredAssets_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StepParameters_MetaData[] = {
		{ "Category", "Step" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_StepType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StepType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StepName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StepDescription;
	static void NewProp_bIsRequired_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsRequired;
	static void NewProp_bIsCompleted_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsCompleted;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Instructions_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Instructions;
	static const UECodeGen_Private::FTextPropertyParams NewProp_Tips_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Tips;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequiredAssets_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredAssets;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_StepParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_StepParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSetupWizardStepData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepType = { "StepType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, StepType), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepType_MetaData), NewProp_StepType_MetaData) }; // 715535417
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepName = { "StepName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, StepName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepName_MetaData), NewProp_StepName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepDescription = { "StepDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, StepDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepDescription_MetaData), NewProp_StepDescription_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsRequired_SetBit(void* Obj)
{
	((FSetupWizardStepData*)Obj)->bIsRequired = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsRequired = { "bIsRequired", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardStepData), &Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsRequired_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsRequired_MetaData), NewProp_bIsRequired_MetaData) };
void Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsCompleted_SetBit(void* Obj)
{
	((FSetupWizardStepData*)Obj)->bIsCompleted = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsCompleted = { "bIsCompleted", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupWizardStepData), &Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsCompleted_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsCompleted_MetaData), NewProp_bIsCompleted_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_EstimatedTime = { "EstimatedTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, EstimatedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedTime_MetaData), NewProp_EstimatedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, Progress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Instructions_Inner = { "Instructions", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Instructions = { "Instructions", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, Instructions), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Instructions_MetaData), NewProp_Instructions_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Tips_Inner = { "Tips", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Tips = { "Tips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, Tips), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Tips_MetaData), NewProp_Tips_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_RequiredAssets_Inner = { "RequiredAssets", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_RequiredAssets = { "RequiredAssets", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, RequiredAssets), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredAssets_MetaData), NewProp_RequiredAssets_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters_ValueProp = { "StepParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters_Key_KeyProp = { "StepParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters = { "StepParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupWizardStepData, StepParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StepParameters_MetaData), NewProp_StepParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsRequired,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_bIsCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_EstimatedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Instructions_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Instructions,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Tips_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_Tips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_RequiredAssets_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_RequiredAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewProp_StepParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"SetupWizardStepData",
	Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::PropPointers),
	sizeof(FSetupWizardStepData),
	alignof(FSetupWizardStepData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSetupWizardStepData()
{
	if (!Z_Registration_Info_UScriptStruct_SetupWizardStepData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_SetupWizardStepData.InnerSingleton, Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_SetupWizardStepData.InnerSingleton;
}
// End ScriptStruct FSetupWizardStepData

// Begin ScriptStruct FSetupValidationResult
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_SetupValidationResult;
class UScriptStruct* FSetupValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_SetupValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_SetupValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FSetupValidationResult, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("SetupValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_SetupValidationResult.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FSetupValidationResult>()
{
	return FSetupValidationResult::StaticStruct();
}
struct Z_Construct_UScriptStruct_FSetupValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorCount_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningCount_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionPercentage_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningMessages_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuccessMessages_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ErrorCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WarningCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CompletionPercentage;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FTextPropertyParams NewProp_WarningMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WarningMessages;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SuccessMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SuccessMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FSetupValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FSetupValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FSetupValidationResult), &Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorCount = { "ErrorCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, ErrorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorCount_MetaData), NewProp_ErrorCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningCount = { "WarningCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, WarningCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningCount_MetaData), NewProp_WarningCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_CompletionPercentage = { "CompletionPercentage", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, CompletionPercentage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionPercentage_MetaData), NewProp_CompletionPercentage_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningMessages_Inner = { "WarningMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningMessages = { "WarningMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, WarningMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningMessages_MetaData), NewProp_WarningMessages_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_SuccessMessages_Inner = { "SuccessMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_SuccessMessages = { "SuccessMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FSetupValidationResult, SuccessMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuccessMessages_MetaData), NewProp_SuccessMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FSetupValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_CompletionPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_ErrorMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_WarningMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_SuccessMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewProp_SuccessMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FSetupValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"SetupValidationResult",
	Z_Construct_UScriptStruct_FSetupValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupValidationResult_Statics::PropPointers),
	sizeof(FSetupValidationResult),
	alignof(FSetupValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FSetupValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FSetupValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FSetupValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_SetupValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_SetupValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FSetupValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_SetupValidationResult.InnerSingleton;
}
// End ScriptStruct FSetupValidationResult

// Begin Delegate FOnSetupStepCompleted
struct Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnSetupStepCompleted_Parms
	{
		ESetupWizardStep CompletedStep;
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompletedStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CompletedStep;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep = { "CompletedStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupStepCompleted_Parms, CompletedStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupStepCompleted_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_CompletedStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnSetupStepCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::_Script_SLT_eventOnSetupStepCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::_Script_SLT_eventOnSetupStepCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSetupStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSetupStepCompleted, ESetupWizardStep CompletedStep, float Progress)
{
	struct _Script_SLT_eventOnSetupStepCompleted_Parms
	{
		ESetupWizardStep CompletedStep;
		float Progress;
	};
	_Script_SLT_eventOnSetupStepCompleted_Parms Parms;
	Parms.CompletedStep=CompletedStep;
	Parms.Progress=Progress;
	OnSetupStepCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSetupStepCompleted

// Begin Delegate FOnSetupWizardCompleted
struct Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnSetupWizardCompleted_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_SLT_eventOnSetupWizardCompleted_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnSetupWizardCompleted_Parms), &Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnSetupWizardCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::_Script_SLT_eventOnSetupWizardCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::_Script_SLT_eventOnSetupWizardCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSetupWizardCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnSetupWizardCompleted, bool bSuccess)
{
	struct _Script_SLT_eventOnSetupWizardCompleted_Parms
	{
		bool bSuccess;
	};
	_Script_SLT_eventOnSetupWizardCompleted_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnSetupWizardCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSetupWizardCompleted

// Begin Delegate FOnSetupValidated
struct Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnSetupValidated_Parms
	{
		FSetupValidationResult Result;
		ESetupWizardStep CurrentStep;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupValidated_Parms, Result), Z_Construct_UScriptStruct_FSetupValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 2135866182
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_CurrentStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_CurrentStep = { "CurrentStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupValidated_Parms, CurrentStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_Result,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_CurrentStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::NewProp_CurrentStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnSetupValidated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::_Script_SLT_eventOnSetupValidated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::_Script_SLT_eventOnSetupValidated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSetupValidated_DelegateWrapper(const FMulticastScriptDelegate& OnSetupValidated, FSetupValidationResult const& Result, ESetupWizardStep CurrentStep)
{
	struct _Script_SLT_eventOnSetupValidated_Parms
	{
		FSetupValidationResult Result;
		ESetupWizardStep CurrentStep;
	};
	_Script_SLT_eventOnSetupValidated_Parms Parms;
	Parms.Result=Result;
	Parms.CurrentStep=CurrentStep;
	OnSetupValidated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSetupValidated

// Begin Delegate FOnSetupError
struct Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnSetupError_Parms
	{
		FString ErrorMessage;
		ESetupWizardStep FailedStep;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FBytePropertyParams NewProp_FailedStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_FailedStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_FailedStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_FailedStep = { "FailedStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnSetupError_Parms, FailedStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_FailedStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::NewProp_FailedStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnSetupError__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::_Script_SLT_eventOnSetupError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::_Script_SLT_eventOnSetupError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnSetupError_DelegateWrapper(const FMulticastScriptDelegate& OnSetupError, const FString& ErrorMessage, ESetupWizardStep FailedStep)
{
	struct _Script_SLT_eventOnSetupError_Parms
	{
		FString ErrorMessage;
		ESetupWizardStep FailedStep;
	};
	_Script_SLT_eventOnSetupError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.FailedStep=FailedStep;
	OnSetupError.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnSetupError

// Begin Class UAutomatedSetupWizard Function AutoFixCommonIssues
struct Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "AutoFixCommonIssues", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execAutoFixCommonIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoFixCommonIssues();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function AutoFixCommonIssues

// Begin Class UAutomatedSetupWizard Function CompleteCurrentStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "CompleteCurrentStep", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execCompleteCurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CompleteCurrentStep();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function CompleteCurrentStep

// Begin Class UAutomatedSetupWizard Function CreateSampleContent
struct Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "CreateSampleContent", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execCreateSampleContent)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CreateSampleContent();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function CreateSampleContent

// Begin Class UAutomatedSetupWizard Function ExportSetupConfiguration
struct Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics
{
	struct AutomatedSetupWizard_eventExportSetupConfiguration_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventExportSetupConfiguration_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "ExportSetupConfiguration", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::AutomatedSetupWizard_eventExportSetupConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::AutomatedSetupWizard_eventExportSetupConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execExportSetupConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportSetupConfiguration(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function ExportSetupConfiguration

// Begin Class UAutomatedSetupWizard Function GetAllSteps
struct Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics
{
	struct AutomatedSetupWizard_eventGetAllSteps_Parms
	{
		TArray<FSetupWizardStepData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Information" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSetupWizardStepData, METADATA_PARAMS(0, nullptr) }; // 3289398018
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventGetAllSteps_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3289398018
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "GetAllSteps", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::AutomatedSetupWizard_eventGetAllSteps_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::AutomatedSetupWizard_eventGetAllSteps_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execGetAllSteps)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FSetupWizardStepData>*)Z_Param__Result=P_THIS->GetAllSteps();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function GetAllSteps

// Begin Class UAutomatedSetupWizard Function GetCurrentStepData
struct Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics
{
	struct AutomatedSetupWizard_eventGetCurrentStepData_Parms
	{
		FSetupWizardStepData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Information" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Information functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Information functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventGetCurrentStepData_Parms, ReturnValue), Z_Construct_UScriptStruct_FSetupWizardStepData, METADATA_PARAMS(0, nullptr) }; // 3289398018
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "GetCurrentStepData", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::AutomatedSetupWizard_eventGetCurrentStepData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::AutomatedSetupWizard_eventGetCurrentStepData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execGetCurrentStepData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSetupWizardStepData*)Z_Param__Result=P_THIS->GetCurrentStepData();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function GetCurrentStepData

// Begin Class UAutomatedSetupWizard Function GetCurrentStepInstructions
struct Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics
{
	struct AutomatedSetupWizard_eventGetCurrentStepInstructions_Parms
	{
		TArray<FText> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Information" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventGetCurrentStepInstructions_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "GetCurrentStepInstructions", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::AutomatedSetupWizard_eventGetCurrentStepInstructions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::AutomatedSetupWizard_eventGetCurrentStepInstructions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execGetCurrentStepInstructions)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FText>*)Z_Param__Result=P_THIS->GetCurrentStepInstructions();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function GetCurrentStepInstructions

// Begin Class UAutomatedSetupWizard Function GetEstimatedRemainingTime
struct Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics
{
	struct AutomatedSetupWizard_eventGetEstimatedRemainingTime_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Information" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventGetEstimatedRemainingTime_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "GetEstimatedRemainingTime", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::AutomatedSetupWizard_eventGetEstimatedRemainingTime_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::AutomatedSetupWizard_eventGetEstimatedRemainingTime_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execGetEstimatedRemainingTime)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEstimatedRemainingTime();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function GetEstimatedRemainingTime

// Begin Class UAutomatedSetupWizard Function GoToStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics
{
	struct AutomatedSetupWizard_eventGoToStep_Parms
	{
		ESetupWizardStep TargetStep;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_TargetStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_TargetStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::NewProp_TargetStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::NewProp_TargetStep = { "TargetStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventGoToStep_Parms, TargetStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::NewProp_TargetStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::NewProp_TargetStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "GoToStep", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::AutomatedSetupWizard_eventGoToStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::AutomatedSetupWizard_eventGoToStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execGoToStep)
{
	P_GET_ENUM(ESetupWizardStep,Z_Param_TargetStep);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GoToStep(ESetupWizardStep(Z_Param_TargetStep));
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function GoToStep

// Begin Class UAutomatedSetupWizard Function ImportSetupConfiguration
struct Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics
{
	struct AutomatedSetupWizard_eventImportSetupConfiguration_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventImportSetupConfiguration_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AutomatedSetupWizard_eventImportSetupConfiguration_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AutomatedSetupWizard_eventImportSetupConfiguration_Parms), &Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "ImportSetupConfiguration", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::AutomatedSetupWizard_eventImportSetupConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::AutomatedSetupWizard_eventImportSetupConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execImportSetupConfiguration)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportSetupConfiguration(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function ImportSetupConfiguration

// Begin Class UAutomatedSetupWizard Function LoadWizardProgress
struct Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics
{
	struct AutomatedSetupWizard_eventLoadWizardProgress_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AutomatedSetupWizard_eventLoadWizardProgress_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AutomatedSetupWizard_eventLoadWizardProgress_Parms), &Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "LoadWizardProgress", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::AutomatedSetupWizard_eventLoadWizardProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::AutomatedSetupWizard_eventLoadWizardProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execLoadWizardProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadWizardProgress();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function LoadWizardProgress

// Begin Class UAutomatedSetupWizard Function NextStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_NextStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_NextStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "NextStep", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_NextStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_NextStep_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_NextStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_NextStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execNextStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->NextStep();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function NextStep

// Begin Class UAutomatedSetupWizard Function OnProgressUpdated
struct AutomatedSetupWizard_eventOnProgressUpdated_Parms
{
	float NewProgress;
	ESetupWizardStep InCurrentStep;
};
static const FName NAME_UAutomatedSetupWizard_OnProgressUpdated = FName(TEXT("OnProgressUpdated"));
void UAutomatedSetupWizard::OnProgressUpdated(float NewProgress, ESetupWizardStep InCurrentStep)
{
	AutomatedSetupWizard_eventOnProgressUpdated_Parms Parms;
	Parms.NewProgress=NewProgress;
	Parms.InCurrentStep=InCurrentStep;
	UFunction* Func = FindFunctionChecked(NAME_UAutomatedSetupWizard_OnProgressUpdated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewProgress;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InCurrentStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_InCurrentStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_NewProgress = { "NewProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventOnProgressUpdated_Parms, NewProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_InCurrentStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_InCurrentStep = { "InCurrentStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventOnProgressUpdated_Parms, InCurrentStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_NewProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_InCurrentStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::NewProp_InCurrentStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "OnProgressUpdated", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::PropPointers), sizeof(AutomatedSetupWizard_eventOnProgressUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::Function_MetaDataParams) };
static_assert(sizeof(AutomatedSetupWizard_eventOnProgressUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAutomatedSetupWizard Function OnProgressUpdated

// Begin Class UAutomatedSetupWizard Function OnStepChanged
struct AutomatedSetupWizard_eventOnStepChanged_Parms
{
	ESetupWizardStep OldStep;
	ESetupWizardStep NewStep;
};
static const FName NAME_UAutomatedSetupWizard_OnStepChanged = FName(TEXT("OnStepChanged"));
void UAutomatedSetupWizard::OnStepChanged(ESetupWizardStep OldStep, ESetupWizardStep NewStep)
{
	AutomatedSetupWizard_eventOnStepChanged_Parms Parms;
	Parms.OldStep=OldStep;
	Parms.NewStep=NewStep;
	UFunction* Func = FindFunctionChecked(NAME_UAutomatedSetupWizard_OnStepChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_OldStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_OldStep;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewStep;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_OldStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_OldStep = { "OldStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventOnStepChanged_Parms, OldStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_NewStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_NewStep = { "NewStep", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventOnStepChanged_Parms, NewStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_OldStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_OldStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_NewStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::NewProp_NewStep,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "OnStepChanged", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::PropPointers), sizeof(AutomatedSetupWizard_eventOnStepChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(AutomatedSetupWizard_eventOnStepChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAutomatedSetupWizard Function OnStepChanged

// Begin Class UAutomatedSetupWizard Function OnWizardStarted
struct AutomatedSetupWizard_eventOnWizardStarted_Parms
{
	FSetupWizardConfiguration Configuration;
};
static const FName NAME_UAutomatedSetupWizard_OnWizardStarted = FName(TEXT("OnWizardStarted"));
void UAutomatedSetupWizard::OnWizardStarted(FSetupWizardConfiguration const& Configuration)
{
	AutomatedSetupWizard_eventOnWizardStarted_Parms Parms;
	Parms.Configuration=Configuration;
	UFunction* Func = FindFunctionChecked(NAME_UAutomatedSetupWizard_OnWizardStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventOnWizardStarted_Parms, Configuration), Z_Construct_UScriptStruct_FSetupWizardConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3944883791
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "OnWizardStarted", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::PropPointers), sizeof(AutomatedSetupWizard_eventOnWizardStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::Function_MetaDataParams) };
static_assert(sizeof(AutomatedSetupWizard_eventOnWizardStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UAutomatedSetupWizard Function OnWizardStarted

// Begin Class UAutomatedSetupWizard Function PreviousStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "PreviousStep", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execPreviousStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreviousStep();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function PreviousStep

// Begin Class UAutomatedSetupWizard Function ResetWizardProgress
struct Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "ResetWizardProgress", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execResetWizardProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetWizardProgress();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function ResetWizardProgress

// Begin Class UAutomatedSetupWizard Function RunFullAutomatedSetup
struct Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "RunFullAutomatedSetup", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execRunFullAutomatedSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunFullAutomatedSetup();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function RunFullAutomatedSetup

// Begin Class UAutomatedSetupWizard Function RunQuickSetup
struct Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Automated setup functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Automated setup functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "RunQuickSetup", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execRunQuickSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RunQuickSetup();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function RunQuickSetup

// Begin Class UAutomatedSetupWizard Function SaveWizardProgress
struct Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SaveWizardProgress", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSaveWizardProgress)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SaveWizardProgress();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SaveWizardProgress

// Begin Class UAutomatedSetupWizard Function SetupEnemyAI
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupEnemyAI", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupEnemyAI)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupEnemyAI();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupEnemyAI

// Begin Class UAutomatedSetupWizard Function SetupInputMappings
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupInputMappings", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupInputMappings)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupInputMappings();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupInputMappings

// Begin Class UAutomatedSetupWizard Function SetupInventorySystem
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupInventorySystem", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupInventorySystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupInventorySystem();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupInventorySystem

// Begin Class UAutomatedSetupWizard Function SetupPlayerCharacter
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupPlayerCharacter", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupPlayerCharacter)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupPlayerCharacter();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupPlayerCharacter

// Begin Class UAutomatedSetupWizard Function SetupPuzzleSystem
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupPuzzleSystem", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupPuzzleSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupPuzzleSystem();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupPuzzleSystem

// Begin Class UAutomatedSetupWizard Function SetupUI
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupUI", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupUI)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupUI();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupUI

// Begin Class UAutomatedSetupWizard Function SetupWeaponSystem
struct Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Automated Setup" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SetupWeaponSystem", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSetupWeaponSystem)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupWeaponSystem();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SetupWeaponSystem

// Begin Class UAutomatedSetupWizard Function SkipCurrentStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "SkipCurrentStep", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execSkipCurrentStep)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SkipCurrentStep();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function SkipCurrentStep

// Begin Class UAutomatedSetupWizard Function StartSetupWizard
struct Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics
{
	struct AutomatedSetupWizard_eventStartSetupWizard_Parms
	{
		FSetupWizardConfiguration Configuration;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main wizard functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main wizard functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Configuration_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Configuration;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::NewProp_Configuration = { "Configuration", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventStartSetupWizard_Parms, Configuration), Z_Construct_UScriptStruct_FSetupWizardConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Configuration_MetaData), NewProp_Configuration_MetaData) }; // 3944883791
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::NewProp_Configuration,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "StartSetupWizard", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::AutomatedSetupWizard_eventStartSetupWizard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::AutomatedSetupWizard_eventStartSetupWizard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execStartSetupWizard)
{
	P_GET_STRUCT_REF(FSetupWizardConfiguration,Z_Param_Out_Configuration);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartSetupWizard(Z_Param_Out_Configuration);
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function StartSetupWizard

// Begin Class UAutomatedSetupWizard Function StopSetupWizard
struct Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup Wizard" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "StopSetupWizard", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execStopSetupWizard)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopSetupWizard();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function StopSetupWizard

// Begin Class UAutomatedSetupWizard Function ValidateCurrentSetup
struct Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics
{
	struct AutomatedSetupWizard_eventValidateCurrentSetup_Parms
	{
		FSetupValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventValidateCurrentSetup_Parms, ReturnValue), Z_Construct_UScriptStruct_FSetupValidationResult, METADATA_PARAMS(0, nullptr) }; // 2135866182
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "ValidateCurrentSetup", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::AutomatedSetupWizard_eventValidateCurrentSetup_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::AutomatedSetupWizard_eventValidateCurrentSetup_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execValidateCurrentSetup)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FSetupValidationResult*)Z_Param__Result=P_THIS->ValidateCurrentSetup();
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function ValidateCurrentSetup

// Begin Class UAutomatedSetupWizard Function ValidateStep
struct Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics
{
	struct AutomatedSetupWizard_eventValidateStep_Parms
	{
		ESetupWizardStep Step;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_Step_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Step;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_Step_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_Step = { "Step", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(AutomatedSetupWizard_eventValidateStep_Parms, Step), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
void Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((AutomatedSetupWizard_eventValidateStep_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(AutomatedSetupWizard_eventValidateStep_Parms), &Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_Step_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_Step,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UAutomatedSetupWizard, nullptr, "ValidateStep", nullptr, nullptr, Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::PropPointers), sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::AutomatedSetupWizard_eventValidateStep_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::Function_MetaDataParams), Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::AutomatedSetupWizard_eventValidateStep_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UAutomatedSetupWizard::execValidateStep)
{
	P_GET_ENUM(ESetupWizardStep,Z_Param_Step);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ValidateStep(ESetupWizardStep(Z_Param_Step));
	P_NATIVE_END;
}
// End Class UAutomatedSetupWizard Function ValidateStep

// Begin Class UAutomatedSetupWizard
void UAutomatedSetupWizard::StaticRegisterNativesUAutomatedSetupWizard()
{
	UClass* Class = UAutomatedSetupWizard::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AutoFixCommonIssues", &UAutomatedSetupWizard::execAutoFixCommonIssues },
		{ "CompleteCurrentStep", &UAutomatedSetupWizard::execCompleteCurrentStep },
		{ "CreateSampleContent", &UAutomatedSetupWizard::execCreateSampleContent },
		{ "ExportSetupConfiguration", &UAutomatedSetupWizard::execExportSetupConfiguration },
		{ "GetAllSteps", &UAutomatedSetupWizard::execGetAllSteps },
		{ "GetCurrentStepData", &UAutomatedSetupWizard::execGetCurrentStepData },
		{ "GetCurrentStepInstructions", &UAutomatedSetupWizard::execGetCurrentStepInstructions },
		{ "GetEstimatedRemainingTime", &UAutomatedSetupWizard::execGetEstimatedRemainingTime },
		{ "GoToStep", &UAutomatedSetupWizard::execGoToStep },
		{ "ImportSetupConfiguration", &UAutomatedSetupWizard::execImportSetupConfiguration },
		{ "LoadWizardProgress", &UAutomatedSetupWizard::execLoadWizardProgress },
		{ "NextStep", &UAutomatedSetupWizard::execNextStep },
		{ "PreviousStep", &UAutomatedSetupWizard::execPreviousStep },
		{ "ResetWizardProgress", &UAutomatedSetupWizard::execResetWizardProgress },
		{ "RunFullAutomatedSetup", &UAutomatedSetupWizard::execRunFullAutomatedSetup },
		{ "RunQuickSetup", &UAutomatedSetupWizard::execRunQuickSetup },
		{ "SaveWizardProgress", &UAutomatedSetupWizard::execSaveWizardProgress },
		{ "SetupEnemyAI", &UAutomatedSetupWizard::execSetupEnemyAI },
		{ "SetupInputMappings", &UAutomatedSetupWizard::execSetupInputMappings },
		{ "SetupInventorySystem", &UAutomatedSetupWizard::execSetupInventorySystem },
		{ "SetupPlayerCharacter", &UAutomatedSetupWizard::execSetupPlayerCharacter },
		{ "SetupPuzzleSystem", &UAutomatedSetupWizard::execSetupPuzzleSystem },
		{ "SetupUI", &UAutomatedSetupWizard::execSetupUI },
		{ "SetupWeaponSystem", &UAutomatedSetupWizard::execSetupWeaponSystem },
		{ "SkipCurrentStep", &UAutomatedSetupWizard::execSkipCurrentStep },
		{ "StartSetupWizard", &UAutomatedSetupWizard::execStartSetupWizard },
		{ "StopSetupWizard", &UAutomatedSetupWizard::execStopSetupWizard },
		{ "ValidateCurrentSetup", &UAutomatedSetupWizard::execValidateCurrentSetup },
		{ "ValidateStep", &UAutomatedSetupWizard::execValidateStep },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UAutomatedSetupWizard);
UClass* Z_Construct_UClass_UAutomatedSetupWizard_NoRegister()
{
	return UAutomatedSetupWizard::StaticClass();
}
struct Z_Construct_UClass_UAutomatedSetupWizard_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Design/AutomatedSetupWizard.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentStep_MetaData[] = {
		{ "Category", "Wizard State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current wizard state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current wizard state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WizardConfiguration_MetaData[] = {
		{ "Category", "Wizard State" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsWizardActive_MetaData[] = {
		{ "Category", "Wizard State" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallProgress_MetaData[] = {
		{ "Category", "Wizard State" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSetupStepCompleted_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSetupWizardCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSetupValidated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnSetupError_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WizardSteps_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletedSteps_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WizardStartTime_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/AutomatedSetupWizard.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentStep_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentStep;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WizardConfiguration;
	static void NewProp_bIsWizardActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsWizardActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSetupStepCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSetupWizardCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSetupValidated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnSetupError;
	static const UECodeGen_Private::FStructPropertyParams NewProp_WizardSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WizardSteps;
	static const UECodeGen_Private::FBoolPropertyParams NewProp_CompletedSteps_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_CompletedSteps_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CompletedSteps_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CompletedSteps;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_WizardStartTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_AutoFixCommonIssues, "AutoFixCommonIssues" }, // 2799769216
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_CompleteCurrentStep, "CompleteCurrentStep" }, // 1678341690
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_CreateSampleContent, "CreateSampleContent" }, // 796633485
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_ExportSetupConfiguration, "ExportSetupConfiguration" }, // 387357315
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_GetAllSteps, "GetAllSteps" }, // 1362707296
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepData, "GetCurrentStepData" }, // 2612774598
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_GetCurrentStepInstructions, "GetCurrentStepInstructions" }, // 3464181982
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_GetEstimatedRemainingTime, "GetEstimatedRemainingTime" }, // 1061441698
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_GoToStep, "GoToStep" }, // 4150743701
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_ImportSetupConfiguration, "ImportSetupConfiguration" }, // 546893681
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_LoadWizardProgress, "LoadWizardProgress" }, // 2948944781
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_NextStep, "NextStep" }, // 3435977908
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_OnProgressUpdated, "OnProgressUpdated" }, // 2237740899
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_OnStepChanged, "OnStepChanged" }, // 15712077
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_OnWizardStarted, "OnWizardStarted" }, // 2794525216
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_PreviousStep, "PreviousStep" }, // 886255675
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_ResetWizardProgress, "ResetWizardProgress" }, // 4220404135
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_RunFullAutomatedSetup, "RunFullAutomatedSetup" }, // 2191697913
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_RunQuickSetup, "RunQuickSetup" }, // 1023309832
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SaveWizardProgress, "SaveWizardProgress" }, // 3798193983
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupEnemyAI, "SetupEnemyAI" }, // 657505359
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupInputMappings, "SetupInputMappings" }, // 1733401672
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupInventorySystem, "SetupInventorySystem" }, // 4216108063
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupPlayerCharacter, "SetupPlayerCharacter" }, // 771871620
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupPuzzleSystem, "SetupPuzzleSystem" }, // 2113072304
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupUI, "SetupUI" }, // 3481891062
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SetupWeaponSystem, "SetupWeaponSystem" }, // 3467188723
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_SkipCurrentStep, "SkipCurrentStep" }, // 2938219495
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_StartSetupWizard, "StartSetupWizard" }, // 3653628554
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_StopSetupWizard, "StopSetupWizard" }, // 1489782167
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_ValidateCurrentSetup, "ValidateCurrentSetup" }, // 1635100709
		{ &Z_Construct_UFunction_UAutomatedSetupWizard_ValidateStep, "ValidateStep" }, // 1907372480
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UAutomatedSetupWizard>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CurrentStep_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CurrentStep = { "CurrentStep", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, CurrentStep), Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentStep_MetaData), NewProp_CurrentStep_MetaData) }; // 715535417
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardConfiguration = { "WizardConfiguration", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, WizardConfiguration), Z_Construct_UScriptStruct_FSetupWizardConfiguration, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WizardConfiguration_MetaData), NewProp_WizardConfiguration_MetaData) }; // 3944883791
void Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_bIsWizardActive_SetBit(void* Obj)
{
	((UAutomatedSetupWizard*)Obj)->bIsWizardActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_bIsWizardActive = { "bIsWizardActive", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UAutomatedSetupWizard), &Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_bIsWizardActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsWizardActive_MetaData), NewProp_bIsWizardActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OverallProgress = { "OverallProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, OverallProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallProgress_MetaData), NewProp_OverallProgress_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupStepCompleted = { "OnSetupStepCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, OnSetupStepCompleted), Z_Construct_UDelegateFunction_SLT_OnSetupStepCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSetupStepCompleted_MetaData), NewProp_OnSetupStepCompleted_MetaData) }; // 3925984399
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupWizardCompleted = { "OnSetupWizardCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, OnSetupWizardCompleted), Z_Construct_UDelegateFunction_SLT_OnSetupWizardCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSetupWizardCompleted_MetaData), NewProp_OnSetupWizardCompleted_MetaData) }; // 1016475203
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupValidated = { "OnSetupValidated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, OnSetupValidated), Z_Construct_UDelegateFunction_SLT_OnSetupValidated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSetupValidated_MetaData), NewProp_OnSetupValidated_MetaData) }; // 240444387
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupError = { "OnSetupError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, OnSetupError), Z_Construct_UDelegateFunction_SLT_OnSetupError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnSetupError_MetaData), NewProp_OnSetupError_MetaData) }; // 4130456120
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardSteps_Inner = { "WizardSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSetupWizardStepData, METADATA_PARAMS(0, nullptr) }; // 3289398018
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardSteps = { "WizardSteps", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, WizardSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WizardSteps_MetaData), NewProp_WizardSteps_MetaData) }; // 3289398018
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_ValueProp = { "CompletedSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_Key_KeyProp = { "CompletedSteps_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_ESetupWizardStep, METADATA_PARAMS(0, nullptr) }; // 715535417
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps = { "CompletedSteps", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, CompletedSteps), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletedSteps_MetaData), NewProp_CompletedSteps_MetaData) }; // 715535417
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardStartTime = { "WizardStartTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UAutomatedSetupWizard, WizardStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WizardStartTime_MetaData), NewProp_WizardStartTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UAutomatedSetupWizard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CurrentStep_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CurrentStep,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardConfiguration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_bIsWizardActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OverallProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupStepCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupWizardCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupValidated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_OnSetupError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_CompletedSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UAutomatedSetupWizard_Statics::NewProp_WizardStartTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAutomatedSetupWizard_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UAutomatedSetupWizard_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UAutomatedSetupWizard_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UAutomatedSetupWizard_Statics::ClassParams = {
	&UAutomatedSetupWizard::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UAutomatedSetupWizard_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UAutomatedSetupWizard_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UAutomatedSetupWizard_Statics::Class_MetaDataParams), Z_Construct_UClass_UAutomatedSetupWizard_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UAutomatedSetupWizard()
{
	if (!Z_Registration_Info_UClass_UAutomatedSetupWizard.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UAutomatedSetupWizard.OuterSingleton, Z_Construct_UClass_UAutomatedSetupWizard_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UAutomatedSetupWizard.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UAutomatedSetupWizard>()
{
	return UAutomatedSetupWizard::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UAutomatedSetupWizard);
UAutomatedSetupWizard::~UAutomatedSetupWizard() {}
// End Class UAutomatedSetupWizard

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ESetupWizardStep_StaticEnum, TEXT("ESetupWizardStep"), &Z_Registration_Info_UEnum_ESetupWizardStep, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 715535417U) },
		{ ESetupComplexity_StaticEnum, TEXT("ESetupComplexity"), &Z_Registration_Info_UEnum_ESetupComplexity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 988790327U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FSetupWizardConfiguration::StaticStruct, Z_Construct_UScriptStruct_FSetupWizardConfiguration_Statics::NewStructOps, TEXT("SetupWizardConfiguration"), &Z_Registration_Info_UScriptStruct_SetupWizardConfiguration, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSetupWizardConfiguration), 3944883791U) },
		{ FSetupWizardStepData::StaticStruct, Z_Construct_UScriptStruct_FSetupWizardStepData_Statics::NewStructOps, TEXT("SetupWizardStepData"), &Z_Registration_Info_UScriptStruct_SetupWizardStepData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSetupWizardStepData), 3289398018U) },
		{ FSetupValidationResult::StaticStruct, Z_Construct_UScriptStruct_FSetupValidationResult_Statics::NewStructOps, TEXT("SetupValidationResult"), &Z_Registration_Info_UScriptStruct_SetupValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FSetupValidationResult), 2135866182U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UAutomatedSetupWizard, UAutomatedSetupWizard::StaticClass, TEXT("UAutomatedSetupWizard"), &Z_Registration_Info_UClass_UAutomatedSetupWizard, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UAutomatedSetupWizard), 1052400292U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_341027359(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_AutomatedSetupWizard_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
