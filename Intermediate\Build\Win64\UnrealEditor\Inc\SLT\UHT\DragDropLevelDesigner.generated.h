// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Design/DragDropLevelDesigner.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
enum class EDragDropCategory : uint8;
enum class EPlacementMode : uint8;
enum class ESnapMode : uint8;
struct FDragDropAsset;
struct FLevelDesignStats;
#ifdef SLT_DragDropLevelDesigner_generated_h
#error "DragDropLevelDesigner.generated.h already included, missing '#pragma once' in DragDropLevelDesigner.h"
#endif
#define SLT_DragDropLevelDesigner_generated_h

#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_53_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FDragDropAsset_Statics; \
	SLT_API static class UScriptStruct* StaticStruct(); \
	typedef FTableRowBase Super;


template<> SLT_API UScriptStruct* StaticStruct<struct FDragDropAsset>();

#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_142_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlacementSettings_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPlacementSettings>();

#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_192_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLevelDesignStats_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FLevelDesignStats>();

#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_218_DELEGATE \
SLT_API void FOnActorPlaced_DelegateWrapper(const FMulticastScriptDelegate& OnActorPlaced, AActor* PlacedActor, FName AssetID, FVector const& Location);


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_219_DELEGATE \
SLT_API void FOnActorRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnActorRemoved, AActor* RemovedActor, FName AssetID);


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_220_DELEGATE \
SLT_API void FOnSelectionChanged_DelegateWrapper(const FMulticastScriptDelegate& OnSelectionChanged, TArray<AActor*> const& SelectedActors, bool bMultiSelect);


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_221_DELEGATE \
SLT_API void FOnPlacementModeChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPlacementModeChanged, EPlacementMode OldMode, EPlacementMode NewMode);


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCanRedo); \
	DECLARE_FUNCTION(execCanUndo); \
	DECLARE_FUNCTION(execRedo); \
	DECLARE_FUNCTION(execUndo); \
	DECLARE_FUNCTION(execGetDesignStatistics); \
	DECLARE_FUNCTION(execOptimizeLevel); \
	DECLARE_FUNCTION(execClearLevel); \
	DECLARE_FUNCTION(execLoadLevelLayout); \
	DECLARE_FUNCTION(execSaveLevelLayout); \
	DECLARE_FUNCTION(execCanPlaceAsset); \
	DECLARE_FUNCTION(execSearchAssets); \
	DECLARE_FUNCTION(execGetAssetData); \
	DECLARE_FUNCTION(execGetAssetsByCategory); \
	DECLARE_FUNCTION(execSnapLocationToSurface); \
	DECLARE_FUNCTION(execSnapLocationToGrid); \
	DECLARE_FUNCTION(execSetSnapMode); \
	DECLARE_FUNCTION(execSetPlacementMode); \
	DECLARE_FUNCTION(execInvertSelection); \
	DECLARE_FUNCTION(execClearSelection); \
	DECLARE_FUNCTION(execSelectActorsByCategory); \
	DECLARE_FUNCTION(execSelectActorsInArea); \
	DECLARE_FUNCTION(execSelectActor); \
	DECLARE_FUNCTION(execUngroupSelectedActors); \
	DECLARE_FUNCTION(execGroupSelectedActors); \
	DECLARE_FUNCTION(execDuplicateSelectedActors); \
	DECLARE_FUNCTION(execRemoveSelectedActors); \
	DECLARE_FUNCTION(execPlaceActorAtLocation); \
	DECLARE_FUNCTION(execSelectAssetForPlacement); \
	DECLARE_FUNCTION(execSetDesignModeActive);


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUDragDropLevelDesigner(); \
	friend struct Z_Construct_UClass_UDragDropLevelDesigner_Statics; \
public: \
	DECLARE_CLASS(UDragDropLevelDesigner, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UDragDropLevelDesigner)


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UDragDropLevelDesigner(UDragDropLevelDesigner&&); \
	UDragDropLevelDesigner(const UDragDropLevelDesigner&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UDragDropLevelDesigner); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UDragDropLevelDesigner); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UDragDropLevelDesigner) \
	NO_API virtual ~UDragDropLevelDesigner();


#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_223_PROLOG
#define FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h_226_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UDragDropLevelDesigner>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Design_DragDropLevelDesigner_h


#define FOREACH_ENUM_EDRAGDROPCATEGORY(op) \
	op(EDragDropCategory::Environment) \
	op(EDragDropCategory::Interactive) \
	op(EDragDropCategory::Enemies) \
	op(EDragDropCategory::Items) \
	op(EDragDropCategory::Puzzles) \
	op(EDragDropCategory::Triggers) \
	op(EDragDropCategory::Spawners) \
	op(EDragDropCategory::Decorative) \
	op(EDragDropCategory::Lighting) \
	op(EDragDropCategory::Audio) \
	op(EDragDropCategory::Custom) 

enum class EDragDropCategory : uint8;
template<> struct TIsUEnumClass<EDragDropCategory> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EDragDropCategory>();

#define FOREACH_ENUM_ESNAPMODE(op) \
	op(ESnapMode::None) \
	op(ESnapMode::Grid) \
	op(ESnapMode::Surface) \
	op(ESnapMode::Object) \
	op(ESnapMode::Smart) 

enum class ESnapMode : uint8;
template<> struct TIsUEnumClass<ESnapMode> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<ESnapMode>();

#define FOREACH_ENUM_EPLACEMENTMODE(op) \
	op(EPlacementMode::Single) \
	op(EPlacementMode::Paint) \
	op(EPlacementMode::Line) \
	op(EPlacementMode::Circle) \
	op(EPlacementMode::Rectangle) \
	op(EPlacementMode::Random) 

enum class EPlacementMode : uint8;
template<> struct TIsUEnumClass<EPlacementMode> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EPlacementMode>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
