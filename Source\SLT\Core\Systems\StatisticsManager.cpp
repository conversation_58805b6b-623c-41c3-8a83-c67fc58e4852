#include "StatisticsManager.h"
#include "Kismet/GameplayStatics.h"
#include "Core/Systems/InventorySaveGame.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/Engine.h"

UStatisticsManager::UStatisticsManager()
{
	// Initialize default statistics
	FPlayerStatistic KillsStat;
	KillsStat.StatisticID = TEXT("Kills");
	KillsStat.StatisticName = FText::FromString(TEXT("Enemies Killed"));
	KillsStat.StatisticType = EStatisticType::Counter;
	KillsStat.Categories.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Combat")));
	PlayerStatistics.Add(KillsStat.StatisticID, KillsStat);

	FPlayerStatistic PlayTimeStat;
	PlayTimeStat.StatisticID = TEXT("PlayTime");
	PlayTimeStat.StatisticName = FText::FromString(TEXT("Total Play Time"));
	PlayTimeStat.StatisticType = EStatisticType::Timer;
	PlayTimeStat.Categories.AddTag(FGameplayTag::RequestGameplayTag(TEXT("General")));
	PlayerStatistics.Add(PlayTimeStat.StatisticID, PlayTimeStat);

	FPlayerStatistic ItemsFoundStat;
	ItemsFoundStat.StatisticID = TEXT("ItemsFound");
	ItemsFoundStat.StatisticName = FText::FromString(TEXT("Items Found"));
	ItemsFoundStat.StatisticType = EStatisticType::Counter;
	ItemsFoundStat.Categories.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Collection")));
	PlayerStatistics.Add(ItemsFoundStat.StatisticID, ItemsFoundStat);

	// Initialize default achievements
	FAchievementData FirstKillAchievement;
	FirstKillAchievement.AchievementID = TEXT("FirstKill");
	FirstKillAchievement.AchievementName = FText::FromString(TEXT("First Blood"));
	FirstKillAchievement.AchievementDescription = FText::FromString(TEXT("Defeat your first enemy"));
	FirstKillAchievement.AchievementType = EAchievementType::Milestone;
	FirstKillAchievement.MaxProgress = 1.0f;
	FirstKillAchievement.Points = 10;
	FirstKillAchievement.RequiredStatistics.Add(TEXT("Kills"), 1.0f);
	PlayerAchievements.Add(FirstKillAchievement.AchievementID, FirstKillAchievement);

	FAchievementData CollectorAchievement;
	CollectorAchievement.AchievementID = TEXT("Collector");
	CollectorAchievement.AchievementName = FText::FromString(TEXT("Collector"));
	CollectorAchievement.AchievementDescription = FText::FromString(TEXT("Find 50 items"));
	CollectorAchievement.AchievementType = EAchievementType::Progress;
	CollectorAchievement.MaxProgress = 50.0f;
	CollectorAchievement.Points = 25;
	CollectorAchievement.RequiredStatistics.Add(TEXT("ItemsFound"), 50.0f);
	PlayerAchievements.Add(CollectorAchievement.AchievementID, CollectorAchievement);
}

void UStatisticsManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	LoadStatistics();
}

void UStatisticsManager::Deinitialize()
{
	SaveStatistics();
	Super::Deinitialize();
}

void UStatisticsManager::RegisterStatistic(FName StatisticID, const FPlayerStatistic& StatisticData)
{
	PlayerStatistics.Add(StatisticID, StatisticData);
}

void UStatisticsManager::UpdateStatistic(FName StatisticID, float Value, bool bIsIncrement)
{
	FPlayerStatistic* Statistic = PlayerStatistics.Find(StatisticID);
	if (!Statistic)
	{
		return;
	}

	float OldValue = Statistic->CurrentValue;
	
	if (bIsIncrement)
	{
		Statistic->CurrentValue += Value;
		Statistic->TotalValue += Value;
	}
	else
	{
		Statistic->CurrentValue = Value;
		Statistic->TotalValue += Value;
	}

	// Update min/max values
	if (Statistic->SampleCount == 0)
	{
		Statistic->MinValue = Statistic->CurrentValue;
		Statistic->MaxValue = Statistic->CurrentValue;
	}
	else
	{
		Statistic->MinValue = FMath::Min(Statistic->MinValue, Statistic->CurrentValue);
		if (Statistic->CurrentValue > Statistic->MaxValue)
		{
			Statistic->MaxValue = Statistic->CurrentValue;
			OnNewPersonalBest(StatisticID, Statistic->CurrentValue, OldValue);
		}
	}

	Statistic->SampleCount++;
	Statistic->LastUpdated = FDateTime::Now();

	// Track history if enabled
	if (Statistic->bTrackHistory)
	{
		Statistic->ValueHistory.Add(Statistic->CurrentValue);
		Statistic->TimeHistory.Add(Statistic->LastUpdated);

		// Keep only last 100 entries
		if (Statistic->ValueHistory.Num() > 100)
		{
			Statistic->ValueHistory.RemoveAt(0);
			Statistic->TimeHistory.RemoveAt(0);
		}
	}

	OnStatisticUpdated.Broadcast(StatisticID, Statistic->CurrentValue);
	CheckAchievementRequirements(StatisticID);
}

void UStatisticsManager::SetStatistic(FName StatisticID, float Value)
{
	UpdateStatistic(StatisticID, Value, false);
}

float UStatisticsManager::GetStatistic(FName StatisticID) const
{
	if (const FPlayerStatistic* Statistic = PlayerStatistics.Find(StatisticID))
	{
		return Statistic->CurrentValue;
	}
	return 0.0f;
}

FPlayerStatistic UStatisticsManager::GetStatisticData(FName StatisticID) const
{
	if (const FPlayerStatistic* Statistic = PlayerStatistics.Find(StatisticID))
	{
		return *Statistic;
	}
	return FPlayerStatistic();
}

TArray<FName> UStatisticsManager::GetStatisticsByCategory(const FGameplayTag& Category) const
{
	TArray<FName> FilteredStatistics;
	
	for (const auto& StatPair : PlayerStatistics)
	{
		if (StatPair.Value.Categories.HasTag(Category))
		{
			FilteredStatistics.Add(StatPair.Key);
		}
	}
	
	return FilteredStatistics;
}

void UStatisticsManager::ResetStatistic(FName StatisticID)
{
	if (FPlayerStatistic* Statistic = PlayerStatistics.Find(StatisticID))
	{
		Statistic->CurrentValue = 0.0f;
		Statistic->MaxValue = 0.0f;
		Statistic->MinValue = 0.0f;
		Statistic->TotalValue = 0.0f;
		Statistic->SampleCount = 0;
		Statistic->ValueHistory.Empty();
		Statistic->TimeHistory.Empty();
		Statistic->LastUpdated = FDateTime::Now();
	}
}

void UStatisticsManager::ResetAllStatistics()
{
	for (auto& StatPair : PlayerStatistics)
	{
		ResetStatistic(StatPair.Key);
	}
}

void UStatisticsManager::RegisterAchievement(FName AchievementID, const FAchievementData& AchievementData)
{
	PlayerAchievements.Add(AchievementID, AchievementData);
}

void UStatisticsManager::UnlockAchievement(FName AchievementID)
{
	FAchievementData* Achievement = PlayerAchievements.Find(AchievementID);
	if (!Achievement || Achievement->bIsUnlocked)
	{
		return;
	}

	Achievement->bIsUnlocked = true;
	Achievement->Progress = Achievement->MaxProgress;
	Achievement->UnlockDate = FDateTime::Now();

	OnAchievementUnlocked.Broadcast(AchievementID, *Achievement);
	
	UE_LOG(LogTemp, Log, TEXT("Achievement Unlocked: %s"), *Achievement->AchievementName.ToString());
}

void UStatisticsManager::UpdateAchievementProgress(FName AchievementID, float Progress)
{
	FAchievementData* Achievement = PlayerAchievements.Find(AchievementID);
	if (!Achievement || Achievement->bIsUnlocked)
	{
		return;
	}

	float OldProgress = Achievement->Progress;
	Achievement->Progress = FMath::Clamp(Progress, 0.0f, Achievement->MaxProgress);

	if (Achievement->Progress != OldProgress)
	{
		OnAchievementProgress.Broadcast(AchievementID, Achievement->Progress, Achievement->MaxProgress);

		// Check if achievement should be unlocked
		if (Achievement->Progress >= Achievement->MaxProgress)
		{
			UnlockAchievement(AchievementID);
		}
	}
}

bool UStatisticsManager::IsAchievementUnlocked(FName AchievementID) const
{
	if (const FAchievementData* Achievement = PlayerAchievements.Find(AchievementID))
	{
		return Achievement->bIsUnlocked;
	}
	return false;
}

float UStatisticsManager::GetAchievementProgress(FName AchievementID) const
{
	if (const FAchievementData* Achievement = PlayerAchievements.Find(AchievementID))
	{
		return Achievement->Progress;
	}
	return 0.0f;
}

TArray<FAchievementData> UStatisticsManager::GetUnlockedAchievements() const
{
	TArray<FAchievementData> UnlockedAchievements;
	
	for (const auto& AchievementPair : PlayerAchievements)
	{
		if (AchievementPair.Value.bIsUnlocked)
		{
			UnlockedAchievements.Add(AchievementPair.Value);
		}
	}
	
	return UnlockedAchievements;
}

TArray<FAchievementData> UStatisticsManager::GetAchievementsByType(EAchievementType AchievementType) const
{
	TArray<FAchievementData> FilteredAchievements;
	
	for (const auto& AchievementPair : PlayerAchievements)
	{
		if (AchievementPair.Value.AchievementType == AchievementType)
		{
			FilteredAchievements.Add(AchievementPair.Value);
		}
	}
	
	return FilteredAchievements;
}

int32 UStatisticsManager::GetTotalAchievementPoints() const
{
	int32 TotalPoints = 0;
	
	for (const auto& AchievementPair : PlayerAchievements)
	{
		if (AchievementPair.Value.bIsUnlocked)
		{
			TotalPoints += AchievementPair.Value.Points;
		}
	}
	
	return TotalPoints;
}

void UStatisticsManager::SubmitScore(const FString& LeaderboardName, float Score, const FString& PlayerName)
{
	FString ActualPlayerName = PlayerName.IsEmpty() ? GetDefaultPlayerName() : PlayerName;
	
	FLeaderboardEntry NewEntry;
	NewEntry.PlayerName = ActualPlayerName;
	NewEntry.Score = Score;
	NewEntry.PlayTime = GetStatistic(TEXT("PlayTime"));
	NewEntry.CompletionDate = FDateTime::Now();
	NewEntry.bIsLocalPlayer = true;

	TArray<FLeaderboardEntry>* Leaderboard = Leaderboards.Find(LeaderboardName);
	if (!Leaderboard)
	{
		Leaderboards.Add(LeaderboardName, TArray<FLeaderboardEntry>());
		Leaderboard = Leaderboards.Find(LeaderboardName);
	}

	Leaderboard->Add(NewEntry);
	SortLeaderboard(LeaderboardName);
	OnLeaderboardUpdated.Broadcast(LeaderboardName, *Leaderboard);
}

TArray<FLeaderboardEntry> UStatisticsManager::GetLeaderboard(const FString& LeaderboardName, int32 MaxEntries) const
{
	if (const TArray<FLeaderboardEntry>* Leaderboard = Leaderboards.Find(LeaderboardName))
	{
		TArray<FLeaderboardEntry> Result = *Leaderboard;
		if (MaxEntries > 0 && Result.Num() > MaxEntries)
		{
			Result.SetNum(MaxEntries);
		}
		return Result;
	}
	return TArray<FLeaderboardEntry>();
}

int32 UStatisticsManager::GetPlayerRank(const FString& LeaderboardName, const FString& PlayerName) const
{
	FString ActualPlayerName = PlayerName.IsEmpty() ? GetDefaultPlayerName() : PlayerName;
	
	if (const TArray<FLeaderboardEntry>* Leaderboard = Leaderboards.Find(LeaderboardName))
	{
		for (int32 i = 0; i < Leaderboard->Num(); i++)
		{
			if ((*Leaderboard)[i].PlayerName == ActualPlayerName)
			{
				return i + 1; // 1-based ranking
			}
		}
	}
	return 0; // Not found
}

void UStatisticsManager::ClearLeaderboard(const FString& LeaderboardName)
{
	if (TArray<FLeaderboardEntry>* Leaderboard = Leaderboards.Find(LeaderboardName))
	{
		Leaderboard->Empty();
		OnLeaderboardUpdated.Broadcast(LeaderboardName, *Leaderboard);
	}
}

void UStatisticsManager::TrackEvent(const FString& EventName, const TMap<FString, FString>& Parameters)
{
	EventTimestamps.Add(EventName, FDateTime::Now());
	
	if (int32* Count = EventCounts.Find(EventName))
	{
		(*Count)++;
	}
	else
	{
		EventCounts.Add(EventName, 1);
	}

	UE_LOG(LogTemp, Log, TEXT("Event Tracked: %s"), *EventName);
}

void UStatisticsManager::TrackPlayerAction(const FString& Action, const FString& Context)
{
	TMap<FString, FString> Parameters;
	Parameters.Add(TEXT("Context"), Context);
	TrackEvent(Action, Parameters);
}

TMap<FString, float> UStatisticsManager::GetPlaytimeStatistics() const
{
	TMap<FString, float> PlaytimeStats;
	
	for (const auto& StatPair : PlayerStatistics)
	{
		if (StatPair.Value.StatisticType == EStatisticType::Timer)
		{
			PlaytimeStats.Add(StatPair.Key.ToString(), StatPair.Value.CurrentValue);
		}
	}
	
	return PlaytimeStats;
}

TMap<FString, int32> UStatisticsManager::GetUsageStatistics() const
{
	return EventCounts;
}

bool UStatisticsManager::SaveStatistics()
{
	if (UInventorySaveGame* SaveGame = Cast<UInventorySaveGame>(UGameplayStatics::CreateSaveGameObject(UInventorySaveGame::StaticClass())))
	{
		// Save statistics
		for (const auto& StatPair : PlayerStatistics)
		{
			FString Key = FString::Printf(TEXT("Stat_%s"), *StatPair.Key.ToString());
			SaveGame->SetCustomData(Key, FString::SanitizeFloat(StatPair.Value.CurrentValue));
		}

		// Save achievements
		for (const auto& AchievementPair : PlayerAchievements)
		{
			FString UnlockedKey = FString::Printf(TEXT("Achievement_%s_Unlocked"), *AchievementPair.Key.ToString());
			FString ProgressKey = FString::Printf(TEXT("Achievement_%s_Progress"), *AchievementPair.Key.ToString());
			
			SaveGame->SetCustomData(UnlockedKey, AchievementPair.Value.bIsUnlocked ? TEXT("1") : TEXT("0"));
			SaveGame->SetCustomData(ProgressKey, FString::SanitizeFloat(AchievementPair.Value.Progress));
		}

		return UGameplayStatics::SaveGameToSlot(SaveGame, TEXT("StatisticsSave"), 0);
	}
	return false;
}

bool UStatisticsManager::LoadStatistics()
{
	if (UInventorySaveGame* SaveGame = Cast<UInventorySaveGame>(UGameplayStatics::LoadGameFromSlot(TEXT("StatisticsSave"), 0)))
	{
		// Load statistics
		for (auto& StatPair : PlayerStatistics)
		{
			FString Key = FString::Printf(TEXT("Stat_%s"), *StatPair.Key.ToString());
			FString ValueString = SaveGame->GetCustomData(Key);
			if (!ValueString.IsEmpty())
			{
				StatPair.Value.CurrentValue = FCString::Atof(*ValueString);
			}
		}

		// Load achievements
		for (auto& AchievementPair : PlayerAchievements)
		{
			FString UnlockedKey = FString::Printf(TEXT("Achievement_%s_Unlocked"), *AchievementPair.Key.ToString());
			FString ProgressKey = FString::Printf(TEXT("Achievement_%s_Progress"), *AchievementPair.Key.ToString());
			
			FString UnlockedString = SaveGame->GetCustomData(UnlockedKey);
			FString ProgressString = SaveGame->GetCustomData(ProgressKey);
			
			if (!UnlockedString.IsEmpty())
			{
				AchievementPair.Value.bIsUnlocked = (UnlockedString == TEXT("1"));
			}
			
			if (!ProgressString.IsEmpty())
			{
				AchievementPair.Value.Progress = FCString::Atof(*ProgressString);
			}
		}

		OnStatisticsLoaded();
		return true;
	}
	return false;
}

void UStatisticsManager::CheckAchievementRequirements(FName StatisticID)
{
	for (auto& AchievementPair : PlayerAchievements)
	{
		FAchievementData& Achievement = AchievementPair.Value;
		
		if (Achievement.bIsUnlocked)
		{
			continue;
		}

		// Check if this statistic affects this achievement
		if (const float* RequiredValue = Achievement.RequiredStatistics.Find(StatisticID))
		{
			float CurrentValue = GetStatistic(StatisticID);
			float Progress = FMath::Min(CurrentValue, *RequiredValue);
			
			UpdateAchievementProgress(AchievementPair.Key, Progress);
		}
	}
}

void UStatisticsManager::SortLeaderboard(const FString& LeaderboardName)
{
	if (TArray<FLeaderboardEntry>* Leaderboard = Leaderboards.Find(LeaderboardName))
	{
		// Sort by score (descending)
		Leaderboard->Sort([](const FLeaderboardEntry& A, const FLeaderboardEntry& B)
		{
			return A.Score > B.Score;
		});

		// Update ranks
		for (int32 i = 0; i < Leaderboard->Num(); i++)
		{
			(*Leaderboard)[i].Rank = i + 1;
		}
	}
}

FString UStatisticsManager::GetDefaultPlayerName() const
{
	return TEXT("Player");
}

void UStatisticsManager::ExportStatistics(const FString& FilePath) const
{
	// This would export statistics to a file for analysis
	UE_LOG(LogTemp, Log, TEXT("Exporting statistics to: %s"), *FilePath);
}

bool UStatisticsManager::ImportStatistics(const FString& FilePath)
{
	// This would import statistics from a file
	UE_LOG(LogTemp, Log, TEXT("Importing statistics from: %s"), *FilePath);
	return false;
}
