{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\slt\\source\\slt\\core\\design\\visualdesigntools.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\slteditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\definitions.slt.h", "g:\\gamedev\\slt\\source\\slt\\core\\design\\visualdesigntools.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\visualdesigntools.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\widgetcomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\blueprint\\userwidget.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetchild.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetchild.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsavecontext.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\cookenums.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\coreuobject\\public\\cooker\\cookdependency.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\serialization\\compactbinary.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\slatewrappertypes.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\slatewrappertypes.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\widget.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\binding\\states\\widgetstatebitfield.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetstatebitfield.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\fieldnotificationdeclaration.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\ifieldnotificationclassdescriptor.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\fieldnotification\\public\\inotifyfieldvaluechanged.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\fieldnotification\\uht\\inotifyfieldvaluechanged.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\visual.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\visual.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\slate\\widgettransform.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgettransform.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\blueprint\\widgetnavigation.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\slatecore\\public\\types\\navigationmetadata.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetnavigation.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widget.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\components\\namedslotinterface.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\namedslotinterface.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\slate\\public\\widgets\\layout\\anchors.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\slate\\uht\\anchors.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\umg\\public\\animation\\widgetanimationevents.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetanimationevents.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\userwidget.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\umg\\uht\\widgetcomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\textrendercomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\textrendercomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}