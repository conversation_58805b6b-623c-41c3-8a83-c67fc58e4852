// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Interfaces/Interactable.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class APawn;
struct FInteractionData;
#ifdef SLT_Interactable_generated_h
#error "Interactable.generated.h already included, missing '#pragma once' in Interactable.h"
#endif
#define SLT_Interactable_generated_h

#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_27_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FInteractionData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FInteractionData>();

#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCheckInteractionRequirements); \
	DECLARE_FUNCTION(execGetInteractionLocation); \
	DECLARE_FUNCTION(execOnInteractionCancel); \
	DECLARE_FUNCTION(execOnInteractionComplete); \
	DECLARE_FUNCTION(execOnInteractionStart); \
	DECLARE_FUNCTION(execCanInteract); \
	DECLARE_FUNCTION(execGetInteractionData);


#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	SLT_API UInteractable(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UInteractable(UInteractable&&); \
	UInteractable(const UInteractable&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(SLT_API, UInteractable); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UInteractable); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UInteractable) \
	SLT_API virtual ~UInteractable();


#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesUInteractable(); \
	friend struct Z_Construct_UClass_UInteractable_Statics; \
public: \
	DECLARE_CLASS(UInteractable, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/SLT"), SLT_API) \
	DECLARE_SERIALIZER(UInteractable)


#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_GENERATED_UINTERFACE_BODY() \
	FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IInteractable() {} \
public: \
	typedef UInteractable UClassType; \
	typedef IInteractable ThisClass; \
	static bool Execute_CanInteract(const UObject* O, APawn* InteractingPawn); \
	static bool Execute_CheckInteractionRequirements(const UObject* O, APawn* InteractingPawn); \
	static FInteractionData Execute_GetInteractionData(const UObject* O); \
	static FVector Execute_GetInteractionLocation(const UObject* O); \
	static void Execute_OnInteractionCancel(UObject* O, APawn* InteractingPawn); \
	static void Execute_OnInteractionComplete(UObject* O, APawn* InteractingPawn); \
	static void Execute_OnInteractionStart(UObject* O, APawn* InteractingPawn); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_82_PROLOG
#define FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_93_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Interfaces_Interactable_h_85_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UInteractable>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Interfaces_Interactable_h


#define FOREACH_ENUM_EINTERACTIONTYPE(op) \
	op(EInteractionType::None) \
	op(EInteractionType::Pickup) \
	op(EInteractionType::Use) \
	op(EInteractionType::Examine) \
	op(EInteractionType::Open) \
	op(EInteractionType::Activate) \
	op(EInteractionType::Talk) \
	op(EInteractionType::Custom) 

enum class EInteractionType : uint8;
template<> struct TIsUEnumClass<EInteractionType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EInteractionType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
