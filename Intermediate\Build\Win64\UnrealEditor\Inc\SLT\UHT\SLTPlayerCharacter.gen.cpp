// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Player/SLTPlayerCharacter.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeSLTPlayerCharacter() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
ENGINE_API UClass* Z_Construct_UClass_ACharacter();
ENGINE_API UClass* Z_Construct_UClass_UCameraComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_USpringArmComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputMappingContext_NoRegister();
SLT_API UClass* Z_Construct_UClass_ASLTPlayerCharacter();
SLT_API UClass* Z_Construct_UClass_ASLTPlayerCharacter_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInteractionComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventoryGridComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventoryInterface_NoRegister();
SLT_API UClass* Z_Construct_UClass_UResourceManager_NoRegister();
SLT_API UClass* Z_Construct_UClass_UWeaponComponent_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlot();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnInventoryToggled
struct Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics
{
	struct SLTPlayerCharacter_eventOnInventoryToggled_Parms
	{
		bool bIsOpen;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_bIsOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsOpen;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::NewProp_bIsOpen_SetBit(void* Obj)
{
	((SLTPlayerCharacter_eventOnInventoryToggled_Parms*)Obj)->bIsOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::NewProp_bIsOpen = { "bIsOpen", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SLTPlayerCharacter_eventOnInventoryToggled_Parms), &Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::NewProp_bIsOpen_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::NewProp_bIsOpen,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OnInventoryToggled__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::SLTPlayerCharacter_eventOnInventoryToggled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::SLTPlayerCharacter_eventOnInventoryToggled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void ASLTPlayerCharacter::FOnInventoryToggled_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryToggled, bool bIsOpen)
{
	struct SLTPlayerCharacter_eventOnInventoryToggled_Parms
	{
		bool bIsOpen;
	};
	SLTPlayerCharacter_eventOnInventoryToggled_Parms Parms;
	Parms.bIsOpen=bIsOpen ? true : false;
	OnInventoryToggled.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryToggled

// Begin Class ASLTPlayerCharacter Function CloseInventory
struct Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "CloseInventory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASLTPlayerCharacter::execCloseInventory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CloseInventory();
	P_NATIVE_END;
}
// End Class ASLTPlayerCharacter Function CloseInventory

// Begin Class ASLTPlayerCharacter Function IsInventoryOpen
struct Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics
{
	struct SLTPlayerCharacter_eventIsInventoryOpen_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((SLTPlayerCharacter_eventIsInventoryOpen_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(SLTPlayerCharacter_eventIsInventoryOpen_Parms), &Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "IsInventoryOpen", nullptr, nullptr, Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::PropPointers), sizeof(Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::SLTPlayerCharacter_eventIsInventoryOpen_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::SLTPlayerCharacter_eventIsInventoryOpen_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASLTPlayerCharacter::execIsInventoryOpen)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInventoryOpen();
	P_NATIVE_END;
}
// End Class ASLTPlayerCharacter Function IsInventoryOpen

// Begin Class ASLTPlayerCharacter Function OnInventoryClosed
static const FName NAME_ASLTPlayerCharacter_OnInventoryClosed = FName(TEXT("OnInventoryClosed"));
void ASLTPlayerCharacter::OnInventoryClosed()
{
	UFunction* Func = FindFunctionChecked(NAME_ASLTPlayerCharacter_OnInventoryClosed);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OnInventoryClosed", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ASLTPlayerCharacter Function OnInventoryClosed

// Begin Class ASLTPlayerCharacter Function OnInventoryOpened
static const FName NAME_ASLTPlayerCharacter_OnInventoryOpened = FName(TEXT("OnInventoryOpened"));
void ASLTPlayerCharacter::OnInventoryOpened()
{
	UFunction* Func = FindFunctionChecked(NAME_ASLTPlayerCharacter_OnInventoryOpened);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OnInventoryOpened", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ASLTPlayerCharacter Function OnInventoryOpened

// Begin Class ASLTPlayerCharacter Function OnItemPickedUp
struct SLTPlayerCharacter_eventOnItemPickedUp_Parms
{
	FInventorySlot PickedUpSlot;
};
static const FName NAME_ASLTPlayerCharacter_OnItemPickedUp = FName(TEXT("OnItemPickedUp"));
void ASLTPlayerCharacter::OnItemPickedUp(FInventorySlot const& PickedUpSlot)
{
	SLTPlayerCharacter_eventOnItemPickedUp_Parms Parms;
	Parms.PickedUpSlot=PickedUpSlot;
	UFunction* Func = FindFunctionChecked(NAME_ASLTPlayerCharacter_OnItemPickedUp);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PickedUpSlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PickedUpSlot;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::NewProp_PickedUpSlot = { "PickedUpSlot", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SLTPlayerCharacter_eventOnItemPickedUp_Parms, PickedUpSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PickedUpSlot_MetaData), NewProp_PickedUpSlot_MetaData) }; // 2390674128
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::NewProp_PickedUpSlot,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OnItemPickedUp", nullptr, nullptr, Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::PropPointers), sizeof(SLTPlayerCharacter_eventOnItemPickedUp_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::Function_MetaDataParams) };
static_assert(sizeof(SLTPlayerCharacter_eventOnItemPickedUp_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ASLTPlayerCharacter Function OnItemPickedUp

// Begin Class ASLTPlayerCharacter Function OnItemUsed
struct SLTPlayerCharacter_eventOnItemUsed_Parms
{
	FName ItemID;
	int32 Quantity;
};
static const FName NAME_ASLTPlayerCharacter_OnItemUsed = FName(TEXT("OnItemUsed"));
void ASLTPlayerCharacter::OnItemUsed(FName ItemID, int32 Quantity)
{
	SLTPlayerCharacter_eventOnItemUsed_Parms Parms;
	Parms.ItemID=ItemID;
	Parms.Quantity=Quantity;
	UFunction* Func = FindFunctionChecked(NAME_ASLTPlayerCharacter_OnItemUsed);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SLTPlayerCharacter_eventOnItemUsed_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(SLTPlayerCharacter_eventOnItemUsed_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::NewProp_Quantity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OnItemUsed", nullptr, nullptr, Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::PropPointers), sizeof(SLTPlayerCharacter_eventOnItemUsed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::Function_MetaDataParams) };
static_assert(sizeof(SLTPlayerCharacter_eventOnItemUsed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ASLTPlayerCharacter Function OnItemUsed

// Begin Class ASLTPlayerCharacter Function OpenInventory
struct Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "OpenInventory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASLTPlayerCharacter::execOpenInventory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OpenInventory();
	P_NATIVE_END;
}
// End Class ASLTPlayerCharacter Function OpenInventory

// Begin Class ASLTPlayerCharacter Function ToggleInventory
struct Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Inventory" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Public functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Public functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ASLTPlayerCharacter, nullptr, "ToggleInventory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ASLTPlayerCharacter::execToggleInventory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ToggleInventory();
	P_NATIVE_END;
}
// End Class ASLTPlayerCharacter Function ToggleInventory

// Begin Class ASLTPlayerCharacter
void ASLTPlayerCharacter::StaticRegisterNativesASLTPlayerCharacter()
{
	UClass* Class = ASLTPlayerCharacter::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CloseInventory", &ASLTPlayerCharacter::execCloseInventory },
		{ "IsInventoryOpen", &ASLTPlayerCharacter::execIsInventoryOpen },
		{ "OpenInventory", &ASLTPlayerCharacter::execOpenInventory },
		{ "ToggleInventory", &ASLTPlayerCharacter::execToggleInventory },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ASLTPlayerCharacter);
UClass* Z_Construct_UClass_ASLTPlayerCharacter_NoRegister()
{
	return ASLTPlayerCharacter::StaticClass();
}
struct Z_Construct_UClass_ASLTPlayerCharacter_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "HideCategories", "Navigation" },
		{ "IncludePath", "Core/Player/SLTPlayerCharacter.h" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CameraBoom_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Camera" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Camera components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Camera components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FollowCamera_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Camera" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Systems" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// System components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "System components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Systems" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceManager_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Systems" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WeaponComponent_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Systems" },
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultMappingContext_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Input Mapping Context\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Mapping Context" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_JumpAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Input Actions\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input Actions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MoveAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LookAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OpenInventoryAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RotateItemAction_MetaData[] = {
		{ "AllowPrivateAccess", "true" },
		{ "Category", "Input" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryWidgetClass_MetaData[] = {
		{ "Category", "UI" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Inventory UI\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Inventory UI" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInventoryToggled_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInventoryOpen_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryWidget_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Player/SLTPlayerCharacter.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CameraBoom;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_FollowCamera;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractionComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ResourceManager;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_WeaponComponent;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_DefaultMappingContext;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_JumpAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_MoveAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LookAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_OpenInventoryAction;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_RotateItemAction;
	static const UECodeGen_Private::FClassPropertyParams NewProp_InventoryWidgetClass;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInventoryToggled;
	static void NewProp_bIsInventoryOpen_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInventoryOpen;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryWidget;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_CloseInventory, "CloseInventory" }, // 1170213087
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_IsInventoryOpen, "IsInventoryOpen" }, // 2160194710
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryClosed, "OnInventoryClosed" }, // 1988565732
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_OnInventoryOpened, "OnInventoryOpened" }, // 1513634553
		{ &Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature, "OnInventoryToggled__DelegateSignature" }, // 1393459856
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_OnItemPickedUp, "OnItemPickedUp" }, // 2380667005
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_OnItemUsed, "OnItemUsed" }, // 2600702136
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_OpenInventory, "OpenInventory" }, // 1192179819
		{ &Z_Construct_UFunction_ASLTPlayerCharacter_ToggleInventory, "ToggleInventory" }, // 3726872026
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ASLTPlayerCharacter>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_CameraBoom = { "CameraBoom", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, CameraBoom), Z_Construct_UClass_USpringArmComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CameraBoom_MetaData), NewProp_CameraBoom_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_FollowCamera = { "FollowCamera", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, FollowCamera), Z_Construct_UClass_UCameraComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FollowCamera_MetaData), NewProp_FollowCamera_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryComponent = { "InventoryComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, InventoryComponent), Z_Construct_UClass_UInventoryGridComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryComponent_MetaData), NewProp_InventoryComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InteractionComponent = { "InteractionComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, InteractionComponent), Z_Construct_UClass_UInteractionComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionComponent_MetaData), NewProp_InteractionComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_ResourceManager = { "ResourceManager", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, ResourceManager), Z_Construct_UClass_UResourceManager_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceManager_MetaData), NewProp_ResourceManager_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_WeaponComponent = { "WeaponComponent", nullptr, (EPropertyFlags)0x00100000000a001d, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, WeaponComponent), Z_Construct_UClass_UWeaponComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WeaponComponent_MetaData), NewProp_WeaponComponent_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_DefaultMappingContext = { "DefaultMappingContext", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, DefaultMappingContext), Z_Construct_UClass_UInputMappingContext_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultMappingContext_MetaData), NewProp_DefaultMappingContext_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_JumpAction = { "JumpAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, JumpAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_JumpAction_MetaData), NewProp_JumpAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_MoveAction = { "MoveAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, MoveAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MoveAction_MetaData), NewProp_MoveAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_LookAction = { "LookAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, LookAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LookAction_MetaData), NewProp_LookAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InteractAction = { "InteractAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, InteractAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractAction_MetaData), NewProp_InteractAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_OpenInventoryAction = { "OpenInventoryAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, OpenInventoryAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OpenInventoryAction_MetaData), NewProp_OpenInventoryAction_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_RotateItemAction = { "RotateItemAction", nullptr, (EPropertyFlags)0x0010000000000015, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, RotateItemAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RotateItemAction_MetaData), NewProp_RotateItemAction_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryWidgetClass = { "InventoryWidgetClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, InventoryWidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryWidgetClass_MetaData), NewProp_InventoryWidgetClass_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_OnInventoryToggled = { "OnInventoryToggled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, OnInventoryToggled), Z_Construct_UDelegateFunction_ASLTPlayerCharacter_OnInventoryToggled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInventoryToggled_MetaData), NewProp_OnInventoryToggled_MetaData) }; // 1393459856
void Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_bIsInventoryOpen_SetBit(void* Obj)
{
	((ASLTPlayerCharacter*)Obj)->bIsInventoryOpen = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_bIsInventoryOpen = { "bIsInventoryOpen", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ASLTPlayerCharacter), &Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_bIsInventoryOpen_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInventoryOpen_MetaData), NewProp_bIsInventoryOpen_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryWidget = { "InventoryWidget", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ASLTPlayerCharacter, InventoryWidget), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryWidget_MetaData), NewProp_InventoryWidget_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ASLTPlayerCharacter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_CameraBoom,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_FollowCamera,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InteractionComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_ResourceManager,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_WeaponComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_DefaultMappingContext,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_JumpAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_MoveAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_LookAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InteractAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_OpenInventoryAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_RotateItemAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryWidgetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_OnInventoryToggled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_bIsInventoryOpen,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ASLTPlayerCharacter_Statics::NewProp_InventoryWidget,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASLTPlayerCharacter_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ASLTPlayerCharacter_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_ACharacter,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ASLTPlayerCharacter_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UInventoryInterface_NoRegister, (int32)VTABLE_OFFSET(ASLTPlayerCharacter, IInventoryInterface), false },  // 3420551761
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_ASLTPlayerCharacter_Statics::ClassParams = {
	&ASLTPlayerCharacter::StaticClass,
	"Game",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ASLTPlayerCharacter_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ASLTPlayerCharacter_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x009000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ASLTPlayerCharacter_Statics::Class_MetaDataParams), Z_Construct_UClass_ASLTPlayerCharacter_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ASLTPlayerCharacter()
{
	if (!Z_Registration_Info_UClass_ASLTPlayerCharacter.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ASLTPlayerCharacter.OuterSingleton, Z_Construct_UClass_ASLTPlayerCharacter_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ASLTPlayerCharacter.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<ASLTPlayerCharacter>()
{
	return ASLTPlayerCharacter::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ASLTPlayerCharacter);
ASLTPlayerCharacter::~ASLTPlayerCharacter() {}
// End Class ASLTPlayerCharacter

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ASLTPlayerCharacter, ASLTPlayerCharacter::StaticClass, TEXT("ASLTPlayerCharacter"), &Z_Registration_Info_UClass_ASLTPlayerCharacter, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ASLTPlayerCharacter), 2586545015U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_2808237949(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
