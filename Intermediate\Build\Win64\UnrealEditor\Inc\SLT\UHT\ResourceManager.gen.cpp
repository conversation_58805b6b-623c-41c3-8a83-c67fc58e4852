// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/ResourceManager.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeResourceManager() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
SLT_API UClass* Z_Construct_UClass_UResourceManager();
SLT_API UClass* Z_Construct_UClass_UResourceManager_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EResourceScarcityLevel();
SLT_API UEnum* Z_Construct_UEnum_SLT_EResourceType();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FResourceConsumptionRule();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FResourceData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EResourceType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EResourceType;
static UEnum* EResourceType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EResourceType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EResourceType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EResourceType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EResourceType"));
	}
	return Z_Registration_Info_UEnum_EResourceType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EResourceType>()
{
	return EResourceType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EResourceType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Ammo.DisplayName", "Ammo" },
		{ "Ammo.Name", "EResourceType::Ammo" },
		{ "BlueprintType", "true" },
		{ "Currency.DisplayName", "Currency" },
		{ "Currency.Name", "EResourceType::Currency" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EResourceType::Custom" },
		{ "Energy.DisplayName", "Energy" },
		{ "Energy.Name", "EResourceType::Energy" },
		{ "Experience.DisplayName", "Experience" },
		{ "Experience.Name", "EResourceType::Experience" },
		{ "Health.DisplayName", "Health" },
		{ "Health.Name", "EResourceType::Health" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EResourceType::Health", (int64)EResourceType::Health },
		{ "EResourceType::Ammo", (int64)EResourceType::Ammo },
		{ "EResourceType::Currency", (int64)EResourceType::Currency },
		{ "EResourceType::Energy", (int64)EResourceType::Energy },
		{ "EResourceType::Experience", (int64)EResourceType::Experience },
		{ "EResourceType::Custom", (int64)EResourceType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EResourceType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EResourceType",
	"EResourceType",
	Z_Construct_UEnum_SLT_EResourceType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EResourceType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EResourceType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EResourceType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EResourceType()
{
	if (!Z_Registration_Info_UEnum_EResourceType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EResourceType.InnerSingleton, Z_Construct_UEnum_SLT_EResourceType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EResourceType.InnerSingleton;
}
// End Enum EResourceType

// Begin Enum EResourceScarcityLevel
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EResourceScarcityLevel;
static UEnum* EResourceScarcityLevel_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EResourceScarcityLevel.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EResourceScarcityLevel.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EResourceScarcityLevel, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EResourceScarcityLevel"));
	}
	return Z_Registration_Info_UEnum_EResourceScarcityLevel.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EResourceScarcityLevel>()
{
	return EResourceScarcityLevel_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Abundant.DisplayName", "Abundant" },
		{ "Abundant.Name", "EResourceScarcityLevel::Abundant" },
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EResourceScarcityLevel::Critical" },
		{ "Empty.DisplayName", "Empty" },
		{ "Empty.Name", "EResourceScarcityLevel::Empty" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
		{ "Normal.DisplayName", "Normal" },
		{ "Normal.Name", "EResourceScarcityLevel::Normal" },
		{ "Scarce.DisplayName", "Scarce" },
		{ "Scarce.Name", "EResourceScarcityLevel::Scarce" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EResourceScarcityLevel::Abundant", (int64)EResourceScarcityLevel::Abundant },
		{ "EResourceScarcityLevel::Normal", (int64)EResourceScarcityLevel::Normal },
		{ "EResourceScarcityLevel::Scarce", (int64)EResourceScarcityLevel::Scarce },
		{ "EResourceScarcityLevel::Critical", (int64)EResourceScarcityLevel::Critical },
		{ "EResourceScarcityLevel::Empty", (int64)EResourceScarcityLevel::Empty },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EResourceScarcityLevel",
	"EResourceScarcityLevel",
	Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EResourceScarcityLevel()
{
	if (!Z_Registration_Info_UEnum_EResourceScarcityLevel.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EResourceScarcityLevel.InnerSingleton, Z_Construct_UEnum_SLT_EResourceScarcityLevel_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EResourceScarcityLevel.InnerSingleton;
}
// End Enum EResourceScarcityLevel

// Begin ScriptStruct FResourceData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ResourceData;
class UScriptStruct* FResourceData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ResourceData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ResourceData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FResourceData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ResourceData"));
	}
	return Z_Registration_Info_UScriptStruct_ResourceData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FResourceData>()
{
	return FResourceData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FResourceData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceType_MetaData[] = {
		{ "Category", "Resource" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceID_MetaData[] = {
		{ "Category", "Resource" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceName_MetaData[] = {
		{ "Category", "Resource" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAmount_MetaData[] = {
		{ "Category", "Values" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current values\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current values" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxAmount_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinAmount_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RegenerationRate_MetaData[] = {
		{ "Category", "Regeneration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Regeneration/Decay\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Regeneration/Decay" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DecayRate_MetaData[] = {
		{ "Category", "Regeneration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Per second\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Per second" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanRegenerate_MetaData[] = {
		{ "Category", "Regeneration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Per second\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Per second" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bCanDecay_MetaData[] = {
		{ "Category", "Regeneration" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bHasMaxLimit_MetaData[] = {
		{ "Category", "Regeneration" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarcityLevel_MetaData[] = {
		{ "Category", "Scarcity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Scarcity management\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Scarcity management" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdateTime_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal tracking\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProperties_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ResourceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ResourceName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RegenerationRate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DecayRate;
	static void NewProp_bCanRegenerate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanRegenerate;
	static void NewProp_bCanDecay_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bCanDecay;
	static void NewProp_bHasMaxLimit_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bHasMaxLimit;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ScarcityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ScarcityLevel;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUpdateTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProperties;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FResourceData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, ResourceType), Z_Construct_UEnum_SLT_EResourceType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceType_MetaData), NewProp_ResourceType_MetaData) }; // 836268483
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, ResourceID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceID_MetaData), NewProp_ResourceID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceName = { "ResourceName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, ResourceName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceName_MetaData), NewProp_ResourceName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CurrentAmount = { "CurrentAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, CurrentAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAmount_MetaData), NewProp_CurrentAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_MaxAmount = { "MaxAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, MaxAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxAmount_MetaData), NewProp_MaxAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_MinAmount = { "MinAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, MinAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinAmount_MetaData), NewProp_MinAmount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_RegenerationRate = { "RegenerationRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, RegenerationRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RegenerationRate_MetaData), NewProp_RegenerationRate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_DecayRate = { "DecayRate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, DecayRate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DecayRate_MetaData), NewProp_DecayRate_MetaData) };
void Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanRegenerate_SetBit(void* Obj)
{
	((FResourceData*)Obj)->bCanRegenerate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanRegenerate = { "bCanRegenerate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResourceData), &Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanRegenerate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanRegenerate_MetaData), NewProp_bCanRegenerate_MetaData) };
void Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanDecay_SetBit(void* Obj)
{
	((FResourceData*)Obj)->bCanDecay = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanDecay = { "bCanDecay", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResourceData), &Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanDecay_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bCanDecay_MetaData), NewProp_bCanDecay_MetaData) };
void Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bHasMaxLimit_SetBit(void* Obj)
{
	((FResourceData*)Obj)->bHasMaxLimit = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bHasMaxLimit = { "bHasMaxLimit", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResourceData), &Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bHasMaxLimit_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bHasMaxLimit_MetaData), NewProp_bHasMaxLimit_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ScarcityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ScarcityLevel = { "ScarcityLevel", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, ScarcityLevel), Z_Construct_UEnum_SLT_EResourceScarcityLevel, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarcityLevel_MetaData), NewProp_ScarcityLevel_MetaData) }; // 272041803
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_LastUpdateTime = { "LastUpdateTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, LastUpdateTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdateTime_MetaData), NewProp_LastUpdateTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties_ValueProp = { "CustomProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties_Key_KeyProp = { "CustomProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties = { "CustomProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceData, CustomProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProperties_MetaData), NewProp_CustomProperties_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FResourceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ResourceName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CurrentAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_MaxAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_MinAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_RegenerationRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_DecayRate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanRegenerate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bCanDecay,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_bHasMaxLimit,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ScarcityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_ScarcityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_LastUpdateTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceData_Statics::NewProp_CustomProperties,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FResourceData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ResourceData",
	Z_Construct_UScriptStruct_FResourceData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceData_Statics::PropPointers),
	sizeof(FResourceData),
	alignof(FResourceData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FResourceData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FResourceData()
{
	if (!Z_Registration_Info_UScriptStruct_ResourceData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ResourceData.InnerSingleton, Z_Construct_UScriptStruct_FResourceData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ResourceData.InnerSingleton;
}
// End ScriptStruct FResourceData

// Begin ScriptStruct FResourceConsumptionRule
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ResourceConsumptionRule;
class UScriptStruct* FResourceConsumptionRule::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FResourceConsumptionRule, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ResourceConsumptionRule"));
	}
	return Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FResourceConsumptionRule>()
{
	return FResourceConsumptionRule::StaticStruct();
}
struct Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTag_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// What action triggers this consumption\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "What action triggers this consumption" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceID_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Which resource to consume\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Which resource to consume" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConsumptionAmount_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How much to consume\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How much to consume" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsPercentage_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is the amount a percentage of max?\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is the amount a percentage of max?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequireFullAmount_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Must have full amount to perform action?\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Must have full amount to perform action?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority for multiple rules\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority for multiple rules" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ConsumptionAmount;
	static void NewProp_bIsPercentage_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsPercentage;
	static void NewProp_bRequireFullAmount_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequireFullAmount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FResourceConsumptionRule>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceConsumptionRule, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTag_MetaData), NewProp_ActionTag_MetaData) }; // 1298103297
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceConsumptionRule, ResourceID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceID_MetaData), NewProp_ResourceID_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ConsumptionAmount = { "ConsumptionAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceConsumptionRule, ConsumptionAmount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConsumptionAmount_MetaData), NewProp_ConsumptionAmount_MetaData) };
void Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bIsPercentage_SetBit(void* Obj)
{
	((FResourceConsumptionRule*)Obj)->bIsPercentage = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bIsPercentage = { "bIsPercentage", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResourceConsumptionRule), &Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bIsPercentage_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsPercentage_MetaData), NewProp_bIsPercentage_MetaData) };
void Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bRequireFullAmount_SetBit(void* Obj)
{
	((FResourceConsumptionRule*)Obj)->bRequireFullAmount = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bRequireFullAmount = { "bRequireFullAmount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FResourceConsumptionRule), &Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bRequireFullAmount_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequireFullAmount_MetaData), NewProp_bRequireFullAmount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FResourceConsumptionRule, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ActionTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_ConsumptionAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bIsPercentage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_bRequireFullAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ResourceConsumptionRule",
	Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::PropPointers),
	sizeof(FResourceConsumptionRule),
	alignof(FResourceConsumptionRule),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FResourceConsumptionRule()
{
	if (!Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.InnerSingleton, Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ResourceConsumptionRule.InnerSingleton;
}
// End ScriptStruct FResourceConsumptionRule

// Begin Delegate FOnResourceChanged
struct Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnResourceChanged_Parms
	{
		FName ResourceID;
		float NewAmount;
		float MaxAmount;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewAmount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxAmount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceChanged_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_NewAmount = { "NewAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceChanged_Parms, NewAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_MaxAmount = { "MaxAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceChanged_Parms, MaxAmount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_NewAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::NewProp_MaxAmount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnResourceChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::_Script_SLT_eventOnResourceChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::_Script_SLT_eventOnResourceChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnResourceChanged_DelegateWrapper(const FMulticastScriptDelegate& OnResourceChanged, FName ResourceID, float NewAmount, float MaxAmount)
{
	struct _Script_SLT_eventOnResourceChanged_Parms
	{
		FName ResourceID;
		float NewAmount;
		float MaxAmount;
	};
	_Script_SLT_eventOnResourceChanged_Parms Parms;
	Parms.ResourceID=ResourceID;
	Parms.NewAmount=NewAmount;
	Parms.MaxAmount=MaxAmount;
	OnResourceChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnResourceChanged

// Begin Delegate FOnResourceDepleted
struct Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnResourceDepleted_Parms
	{
		FName ResourceID;
		EResourceType ResourceType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ResourceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceDepleted_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceDepleted_Parms, ResourceType), Z_Construct_UEnum_SLT_EResourceType, METADATA_PARAMS(0, nullptr) }; // 836268483
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::NewProp_ResourceType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnResourceDepleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::_Script_SLT_eventOnResourceDepleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::_Script_SLT_eventOnResourceDepleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnResourceDepleted_DelegateWrapper(const FMulticastScriptDelegate& OnResourceDepleted, FName ResourceID, EResourceType ResourceType)
{
	struct _Script_SLT_eventOnResourceDepleted_Parms
	{
		FName ResourceID;
		EResourceType ResourceType;
	};
	_Script_SLT_eventOnResourceDepleted_Parms Parms;
	Parms.ResourceID=ResourceID;
	Parms.ResourceType=ResourceType;
	OnResourceDepleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnResourceDepleted

// Begin Delegate FOnResourceScarcityChanged
struct Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnResourceScarcityChanged_Parms
	{
		FName ResourceID;
		EResourceScarcityLevel NewLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceScarcityChanged_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_NewLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_NewLevel = { "NewLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceScarcityChanged_Parms, NewLevel), Z_Construct_UEnum_SLT_EResourceScarcityLevel, METADATA_PARAMS(0, nullptr) }; // 272041803
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_NewLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::NewProp_NewLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnResourceScarcityChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::_Script_SLT_eventOnResourceScarcityChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::_Script_SLT_eventOnResourceScarcityChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnResourceScarcityChanged_DelegateWrapper(const FMulticastScriptDelegate& OnResourceScarcityChanged, FName ResourceID, EResourceScarcityLevel NewLevel)
{
	struct _Script_SLT_eventOnResourceScarcityChanged_Parms
	{
		FName ResourceID;
		EResourceScarcityLevel NewLevel;
	};
	_Script_SLT_eventOnResourceScarcityChanged_Parms Parms;
	Parms.ResourceID=ResourceID;
	Parms.NewLevel=NewLevel;
	OnResourceScarcityChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnResourceScarcityChanged

// Begin Delegate FOnResourceConsumed
struct Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnResourceConsumed_Parms
	{
		FName ResourceID;
		float Amount;
		FGameplayTag ActionTag;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amount;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceConsumed_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceConsumed_Parms, Amount), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnResourceConsumed_Parms, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(0, nullptr) }; // 1298103297
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::NewProp_ActionTag,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnResourceConsumed__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::_Script_SLT_eventOnResourceConsumed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::_Script_SLT_eventOnResourceConsumed_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnResourceConsumed_DelegateWrapper(const FMulticastScriptDelegate& OnResourceConsumed, FName ResourceID, float Amount, FGameplayTag ActionTag)
{
	struct _Script_SLT_eventOnResourceConsumed_Parms
	{
		FName ResourceID;
		float Amount;
		FGameplayTag ActionTag;
	};
	_Script_SLT_eventOnResourceConsumed_Parms Parms;
	Parms.ResourceID=ResourceID;
	Parms.Amount=Amount;
	Parms.ActionTag=ActionTag;
	OnResourceConsumed.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnResourceConsumed

// Begin Class UResourceManager Function AddResource
struct Z_Construct_UFunction_UResourceManager_AddResource_Statics
{
	struct ResourceManager_eventAddResource_Parms
	{
		FName ResourceID;
		FResourceData ResourceData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource management functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource management functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResourceData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventAddResource_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ResourceData = { "ResourceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventAddResource_Parms, ResourceData), Z_Construct_UScriptStruct_FResourceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceData_MetaData), NewProp_ResourceData_MetaData) }; // 1148918532
void Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventAddResource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventAddResource_Parms), &Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_AddResource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ResourceData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_AddResource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_AddResource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_AddResource_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "AddResource", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_AddResource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_AddResource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_AddResource_Statics::ResourceManager_eventAddResource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_AddResource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_AddResource_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_AddResource_Statics::ResourceManager_eventAddResource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_AddResource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_AddResource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execAddResource)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_GET_STRUCT_REF(FResourceData,Z_Param_Out_ResourceData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->AddResource(Z_Param_ResourceID,Z_Param_Out_ResourceData);
	P_NATIVE_END;
}
// End Class UResourceManager Function AddResource

// Begin Class UResourceManager Function CanPerformAction
struct Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics
{
	struct ResourceManager_eventCanPerformAction_Parms
	{
		FGameplayTag ActionTag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Consumption" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Consumption functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Consumption functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventCanPerformAction_Parms, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTag_MetaData), NewProp_ActionTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventCanPerformAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventCanPerformAction_Parms), &Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ActionTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "CanPerformAction", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::ResourceManager_eventCanPerformAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::ResourceManager_eventCanPerformAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_CanPerformAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_CanPerformAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execCanPerformAction)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_ActionTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPerformAction(Z_Param_Out_ActionTag);
	P_NATIVE_END;
}
// End Class UResourceManager Function CanPerformAction

// Begin Class UResourceManager Function ConsumeForAction
struct Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics
{
	struct ResourceManager_eventConsumeForAction_Parms
	{
		FGameplayTag ActionTag;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Consumption" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventConsumeForAction_Parms, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTag_MetaData), NewProp_ActionTag_MetaData) }; // 1298103297
void Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventConsumeForAction_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventConsumeForAction_Parms), &Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ActionTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "ConsumeForAction", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::ResourceManager_eventConsumeForAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::ResourceManager_eventConsumeForAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_ConsumeForAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_ConsumeForAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execConsumeForAction)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_ActionTag);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConsumeForAction(Z_Param_Out_ActionTag);
	P_NATIVE_END;
}
// End Class UResourceManager Function ConsumeForAction

// Begin Class UResourceManager Function ConsumeItem
struct Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics
{
	struct ResourceManager_eventConsumeItem_Parms
	{
		FInventoryItemData ItemData;
		int32 Quantity;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Items" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Item integration\n" },
#endif
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Item integration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventConsumeItem_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventConsumeItem_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventConsumeItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventConsumeItem_Parms), &Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "ConsumeItem", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::ResourceManager_eventConsumeItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::ResourceManager_eventConsumeItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_ConsumeItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_ConsumeItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execConsumeItem)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ConsumeItem(Z_Param_Out_ItemData,Z_Param_Quantity);
	P_NATIVE_END;
}
// End Class UResourceManager Function ConsumeItem

// Begin Class UResourceManager Function GetActionCost
struct Z_Construct_UFunction_UResourceManager_GetActionCost_Statics
{
	struct ResourceManager_eventGetActionCost_Parms
	{
		FGameplayTag ActionTag;
		FName ResourceID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Consumption" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetActionCost_Parms, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTag_MetaData), NewProp_ActionTag_MetaData) }; // 1298103297
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetActionCost_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetActionCost_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ActionTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetActionCost", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::ResourceManager_eventGetActionCost_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::ResourceManager_eventGetActionCost_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetActionCost()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetActionCost_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetActionCost)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_ActionTag);
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetActionCost(Z_Param_Out_ActionTag,Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetActionCost

// Begin Class UResourceManager Function GetAllResourceIDs
struct Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics
{
	struct ResourceManager_eventGetAllResourceIDs_Parms
	{
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetAllResourceIDs_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetAllResourceIDs", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::ResourceManager_eventGetAllResourceIDs_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::ResourceManager_eventGetAllResourceIDs_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetAllResourceIDs()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetAllResourceIDs_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetAllResourceIDs)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetAllResourceIDs();
	P_NATIVE_END;
}
// End Class UResourceManager Function GetAllResourceIDs

// Begin Class UResourceManager Function GetItemResourceValue
struct Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics
{
	struct ResourceManager_eventGetItemResourceValue_Parms
	{
		FInventoryItemData ItemData;
		FName ResourceID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Items" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetItemResourceValue_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetItemResourceValue_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetItemResourceValue_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetItemResourceValue", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::ResourceManager_eventGetItemResourceValue_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::ResourceManager_eventGetItemResourceValue_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetItemResourceValue()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetItemResourceValue_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetItemResourceValue)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetItemResourceValue(Z_Param_Out_ItemData,Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetItemResourceValue

// Begin Class UResourceManager Function GetResourceAmount
struct Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics
{
	struct ResourceManager_eventGetResourceAmount_Parms
	{
		FName ResourceID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource value functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource value functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceAmount_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceAmount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourceAmount", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::ResourceManager_eventGetResourceAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::ResourceManager_eventGetResourceAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourceAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourceAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourceAmount)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetResourceAmount(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourceAmount

// Begin Class UResourceManager Function GetResourceData
struct Z_Construct_UFunction_UResourceManager_GetResourceData_Statics
{
	struct ResourceManager_eventGetResourceData_Parms
	{
		FName ResourceID;
		FResourceData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceData_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceData_Parms, ReturnValue), Z_Construct_UScriptStruct_FResourceData, METADATA_PARAMS(0, nullptr) }; // 1148918532
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourceData", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::ResourceManager_eventGetResourceData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::ResourceManager_eventGetResourceData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourceData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourceData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourceData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FResourceData*)Z_Param__Result=P_THIS->GetResourceData(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourceData

// Begin Class UResourceManager Function GetResourcePercentage
struct Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics
{
	struct ResourceManager_eventGetResourcePercentage_Parms
	{
		FName ResourceID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcePercentage_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcePercentage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourcePercentage", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::ResourceManager_eventGetResourcePercentage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::ResourceManager_eventGetResourcePercentage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourcePercentage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourcePercentage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourcePercentage)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetResourcePercentage(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourcePercentage

// Begin Class UResourceManager Function GetResourcesByScarcity
struct Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics
{
	struct ResourceManager_eventGetResourcesByScarcity_Parms
	{
		EResourceScarcityLevel ScarcityLevel;
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Scarcity" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ScarcityLevel_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ScarcityLevel;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ScarcityLevel_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ScarcityLevel = { "ScarcityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcesByScarcity_Parms, ScarcityLevel), Z_Construct_UEnum_SLT_EResourceScarcityLevel, METADATA_PARAMS(0, nullptr) }; // 272041803
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcesByScarcity_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ScarcityLevel_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ScarcityLevel,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourcesByScarcity", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::ResourceManager_eventGetResourcesByScarcity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::ResourceManager_eventGetResourcesByScarcity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourcesByScarcity)
{
	P_GET_ENUM(EResourceScarcityLevel,Z_Param_ScarcityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetResourcesByScarcity(EResourceScarcityLevel(Z_Param_ScarcityLevel));
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourcesByScarcity

// Begin Class UResourceManager Function GetResourcesByType
struct Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics
{
	struct ResourceManager_eventGetResourcesByType_Parms
	{
		EResourceType ResourceType;
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ResourceType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ResourceType;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ResourceType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ResourceType = { "ResourceType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcesByType_Parms, ResourceType), Z_Construct_UEnum_SLT_EResourceType, METADATA_PARAMS(0, nullptr) }; // 836268483
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourcesByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ResourceType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ResourceType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourcesByType", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::ResourceManager_eventGetResourcesByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::ResourceManager_eventGetResourcesByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourcesByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourcesByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourcesByType)
{
	P_GET_ENUM(EResourceType,Z_Param_ResourceType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetResourcesByType(EResourceType(Z_Param_ResourceType));
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourcesByType

// Begin Class UResourceManager Function GetResourceScarcity
struct Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics
{
	struct ResourceManager_eventGetResourceScarcity_Parms
	{
		FName ResourceID;
		EResourceScarcityLevel ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Scarcity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Scarcity functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Scarcity functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceScarcity_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ReturnValue_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventGetResourceScarcity_Parms, ReturnValue), Z_Construct_UEnum_SLT_EResourceScarcityLevel, METADATA_PARAMS(0, nullptr) }; // 272041803
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ReturnValue_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "GetResourceScarcity", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::ResourceManager_eventGetResourceScarcity_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::ResourceManager_eventGetResourceScarcity_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_GetResourceScarcity()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_GetResourceScarcity_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execGetResourceScarcity)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(EResourceScarcityLevel*)Z_Param__Result=P_THIS->GetResourceScarcity(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function GetResourceScarcity

// Begin Class UResourceManager Function HasResource
struct Z_Construct_UFunction_UResourceManager_HasResource_Statics
{
	struct ResourceManager_eventHasResource_Parms
	{
		FName ResourceID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventHasResource_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventHasResource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventHasResource_Parms), &Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_HasResource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_HasResource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_HasResource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_HasResource_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "HasResource", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_HasResource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_HasResource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_HasResource_Statics::ResourceManager_eventHasResource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_HasResource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_HasResource_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_HasResource_Statics::ResourceManager_eventHasResource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_HasResource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_HasResource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execHasResource)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->HasResource(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function HasResource

// Begin Class UResourceManager Function IsResourceCritical
struct Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics
{
	struct ResourceManager_eventIsResourceCritical_Parms
	{
		FName ResourceID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Scarcity" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventIsResourceCritical_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventIsResourceCritical_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventIsResourceCritical_Parms), &Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "IsResourceCritical", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::ResourceManager_eventIsResourceCritical_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::ResourceManager_eventIsResourceCritical_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_IsResourceCritical()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_IsResourceCritical_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execIsResourceCritical)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsResourceCritical(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function IsResourceCritical

// Begin Class UResourceManager Function ModifyResource
struct Z_Construct_UFunction_UResourceManager_ModifyResource_Statics
{
	struct ResourceManager_eventModifyResource_Parms
	{
		FName ResourceID;
		float Amount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Amount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventModifyResource_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_Amount = { "Amount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventModifyResource_Parms, Amount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventModifyResource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventModifyResource_Parms), &Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_Amount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "ModifyResource", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::ResourceManager_eventModifyResource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::ResourceManager_eventModifyResource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_ModifyResource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_ModifyResource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execModifyResource)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Amount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ModifyResource(Z_Param_ResourceID,Z_Param_Amount);
	P_NATIVE_END;
}
// End Class UResourceManager Function ModifyResource

// Begin Class UResourceManager Function OnActionBlocked
struct ResourceManager_eventOnActionBlocked_Parms
{
	FGameplayTag ActionTag;
	FName InsufficientResource;
};
static const FName NAME_UResourceManager_OnActionBlocked = FName(TEXT("OnActionBlocked"));
void UResourceManager::OnActionBlocked(FGameplayTag const& ActionTag, FName InsufficientResource)
{
	ResourceManager_eventOnActionBlocked_Parms Parms;
	Parms.ActionTag=ActionTag;
	Parms.InsufficientResource=InsufficientResource;
	UFunction* Func = FindFunctionChecked(NAME_UResourceManager_OnActionBlocked);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActionTag_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActionTag;
	static const UECodeGen_Private::FNamePropertyParams NewProp_InsufficientResource;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::NewProp_ActionTag = { "ActionTag", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventOnActionBlocked_Parms, ActionTag), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActionTag_MetaData), NewProp_ActionTag_MetaData) }; // 1298103297
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::NewProp_InsufficientResource = { "InsufficientResource", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventOnActionBlocked_Parms, InsufficientResource), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::NewProp_ActionTag,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::NewProp_InsufficientResource,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "OnActionBlocked", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::PropPointers), sizeof(ResourceManager_eventOnActionBlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::Function_MetaDataParams) };
static_assert(sizeof(ResourceManager_eventOnActionBlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_OnActionBlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_OnActionBlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UResourceManager Function OnActionBlocked

// Begin Class UResourceManager Function OnResourceUpdated
struct ResourceManager_eventOnResourceUpdated_Parms
{
	FName ResourceID;
	FResourceData ResourceData;
};
static const FName NAME_UResourceManager_OnResourceUpdated = FName(TEXT("OnResourceUpdated"));
void UResourceManager::OnResourceUpdated(FName ResourceID, FResourceData const& ResourceData)
{
	ResourceManager_eventOnResourceUpdated_Parms Parms;
	Parms.ResourceID=ResourceID;
	Parms.ResourceData=ResourceData;
	UFunction* Func = FindFunctionChecked(NAME_UResourceManager_OnResourceUpdated);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ResourceData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ResourceData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventOnResourceUpdated_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::NewProp_ResourceData = { "ResourceData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventOnResourceUpdated_Parms, ResourceData), Z_Construct_UScriptStruct_FResourceData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ResourceData_MetaData), NewProp_ResourceData_MetaData) }; // 1148918532
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::NewProp_ResourceData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "OnResourceUpdated", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::PropPointers), sizeof(ResourceManager_eventOnResourceUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::Function_MetaDataParams) };
static_assert(sizeof(ResourceManager_eventOnResourceUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_OnResourceUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_OnResourceUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UResourceManager Function OnResourceUpdated

// Begin Class UResourceManager Function RegenerateAllResources
struct Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics
{
	struct ResourceManager_eventRegenerateAllResources_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventRegenerateAllResources_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "RegenerateAllResources", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::ResourceManager_eventRegenerateAllResources_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::ResourceManager_eventRegenerateAllResources_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_RegenerateAllResources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_RegenerateAllResources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execRegenerateAllResources)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegenerateAllResources(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// End Class UResourceManager Function RegenerateAllResources

// Begin Class UResourceManager Function RemoveResource
struct Z_Construct_UFunction_UResourceManager_RemoveResource_Statics
{
	struct ResourceManager_eventRemoveResource_Parms
	{
		FName ResourceID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventRemoveResource_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventRemoveResource_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventRemoveResource_Parms), &Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "RemoveResource", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::ResourceManager_eventRemoveResource_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::ResourceManager_eventRemoveResource_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_RemoveResource()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_RemoveResource_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execRemoveResource)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveResource(Z_Param_ResourceID);
	P_NATIVE_END;
}
// End Class UResourceManager Function RemoveResource

// Begin Class UResourceManager Function ResetAllResources
struct Z_Construct_UFunction_UResourceManager_ResetAllResources_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_ResetAllResources_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "ResetAllResources", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_ResetAllResources_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_ResetAllResources_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UResourceManager_ResetAllResources()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_ResetAllResources_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execResetAllResources)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAllResources();
	P_NATIVE_END;
}
// End Class UResourceManager Function ResetAllResources

// Begin Class UResourceManager Function SetResourceAmount
struct Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics
{
	struct ResourceManager_eventSetResourceAmount_Parms
	{
		FName ResourceID;
		float NewAmount;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewAmount;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventSetResourceAmount_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_NewAmount = { "NewAmount", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventSetResourceAmount_Parms, NewAmount), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventSetResourceAmount_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventSetResourceAmount_Parms), &Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_NewAmount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "SetResourceAmount", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::ResourceManager_eventSetResourceAmount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::ResourceManager_eventSetResourceAmount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_SetResourceAmount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_SetResourceAmount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execSetResourceAmount)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewAmount);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetResourceAmount(Z_Param_ResourceID,Z_Param_NewAmount);
	P_NATIVE_END;
}
// End Class UResourceManager Function SetResourceAmount

// Begin Class UResourceManager Function SetResourceMax
struct Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics
{
	struct ResourceManager_eventSetResourceMax_Parms
	{
		FName ResourceID;
		float NewMax;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Resources" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ResourceID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewMax;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ResourceID = { "ResourceID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventSetResourceMax_Parms, ResourceID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_NewMax = { "NewMax", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ResourceManager_eventSetResourceMax_Parms, NewMax), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ResourceManager_eventSetResourceMax_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ResourceManager_eventSetResourceMax_Parms), &Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ResourceID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_NewMax,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UResourceManager, nullptr, "SetResourceMax", nullptr, nullptr, Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::PropPointers), sizeof(Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::ResourceManager_eventSetResourceMax_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::Function_MetaDataParams), Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::ResourceManager_eventSetResourceMax_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UResourceManager_SetResourceMax()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UResourceManager_SetResourceMax_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UResourceManager::execSetResourceMax)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ResourceID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewMax);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SetResourceMax(Z_Param_ResourceID,Z_Param_NewMax);
	P_NATIVE_END;
}
// End Class UResourceManager Function SetResourceMax

// Begin Class UResourceManager
void UResourceManager::StaticRegisterNativesUResourceManager()
{
	UClass* Class = UResourceManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddResource", &UResourceManager::execAddResource },
		{ "CanPerformAction", &UResourceManager::execCanPerformAction },
		{ "ConsumeForAction", &UResourceManager::execConsumeForAction },
		{ "ConsumeItem", &UResourceManager::execConsumeItem },
		{ "GetActionCost", &UResourceManager::execGetActionCost },
		{ "GetAllResourceIDs", &UResourceManager::execGetAllResourceIDs },
		{ "GetItemResourceValue", &UResourceManager::execGetItemResourceValue },
		{ "GetResourceAmount", &UResourceManager::execGetResourceAmount },
		{ "GetResourceData", &UResourceManager::execGetResourceData },
		{ "GetResourcePercentage", &UResourceManager::execGetResourcePercentage },
		{ "GetResourcesByScarcity", &UResourceManager::execGetResourcesByScarcity },
		{ "GetResourcesByType", &UResourceManager::execGetResourcesByType },
		{ "GetResourceScarcity", &UResourceManager::execGetResourceScarcity },
		{ "HasResource", &UResourceManager::execHasResource },
		{ "IsResourceCritical", &UResourceManager::execIsResourceCritical },
		{ "ModifyResource", &UResourceManager::execModifyResource },
		{ "RegenerateAllResources", &UResourceManager::execRegenerateAllResources },
		{ "RemoveResource", &UResourceManager::execRemoveResource },
		{ "ResetAllResources", &UResourceManager::execResetAllResources },
		{ "SetResourceAmount", &UResourceManager::execSetResourceAmount },
		{ "SetResourceMax", &UResourceManager::execSetResourceMax },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UResourceManager);
UClass* Z_Construct_UClass_UResourceManager_NoRegister()
{
	return UResourceManager::StaticClass();
}
struct Z_Construct_UClass_UResourceManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "Core/Systems/ResourceManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Resources_MetaData[] = {
		{ "Category", "Resources" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Resource storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Resource storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ConsumptionRules_MetaData[] = {
		{ "Category", "Consumption" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Consumption rules\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Consumption rules" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AbundantThreshold_MetaData[] = {
		{ "Category", "Scarcity" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Scarcity settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Scarcity settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_NormalThreshold_MetaData[] = {
		{ "Category", "Scarcity" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScarceThreshold_MetaData[] = {
		{ "Category", "Scarcity" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CriticalThreshold_MetaData[] = {
		{ "Category", "Scarcity" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnResourceChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnResourceDepleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnResourceScarcityChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnResourceConsumed_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ResourceManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Resources_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_Resources_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Resources;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ConsumptionRules_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ConsumptionRules;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AbundantThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NormalThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScarceThreshold;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CriticalThreshold;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnResourceChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnResourceDepleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnResourceScarcityChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnResourceConsumed;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UResourceManager_AddResource, "AddResource" }, // 2804563209
		{ &Z_Construct_UFunction_UResourceManager_CanPerformAction, "CanPerformAction" }, // 2107341514
		{ &Z_Construct_UFunction_UResourceManager_ConsumeForAction, "ConsumeForAction" }, // 4106499214
		{ &Z_Construct_UFunction_UResourceManager_ConsumeItem, "ConsumeItem" }, // 755325572
		{ &Z_Construct_UFunction_UResourceManager_GetActionCost, "GetActionCost" }, // 3643700338
		{ &Z_Construct_UFunction_UResourceManager_GetAllResourceIDs, "GetAllResourceIDs" }, // 1526030091
		{ &Z_Construct_UFunction_UResourceManager_GetItemResourceValue, "GetItemResourceValue" }, // 1430967808
		{ &Z_Construct_UFunction_UResourceManager_GetResourceAmount, "GetResourceAmount" }, // 2170200226
		{ &Z_Construct_UFunction_UResourceManager_GetResourceData, "GetResourceData" }, // 473514136
		{ &Z_Construct_UFunction_UResourceManager_GetResourcePercentage, "GetResourcePercentage" }, // 3809837988
		{ &Z_Construct_UFunction_UResourceManager_GetResourcesByScarcity, "GetResourcesByScarcity" }, // 4057839660
		{ &Z_Construct_UFunction_UResourceManager_GetResourcesByType, "GetResourcesByType" }, // 480576948
		{ &Z_Construct_UFunction_UResourceManager_GetResourceScarcity, "GetResourceScarcity" }, // 3769966982
		{ &Z_Construct_UFunction_UResourceManager_HasResource, "HasResource" }, // 2484357915
		{ &Z_Construct_UFunction_UResourceManager_IsResourceCritical, "IsResourceCritical" }, // 3855043212
		{ &Z_Construct_UFunction_UResourceManager_ModifyResource, "ModifyResource" }, // 3277832300
		{ &Z_Construct_UFunction_UResourceManager_OnActionBlocked, "OnActionBlocked" }, // 4120589551
		{ &Z_Construct_UFunction_UResourceManager_OnResourceUpdated, "OnResourceUpdated" }, // 1550149666
		{ &Z_Construct_UFunction_UResourceManager_RegenerateAllResources, "RegenerateAllResources" }, // 3372855431
		{ &Z_Construct_UFunction_UResourceManager_RemoveResource, "RemoveResource" }, // 472879604
		{ &Z_Construct_UFunction_UResourceManager_ResetAllResources, "ResetAllResources" }, // 3840503753
		{ &Z_Construct_UFunction_UResourceManager_SetResourceAmount, "SetResourceAmount" }, // 3616608674
		{ &Z_Construct_UFunction_UResourceManager_SetResourceMax, "SetResourceMax" }, // 2074061680
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UResourceManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources_ValueProp = { "Resources", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FResourceData, METADATA_PARAMS(0, nullptr) }; // 1148918532
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources_Key_KeyProp = { "Resources_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources = { "Resources", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, Resources), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Resources_MetaData), NewProp_Resources_MetaData) }; // 1148918532
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_ConsumptionRules_Inner = { "ConsumptionRules", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FResourceConsumptionRule, METADATA_PARAMS(0, nullptr) }; // 1885803170
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_ConsumptionRules = { "ConsumptionRules", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, ConsumptionRules), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ConsumptionRules_MetaData), NewProp_ConsumptionRules_MetaData) }; // 1885803170
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_AbundantThreshold = { "AbundantThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, AbundantThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AbundantThreshold_MetaData), NewProp_AbundantThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_NormalThreshold = { "NormalThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, NormalThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_NormalThreshold_MetaData), NewProp_NormalThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_ScarceThreshold = { "ScarceThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, ScarceThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScarceThreshold_MetaData), NewProp_ScarceThreshold_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_CriticalThreshold = { "CriticalThreshold", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, CriticalThreshold), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CriticalThreshold_MetaData), NewProp_CriticalThreshold_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceChanged = { "OnResourceChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, OnResourceChanged), Z_Construct_UDelegateFunction_SLT_OnResourceChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnResourceChanged_MetaData), NewProp_OnResourceChanged_MetaData) }; // 3673138158
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceDepleted = { "OnResourceDepleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, OnResourceDepleted), Z_Construct_UDelegateFunction_SLT_OnResourceDepleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnResourceDepleted_MetaData), NewProp_OnResourceDepleted_MetaData) }; // 799674599
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceScarcityChanged = { "OnResourceScarcityChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, OnResourceScarcityChanged), Z_Construct_UDelegateFunction_SLT_OnResourceScarcityChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnResourceScarcityChanged_MetaData), NewProp_OnResourceScarcityChanged_MetaData) }; // 3982901832
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceConsumed = { "OnResourceConsumed", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UResourceManager, OnResourceConsumed), Z_Construct_UDelegateFunction_SLT_OnResourceConsumed__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnResourceConsumed_MetaData), NewProp_OnResourceConsumed_MetaData) }; // 1451894326
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UResourceManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_Resources,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_ConsumptionRules_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_ConsumptionRules,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_AbundantThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_NormalThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_ScarceThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_CriticalThreshold,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceDepleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceScarcityChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UResourceManager_Statics::NewProp_OnResourceConsumed,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UResourceManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UResourceManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UResourceManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UResourceManager_Statics::ClassParams = {
	&UResourceManager::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UResourceManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UResourceManager_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UResourceManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UResourceManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UResourceManager()
{
	if (!Z_Registration_Info_UClass_UResourceManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UResourceManager.OuterSingleton, Z_Construct_UClass_UResourceManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UResourceManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UResourceManager>()
{
	return UResourceManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UResourceManager);
UResourceManager::~UResourceManager() {}
// End Class UResourceManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EResourceType_StaticEnum, TEXT("EResourceType"), &Z_Registration_Info_UEnum_EResourceType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 836268483U) },
		{ EResourceScarcityLevel_StaticEnum, TEXT("EResourceScarcityLevel"), &Z_Registration_Info_UEnum_EResourceScarcityLevel, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 272041803U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FResourceData::StaticStruct, Z_Construct_UScriptStruct_FResourceData_Statics::NewStructOps, TEXT("ResourceData"), &Z_Registration_Info_UScriptStruct_ResourceData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FResourceData), 1148918532U) },
		{ FResourceConsumptionRule::StaticStruct, Z_Construct_UScriptStruct_FResourceConsumptionRule_Statics::NewStructOps, TEXT("ResourceConsumptionRule"), &Z_Registration_Info_UScriptStruct_ResourceConsumptionRule, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FResourceConsumptionRule), 1885803170U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UResourceManager, UResourceManager::StaticClass, TEXT("UResourceManager"), &Z_Registration_Info_UClass_UResourceManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UResourceManager), 3363538432U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_1126434449(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ResourceManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
