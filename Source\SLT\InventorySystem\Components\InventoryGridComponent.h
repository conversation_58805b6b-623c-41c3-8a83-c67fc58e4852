#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "../../Core/Interfaces/InventoryInterface.h"
#include "../Data/InventoryItemData.h"
#include "Engine/DataTable.h"
#include "InventoryGridComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInventoryItemAdded, const FInventorySlot&, AddedSlot, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInventoryItemRemoved, const FInventorySlot&, RemovedSlot, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnInventoryItemMoved, const FInventorySlot&, MovedSlot, int32, NewX, int32, NewY);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInventoryGridChanged, UInventoryGridComponent*, InventoryComponent);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UInventoryGridComponent : public UActorComponent, public IInventoryInterface
{
	GENERATED_BODY()

public:
	UInventoryGridComponent();

protected:
	virtual void BeginPlay() override;

public:
	// Grid dimensions
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings", meta = (ClampMin = "1", ClampMax = "20"))
	int32 GridWidth = 8;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid Settings", meta = (ClampMin = "1", ClampMax = "20"))
	int32 GridHeight = 6;

	// Weight limits
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Inventory Settings", meta = (ClampMin = "0.0"))
	float MaxWeight = 100.0f;

	// Item database reference
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Data")
	TSoftObjectPtr<UDataTable> ItemDatabase;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryItemAdded OnInventoryItemAdded;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryItemRemoved OnInventoryItemRemoved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryItemMoved OnInventoryItemMoved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryGridChanged OnInventoryGridChanged;

	// Grid management functions
	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool CanPlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, bool bRotated = false) const;

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool PlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, int32 Quantity = 1, bool bRotated = false);

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool MoveItem(const FGuid& SlotID, int32 NewX, int32 NewY, bool bNewRotation = false);

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool RotateItem(const FGuid& SlotID);

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool FindSlotByID(const FGuid& SlotID, FInventorySlot& OutSlot);

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool FindSlotAt(int32 X, int32 Y, FInventorySlot& OutSlot);

	UFUNCTION(BlueprintCallable, Category = "Grid")
	TArray<FInventorySlot> GetSlotsInArea(int32 X, int32 Y, int32 Width, int32 Height) const;

	UFUNCTION(BlueprintCallable, Category = "Grid")
	bool IsValidGridPosition(int32 X, int32 Y) const;

	UFUNCTION(BlueprintCallable, Category = "Grid")
	FVector2D FindBestFitPosition(const FInventoryItemData& ItemData, bool bAllowRotation = true) const;

	// Item database functions
	UFUNCTION(BlueprintCallable, Category = "Database")
	bool GetItemDataByID(FName ItemID, FInventoryItemData& OutItemData) const;

	UFUNCTION(BlueprintCallable, Category = "Database")
	void LoadItemDatabase();

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void GetItemDimensions(const FInventoryItemData& ItemData, bool bRotated, int32& OutWidth, int32& OutHeight) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool DoesItemFitInGrid(const FInventoryItemData& ItemData, bool bRotated = false) const;

	// IInventoryInterface implementation
	virtual bool AddItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity, FInventorySlot& OutSlot) override;
	virtual bool RemoveItem_Implementation(FName ItemID, int32 Quantity) override;
	virtual bool RemoveItemBySlotID_Implementation(const FGuid& SlotID) override;
	virtual bool HasItem_Implementation(FName ItemID) const override;
	virtual int32 GetItemQuantity_Implementation(FName ItemID) const override;
	virtual TArray<FInventorySlot> GetAllItems_Implementation() const override;
	virtual TArray<FInventorySlot> GetItemsByType_Implementation(EItemType ItemType) const override;
	virtual bool IsInventoryFull_Implementation() const override;
	virtual bool CanFitItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity) const override;
	virtual void ClearInventory_Implementation() override;
	virtual int32 GetInventoryCapacity_Implementation() const override;
	virtual float GetCurrentWeight_Implementation() const override;
	virtual float GetMaxWeight_Implementation() const override;
	virtual bool UseItem_Implementation(FName ItemID, int32 Quantity) override;
	virtual bool DropItem_Implementation(FName ItemID, int32 Quantity, const FVector& DropLocation) override;

protected:
	// Internal storage
	UPROPERTY(SaveGame)
	TArray<FInventorySlot> InventorySlots;

	// Grid state tracking
	TArray<TArray<FGuid>> GridState;

	// Cached item database
	UPROPERTY()
	UDataTable* CachedItemDatabase;

	// Internal helper functions
	void InitializeGrid();
	void UpdateGridState();
	bool IsAreaFree(int32 X, int32 Y, int32 Width, int32 Height, const FGuid& IgnoreSlotID = FGuid()) const;
	void OccupyGridArea(int32 X, int32 Y, int32 Width, int32 Height, const FGuid& SlotID);
	void FreeGridArea(int32 X, int32 Y, int32 Width, int32 Height);
	bool FindSlotByItemID(FName ItemID, FInventorySlot& OutSlot);
};
