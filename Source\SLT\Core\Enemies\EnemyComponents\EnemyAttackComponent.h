#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../BaseEnemyCharacter.h"
#include "EnemyAttackComponent.generated.h"

class UAnimMontage;
class UParticleSystem;
class USoundBase;
class UNiagaraSystem;

UENUM(BlueprintType)
enum class EAttackType : uint8
{
	Melee			UMETA(DisplayName = "Melee"),
	Ranged			UMETA(DisplayName = "Ranged"),
	Area			UMETA(DisplayName = "Area of Effect"),
	Projectile		UMETA(DisplayName = "Projectile"),
	Charge			UMETA(DisplayName = "Charge"),
	Grab			UMETA(DisplayName = "Grab"),
	Special			UMETA(DisplayName = "Special"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EAttackPhase : uint8
{
	Windup			UMETA(DisplayName = "Windup"),
	Active			UMETA(DisplayName = "Active"),
	Recovery		UMETA(DisplayName = "Recovery"),
	Cooldown		UMETA(DisplayName = "Cooldown")
};

USTRUCT(BlueprintType)
struct FAttackData : public FTableRowBase
{
	GENERATED_BODY()

	FAttackData()
	{
		AttackID = NAME_None;
		AttackName = FText::GetEmpty();
		AttackType = EAttackType::Melee;
		BaseDamage = 25.0f;
		Range = 150.0f;
		WindupTime = 0.5f;
		ActiveTime = 0.2f;
		RecoveryTime = 0.3f;
		CooldownTime = 2.0f;
		AttackAngle = 45.0f;
		KnockbackForce = 500.0f;
		StunDuration = 0.0f;
		bCanBeBlocked = true;
		bCanBeDodged = true;
		bCanBeInterrupted = true;
		Priority = 1;
		RequiredDistance = 0.0f;
		MaxDistance = 300.0f;
		AttackAnimation = nullptr;
		AttackSound = nullptr;
		HitEffect = nullptr;
		ProjectileClass = nullptr;
		ProjectileSpeed = 1000.0f;
	}

	// Basic attack info
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Info")
	FName AttackID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Info")
	FText AttackName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Info")
	FText AttackDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Info")
	EAttackType AttackType;

	// Damage properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float BaseDamage;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float Range;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float AttackAngle;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float KnockbackForce;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
	float StunDuration;

	// Timing properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float WindupTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float ActiveTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float RecoveryTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float CooldownTime;

	// Behavior properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	bool bCanBeBlocked;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	bool bCanBeDodged;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	bool bCanBeInterrupted;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	int32 Priority;

	// Range properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range")
	float RequiredDistance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Range")
	float MaxDistance;

	// Visual and audio
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UAnimMontage> AttackAnimation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<USoundBase> AttackSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UParticleSystem> HitEffect;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Presentation")
	TSoftObjectPtr<UNiagaraSystem> NiagaraHitEffect;

	// Projectile properties (for ranged attacks)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projectile", meta = (EditCondition = "AttackType == EAttackType::Ranged || AttackType == EAttackType::Projectile"))
	TSubclassOf<AActor> ProjectileClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Projectile", meta = (EditCondition = "AttackType == EAttackType::Ranged || AttackType == EAttackType::Projectile"))
	float ProjectileSpeed;

	// Tags and conditions
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer AttackTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	FGameplayTagContainer RequiredTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Conditions")
	FGameplayTagContainer BlockedTags;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, float> CustomProperties;
};

USTRUCT(BlueprintType)
struct FAttackInstance
{
	GENERATED_BODY()

	FAttackInstance()
	{
		AttackData = FAttackData();
		CurrentPhase = EAttackPhase::Windup;
		PhaseStartTime = 0.0f;
		TotalDuration = 0.0f;
		bIsActive = false;
		Target = nullptr;
		AttackDirection = FVector::ForwardVector;
		AttackLocation = FVector::ZeroVector;
	}

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	FAttackData AttackData;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	EAttackPhase CurrentPhase;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	float PhaseStartTime;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	float TotalDuration;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	bool bIsActive;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	APawn* Target;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	FVector AttackDirection;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	FVector AttackLocation;

	UPROPERTY(BlueprintReadOnly, Category = "Attack Instance")
	TArray<AActor*> HitActors;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAttackStarted, FName, AttackID, const FAttackData&, AttackData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAttackPhaseChanged, EAttackPhase, OldPhase, EAttackPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAttackHit, AActor*, HitActor, float, Damage, const FVector&, HitLocation);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAttackCompleted, FName, AttackID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAttackInterrupted, FName, AttackID);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UEnemyAttackComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UEnemyAttackComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Attack Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Configuration")
	TArray<FAttackData> AvailableAttacks;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Configuration")
	TSoftObjectPtr<UDataTable> AttackDatabase;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Configuration")
	bool bAutoSelectAttacks = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Configuration")
	float AttackSelectionRange = 300.0f;

	// Current Attack State
	UPROPERTY(BlueprintReadOnly, Category = "Attack State")
	FAttackInstance CurrentAttack;

	UPROPERTY(BlueprintReadOnly, Category = "Attack State")
	bool bIsAttacking = false;

	UPROPERTY(BlueprintReadOnly, Category = "Attack State")
	float LastAttackTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "Attack State")
	TMap<FName, float> AttackCooldowns;

	// Attack Modifiers
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Modifiers")
	float DamageMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Modifiers")
	float SpeedMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Modifiers")
	float RangeMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack Modifiers")
	float CooldownMultiplier = 1.0f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAttackStarted OnAttackStarted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAttackPhaseChanged OnAttackPhaseChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAttackHit OnAttackHit;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAttackCompleted OnAttackCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAttackInterrupted OnAttackInterrupted;

	// Main Attack Functions
	UFUNCTION(BlueprintCallable, Category = "Attack")
	void InitializeAttack(ABaseEnemyCharacter* OwnerEnemy);

	UFUNCTION(BlueprintCallable, Category = "Attack")
	bool StartAttack(FName AttackID, APawn* Target = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Attack")
	void StopAttack();

	UFUNCTION(BlueprintCallable, Category = "Attack")
	void InterruptAttack();

	UFUNCTION(BlueprintCallable, Category = "Attack")
	bool CanAttack(FName AttackID = NAME_None) const;

	UFUNCTION(BlueprintCallable, Category = "Attack")
	FName SelectBestAttack(APawn* Target) const;

	// Attack Management
	UFUNCTION(BlueprintCallable, Category = "Attack Management")
	void AddAttack(const FAttackData& AttackData);

	UFUNCTION(BlueprintCallable, Category = "Attack Management")
	void RemoveAttack(FName AttackID);

	UFUNCTION(BlueprintCallable, Category = "Attack Management")
	FAttackData GetAttackData(FName AttackID) const;

	UFUNCTION(BlueprintCallable, Category = "Attack Management")
	TArray<FName> GetAvailableAttackIDs() const;

	UFUNCTION(BlueprintCallable, Category = "Attack Management")
	void LoadAttacksFromDatabase();

	// Attack Execution
	UFUNCTION(BlueprintCallable, Category = "Attack Execution")
	void ExecuteAttackDamage();

	UFUNCTION(BlueprintCallable, Category = "Attack Execution")
	TArray<AActor*> GetAttackTargets() const;

	UFUNCTION(BlueprintCallable, Category = "Attack Execution")
	void ApplyDamageToTarget(AActor* Target, float Damage, const FVector& HitLocation);

	UFUNCTION(BlueprintCallable, Category = "Attack Execution")
	void SpawnProjectile(const FVector& SpawnLocation, const FVector& Direction);

	// Utility Functions
	UFUNCTION(BlueprintCallable, Category = "Attack Utility")
	float GetAttackCooldownRemaining(FName AttackID) const;

	UFUNCTION(BlueprintCallable, Category = "Attack Utility")
	bool IsAttackInRange(FName AttackID, APawn* Target) const;

	UFUNCTION(BlueprintCallable, Category = "Attack Utility")
	float GetModifiedDamage(float BaseDamage) const;

	UFUNCTION(BlueprintCallable, Category = "Attack Utility")
	float GetModifiedRange(float BaseRange) const;

	UFUNCTION(BlueprintCallable, Category = "Attack Utility")
	float GetAttackProgress() const;

	// Blueprint Events
	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnAttackInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnWindupStarted(const FAttackData& AttackData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnAttackActive(const FAttackData& AttackData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnRecoveryStarted(const FAttackData& AttackData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnCooldownStarted(const FAttackData& AttackData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Attack Events")
	void OnTargetHit(AActor* HitTarget, float DamageDealt, const FVector& HitLocation);

protected:
	// Internal references
	UPROPERTY()
	ABaseEnemyCharacter* OwnerEnemyCharacter;

	// Timer handles
	FTimerHandle AttackPhaseTimerHandle;
	FTimerHandle CooldownTimerHandle;

	// Internal functions
	void UpdateAttackPhase();
	void ProcessAttackPhase(EAttackPhase Phase);
	void SetAttackPhase(EAttackPhase NewPhase);
	void UpdateCooldowns(float DeltaTime);
	bool ValidateAttackConditions(const FAttackData& AttackData, APawn* Target) const;
	FVector CalculateAttackDirection(APawn* Target) const;
	void PlayAttackEffects(const FAttackData& AttackData);
	void CleanupAttack();
};
