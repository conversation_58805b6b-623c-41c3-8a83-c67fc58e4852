// Fill out your copyright notice in the Description page of Project Settings.

using UnrealBuildTool;

public class SLT : ModuleRules
{
	public SLT(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"EnhancedInput",
			"UMG",
			"CommonUI",
			"GameplayTags",
			"GameplayAbilities",
			"GameplayTasks",
			"AIModule",
			"NavigationSystem",
			"DeveloperSettings",
			"Json",
			"JsonUtilities",
			"Niagara",
			"NiagaraCore",
			"AudioMixer",
			"PhysicsCore",
			"Chaos"
		});

		PrivateDependencyModuleNames.AddRange(new string[] {
			"Slate",
			"SlateCore",
			"ToolMenus",
			"EditorStyle",
			"EditorWidgets",
			"UnrealEd",
			"RenderCore",
			"RHI",
			"EngineSettings",
			"LevelSequence",
			"MovieScene",
			"SignificanceManager",
			"Projects",
			"HTTP",
			"Networking",
			"ApplicationCore",
			"DesktopPlatform",
			"PropertyEditor",
			"DetailCustomizations",
			"ComponentVisualizers",
			"AssetTools",
			"ContentBrowser",
			"EditorSubsystem",
			"SourceControl",
			"UnrealEdMessages"
		});

		if (Target.bBuildEditor)
		{
			PrivateDependencyModuleNames.AddRange(new string[] {
				"BlueprintGraph",
				"KismetCompiler",
				"KismetWidgets",
				"GraphEditor",
				"Kismet",
				"WorkspaceMenuStructure",
				"AssetRegistry",
				"ToolWidgets"
			});
		}
	}
}
