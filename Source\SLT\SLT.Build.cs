// Fill out your copyright notice in the Description page of Project Settings.

using UnrealBuildTool;

public class SLT : ModuleRules
{
	public SLT(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] {
			"Core",
			"CoreUObject",
			"Engine",
			"InputCore",
			"EnhancedInput",
			"UMG",
			"CommonUI",
			"GameplayTags",
			"GameplayAbilities",
			"GameplayTasks",
			"AIModule",
			"NavigationSystem",
			"DeveloperSettings",
			"Json",
			"JsonUtilities"
		});

		PrivateDependencyModuleNames.AddRange(new string[] {
			"Slate",
			"SlateCore",
			"ToolMenus",
			"EditorStyle",
			"EditorWidgets",
			"UnrealEd",
			"RenderCore",
			"RHI",
			"Engine",
			"EngineSettings",
			"LevelSequence",
			"MovieScene",
			"AudioMixer",
			"SignificanceManager",
			"BehaviorTreeEditor",
			"Niagara",
			"NiagaraCore",
			"Projects",
			"HTTP",
			"Networking"
		});
	}
}
