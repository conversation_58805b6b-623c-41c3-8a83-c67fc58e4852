// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/ShaderManager.h"
#include "Runtime/Engine/Classes/Engine/GameInstance.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeShaderManager() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialInterface_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UMaterialParameterCollection_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UPrimitiveComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture_NoRegister();
SLT_API UClass* Z_Construct_UClass_UShaderManager();
SLT_API UClass* Z_Construct_UClass_UShaderManager_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EItemRarity();
SLT_API UEnum* Z_Construct_UEnum_SLT_EPostProcessType();
SLT_API UEnum* Z_Construct_UEnum_SLT_EShaderVariant();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FShaderPostProcessSettings();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FShaderVariantData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EShaderVariant
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EShaderVariant;
static UEnum* EShaderVariant_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EShaderVariant.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EShaderVariant.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EShaderVariant, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EShaderVariant"));
	}
	return Z_Registration_Info_UEnum_EShaderVariant.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EShaderVariant>()
{
	return EShaderVariant_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EShaderVariant_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Damaged.DisplayName", "Damaged" },
		{ "Damaged.Name", "EShaderVariant::Damaged" },
		{ "Default.DisplayName", "Default" },
		{ "Default.Name", "EShaderVariant::Default" },
		{ "Disabled.DisplayName", "Disabled" },
		{ "Disabled.Name", "EShaderVariant::Disabled" },
		{ "Epic.DisplayName", "Epic" },
		{ "Epic.Name", "EShaderVariant::Epic" },
		{ "Highlighted.DisplayName", "Highlighted" },
		{ "Highlighted.Name", "EShaderVariant::Highlighted" },
		{ "Interactive.DisplayName", "Interactive" },
		{ "Interactive.Name", "EShaderVariant::Interactive" },
		{ "Legendary.DisplayName", "Legendary" },
		{ "Legendary.Name", "EShaderVariant::Legendary" },
		{ "Locked.DisplayName", "Locked" },
		{ "Locked.Name", "EShaderVariant::Locked" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EShaderVariant::Rare" },
		{ "Selected.DisplayName", "Selected" },
		{ "Selected.Name", "EShaderVariant::Selected" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EShaderVariant::Default", (int64)EShaderVariant::Default },
		{ "EShaderVariant::Highlighted", (int64)EShaderVariant::Highlighted },
		{ "EShaderVariant::Selected", (int64)EShaderVariant::Selected },
		{ "EShaderVariant::Disabled", (int64)EShaderVariant::Disabled },
		{ "EShaderVariant::Damaged", (int64)EShaderVariant::Damaged },
		{ "EShaderVariant::Rare", (int64)EShaderVariant::Rare },
		{ "EShaderVariant::Epic", (int64)EShaderVariant::Epic },
		{ "EShaderVariant::Legendary", (int64)EShaderVariant::Legendary },
		{ "EShaderVariant::Interactive", (int64)EShaderVariant::Interactive },
		{ "EShaderVariant::Locked", (int64)EShaderVariant::Locked },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EShaderVariant_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EShaderVariant",
	"EShaderVariant",
	Z_Construct_UEnum_SLT_EShaderVariant_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EShaderVariant_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EShaderVariant_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EShaderVariant_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EShaderVariant()
{
	if (!Z_Registration_Info_UEnum_EShaderVariant.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EShaderVariant.InnerSingleton, Z_Construct_UEnum_SLT_EShaderVariant_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EShaderVariant.InnerSingleton;
}
// End Enum EShaderVariant

// Begin Enum EPostProcessType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EPostProcessType;
static UEnum* EPostProcessType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EPostProcessType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EPostProcessType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EPostProcessType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EPostProcessType"));
	}
	return Z_Registration_Info_UEnum_EPostProcessType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EPostProcessType>()
{
	return EPostProcessType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EPostProcessType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Combat.DisplayName", "Combat" },
		{ "Combat.Name", "EPostProcessType::Combat" },
		{ "Death.DisplayName", "Death" },
		{ "Death.Name", "EPostProcessType::Death" },
		{ "Interaction.DisplayName", "Interaction" },
		{ "Interaction.Name", "EPostProcessType::Interaction" },
		{ "Inventory.DisplayName", "Inventory" },
		{ "Inventory.Name", "EPostProcessType::Inventory" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EPostProcessType::None" },
		{ "Puzzle.DisplayName", "Puzzle" },
		{ "Puzzle.Name", "EPostProcessType::Puzzle" },
		{ "Transition.DisplayName", "Transition" },
		{ "Transition.Name", "EPostProcessType::Transition" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EPostProcessType::None", (int64)EPostProcessType::None },
		{ "EPostProcessType::Interaction", (int64)EPostProcessType::Interaction },
		{ "EPostProcessType::Inventory", (int64)EPostProcessType::Inventory },
		{ "EPostProcessType::Combat", (int64)EPostProcessType::Combat },
		{ "EPostProcessType::Puzzle", (int64)EPostProcessType::Puzzle },
		{ "EPostProcessType::Transition", (int64)EPostProcessType::Transition },
		{ "EPostProcessType::Death", (int64)EPostProcessType::Death },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EPostProcessType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EPostProcessType",
	"EPostProcessType",
	Z_Construct_UEnum_SLT_EPostProcessType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPostProcessType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EPostProcessType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EPostProcessType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EPostProcessType()
{
	if (!Z_Registration_Info_UEnum_EPostProcessType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EPostProcessType.InnerSingleton, Z_Construct_UEnum_SLT_EPostProcessType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EPostProcessType.InnerSingleton;
}
// End Enum EPostProcessType

// Begin ScriptStruct FShaderVariantData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ShaderVariantData;
class UScriptStruct* FShaderVariantData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ShaderVariantData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ShaderVariantData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FShaderVariantData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ShaderVariantData"));
	}
	return Z_Registration_Info_UScriptStruct_ShaderVariantData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FShaderVariantData>()
{
	return FShaderVariantData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FShaderVariantData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariantType_MetaData[] = {
		{ "Category", "Variant" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Variant identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Variant identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VariantName_MetaData[] = {
		{ "Category", "Variant" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BaseMaterial_MetaData[] = {
		{ "Category", "Materials" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material references\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material references" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverrideMaterial_MetaData[] = {
		{ "Category", "Materials" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveColor_MetaData[] = {
		{ "Category", "Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameters\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EmissiveIntensity_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Metallic_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Roughness_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Opacity_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseCustomParameters_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom parameters\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarParameters_MetaData[] = {
		{ "Category", "Custom" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "Custom" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TextureParameters_MetaData[] = {
		{ "Category", "Custom" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableAnimation_MetaData[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Animation properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationSpeed_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PulseFrequency_MetaData[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_VariantName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_BaseMaterial;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_OverrideMaterial;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EmissiveColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EmissiveIntensity;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Metallic;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Roughness;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Opacity;
	static void NewProp_bUseCustomParameters_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseCustomParameters;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScalarParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScalarParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ScalarParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TextureParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_TextureParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_TextureParameters;
	static void NewProp_bEnableAnimation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableAnimation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationSpeed;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PulseFrequency;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FShaderVariantData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariantType_MetaData), NewProp_VariantType_MetaData) }; // 2259123310
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantName = { "VariantName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, VariantName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VariantName_MetaData), NewProp_VariantName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_BaseMaterial = { "BaseMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, BaseMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BaseMaterial_MetaData), NewProp_BaseMaterial_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_OverrideMaterial = { "OverrideMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, OverrideMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverrideMaterial_MetaData), NewProp_OverrideMaterial_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_EmissiveColor = { "EmissiveColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, EmissiveColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveColor_MetaData), NewProp_EmissiveColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_EmissiveIntensity = { "EmissiveIntensity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, EmissiveIntensity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EmissiveIntensity_MetaData), NewProp_EmissiveIntensity_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Metallic = { "Metallic", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, Metallic), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Metallic_MetaData), NewProp_Metallic_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Roughness = { "Roughness", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, Roughness), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Roughness_MetaData), NewProp_Roughness_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Opacity = { "Opacity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, Opacity), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Opacity_MetaData), NewProp_Opacity_MetaData) };
void Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bUseCustomParameters_SetBit(void* Obj)
{
	((FShaderVariantData*)Obj)->bUseCustomParameters = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bUseCustomParameters = { "bUseCustomParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FShaderVariantData), &Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bUseCustomParameters_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseCustomParameters_MetaData), NewProp_bUseCustomParameters_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters_ValueProp = { "ScalarParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters_Key_KeyProp = { "ScalarParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters = { "ScalarParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, ScalarParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarParameters_MetaData), NewProp_ScalarParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters_ValueProp = { "TextureParameters", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UTexture_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters_Key_KeyProp = { "TextureParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters = { "TextureParameters", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, TextureParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TextureParameters_MetaData), NewProp_TextureParameters_MetaData) };
void Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bEnableAnimation_SetBit(void* Obj)
{
	((FShaderVariantData*)Obj)->bEnableAnimation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bEnableAnimation = { "bEnableAnimation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FShaderVariantData), &Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bEnableAnimation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableAnimation_MetaData), NewProp_bEnableAnimation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_AnimationSpeed = { "AnimationSpeed", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, AnimationSpeed), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationSpeed_MetaData), NewProp_AnimationSpeed_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_PulseFrequency = { "PulseFrequency", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderVariantData, PulseFrequency), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PulseFrequency_MetaData), NewProp_PulseFrequency_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FShaderVariantData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VariantName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_BaseMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_OverrideMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_EmissiveColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_EmissiveIntensity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Metallic,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Roughness,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_Opacity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bUseCustomParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_ScalarParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_VectorParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_TextureParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_bEnableAnimation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_AnimationSpeed,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewProp_PulseFrequency,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderVariantData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FShaderVariantData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ShaderVariantData",
	Z_Construct_UScriptStruct_FShaderVariantData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderVariantData_Statics::PropPointers),
	sizeof(FShaderVariantData),
	alignof(FShaderVariantData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderVariantData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FShaderVariantData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FShaderVariantData()
{
	if (!Z_Registration_Info_UScriptStruct_ShaderVariantData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ShaderVariantData.InnerSingleton, Z_Construct_UScriptStruct_FShaderVariantData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ShaderVariantData.InnerSingleton;
}
// End ScriptStruct FShaderVariantData

// Begin ScriptStruct FShaderPostProcessSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings;
class UScriptStruct* FShaderPostProcessSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FShaderPostProcessSettings, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ShaderPostProcessSettings"));
	}
	return Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FShaderPostProcessSettings>()
{
	return FShaderPostProcessSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessType_MetaData[] = {
		{ "Category", "Post Process" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Post process identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Post process identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ProcessName_MetaData[] = {
		{ "Category", "Post Process" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessMaterial_MetaData[] = {
		{ "Category", "Material" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material reference\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BlendWeight_MetaData[] = {
		{ "Category", "Blend" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blend settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blend settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeInTime_MetaData[] = {
		{ "Category", "Blend" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FadeOutTime_MetaData[] = {
		{ "Category", "Blend" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Duration_MetaData[] = {
		{ "Category", "Duration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Duration settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Duration settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoRemove_MetaData[] = {
		{ "Category", "Duration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// -1 for infinite\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "-1 for infinite" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Priority" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Priority for multiple effects\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Priority for multiple effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ScalarParameters_MetaData[] = {
		{ "Category", "Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom parameters\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom parameters" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VectorParameters_MetaData[] = {
		{ "Category", "Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ProcessName;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PostProcessMaterial;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeInTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_FadeOutTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_bAutoRemove_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoRemove;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ScalarParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ScalarParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ScalarParameters;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VectorParameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_VectorParameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_VectorParameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FShaderPostProcessSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessType_MetaData), NewProp_ProcessType_MetaData) }; // 668001845
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessName = { "ProcessName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, ProcessName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ProcessName_MetaData), NewProp_ProcessName_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_PostProcessMaterial = { "PostProcessMaterial", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, PostProcessMaterial), Z_Construct_UClass_UMaterialInterface_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessMaterial_MetaData), NewProp_PostProcessMaterial_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_BlendWeight = { "BlendWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, BlendWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BlendWeight_MetaData), NewProp_BlendWeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_FadeInTime = { "FadeInTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, FadeInTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeInTime_MetaData), NewProp_FadeInTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_FadeOutTime = { "FadeOutTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, FadeOutTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FadeOutTime_MetaData), NewProp_FadeOutTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, Duration), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Duration_MetaData), NewProp_Duration_MetaData) };
void Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_bAutoRemove_SetBit(void* Obj)
{
	((FShaderPostProcessSettings*)Obj)->bAutoRemove = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_bAutoRemove = { "bAutoRemove", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FShaderPostProcessSettings), &Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_bAutoRemove_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoRemove_MetaData), NewProp_bAutoRemove_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters_ValueProp = { "ScalarParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters_Key_KeyProp = { "ScalarParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters = { "ScalarParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, ScalarParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ScalarParameters_MetaData), NewProp_ScalarParameters_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters_ValueProp = { "VectorParameters", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters_Key_KeyProp = { "VectorParameters_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters = { "VectorParameters", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FShaderPostProcessSettings, VectorParameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VectorParameters_MetaData), NewProp_VectorParameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ProcessName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_PostProcessMaterial,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_BlendWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_FadeInTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_FadeOutTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_bAutoRemove,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_ScalarParameters,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewProp_VectorParameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ShaderPostProcessSettings",
	Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::PropPointers),
	sizeof(FShaderPostProcessSettings),
	alignof(FShaderPostProcessSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FShaderPostProcessSettings()
{
	if (!Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.InnerSingleton, Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings.InnerSingleton;
}
// End ScriptStruct FShaderPostProcessSettings

// Begin Delegate FOnShaderVariantApplied
struct Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnShaderVariantApplied_Parms
	{
		UPrimitiveComponent* Component;
		EShaderVariant VariantType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnShaderVariantApplied_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnShaderVariantApplied_Parms, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::NewProp_VariantType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnShaderVariantApplied__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::_Script_SLT_eventOnShaderVariantApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::_Script_SLT_eventOnShaderVariantApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnShaderVariantApplied_DelegateWrapper(const FMulticastScriptDelegate& OnShaderVariantApplied, UPrimitiveComponent* Component, EShaderVariant VariantType)
{
	struct _Script_SLT_eventOnShaderVariantApplied_Parms
	{
		UPrimitiveComponent* Component;
		EShaderVariant VariantType;
	};
	_Script_SLT_eventOnShaderVariantApplied_Parms Parms;
	Parms.Component=Component;
	Parms.VariantType=VariantType;
	OnShaderVariantApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnShaderVariantApplied

// Begin Delegate FOnPostProcessApplied
struct Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPostProcessApplied_Parms
	{
		EPostProcessType ProcessType;
		float BlendWeight;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPostProcessApplied_Parms, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_BlendWeight = { "BlendWeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPostProcessApplied_Parms, BlendWeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_ProcessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::NewProp_BlendWeight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPostProcessApplied__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::_Script_SLT_eventOnPostProcessApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::_Script_SLT_eventOnPostProcessApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPostProcessApplied_DelegateWrapper(const FMulticastScriptDelegate& OnPostProcessApplied, EPostProcessType ProcessType, float BlendWeight)
{
	struct _Script_SLT_eventOnPostProcessApplied_Parms
	{
		EPostProcessType ProcessType;
		float BlendWeight;
	};
	_Script_SLT_eventOnPostProcessApplied_Parms Parms;
	Parms.ProcessType=ProcessType;
	Parms.BlendWeight=BlendWeight;
	OnPostProcessApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPostProcessApplied

// Begin Delegate FOnPostProcessRemoved
struct Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPostProcessRemoved_Parms
	{
		EPostProcessType ProcessType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPostProcessRemoved_Parms, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::NewProp_ProcessType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPostProcessRemoved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::_Script_SLT_eventOnPostProcessRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::_Script_SLT_eventOnPostProcessRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPostProcessRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPostProcessRemoved, EPostProcessType ProcessType)
{
	struct _Script_SLT_eventOnPostProcessRemoved_Parms
	{
		EPostProcessType ProcessType;
	};
	_Script_SLT_eventOnPostProcessRemoved_Parms Parms;
	Parms.ProcessType=ProcessType;
	OnPostProcessRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPostProcessRemoved

// Begin Class UShaderManager Function ApplyItemRarityShader
struct Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics
{
	struct ShaderManager_eventApplyItemRarityShader_Parms
	{
		UPrimitiveComponent* Component;
		EItemRarity Rarity;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Shader Variants" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Rarity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Rarity;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyItemRarityShader_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Rarity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Rarity = { "Rarity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyItemRarityShader_Parms, Rarity), Z_Construct_UEnum_SLT_EItemRarity, METADATA_PARAMS(0, nullptr) }; // 2495340207
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Rarity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::NewProp_Rarity,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "ApplyItemRarityShader", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::ShaderManager_eventApplyItemRarityShader_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::ShaderManager_eventApplyItemRarityShader_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execApplyItemRarityShader)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_GET_ENUM(EItemRarity,Z_Param_Rarity);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyItemRarityShader(Z_Param_Component,EItemRarity(Z_Param_Rarity));
	P_NATIVE_END;
}
// End Class UShaderManager Function ApplyItemRarityShader

// Begin Class UShaderManager Function ApplyPostProcess
struct Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics
{
	struct ShaderManager_eventApplyPostProcess_Parms
	{
		EPostProcessType ProcessType;
		float BlendWeight;
		float Duration;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Post process functions\n" },
#endif
		{ "CPP_Default_BlendWeight", "1.000000" },
		{ "CPP_Default_Duration", "-1.000000" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Post process functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_BlendWeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Duration;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyPostProcess_Parms, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_BlendWeight = { "BlendWeight", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyPostProcess_Parms, BlendWeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_Duration = { "Duration", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyPostProcess_Parms, Duration), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ShaderManager_eventApplyPostProcess_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ShaderManager_eventApplyPostProcess_Parms), &Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ProcessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_BlendWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_Duration,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "ApplyPostProcess", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::ShaderManager_eventApplyPostProcess_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::ShaderManager_eventApplyPostProcess_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_ApplyPostProcess()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_ApplyPostProcess_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execApplyPostProcess)
{
	P_GET_ENUM(EPostProcessType,Z_Param_ProcessType);
	P_GET_PROPERTY(FFloatProperty,Z_Param_BlendWeight);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Duration);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyPostProcess(EPostProcessType(Z_Param_ProcessType),Z_Param_BlendWeight,Z_Param_Duration);
	P_NATIVE_END;
}
// End Class UShaderManager Function ApplyPostProcess

// Begin Class UShaderManager Function ApplyShaderVariant
struct Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics
{
	struct ShaderManager_eventApplyShaderVariant_Parms
	{
		UPrimitiveComponent* Component;
		EShaderVariant VariantType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Shader Variants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Shader variant functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shader variant functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyShaderVariant_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventApplyShaderVariant_Parms, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
void Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ShaderManager_eventApplyShaderVariant_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ShaderManager_eventApplyShaderVariant_Parms), &Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_VariantType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "ApplyShaderVariant", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::ShaderManager_eventApplyShaderVariant_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::ShaderManager_eventApplyShaderVariant_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_ApplyShaderVariant()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_ApplyShaderVariant_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execApplyShaderVariant)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_GET_ENUM(EShaderVariant,Z_Param_VariantType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ApplyShaderVariant(Z_Param_Component,EShaderVariant(Z_Param_VariantType));
	P_NATIVE_END;
}
// End Class UShaderManager Function ApplyShaderVariant

// Begin Class UShaderManager Function CacheShaderVariants
struct Z_Construct_UFunction_UShaderManager_CacheShaderVariants_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_CacheShaderVariants_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "CacheShaderVariants", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_CacheShaderVariants_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_CacheShaderVariants_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UShaderManager_CacheShaderVariants()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_CacheShaderVariants_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execCacheShaderVariants)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CacheShaderVariants();
	P_NATIVE_END;
}
// End Class UShaderManager Function CacheShaderVariants

// Begin Class UShaderManager Function ClearAllPostProcesses
struct Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "ClearAllPostProcesses", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execClearAllPostProcesses)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllPostProcesses();
	P_NATIVE_END;
}
// End Class UShaderManager Function ClearAllPostProcesses

// Begin Class UShaderManager Function CreateDynamicMaterial
struct Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics
{
	struct ShaderManager_eventCreateDynamicMaterial_Parms
	{
		UPrimitiveComponent* Component;
		EShaderVariant VariantType;
		UMaterialInstanceDynamic* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Shader Variants" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventCreateDynamicMaterial_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventCreateDynamicMaterial_Parms, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventCreateDynamicMaterial_Parms, ReturnValue), Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_VariantType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "CreateDynamicMaterial", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::ShaderManager_eventCreateDynamicMaterial_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::ShaderManager_eventCreateDynamicMaterial_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execCreateDynamicMaterial)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_GET_ENUM(EShaderVariant,Z_Param_VariantType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(UMaterialInstanceDynamic**)Z_Param__Result=P_THIS->CreateDynamicMaterial(Z_Param_Component,EShaderVariant(Z_Param_VariantType));
	P_NATIVE_END;
}
// End Class UShaderManager Function CreateDynamicMaterial

// Begin Class UShaderManager Function GetAvailableVariants
struct Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics
{
	struct ShaderManager_eventGetAvailableVariants_Parms
	{
		TArray<EShaderVariant> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ReturnValue_Inner_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue_Inner_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventGetAvailableVariants_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue_Inner_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "GetAvailableVariants", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::ShaderManager_eventGetAvailableVariants_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::ShaderManager_eventGetAvailableVariants_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_GetAvailableVariants()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_GetAvailableVariants_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execGetAvailableVariants)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<EShaderVariant>*)Z_Param__Result=P_THIS->GetAvailableVariants();
	P_NATIVE_END;
}
// End Class UShaderManager Function GetAvailableVariants

// Begin Class UShaderManager Function GetGlobalScalarParameter
struct Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics
{
	struct ShaderManager_eventGetGlobalScalarParameter_Parms
	{
		FString ParameterName;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Global Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventGetGlobalScalarParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventGetGlobalScalarParameter_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "GetGlobalScalarParameter", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::ShaderManager_eventGetGlobalScalarParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::ShaderManager_eventGetGlobalScalarParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execGetGlobalScalarParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetGlobalScalarParameter(Z_Param_ParameterName);
	P_NATIVE_END;
}
// End Class UShaderManager Function GetGlobalScalarParameter

// Begin Class UShaderManager Function GetGlobalVectorParameter
struct Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics
{
	struct ShaderManager_eventGetGlobalVectorParameter_Parms
	{
		FString ParameterName;
		FLinearColor ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Global Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventGetGlobalVectorParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventGetGlobalVectorParameter_Parms, ReturnValue), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "GetGlobalVectorParameter", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::ShaderManager_eventGetGlobalVectorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54820401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::ShaderManager_eventGetGlobalVectorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execGetGlobalVectorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLinearColor*)Z_Param__Result=P_THIS->GetGlobalVectorParameter(Z_Param_ParameterName);
	P_NATIVE_END;
}
// End Class UShaderManager Function GetGlobalVectorParameter

// Begin Class UShaderManager Function IsPostProcessActive
struct Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics
{
	struct ShaderManager_eventIsPostProcessActive_Parms
	{
		EPostProcessType ProcessType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventIsPostProcessActive_Parms, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
void Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ShaderManager_eventIsPostProcessActive_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ShaderManager_eventIsPostProcessActive_Parms), &Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ProcessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "IsPostProcessActive", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::ShaderManager_eventIsPostProcessActive_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::ShaderManager_eventIsPostProcessActive_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_IsPostProcessActive()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_IsPostProcessActive_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execIsPostProcessActive)
{
	P_GET_ENUM(EPostProcessType,Z_Param_ProcessType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsPostProcessActive(EPostProcessType(Z_Param_ProcessType));
	P_NATIVE_END;
}
// End Class UShaderManager Function IsPostProcessActive

// Begin Class UShaderManager Function OnMaterialParametersChanged
struct ShaderManager_eventOnMaterialParametersChanged_Parms
{
	UPrimitiveComponent* Component;
	EShaderVariant VariantType;
};
static const FName NAME_UShaderManager_OnMaterialParametersChanged = FName(TEXT("OnMaterialParametersChanged"));
void UShaderManager::OnMaterialParametersChanged(UPrimitiveComponent* Component, EShaderVariant VariantType)
{
	ShaderManager_eventOnMaterialParametersChanged_Parms Parms;
	Parms.Component=Component;
	Parms.VariantType=VariantType;
	UFunction* Func = FindFunctionChecked(NAME_UShaderManager_OnMaterialParametersChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventOnMaterialParametersChanged_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventOnMaterialParametersChanged_Parms, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::NewProp_VariantType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "OnMaterialParametersChanged", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::PropPointers), sizeof(ShaderManager_eventOnMaterialParametersChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(ShaderManager_eventOnMaterialParametersChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UShaderManager Function OnMaterialParametersChanged

// Begin Class UShaderManager Function OnShaderSystemInitialized
static const FName NAME_UShaderManager_OnShaderSystemInitialized = FName(TEXT("OnShaderSystemInitialized"));
void UShaderManager::OnShaderSystemInitialized()
{
	UFunction* Func = FindFunctionChecked(NAME_UShaderManager_OnShaderSystemInitialized);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "OnShaderSystemInitialized", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UShaderManager Function OnShaderSystemInitialized

// Begin Class UShaderManager Function PrecompileShaderVariants
struct Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "PrecompileShaderVariants", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execPrecompileShaderVariants)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrecompileShaderVariants();
	P_NATIVE_END;
}
// End Class UShaderManager Function PrecompileShaderVariants

// Begin Class UShaderManager Function RemovePostProcess
struct Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics
{
	struct ShaderManager_eventRemovePostProcess_Parms
	{
		EPostProcessType ProcessType;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Post Process" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_ProcessType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ProcessType;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ProcessType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ProcessType = { "ProcessType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventRemovePostProcess_Parms, ProcessType), Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
void Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ShaderManager_eventRemovePostProcess_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ShaderManager_eventRemovePostProcess_Parms), &Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ProcessType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ProcessType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "RemovePostProcess", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::ShaderManager_eventRemovePostProcess_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::ShaderManager_eventRemovePostProcess_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_RemovePostProcess()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_RemovePostProcess_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execRemovePostProcess)
{
	P_GET_ENUM(EPostProcessType,Z_Param_ProcessType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemovePostProcess(EPostProcessType(Z_Param_ProcessType));
	P_NATIVE_END;
}
// End Class UShaderManager Function RemovePostProcess

// Begin Class UShaderManager Function RemoveShaderVariant
struct Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics
{
	struct ShaderManager_eventRemoveShaderVariant_Parms
	{
		UPrimitiveComponent* Component;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Shader Variants" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventRemoveShaderVariant_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
void Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ShaderManager_eventRemoveShaderVariant_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ShaderManager_eventRemoveShaderVariant_Parms), &Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "RemoveShaderVariant", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::ShaderManager_eventRemoveShaderVariant_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::ShaderManager_eventRemoveShaderVariant_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_RemoveShaderVariant()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_RemoveShaderVariant_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execRemoveShaderVariant)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RemoveShaderVariant(Z_Param_Component);
	P_NATIVE_END;
}
// End Class UShaderManager Function RemoveShaderVariant

// Begin Class UShaderManager Function SetGlobalScalarParameter
struct Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics
{
	struct ShaderManager_eventSetGlobalScalarParameter_Parms
	{
		FString ParameterName;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Global Parameters" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Global parameter functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Global parameter functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventSetGlobalScalarParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventSetGlobalScalarParameter_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "SetGlobalScalarParameter", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::ShaderManager_eventSetGlobalScalarParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::ShaderManager_eventSetGlobalScalarParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execSetGlobalScalarParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalScalarParameter(Z_Param_ParameterName,Z_Param_Value);
	P_NATIVE_END;
}
// End Class UShaderManager Function SetGlobalScalarParameter

// Begin Class UShaderManager Function SetGlobalVectorParameter
struct Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics
{
	struct ShaderManager_eventSetGlobalVectorParameter_Parms
	{
		FString ParameterName;
		FLinearColor Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Global Parameters" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ParameterName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Value_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ParameterName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::NewProp_ParameterName = { "ParameterName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventSetGlobalVectorParameter_Parms, ParameterName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ParameterName_MetaData), NewProp_ParameterName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventSetGlobalVectorParameter_Parms, Value), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Value_MetaData), NewProp_Value_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::NewProp_ParameterName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "SetGlobalVectorParameter", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::ShaderManager_eventSetGlobalVectorParameter_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::ShaderManager_eventSetGlobalVectorParameter_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execSetGlobalVectorParameter)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_ParameterName);
	P_GET_STRUCT_REF(FLinearColor,Z_Param_Out_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetGlobalVectorParameter(Z_Param_ParameterName,Z_Param_Out_Value);
	P_NATIVE_END;
}
// End Class UShaderManager Function SetGlobalVectorParameter

// Begin Class UShaderManager Function SetShaderQuality
struct Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics
{
	struct ShaderManager_eventSetShaderQuality_Parms
	{
		int32 QualityLevel;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_QualityLevel;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::NewProp_QualityLevel = { "QualityLevel", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventSetShaderQuality_Parms, QualityLevel), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::NewProp_QualityLevel,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "SetShaderQuality", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::ShaderManager_eventSetShaderQuality_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::ShaderManager_eventSetShaderQuality_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_SetShaderQuality()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_SetShaderQuality_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execSetShaderQuality)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_QualityLevel);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetShaderQuality(Z_Param_QualityLevel);
	P_NATIVE_END;
}
// End Class UShaderManager Function SetShaderQuality

// Begin Class UShaderManager Function StartShaderAnimation
struct Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics
{
	struct ShaderManager_eventStartShaderAnimation_Parms
	{
		UPrimitiveComponent* Component;
		EShaderVariant VariantType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Animation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Animation functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FBytePropertyParams NewProp_VariantType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VariantType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventStartShaderAnimation_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_VariantType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_VariantType = { "VariantType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventStartShaderAnimation_Parms, VariantType), Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_Component,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_VariantType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::NewProp_VariantType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "StartShaderAnimation", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::ShaderManager_eventStartShaderAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::ShaderManager_eventStartShaderAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_StartShaderAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_StartShaderAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execStartShaderAnimation)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_GET_ENUM(EShaderVariant,Z_Param_VariantType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartShaderAnimation(Z_Param_Component,EShaderVariant(Z_Param_VariantType));
	P_NATIVE_END;
}
// End Class UShaderManager Function StartShaderAnimation

// Begin Class UShaderManager Function StopShaderAnimation
struct Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics
{
	struct ShaderManager_eventStopShaderAnimation_Parms
	{
		UPrimitiveComponent* Component;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Component_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Component;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::NewProp_Component = { "Component", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventStopShaderAnimation_Parms, Component), Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Component_MetaData), NewProp_Component_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::NewProp_Component,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "StopShaderAnimation", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::ShaderManager_eventStopShaderAnimation_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::ShaderManager_eventStopShaderAnimation_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_StopShaderAnimation()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_StopShaderAnimation_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execStopShaderAnimation)
{
	P_GET_OBJECT(UPrimitiveComponent,Z_Param_Component);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopShaderAnimation(Z_Param_Component);
	P_NATIVE_END;
}
// End Class UShaderManager Function StopShaderAnimation

// Begin Class UShaderManager Function UpdateShaderAnimations
struct Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics
{
	struct ShaderManager_eventUpdateShaderAnimations_Parms
	{
		float DeltaTime;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Animation" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_DeltaTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::NewProp_DeltaTime = { "DeltaTime", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ShaderManager_eventUpdateShaderAnimations_Parms, DeltaTime), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::NewProp_DeltaTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UShaderManager, nullptr, "UpdateShaderAnimations", nullptr, nullptr, Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::PropPointers), sizeof(Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::ShaderManager_eventUpdateShaderAnimations_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::Function_MetaDataParams), Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::ShaderManager_eventUpdateShaderAnimations_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UShaderManager::execUpdateShaderAnimations)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_DeltaTime);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateShaderAnimations(Z_Param_DeltaTime);
	P_NATIVE_END;
}
// End Class UShaderManager Function UpdateShaderAnimations

// Begin Class UShaderManager
void UShaderManager::StaticRegisterNativesUShaderManager()
{
	UClass* Class = UShaderManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ApplyItemRarityShader", &UShaderManager::execApplyItemRarityShader },
		{ "ApplyPostProcess", &UShaderManager::execApplyPostProcess },
		{ "ApplyShaderVariant", &UShaderManager::execApplyShaderVariant },
		{ "CacheShaderVariants", &UShaderManager::execCacheShaderVariants },
		{ "ClearAllPostProcesses", &UShaderManager::execClearAllPostProcesses },
		{ "CreateDynamicMaterial", &UShaderManager::execCreateDynamicMaterial },
		{ "GetAvailableVariants", &UShaderManager::execGetAvailableVariants },
		{ "GetGlobalScalarParameter", &UShaderManager::execGetGlobalScalarParameter },
		{ "GetGlobalVectorParameter", &UShaderManager::execGetGlobalVectorParameter },
		{ "IsPostProcessActive", &UShaderManager::execIsPostProcessActive },
		{ "PrecompileShaderVariants", &UShaderManager::execPrecompileShaderVariants },
		{ "RemovePostProcess", &UShaderManager::execRemovePostProcess },
		{ "RemoveShaderVariant", &UShaderManager::execRemoveShaderVariant },
		{ "SetGlobalScalarParameter", &UShaderManager::execSetGlobalScalarParameter },
		{ "SetGlobalVectorParameter", &UShaderManager::execSetGlobalVectorParameter },
		{ "SetShaderQuality", &UShaderManager::execSetShaderQuality },
		{ "StartShaderAnimation", &UShaderManager::execStartShaderAnimation },
		{ "StopShaderAnimation", &UShaderManager::execStopShaderAnimation },
		{ "UpdateShaderAnimations", &UShaderManager::execUpdateShaderAnimations },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UShaderManager);
UClass* Z_Construct_UClass_UShaderManager_NoRegister()
{
	return UShaderManager::StaticClass();
}
struct Z_Construct_UClass_UShaderManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/ShaderManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ShaderVariants_MetaData[] = {
		{ "Category", "Shader Variants" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Shader variant configurations\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Shader variant configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PostProcessSettings_MetaData[] = {
		{ "Category", "Post Process" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Post process configurations\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Post process configurations" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GlobalParameterCollection_MetaData[] = {
		{ "Category", "Global" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Material parameter collection for global effects\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Material parameter collection for global effects" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnShaderVariantApplied_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPostProcessApplied_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnPostProcessRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentMaterials_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ComponentVariants_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActivePostProcesses_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AnimationTimers_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Animation tracking\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Animation tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedParameterCollection_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached parameter collection\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ShaderManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached parameter collection" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ShaderVariants_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ShaderVariants_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ShaderVariants_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ShaderVariants;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PostProcessSettings_ValueProp;
	static const UECodeGen_Private::FBytePropertyParams NewProp_PostProcessSettings_Key_KeyProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_PostProcessSettings_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PostProcessSettings;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_GlobalParameterCollection;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnShaderVariantApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPostProcessApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnPostProcessRemoved;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ComponentMaterials_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ComponentMaterials_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ComponentMaterials;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ComponentVariants_ValueProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ComponentVariants_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ComponentVariants_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ComponentVariants;
	static const UECodeGen_Private::FBytePropertyParams NewProp_ActivePostProcesses_ElementProp_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_ActivePostProcesses_ElementProp;
	static const UECodeGen_Private::FSetPropertyParams NewProp_ActivePostProcesses;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AnimationTimers_ValueProp;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_AnimationTimers_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_AnimationTimers;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedParameterCollection;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UShaderManager_ApplyItemRarityShader, "ApplyItemRarityShader" }, // 3093950954
		{ &Z_Construct_UFunction_UShaderManager_ApplyPostProcess, "ApplyPostProcess" }, // 875425709
		{ &Z_Construct_UFunction_UShaderManager_ApplyShaderVariant, "ApplyShaderVariant" }, // 2860619345
		{ &Z_Construct_UFunction_UShaderManager_CacheShaderVariants, "CacheShaderVariants" }, // 726879151
		{ &Z_Construct_UFunction_UShaderManager_ClearAllPostProcesses, "ClearAllPostProcesses" }, // 3928146302
		{ &Z_Construct_UFunction_UShaderManager_CreateDynamicMaterial, "CreateDynamicMaterial" }, // 1524774117
		{ &Z_Construct_UFunction_UShaderManager_GetAvailableVariants, "GetAvailableVariants" }, // 1913483635
		{ &Z_Construct_UFunction_UShaderManager_GetGlobalScalarParameter, "GetGlobalScalarParameter" }, // 1031334087
		{ &Z_Construct_UFunction_UShaderManager_GetGlobalVectorParameter, "GetGlobalVectorParameter" }, // 2238342860
		{ &Z_Construct_UFunction_UShaderManager_IsPostProcessActive, "IsPostProcessActive" }, // 2067028327
		{ &Z_Construct_UFunction_UShaderManager_OnMaterialParametersChanged, "OnMaterialParametersChanged" }, // 4099631767
		{ &Z_Construct_UFunction_UShaderManager_OnShaderSystemInitialized, "OnShaderSystemInitialized" }, // 672081089
		{ &Z_Construct_UFunction_UShaderManager_PrecompileShaderVariants, "PrecompileShaderVariants" }, // 3096336117
		{ &Z_Construct_UFunction_UShaderManager_RemovePostProcess, "RemovePostProcess" }, // 2936515005
		{ &Z_Construct_UFunction_UShaderManager_RemoveShaderVariant, "RemoveShaderVariant" }, // 1124870037
		{ &Z_Construct_UFunction_UShaderManager_SetGlobalScalarParameter, "SetGlobalScalarParameter" }, // 2110631569
		{ &Z_Construct_UFunction_UShaderManager_SetGlobalVectorParameter, "SetGlobalVectorParameter" }, // 1620604150
		{ &Z_Construct_UFunction_UShaderManager_SetShaderQuality, "SetShaderQuality" }, // 520823524
		{ &Z_Construct_UFunction_UShaderManager_StartShaderAnimation, "StartShaderAnimation" }, // 761975427
		{ &Z_Construct_UFunction_UShaderManager_StopShaderAnimation, "StopShaderAnimation" }, // 3921549914
		{ &Z_Construct_UFunction_UShaderManager_UpdateShaderAnimations, "UpdateShaderAnimations" }, // 1478904018
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UShaderManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_ValueProp = { "ShaderVariants", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FShaderVariantData, METADATA_PARAMS(0, nullptr) }; // 3174062725
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_Key_KeyProp = { "ShaderVariants_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants = { "ShaderVariants", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, ShaderVariants), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ShaderVariants_MetaData), NewProp_ShaderVariants_MetaData) }; // 2259123310 3174062725
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_ValueProp = { "PostProcessSettings", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FShaderPostProcessSettings, METADATA_PARAMS(0, nullptr) }; // 486031952
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_Key_KeyProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_Key_KeyProp = { "PostProcessSettings_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings = { "PostProcessSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, PostProcessSettings), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PostProcessSettings_MetaData), NewProp_PostProcessSettings_MetaData) }; // 668001845 486031952
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_GlobalParameterCollection = { "GlobalParameterCollection", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, GlobalParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GlobalParameterCollection_MetaData), NewProp_GlobalParameterCollection_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_OnShaderVariantApplied = { "OnShaderVariantApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, OnShaderVariantApplied), Z_Construct_UDelegateFunction_SLT_OnShaderVariantApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnShaderVariantApplied_MetaData), NewProp_OnShaderVariantApplied_MetaData) }; // 435389462
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_OnPostProcessApplied = { "OnPostProcessApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, OnPostProcessApplied), Z_Construct_UDelegateFunction_SLT_OnPostProcessApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPostProcessApplied_MetaData), NewProp_OnPostProcessApplied_MetaData) }; // 1296027630
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_OnPostProcessRemoved = { "OnPostProcessRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, OnPostProcessRemoved), Z_Construct_UDelegateFunction_SLT_OnPostProcessRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnPostProcessRemoved_MetaData), NewProp_OnPostProcessRemoved_MetaData) }; // 3439735562
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials_ValueProp = { "ComponentMaterials", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UClass_UMaterialInstanceDynamic_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials_Key_KeyProp = { "ComponentMaterials_Key", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials = { "ComponentMaterials", nullptr, (EPropertyFlags)0x0020088000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, ComponentMaterials), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentMaterials_MetaData), NewProp_ComponentMaterials_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_ValueProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_ValueProp = { "ComponentVariants", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UEnum_SLT_EShaderVariant, METADATA_PARAMS(0, nullptr) }; // 2259123310
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_Key_KeyProp = { "ComponentVariants_Key", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants = { "ComponentVariants", nullptr, (EPropertyFlags)0x0020088000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, ComponentVariants), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ComponentVariants_MetaData), NewProp_ComponentVariants_MetaData) }; // 2259123310
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses_ElementProp_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses_ElementProp = { "ActivePostProcesses", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_SLT_EPostProcessType, METADATA_PARAMS(0, nullptr) }; // 668001845
const UECodeGen_Private::FSetPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses = { "ActivePostProcesses", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Set, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, ActivePostProcesses), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActivePostProcesses_MetaData), NewProp_ActivePostProcesses_MetaData) }; // 668001845
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers_ValueProp = { "AnimationTimers", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers_Key_KeyProp = { "AnimationTimers_Key", nullptr, (EPropertyFlags)0x0000000000080000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UPrimitiveComponent_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers = { "AnimationTimers", nullptr, (EPropertyFlags)0x0020088000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, AnimationTimers), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AnimationTimers_MetaData), NewProp_AnimationTimers_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UShaderManager_Statics::NewProp_CachedParameterCollection = { "CachedParameterCollection", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UShaderManager, CachedParameterCollection), Z_Construct_UClass_UMaterialParameterCollection_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedParameterCollection_MetaData), NewProp_CachedParameterCollection_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UShaderManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ShaderVariants,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_Key_KeyProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_PostProcessSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_GlobalParameterCollection,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_OnShaderVariantApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_OnPostProcessApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_OnPostProcessRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentMaterials,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_ValueProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ComponentVariants,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses_ElementProp_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses_ElementProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_ActivePostProcesses,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_AnimationTimers,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UShaderManager_Statics::NewProp_CachedParameterCollection,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UShaderManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UShaderManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UShaderManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UShaderManager_Statics::ClassParams = {
	&UShaderManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UShaderManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UShaderManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UShaderManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UShaderManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UShaderManager()
{
	if (!Z_Registration_Info_UClass_UShaderManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UShaderManager.OuterSingleton, Z_Construct_UClass_UShaderManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UShaderManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UShaderManager>()
{
	return UShaderManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UShaderManager);
UShaderManager::~UShaderManager() {}
// End Class UShaderManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EShaderVariant_StaticEnum, TEXT("EShaderVariant"), &Z_Registration_Info_UEnum_EShaderVariant, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2259123310U) },
		{ EPostProcessType_StaticEnum, TEXT("EPostProcessType"), &Z_Registration_Info_UEnum_EPostProcessType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 668001845U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FShaderVariantData::StaticStruct, Z_Construct_UScriptStruct_FShaderVariantData_Statics::NewStructOps, TEXT("ShaderVariantData"), &Z_Registration_Info_UScriptStruct_ShaderVariantData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FShaderVariantData), 3174062725U) },
		{ FShaderPostProcessSettings::StaticStruct, Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics::NewStructOps, TEXT("ShaderPostProcessSettings"), &Z_Registration_Info_UScriptStruct_ShaderPostProcessSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FShaderPostProcessSettings), 486031952U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UShaderManager, UShaderManager::StaticClass, TEXT("UShaderManager"), &Z_Registration_Info_UClass_UShaderManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UShaderManager), 1349746791U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_1477036579(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
