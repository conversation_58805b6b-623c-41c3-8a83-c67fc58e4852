// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/ObjectPoolManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
struct FObjectPoolSettings;
#ifdef SLT_ObjectPoolManager_generated_h
#error "ObjectPoolManager.generated.h already included, missing '#pragma once' in ObjectPoolManager.h"
#endif
#define SLT_ObjectPoolManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_9_DELEGATE \
SLT_API void FOnActorPooled_DelegateWrapper(const FMulticastScriptDelegate& OnActorPooled, AActor* Actor, bool bWasReturned);


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_10_DELEGATE \
SLT_API void FOnPoolActorSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnPoolActorSpawned, AActor* Actor, TSubclassOf<AActor> ActorClass);


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_15_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FObjectPoolSettings_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FObjectPoolSettings>();

#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_65_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPooledActorInfo_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPooledActorInfo>();

#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_95_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FActorPool_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FActorPool>();

#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execCanBePooled); \
	DECLARE_FUNCTION(execResetActorState); \
	DECLARE_FUNCTION(execOnActorReturned); \
	DECLARE_FUNCTION(execOnActorPooled);


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	SLT_API UPoolableActor(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UPoolableActor(UPoolableActor&&); \
	UPoolableActor(const UPoolableActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(SLT_API, UPoolableActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPoolableActor); \
	DEFINE_ABSTRACT_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UPoolableActor) \
	SLT_API virtual ~UPoolableActor();


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_GENERATED_UINTERFACE_BODY() \
private: \
	static void StaticRegisterNativesUPoolableActor(); \
	friend struct Z_Construct_UClass_UPoolableActor_Statics; \
public: \
	DECLARE_CLASS(UPoolableActor, UInterface, COMPILED_IN_FLAGS(CLASS_Abstract | CLASS_Interface), CASTCLASS_None, TEXT("/Script/SLT"), SLT_API) \
	DECLARE_SERIALIZER(UPoolableActor)


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_GENERATED_BODY \
	PRAGMA_DISABLE_DEPRECATION_WARNINGS \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_GENERATED_UINTERFACE_BODY() \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_ENHANCED_CONSTRUCTORS \
private: \
	PRAGMA_ENABLE_DEPRECATION_WARNINGS


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_INCLASS_IINTERFACE_NO_PURE_DECLS \
protected: \
	virtual ~IPoolableActor() {} \
public: \
	typedef UPoolableActor UClassType; \
	typedef IPoolableActor ThisClass; \
	static bool Execute_CanBePooled(const UObject* O); \
	static void Execute_OnActorPooled(UObject* O); \
	static void Execute_OnActorReturned(UObject* O); \
	static void Execute_ResetActorState(UObject* O); \
	virtual UObject* _getUObject() const { return nullptr; }


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_116_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_127_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_119_INCLASS_IINTERFACE_NO_PURE_DECLS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UPoolableActor>();

#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetPoolStatistics); \
	DECLARE_FUNCTION(execGetPoolEfficiency); \
	DECLARE_FUNCTION(execGetActiveActorCount); \
	DECLARE_FUNCTION(execGetPoolSize); \
	DECLARE_FUNCTION(execSetPoolingEnabled); \
	DECLARE_FUNCTION(execCleanupIdleActors); \
	DECLARE_FUNCTION(execClearAllPools); \
	DECLARE_FUNCTION(execClearPool); \
	DECLARE_FUNCTION(execPrewarmPool); \
	DECLARE_FUNCTION(execRegisterPoolConfiguration); \
	DECLARE_FUNCTION(execReturnActorToPool); \
	DECLARE_FUNCTION(execGetPooledActor);


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUObjectPoolManager(); \
	friend struct Z_Construct_UClass_UObjectPoolManager_Statics; \
public: \
	DECLARE_CLASS(UObjectPoolManager, UWorldSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UObjectPoolManager)


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UObjectPoolManager(UObjectPoolManager&&); \
	UObjectPoolManager(const UObjectPoolManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UObjectPoolManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UObjectPoolManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UObjectPoolManager) \
	NO_API virtual ~UObjectPoolManager();


#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_151_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_154_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UObjectPoolManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
