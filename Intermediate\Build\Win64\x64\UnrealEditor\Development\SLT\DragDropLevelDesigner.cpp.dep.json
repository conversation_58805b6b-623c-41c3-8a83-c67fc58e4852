{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\slt\\source\\slt\\core\\design\\dragdropleveldesigner.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\slteditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\definitions.slt.h", "g:\\gamedev\\slt\\source\\slt\\core\\design\\dragdropleveldesigner.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\dragdropleveldesigner.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\engine\\staticmeshactor.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\staticmeshactor.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl"], "ImportedModules": [], "ImportedHeaderUnits": []}}