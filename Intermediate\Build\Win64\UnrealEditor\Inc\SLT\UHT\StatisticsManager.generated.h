// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/StatisticsManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
 
enum class EAchievementType : uint8;
struct FAchievementData;
struct FGameplayTag;
struct FLeaderboardEntry;
struct FPlayerStatistic;
#ifdef SLT_StatisticsManager_generated_h
#error "StatisticsManager.generated.h already included, missing '#pragma once' in StatisticsManager.h"
#endif
#define SLT_StatisticsManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPlayerStatistic_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPlayerStatistic>();

#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_108_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAchievementData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FAchievementData>();

#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_174_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeaderboardEntry_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FLeaderboardEntry>();

#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_208_DELEGATE \
SLT_API void FOnStatisticUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnStatisticUpdated, FName StatisticID, float NewValue);


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_209_DELEGATE \
SLT_API void FOnAchievementUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementUnlocked, FName AchievementID, FAchievementData const& AchievementData);


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_210_DELEGATE \
SLT_API void FOnAchievementProgress_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementProgress, FName AchievementID, float Progress, float MaxProgress);


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_211_DELEGATE \
SLT_API void FOnLeaderboardUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLeaderboardUpdated, const FString& LeaderboardName, TArray<FLeaderboardEntry> const& Entries);


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execImportStatistics); \
	DECLARE_FUNCTION(execExportStatistics); \
	DECLARE_FUNCTION(execLoadStatistics); \
	DECLARE_FUNCTION(execSaveStatistics); \
	DECLARE_FUNCTION(execGetUsageStatistics); \
	DECLARE_FUNCTION(execGetPlaytimeStatistics); \
	DECLARE_FUNCTION(execTrackPlayerAction); \
	DECLARE_FUNCTION(execTrackEvent); \
	DECLARE_FUNCTION(execClearLeaderboard); \
	DECLARE_FUNCTION(execGetPlayerRank); \
	DECLARE_FUNCTION(execGetLeaderboard); \
	DECLARE_FUNCTION(execSubmitScore); \
	DECLARE_FUNCTION(execGetTotalAchievementPoints); \
	DECLARE_FUNCTION(execGetAchievementsByType); \
	DECLARE_FUNCTION(execGetUnlockedAchievements); \
	DECLARE_FUNCTION(execGetAchievementProgress); \
	DECLARE_FUNCTION(execIsAchievementUnlocked); \
	DECLARE_FUNCTION(execUpdateAchievementProgress); \
	DECLARE_FUNCTION(execUnlockAchievement); \
	DECLARE_FUNCTION(execRegisterAchievement); \
	DECLARE_FUNCTION(execResetAllStatistics); \
	DECLARE_FUNCTION(execResetStatistic); \
	DECLARE_FUNCTION(execGetStatisticsByCategory); \
	DECLARE_FUNCTION(execGetStatisticData); \
	DECLARE_FUNCTION(execGetStatistic); \
	DECLARE_FUNCTION(execSetStatistic); \
	DECLARE_FUNCTION(execUpdateStatistic); \
	DECLARE_FUNCTION(execRegisterStatistic);


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUStatisticsManager(); \
	friend struct Z_Construct_UClass_UStatisticsManager_Statics; \
public: \
	DECLARE_CLASS(UStatisticsManager, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UStatisticsManager)


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UStatisticsManager(UStatisticsManager&&); \
	UStatisticsManager(const UStatisticsManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UStatisticsManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UStatisticsManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UStatisticsManager) \
	NO_API virtual ~UStatisticsManager();


#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_213_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_216_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UStatisticsManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h


#define FOREACH_ENUM_ESTATISTICTYPE(op) \
	op(EStatisticType::Counter) \
	op(EStatisticType::Timer) \
	op(EStatisticType::Maximum) \
	op(EStatisticType::Minimum) \
	op(EStatisticType::Average) \
	op(EStatisticType::Percentage) \
	op(EStatisticType::Custom) 

enum class EStatisticType : uint8;
template<> struct TIsUEnumClass<EStatisticType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EStatisticType>();

#define FOREACH_ENUM_EACHIEVEMENTTYPE(op) \
	op(EAchievementType::Progress) \
	op(EAchievementType::Milestone) \
	op(EAchievementType::Hidden) \
	op(EAchievementType::Rare) \
	op(EAchievementType::Platinum) 

enum class EAchievementType : uint8;
template<> struct TIsUEnumClass<EAchievementType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EAchievementType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
