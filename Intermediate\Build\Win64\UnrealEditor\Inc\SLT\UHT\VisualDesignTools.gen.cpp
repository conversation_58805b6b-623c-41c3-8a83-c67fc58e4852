// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Design/VisualDesignTools.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeVisualDesignTools() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UParticleSystemComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMesh_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UStaticMeshComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTextRenderComponent_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UVisualDesignComponent();
SLT_API UClass* Z_Construct_UClass_UVisualDesignComponent_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EDesignComplexity();
SLT_API UEnum* Z_Construct_UEnum_SLT_EDesignVisualizationType();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FDesignTemplate();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FDesignValidationResult();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FDesignValidationRule();
UMG_API UClass* Z_Construct_UClass_UWidgetComponent_NoRegister();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EDesignVisualizationType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDesignVisualizationType;
static UEnum* EDesignVisualizationType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDesignVisualizationType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDesignVisualizationType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EDesignVisualizationType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EDesignVisualizationType"));
	}
	return Z_Registration_Info_UEnum_EDesignVisualizationType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EDesignVisualizationType>()
{
	return EDesignVisualizationType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "AudioZone.DisplayName", "Audio Zone" },
		{ "AudioZone.Name", "EDesignVisualizationType::AudioZone" },
		{ "BlueprintType", "true" },
		{ "CameraPoint.DisplayName", "Camera Point" },
		{ "CameraPoint.Name", "EDesignVisualizationType::CameraPoint" },
		{ "Checkpoint.DisplayName", "Checkpoint" },
		{ "Checkpoint.Name", "EDesignVisualizationType::Checkpoint" },
		{ "InteractionRange.DisplayName", "Interaction Range" },
		{ "InteractionRange.Name", "EDesignVisualizationType::InteractionRange" },
		{ "InventoryGrid.DisplayName", "Inventory Grid" },
		{ "InventoryGrid.Name", "EDesignVisualizationType::InventoryGrid" },
		{ "LightingSetup.DisplayName", "Lighting Setup" },
		{ "LightingSetup.Name", "EDesignVisualizationType::LightingSetup" },
		{ "LootSpawn.DisplayName", "Loot Spawn" },
		{ "LootSpawn.Name", "EDesignVisualizationType::LootSpawn" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
		{ "NavMeshDebug.DisplayName", "NavMesh Debug" },
		{ "NavMeshDebug.Name", "EDesignVisualizationType::NavMeshDebug" },
		{ "None.DisplayName", "None" },
		{ "None.Name", "EDesignVisualizationType::None" },
		{ "PatrolPath.DisplayName", "Patrol Path" },
		{ "PatrolPath.Name", "EDesignVisualizationType::PatrolPath" },
		{ "PuzzleElement.DisplayName", "Puzzle Element" },
		{ "PuzzleElement.Name", "EDesignVisualizationType::PuzzleElement" },
		{ "ResourceNode.DisplayName", "Resource Node" },
		{ "ResourceNode.Name", "EDesignVisualizationType::ResourceNode" },
		{ "SpawnPoint.DisplayName", "Spawn Point" },
		{ "SpawnPoint.Name", "EDesignVisualizationType::SpawnPoint" },
		{ "TriggerZone.DisplayName", "Trigger Zone" },
		{ "TriggerZone.Name", "EDesignVisualizationType::TriggerZone" },
		{ "WeaponUpgrade.DisplayName", "Weapon Upgrade" },
		{ "WeaponUpgrade.Name", "EDesignVisualizationType::WeaponUpgrade" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDesignVisualizationType::None", (int64)EDesignVisualizationType::None },
		{ "EDesignVisualizationType::InventoryGrid", (int64)EDesignVisualizationType::InventoryGrid },
		{ "EDesignVisualizationType::InteractionRange", (int64)EDesignVisualizationType::InteractionRange },
		{ "EDesignVisualizationType::PatrolPath", (int64)EDesignVisualizationType::PatrolPath },
		{ "EDesignVisualizationType::TriggerZone", (int64)EDesignVisualizationType::TriggerZone },
		{ "EDesignVisualizationType::SpawnPoint", (int64)EDesignVisualizationType::SpawnPoint },
		{ "EDesignVisualizationType::Checkpoint", (int64)EDesignVisualizationType::Checkpoint },
		{ "EDesignVisualizationType::ResourceNode", (int64)EDesignVisualizationType::ResourceNode },
		{ "EDesignVisualizationType::WeaponUpgrade", (int64)EDesignVisualizationType::WeaponUpgrade },
		{ "EDesignVisualizationType::PuzzleElement", (int64)EDesignVisualizationType::PuzzleElement },
		{ "EDesignVisualizationType::LootSpawn", (int64)EDesignVisualizationType::LootSpawn },
		{ "EDesignVisualizationType::CameraPoint", (int64)EDesignVisualizationType::CameraPoint },
		{ "EDesignVisualizationType::AudioZone", (int64)EDesignVisualizationType::AudioZone },
		{ "EDesignVisualizationType::LightingSetup", (int64)EDesignVisualizationType::LightingSetup },
		{ "EDesignVisualizationType::NavMeshDebug", (int64)EDesignVisualizationType::NavMeshDebug },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EDesignVisualizationType",
	"EDesignVisualizationType",
	Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EDesignVisualizationType()
{
	if (!Z_Registration_Info_UEnum_EDesignVisualizationType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDesignVisualizationType.InnerSingleton, Z_Construct_UEnum_SLT_EDesignVisualizationType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDesignVisualizationType.InnerSingleton;
}
// End Enum EDesignVisualizationType

// Begin Enum EDesignComplexity
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EDesignComplexity;
static UEnum* EDesignComplexity_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EDesignComplexity.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EDesignComplexity.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EDesignComplexity, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EDesignComplexity"));
	}
	return Z_Registration_Info_UEnum_EDesignComplexity.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EDesignComplexity>()
{
	return EDesignComplexity_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EDesignComplexity_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Advanced.DisplayName", "Advanced" },
		{ "Advanced.Name", "EDesignComplexity::Advanced" },
		{ "Beginner.DisplayName", "Beginner" },
		{ "Beginner.Name", "EDesignComplexity::Beginner" },
		{ "BlueprintType", "true" },
		{ "Expert.DisplayName", "Expert" },
		{ "Expert.Name", "EDesignComplexity::Expert" },
		{ "Intermediate.DisplayName", "Intermediate" },
		{ "Intermediate.Name", "EDesignComplexity::Intermediate" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EDesignComplexity::Beginner", (int64)EDesignComplexity::Beginner },
		{ "EDesignComplexity::Intermediate", (int64)EDesignComplexity::Intermediate },
		{ "EDesignComplexity::Advanced", (int64)EDesignComplexity::Advanced },
		{ "EDesignComplexity::Expert", (int64)EDesignComplexity::Expert },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EDesignComplexity_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EDesignComplexity",
	"EDesignComplexity",
	Z_Construct_UEnum_SLT_EDesignComplexity_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDesignComplexity_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EDesignComplexity_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EDesignComplexity_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EDesignComplexity()
{
	if (!Z_Registration_Info_UEnum_EDesignComplexity.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EDesignComplexity.InnerSingleton, Z_Construct_UEnum_SLT_EDesignComplexity_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EDesignComplexity.InnerSingleton;
}
// End Enum EDesignComplexity

// Begin ScriptStruct FDesignTemplate
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_DesignTemplate;
class UScriptStruct* FDesignTemplate::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_DesignTemplate.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_DesignTemplate.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDesignTemplate, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("DesignTemplate"));
	}
	return Z_Registration_Info_UScriptStruct_DesignTemplate.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FDesignTemplate>()
{
	return FDesignTemplate::StaticStruct();
}
struct Z_Construct_UScriptStruct_FDesignTemplate_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateID_MetaData[] = {
		{ "Category", "Template" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateName_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateDescription_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Complexity_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBuiltIn_MetaData[] = {
		{ "Category", "Properties" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template properties" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequiresValidation_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedSetupTime_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Properties" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewImage_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual assets\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual assets" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PreviewMesh_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template data\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template data" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultProperties_MetaData[] = {
		{ "Category", "Data" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredComponents_MetaData[] = {
		{ "Category", "Data" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SetupSteps_MetaData[] = {
		{ "Category", "Instructions" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Setup instructions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup instructions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignTips_MetaData[] = {
		{ "Category", "Instructions" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CommonMistakes_MetaData[] = {
		{ "Category", "Instructions" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TemplateTags_MetaData[] = {
		{ "Category", "Tags" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Tags and categorization\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Tags and categorization" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SearchKeywords_MetaData[] = {
		{ "Category", "Tags" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TemplateName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_TemplateDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Complexity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Complexity;
	static void NewProp_bIsBuiltIn_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBuiltIn;
	static void NewProp_bRequiresValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequiresValidation;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedSetupTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewImage;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_PreviewMesh;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_DefaultProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_DefaultProperties;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredComponents_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_RequiredComponents;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SetupSteps_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SetupSteps;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DesignTips_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_DesignTips;
	static const UECodeGen_Private::FTextPropertyParams NewProp_CommonMistakes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_CommonMistakes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TemplateTags;
	static const UECodeGen_Private::FStrPropertyParams NewProp_SearchKeywords_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SearchKeywords;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDesignTemplate>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, TemplateID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateID_MetaData), NewProp_TemplateID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateName = { "TemplateName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, TemplateName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateName_MetaData), NewProp_TemplateName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateDescription = { "TemplateDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, TemplateDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateDescription_MetaData), NewProp_TemplateDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Complexity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Complexity = { "Complexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, Complexity), Z_Construct_UEnum_SLT_EDesignComplexity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Complexity_MetaData), NewProp_Complexity_MetaData) }; // 4290637674
void Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bIsBuiltIn_SetBit(void* Obj)
{
	((FDesignTemplate*)Obj)->bIsBuiltIn = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bIsBuiltIn = { "bIsBuiltIn", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDesignTemplate), &Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bIsBuiltIn_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBuiltIn_MetaData), NewProp_bIsBuiltIn_MetaData) };
void Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bRequiresValidation_SetBit(void* Obj)
{
	((FDesignTemplate*)Obj)->bRequiresValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bRequiresValidation = { "bRequiresValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDesignTemplate), &Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bRequiresValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequiresValidation_MetaData), NewProp_bRequiresValidation_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_EstimatedSetupTime = { "EstimatedSetupTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, EstimatedSetupTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedSetupTime_MetaData), NewProp_EstimatedSetupTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_PreviewImage = { "PreviewImage", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, PreviewImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewImage_MetaData), NewProp_PreviewImage_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_PreviewMesh = { "PreviewMesh", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, PreviewMesh), Z_Construct_UClass_UStaticMesh_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PreviewMesh_MetaData), NewProp_PreviewMesh_MetaData) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties_ValueProp = { "DefaultProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties_Key_KeyProp = { "DefaultProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties = { "DefaultProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, DefaultProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultProperties_MetaData), NewProp_DefaultProperties_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_RequiredComponents_Inner = { "RequiredComponents", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_RequiredComponents = { "RequiredComponents", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, RequiredComponents), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredComponents_MetaData), NewProp_RequiredComponents_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SetupSteps_Inner = { "SetupSteps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SetupSteps = { "SetupSteps", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, SetupSteps), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SetupSteps_MetaData), NewProp_SetupSteps_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DesignTips_Inner = { "DesignTips", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DesignTips = { "DesignTips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, DesignTips), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignTips_MetaData), NewProp_DesignTips_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_CommonMistakes_Inner = { "CommonMistakes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_CommonMistakes = { "CommonMistakes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, CommonMistakes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CommonMistakes_MetaData), NewProp_CommonMistakes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateTags = { "TemplateTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, TemplateTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TemplateTags_MetaData), NewProp_TemplateTags_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SearchKeywords_Inner = { "SearchKeywords", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SearchKeywords = { "SearchKeywords", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignTemplate, SearchKeywords), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SearchKeywords_MetaData), NewProp_SearchKeywords_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDesignTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Complexity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Complexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bIsBuiltIn,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_bRequiresValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_EstimatedSetupTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_PreviewImage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_PreviewMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DefaultProperties,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_RequiredComponents_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_RequiredComponents,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SetupSteps_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SetupSteps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DesignTips_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_DesignTips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_CommonMistakes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_CommonMistakes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_TemplateTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SearchKeywords_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewProp_SearchKeywords,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDesignTemplate_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"DesignTemplate",
	Z_Construct_UScriptStruct_FDesignTemplate_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignTemplate_Statics::PropPointers),
	sizeof(FDesignTemplate),
	alignof(FDesignTemplate),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignTemplate_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDesignTemplate_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDesignTemplate()
{
	if (!Z_Registration_Info_UScriptStruct_DesignTemplate.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_DesignTemplate.InnerSingleton, Z_Construct_UScriptStruct_FDesignTemplate_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_DesignTemplate.InnerSingleton;
}
// End ScriptStruct FDesignTemplate

// Begin ScriptStruct FDesignValidationRule
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_DesignValidationRule;
class UScriptStruct* FDesignValidationRule::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_DesignValidationRule.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_DesignValidationRule.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDesignValidationRule, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("DesignValidationRule"));
	}
	return Z_Registration_Info_UScriptStruct_DesignValidationRule.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FDesignValidationRule>()
{
	return FDesignValidationRule::StaticStruct();
}
struct Z_Construct_UScriptStruct_FDesignValidationRule_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleID_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleName_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RuleDescription_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsEnabled_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsWarning_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Rule" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// false = error\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "false = error" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ApplicableTags_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ApplicableClasses_MetaData[] = {
		{ "Category", "Rule" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_RuleID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RuleName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_RuleDescription;
	static void NewProp_bIsEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsEnabled;
	static void NewProp_bIsWarning_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsWarning;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ApplicableTags;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ApplicableClasses_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ApplicableClasses;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDesignValidationRule>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleID = { "RuleID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, RuleID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleID_MetaData), NewProp_RuleID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleName = { "RuleName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, RuleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleName_MetaData), NewProp_RuleName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleDescription = { "RuleDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, RuleDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RuleDescription_MetaData), NewProp_RuleDescription_MetaData) };
void Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsEnabled_SetBit(void* Obj)
{
	((FDesignValidationRule*)Obj)->bIsEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsEnabled = { "bIsEnabled", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDesignValidationRule), &Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsEnabled_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsEnabled_MetaData), NewProp_bIsEnabled_MetaData) };
void Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsWarning_SetBit(void* Obj)
{
	((FDesignValidationRule*)Obj)->bIsWarning = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsWarning = { "bIsWarning", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDesignValidationRule), &Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsWarning_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsWarning_MetaData), NewProp_bIsWarning_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableTags = { "ApplicableTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, ApplicableTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ApplicableTags_MetaData), NewProp_ApplicableTags_MetaData) }; // 3352185621
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableClasses_Inner = { "ApplicableClasses", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableClasses = { "ApplicableClasses", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationRule, ApplicableClasses), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ApplicableClasses_MetaData), NewProp_ApplicableClasses_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDesignValidationRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_RuleDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsEnabled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_bIsWarning,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableClasses_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewProp_ApplicableClasses,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDesignValidationRule_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"DesignValidationRule",
	Z_Construct_UScriptStruct_FDesignValidationRule_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationRule_Statics::PropPointers),
	sizeof(FDesignValidationRule),
	alignof(FDesignValidationRule),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationRule_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDesignValidationRule_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDesignValidationRule()
{
	if (!Z_Registration_Info_UScriptStruct_DesignValidationRule.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_DesignValidationRule.InnerSingleton, Z_Construct_UScriptStruct_FDesignValidationRule_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_DesignValidationRule.InnerSingleton;
}
// End ScriptStruct FDesignValidationRule

// Begin ScriptStruct FDesignValidationResult
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_DesignValidationResult;
class UScriptStruct* FDesignValidationResult::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_DesignValidationResult.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_DesignValidationResult.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FDesignValidationResult, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("DesignValidationResult"));
	}
	return Z_Registration_Info_UScriptStruct_DesignValidationResult.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FDesignValidationResult>()
{
	return FDesignValidationResult::StaticStruct();
}
struct Z_Construct_UScriptStruct_FDesignValidationResult_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsValid_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorCount_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningCount_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationTime_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessages_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_WarningMessages_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SuggestionMessages_MetaData[] = {
		{ "Category", "Result" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bIsValid_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsValid;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ErrorCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_WarningCount;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationTime;
	static const UECodeGen_Private::FTextPropertyParams NewProp_ErrorMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ErrorMessages;
	static const UECodeGen_Private::FTextPropertyParams NewProp_WarningMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_WarningMessages;
	static const UECodeGen_Private::FTextPropertyParams NewProp_SuggestionMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_SuggestionMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FDesignValidationResult>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
void Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_bIsValid_SetBit(void* Obj)
{
	((FDesignValidationResult*)Obj)->bIsValid = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_bIsValid = { "bIsValid", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FDesignValidationResult), &Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_bIsValid_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsValid_MetaData), NewProp_bIsValid_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorCount = { "ErrorCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, ErrorCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorCount_MetaData), NewProp_ErrorCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningCount = { "WarningCount", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, WarningCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningCount_MetaData), NewProp_WarningCount_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ValidationTime = { "ValidationTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, ValidationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationTime_MetaData), NewProp_ValidationTime_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorMessages_Inner = { "ErrorMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorMessages = { "ErrorMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, ErrorMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessages_MetaData), NewProp_ErrorMessages_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningMessages_Inner = { "WarningMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningMessages = { "WarningMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, WarningMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_WarningMessages_MetaData), NewProp_WarningMessages_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_SuggestionMessages_Inner = { "SuggestionMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_SuggestionMessages = { "SuggestionMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FDesignValidationResult, SuggestionMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SuggestionMessages_MetaData), NewProp_SuggestionMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FDesignValidationResult_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_bIsValid,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ValidationTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_ErrorMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_WarningMessages,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_SuggestionMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewProp_SuggestionMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationResult_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FDesignValidationResult_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"DesignValidationResult",
	Z_Construct_UScriptStruct_FDesignValidationResult_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationResult_Statics::PropPointers),
	sizeof(FDesignValidationResult),
	alignof(FDesignValidationResult),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FDesignValidationResult_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FDesignValidationResult_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FDesignValidationResult()
{
	if (!Z_Registration_Info_UScriptStruct_DesignValidationResult.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_DesignValidationResult.InnerSingleton, Z_Construct_UScriptStruct_FDesignValidationResult_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_DesignValidationResult.InnerSingleton;
}
// End ScriptStruct FDesignValidationResult

// Begin Delegate FOnDesignValidated
struct Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnDesignValidated_Parms
	{
		AActor* ValidatedActor;
		FDesignValidationResult Result;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ValidatedActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::NewProp_ValidatedActor = { "ValidatedActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnDesignValidated_Parms, ValidatedActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnDesignValidated_Parms, Result), Z_Construct_UScriptStruct_FDesignValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 14622868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::NewProp_ValidatedActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnDesignValidated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::_Script_SLT_eventOnDesignValidated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::_Script_SLT_eventOnDesignValidated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnDesignValidated_DelegateWrapper(const FMulticastScriptDelegate& OnDesignValidated, AActor* ValidatedActor, FDesignValidationResult const& Result)
{
	struct _Script_SLT_eventOnDesignValidated_Parms
	{
		AActor* ValidatedActor;
		FDesignValidationResult Result;
	};
	_Script_SLT_eventOnDesignValidated_Parms Parms;
	Parms.ValidatedActor=ValidatedActor;
	Parms.Result=Result;
	OnDesignValidated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnDesignValidated

// Begin Delegate FOnTemplateApplied
struct Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnTemplateApplied_Parms
	{
		AActor* TargetActor;
		FName TemplateID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_TargetActor;
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::NewProp_TargetActor = { "TargetActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnTemplateApplied_Parms, TargetActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnTemplateApplied_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::NewProp_TargetActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::NewProp_TemplateID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnTemplateApplied__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::_Script_SLT_eventOnTemplateApplied_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::_Script_SLT_eventOnTemplateApplied_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnTemplateApplied_DelegateWrapper(const FMulticastScriptDelegate& OnTemplateApplied, AActor* TargetActor, FName TemplateID)
{
	struct _Script_SLT_eventOnTemplateApplied_Parms
	{
		AActor* TargetActor;
		FName TemplateID;
	};
	_Script_SLT_eventOnTemplateApplied_Parms Parms;
	Parms.TargetActor=TargetActor;
	Parms.TemplateID=TemplateID;
	OnTemplateApplied.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnTemplateApplied

// Begin Delegate FOnVisualizationChanged
struct Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnVisualizationChanged_Parms
	{
		AActor* Actor;
		EDesignVisualizationType Type;
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Type_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Type;
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnVisualizationChanged_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Type_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Type = { "Type", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnVisualizationChanged_Parms, Type), Z_Construct_UEnum_SLT_EDesignVisualizationType, METADATA_PARAMS(0, nullptr) }; // 2455685326
void Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((_Script_SLT_eventOnVisualizationChanged_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnVisualizationChanged_Parms), &Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Type_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_Type,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnVisualizationChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::_Script_SLT_eventOnVisualizationChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::_Script_SLT_eventOnVisualizationChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnVisualizationChanged_DelegateWrapper(const FMulticastScriptDelegate& OnVisualizationChanged, AActor* Actor, EDesignVisualizationType Type, bool bEnabled)
{
	struct _Script_SLT_eventOnVisualizationChanged_Parms
	{
		AActor* Actor;
		EDesignVisualizationType Type;
		bool bEnabled;
	};
	_Script_SLT_eventOnVisualizationChanged_Parms Parms;
	Parms.Actor=Actor;
	Parms.Type=Type;
	Parms.bEnabled=bEnabled ? true : false;
	OnVisualizationChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnVisualizationChanged

// Begin Class UVisualDesignComponent Function AddValidationRule
struct Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics
{
	struct VisualDesignComponent_eventAddValidationRule_Parms
	{
		FDesignValidationRule Rule;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rule_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rule;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::NewProp_Rule = { "Rule", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventAddValidationRule_Parms, Rule), Z_Construct_UScriptStruct_FDesignValidationRule, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rule_MetaData), NewProp_Rule_MetaData) }; // 3865219701
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::NewProp_Rule,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "AddValidationRule", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::VisualDesignComponent_eventAddValidationRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::VisualDesignComponent_eventAddValidationRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execAddValidationRule)
{
	P_GET_STRUCT_REF(FDesignValidationRule,Z_Param_Out_Rule);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddValidationRule(Z_Param_Out_Rule);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function AddValidationRule

// Begin Class UVisualDesignComponent Function AlignToSurface
struct Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "AlignToSurface", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execAlignToSurface)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AlignToSurface();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function AlignToSurface

// Begin Class UVisualDesignComponent Function ApplyTemplate
struct Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics
{
	struct VisualDesignComponent_eventApplyTemplate_Parms
	{
		FName TemplateID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventApplyTemplate_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::NewProp_TemplateID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "ApplyTemplate", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::VisualDesignComponent_eventApplyTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::VisualDesignComponent_eventApplyTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execApplyTemplate)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_TemplateID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ApplyTemplate(Z_Param_TemplateID);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function ApplyTemplate

// Begin Class UVisualDesignComponent Function AutoFixCommonIssues
struct Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "AutoFixCommonIssues", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execAutoFixCommonIssues)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AutoFixCommonIssues();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function AutoFixCommonIssues

// Begin Class UVisualDesignComponent Function CopyDesignSettings
struct Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics
{
	struct VisualDesignComponent_eventCopyDesignSettings_Parms
	{
		UVisualDesignComponent* SourceComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SourceComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_SourceComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::NewProp_SourceComponent = { "SourceComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventCopyDesignSettings_Parms, SourceComponent), Z_Construct_UClass_UVisualDesignComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SourceComponent_MetaData), NewProp_SourceComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::NewProp_SourceComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "CopyDesignSettings", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::VisualDesignComponent_eventCopyDesignSettings_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::VisualDesignComponent_eventCopyDesignSettings_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execCopyDesignSettings)
{
	P_GET_OBJECT(UVisualDesignComponent,Z_Param_SourceComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CopyDesignSettings(Z_Param_SourceComponent);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function CopyDesignSettings

// Begin Class UVisualDesignComponent Function CreateCustomTemplate
struct Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics
{
	struct VisualDesignComponent_eventCreateCustomTemplate_Parms
	{
		FDesignTemplate Template;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Template_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Template;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_Template = { "Template", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventCreateCustomTemplate_Parms, Template), Z_Construct_UScriptStruct_FDesignTemplate, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Template_MetaData), NewProp_Template_MetaData) }; // 2665605094
void Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VisualDesignComponent_eventCreateCustomTemplate_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VisualDesignComponent_eventCreateCustomTemplate_Parms), &Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_Template,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "CreateCustomTemplate", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::VisualDesignComponent_eventCreateCustomTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::VisualDesignComponent_eventCreateCustomTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execCreateCustomTemplate)
{
	P_GET_STRUCT_REF(FDesignTemplate,Z_Param_Out_Template);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CreateCustomTemplate(Z_Param_Out_Template);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function CreateCustomTemplate

// Begin Class UVisualDesignComponent Function ExportDesignData
struct Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics
{
	struct VisualDesignComponent_eventExportDesignData_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventExportDesignData_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "ExportDesignData", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::VisualDesignComponent_eventExportDesignData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::VisualDesignComponent_eventExportDesignData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execExportDesignData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportDesignData(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function ExportDesignData

// Begin Class UVisualDesignComponent Function GetAvailableTemplates
struct Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics
{
	struct VisualDesignComponent_eventGetAvailableTemplates_Parms
	{
		TArray<FDesignTemplate> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Template functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Template functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDesignTemplate, METADATA_PARAMS(0, nullptr) }; // 2665605094
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventGetAvailableTemplates_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2665605094
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "GetAvailableTemplates", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::VisualDesignComponent_eventGetAvailableTemplates_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::VisualDesignComponent_eventGetAvailableTemplates_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execGetAvailableTemplates)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FDesignTemplate>*)Z_Param__Result=P_THIS->GetAvailableTemplates();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function GetAvailableTemplates

// Begin Class UVisualDesignComponent Function GetTemplate
struct Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics
{
	struct VisualDesignComponent_eventGetTemplate_Parms
	{
		FName TemplateID;
		FDesignTemplate ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Templates" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_TemplateID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::NewProp_TemplateID = { "TemplateID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventGetTemplate_Parms, TemplateID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventGetTemplate_Parms, ReturnValue), Z_Construct_UScriptStruct_FDesignTemplate, METADATA_PARAMS(0, nullptr) }; // 2665605094
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::NewProp_TemplateID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "GetTemplate", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::VisualDesignComponent_eventGetTemplate_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::VisualDesignComponent_eventGetTemplate_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_GetTemplate()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_GetTemplate_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execGetTemplate)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_TemplateID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FDesignTemplate*)Z_Param__Result=P_THIS->GetTemplate(Z_Param_TemplateID);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function GetTemplate

// Begin Class UVisualDesignComponent Function GetValidationRules
struct Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics
{
	struct VisualDesignComponent_eventGetValidationRules_Parms
	{
		TArray<FDesignValidationRule> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDesignValidationRule, METADATA_PARAMS(0, nullptr) }; // 3865219701
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventGetValidationRules_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3865219701
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "GetValidationRules", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::VisualDesignComponent_eventGetValidationRules_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::VisualDesignComponent_eventGetValidationRules_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execGetValidationRules)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FDesignValidationRule>*)Z_Param__Result=P_THIS->GetValidationRules();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function GetValidationRules

// Begin Class UVisualDesignComponent Function HideDesignHelper
struct Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "HideDesignHelper", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execHideDesignHelper)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideDesignHelper();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function HideDesignHelper

// Begin Class UVisualDesignComponent Function ImportDesignData
struct Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics
{
	struct VisualDesignComponent_eventImportDesignData_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventImportDesignData_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((VisualDesignComponent_eventImportDesignData_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(VisualDesignComponent_eventImportDesignData_Parms), &Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "ImportDesignData", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::VisualDesignComponent_eventImportDesignData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::VisualDesignComponent_eventImportDesignData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execImportDesignData)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportDesignData(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function ImportDesignData

// Begin Class UVisualDesignComponent Function OnTemplateChanged
struct VisualDesignComponent_eventOnTemplateChanged_Parms
{
	FName OldTemplate;
	FName NewTemplate;
};
static const FName NAME_UVisualDesignComponent_OnTemplateChanged = FName(TEXT("OnTemplateChanged"));
void UVisualDesignComponent::OnTemplateChanged(FName OldTemplate, FName NewTemplate)
{
	VisualDesignComponent_eventOnTemplateChanged_Parms Parms;
	Parms.OldTemplate=OldTemplate;
	Parms.NewTemplate=NewTemplate;
	UFunction* Func = FindFunctionChecked(NAME_UVisualDesignComponent_OnTemplateChanged);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_OldTemplate;
	static const UECodeGen_Private::FNamePropertyParams NewProp_NewTemplate;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::NewProp_OldTemplate = { "OldTemplate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventOnTemplateChanged_Parms, OldTemplate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::NewProp_NewTemplate = { "NewTemplate", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventOnTemplateChanged_Parms, NewTemplate), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::NewProp_OldTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::NewProp_NewTemplate,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "OnTemplateChanged", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::PropPointers), sizeof(VisualDesignComponent_eventOnTemplateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::Function_MetaDataParams) };
static_assert(sizeof(VisualDesignComponent_eventOnTemplateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UVisualDesignComponent Function OnTemplateChanged

// Begin Class UVisualDesignComponent Function OnValidationCompleted
struct VisualDesignComponent_eventOnValidationCompleted_Parms
{
	FDesignValidationResult Result;
};
static const FName NAME_UVisualDesignComponent_OnValidationCompleted = FName(TEXT("OnValidationCompleted"));
void UVisualDesignComponent::OnValidationCompleted(FDesignValidationResult const& Result)
{
	VisualDesignComponent_eventOnValidationCompleted_Parms Parms;
	Parms.Result=Result;
	UFunction* Func = FindFunctionChecked(NAME_UVisualDesignComponent_OnValidationCompleted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Result_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Result;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::NewProp_Result = { "Result", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventOnValidationCompleted_Parms, Result), Z_Construct_UScriptStruct_FDesignValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Result_MetaData), NewProp_Result_MetaData) }; // 14622868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::NewProp_Result,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "OnValidationCompleted", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::PropPointers), sizeof(VisualDesignComponent_eventOnValidationCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08480800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::Function_MetaDataParams) };
static_assert(sizeof(VisualDesignComponent_eventOnValidationCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UVisualDesignComponent Function OnValidationCompleted

// Begin Class UVisualDesignComponent Function OnVisualizationUpdated
static const FName NAME_UVisualDesignComponent_OnVisualizationUpdated = FName(TEXT("OnVisualizationUpdated"));
void UVisualDesignComponent::OnVisualizationUpdated()
{
	UFunction* Func = FindFunctionChecked(NAME_UVisualDesignComponent_OnVisualizationUpdated);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "OnVisualizationUpdated", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UVisualDesignComponent Function OnVisualizationUpdated

// Begin Class UVisualDesignComponent Function RemoveValidationRule
struct Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics
{
	struct VisualDesignComponent_eventRemoveValidationRule_Parms
	{
		FName RuleID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_RuleID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::NewProp_RuleID = { "RuleID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventRemoveValidationRule_Parms, RuleID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::NewProp_RuleID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "RemoveValidationRule", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::VisualDesignComponent_eventRemoveValidationRule_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::VisualDesignComponent_eventRemoveValidationRule_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execRemoveValidationRule)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_RuleID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RemoveValidationRule(Z_Param_RuleID);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function RemoveValidationRule

// Begin Class UVisualDesignComponent Function SetVisualizationType
struct Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics
{
	struct VisualDesignComponent_eventSetVisualizationType_Parms
	{
		EDesignVisualizationType NewType;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main design functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main design functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewType;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::NewProp_NewType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::NewProp_NewType = { "NewType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventSetVisualizationType_Parms, NewType), Z_Construct_UEnum_SLT_EDesignVisualizationType, METADATA_PARAMS(0, nullptr) }; // 2455685326
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::NewProp_NewType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::NewProp_NewType,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "SetVisualizationType", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::VisualDesignComponent_eventSetVisualizationType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::VisualDesignComponent_eventSetVisualizationType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execSetVisualizationType)
{
	P_GET_ENUM(EDesignVisualizationType,Z_Param_NewType);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetVisualizationType(EDesignVisualizationType(Z_Param_NewType));
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function SetVisualizationType

// Begin Class UVisualDesignComponent Function ShowDesignHelper
struct Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "ShowDesignHelper", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execShowDesignHelper)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowDesignHelper();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function ShowDesignHelper

// Begin Class UVisualDesignComponent Function SnapToGrid
struct Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics
{
	struct VisualDesignComponent_eventSnapToGrid_Parms
	{
		float GridSize;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "CPP_Default_GridSize", "100.000000" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_GridSize;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::NewProp_GridSize = { "GridSize", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventSnapToGrid_Parms, GridSize), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::NewProp_GridSize,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "SnapToGrid", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::VisualDesignComponent_eventSnapToGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::VisualDesignComponent_eventSnapToGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execSnapToGrid)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_GridSize);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SnapToGrid(Z_Param_GridSize);
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function SnapToGrid

// Begin Class UVisualDesignComponent Function ValidateDesign
struct Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics
{
	struct VisualDesignComponent_eventValidateDesign_Parms
	{
		FDesignValidationResult ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Design" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(VisualDesignComponent_eventValidateDesign_Parms, ReturnValue), Z_Construct_UScriptStruct_FDesignValidationResult, METADATA_PARAMS(0, nullptr) }; // 14622868
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UVisualDesignComponent, nullptr, "ValidateDesign", nullptr, nullptr, Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::PropPointers), sizeof(Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::VisualDesignComponent_eventValidateDesign_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::Function_MetaDataParams), Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::VisualDesignComponent_eventValidateDesign_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UVisualDesignComponent::execValidateDesign)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FDesignValidationResult*)Z_Param__Result=P_THIS->ValidateDesign();
	P_NATIVE_END;
}
// End Class UVisualDesignComponent Function ValidateDesign

// Begin Class UVisualDesignComponent
void UVisualDesignComponent::StaticRegisterNativesUVisualDesignComponent()
{
	UClass* Class = UVisualDesignComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddValidationRule", &UVisualDesignComponent::execAddValidationRule },
		{ "AlignToSurface", &UVisualDesignComponent::execAlignToSurface },
		{ "ApplyTemplate", &UVisualDesignComponent::execApplyTemplate },
		{ "AutoFixCommonIssues", &UVisualDesignComponent::execAutoFixCommonIssues },
		{ "CopyDesignSettings", &UVisualDesignComponent::execCopyDesignSettings },
		{ "CreateCustomTemplate", &UVisualDesignComponent::execCreateCustomTemplate },
		{ "ExportDesignData", &UVisualDesignComponent::execExportDesignData },
		{ "GetAvailableTemplates", &UVisualDesignComponent::execGetAvailableTemplates },
		{ "GetTemplate", &UVisualDesignComponent::execGetTemplate },
		{ "GetValidationRules", &UVisualDesignComponent::execGetValidationRules },
		{ "HideDesignHelper", &UVisualDesignComponent::execHideDesignHelper },
		{ "ImportDesignData", &UVisualDesignComponent::execImportDesignData },
		{ "RemoveValidationRule", &UVisualDesignComponent::execRemoveValidationRule },
		{ "SetVisualizationType", &UVisualDesignComponent::execSetVisualizationType },
		{ "ShowDesignHelper", &UVisualDesignComponent::execShowDesignHelper },
		{ "SnapToGrid", &UVisualDesignComponent::execSnapToGrid },
		{ "ValidateDesign", &UVisualDesignComponent::execValidateDesign },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UVisualDesignComponent);
UClass* Z_Construct_UClass_UVisualDesignComponent_NoRegister()
{
	return UVisualDesignComponent::StaticClass();
}
struct Z_Construct_UClass_UVisualDesignComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "Core/Design/VisualDesignTools.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationType_MetaData[] = {
		{ "Category", "Visualization" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Design visualization settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Design visualization settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInGame_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowInEditor_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationColor_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationScale_MetaData[] = {
		{ "Category", "Visualization" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTemplate_MetaData[] = {
		{ "Category", "Template" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Design template\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Design template" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoApplyTemplate_MetaData[] = {
		{ "Category", "Template" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableValidation_MetaData[] = {
		{ "Category", "Validation" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Validation settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Validation settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoValidate_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValidationInterval_MetaData[] = {
		{ "Category", "Validation" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignNotes_MetaData[] = {
		{ "Category", "Metadata" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Design metadata\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Design metadata" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignTags_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DesignComplexity_MetaData[] = {
		{ "Category", "Metadata" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnDesignValidated_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnTemplateApplied_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnVisualizationChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_VisualizationMesh_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual components\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual components" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InfoWidget_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LabelText_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EffectComponent_MetaData[] = {
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastValidationResult_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastValidationTime_MetaData[] = {
		{ "ModuleRelativePath", "Core/Design/VisualDesignTools.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_VisualizationType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_VisualizationType;
	static void NewProp_bShowInGame_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInGame;
	static void NewProp_bShowInEditor_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowInEditor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_VisualizationColor;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_VisualizationScale;
	static const UECodeGen_Private::FNamePropertyParams NewProp_CurrentTemplate;
	static void NewProp_bAutoApplyTemplate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoApplyTemplate;
	static void NewProp_bEnableValidation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableValidation;
	static void NewProp_bAutoValidate_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoValidate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValidationInterval;
	static const UECodeGen_Private::FTextPropertyParams NewProp_DesignNotes;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DesignTags;
	static const UECodeGen_Private::FBytePropertyParams NewProp_DesignComplexity_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_DesignComplexity;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnDesignValidated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnTemplateApplied;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnVisualizationChanged;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_VisualizationMesh;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InfoWidget;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LabelText;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EffectComponent;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastValidationResult;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastValidationTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UVisualDesignComponent_AddValidationRule, "AddValidationRule" }, // 539912233
		{ &Z_Construct_UFunction_UVisualDesignComponent_AlignToSurface, "AlignToSurface" }, // 2698070895
		{ &Z_Construct_UFunction_UVisualDesignComponent_ApplyTemplate, "ApplyTemplate" }, // 4058418036
		{ &Z_Construct_UFunction_UVisualDesignComponent_AutoFixCommonIssues, "AutoFixCommonIssues" }, // 2098236304
		{ &Z_Construct_UFunction_UVisualDesignComponent_CopyDesignSettings, "CopyDesignSettings" }, // 2627898459
		{ &Z_Construct_UFunction_UVisualDesignComponent_CreateCustomTemplate, "CreateCustomTemplate" }, // 2185175786
		{ &Z_Construct_UFunction_UVisualDesignComponent_ExportDesignData, "ExportDesignData" }, // 2874353806
		{ &Z_Construct_UFunction_UVisualDesignComponent_GetAvailableTemplates, "GetAvailableTemplates" }, // 3577062541
		{ &Z_Construct_UFunction_UVisualDesignComponent_GetTemplate, "GetTemplate" }, // 629476187
		{ &Z_Construct_UFunction_UVisualDesignComponent_GetValidationRules, "GetValidationRules" }, // 620299938
		{ &Z_Construct_UFunction_UVisualDesignComponent_HideDesignHelper, "HideDesignHelper" }, // 3904905066
		{ &Z_Construct_UFunction_UVisualDesignComponent_ImportDesignData, "ImportDesignData" }, // 4277225608
		{ &Z_Construct_UFunction_UVisualDesignComponent_OnTemplateChanged, "OnTemplateChanged" }, // 3271646551
		{ &Z_Construct_UFunction_UVisualDesignComponent_OnValidationCompleted, "OnValidationCompleted" }, // 1129741754
		{ &Z_Construct_UFunction_UVisualDesignComponent_OnVisualizationUpdated, "OnVisualizationUpdated" }, // 1390826370
		{ &Z_Construct_UFunction_UVisualDesignComponent_RemoveValidationRule, "RemoveValidationRule" }, // 1173928550
		{ &Z_Construct_UFunction_UVisualDesignComponent_SetVisualizationType, "SetVisualizationType" }, // 3034904485
		{ &Z_Construct_UFunction_UVisualDesignComponent_ShowDesignHelper, "ShowDesignHelper" }, // 1587271008
		{ &Z_Construct_UFunction_UVisualDesignComponent_SnapToGrid, "SnapToGrid" }, // 503699795
		{ &Z_Construct_UFunction_UVisualDesignComponent_ValidateDesign, "ValidateDesign" }, // 94586989
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UVisualDesignComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationType = { "VisualizationType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, VisualizationType), Z_Construct_UEnum_SLT_EDesignVisualizationType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationType_MetaData), NewProp_VisualizationType_MetaData) }; // 2455685326
void Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInGame_SetBit(void* Obj)
{
	((UVisualDesignComponent*)Obj)->bShowInGame = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInGame = { "bShowInGame", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVisualDesignComponent), &Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInGame_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInGame_MetaData), NewProp_bShowInGame_MetaData) };
void Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInEditor_SetBit(void* Obj)
{
	((UVisualDesignComponent*)Obj)->bShowInEditor = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInEditor = { "bShowInEditor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVisualDesignComponent), &Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInEditor_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowInEditor_MetaData), NewProp_bShowInEditor_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationColor = { "VisualizationColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, VisualizationColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationColor_MetaData), NewProp_VisualizationColor_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationScale = { "VisualizationScale", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, VisualizationScale), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationScale_MetaData), NewProp_VisualizationScale_MetaData) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_CurrentTemplate = { "CurrentTemplate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, CurrentTemplate), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTemplate_MetaData), NewProp_CurrentTemplate_MetaData) };
void Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoApplyTemplate_SetBit(void* Obj)
{
	((UVisualDesignComponent*)Obj)->bAutoApplyTemplate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoApplyTemplate = { "bAutoApplyTemplate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVisualDesignComponent), &Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoApplyTemplate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoApplyTemplate_MetaData), NewProp_bAutoApplyTemplate_MetaData) };
void Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bEnableValidation_SetBit(void* Obj)
{
	((UVisualDesignComponent*)Obj)->bEnableValidation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bEnableValidation = { "bEnableValidation", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVisualDesignComponent), &Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bEnableValidation_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableValidation_MetaData), NewProp_bEnableValidation_MetaData) };
void Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoValidate_SetBit(void* Obj)
{
	((UVisualDesignComponent*)Obj)->bAutoValidate = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoValidate = { "bAutoValidate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UVisualDesignComponent), &Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoValidate_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoValidate_MetaData), NewProp_bAutoValidate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_ValidationInterval = { "ValidationInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, ValidationInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValidationInterval_MetaData), NewProp_ValidationInterval_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignNotes = { "DesignNotes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, DesignNotes), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignNotes_MetaData), NewProp_DesignNotes_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignTags = { "DesignTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, DesignTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignTags_MetaData), NewProp_DesignTags_MetaData) }; // 3352185621
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignComplexity_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignComplexity = { "DesignComplexity", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, DesignComplexity), Z_Construct_UEnum_SLT_EDesignComplexity, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DesignComplexity_MetaData), NewProp_DesignComplexity_MetaData) }; // 4290637674
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnDesignValidated = { "OnDesignValidated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, OnDesignValidated), Z_Construct_UDelegateFunction_SLT_OnDesignValidated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnDesignValidated_MetaData), NewProp_OnDesignValidated_MetaData) }; // 1501867103
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnTemplateApplied = { "OnTemplateApplied", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, OnTemplateApplied), Z_Construct_UDelegateFunction_SLT_OnTemplateApplied__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnTemplateApplied_MetaData), NewProp_OnTemplateApplied_MetaData) }; // 2355432968
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnVisualizationChanged = { "OnVisualizationChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, OnVisualizationChanged), Z_Construct_UDelegateFunction_SLT_OnVisualizationChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnVisualizationChanged_MetaData), NewProp_OnVisualizationChanged_MetaData) }; // 2008636084
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationMesh = { "VisualizationMesh", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, VisualizationMesh), Z_Construct_UClass_UStaticMeshComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_VisualizationMesh_MetaData), NewProp_VisualizationMesh_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_InfoWidget = { "InfoWidget", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, InfoWidget), Z_Construct_UClass_UWidgetComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InfoWidget_MetaData), NewProp_InfoWidget_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LabelText = { "LabelText", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, LabelText), Z_Construct_UClass_UTextRenderComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LabelText_MetaData), NewProp_LabelText_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_EffectComponent = { "EffectComponent", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, EffectComponent), Z_Construct_UClass_UParticleSystemComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EffectComponent_MetaData), NewProp_EffectComponent_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LastValidationResult = { "LastValidationResult", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, LastValidationResult), Z_Construct_UScriptStruct_FDesignValidationResult, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastValidationResult_MetaData), NewProp_LastValidationResult_MetaData) }; // 14622868
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LastValidationTime = { "LastValidationTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UVisualDesignComponent, LastValidationTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastValidationTime_MetaData), NewProp_LastValidationTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UVisualDesignComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInGame,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bShowInEditor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationColor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationScale,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_CurrentTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoApplyTemplate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bEnableValidation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_bAutoValidate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_ValidationInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignNotes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignComplexity_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_DesignComplexity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnDesignValidated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnTemplateApplied,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_OnVisualizationChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_VisualizationMesh,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_InfoWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LabelText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_EffectComponent,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LastValidationResult,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UVisualDesignComponent_Statics::NewProp_LastValidationTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVisualDesignComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UVisualDesignComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UVisualDesignComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UVisualDesignComponent_Statics::ClassParams = {
	&UVisualDesignComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UVisualDesignComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UVisualDesignComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UVisualDesignComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UVisualDesignComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UVisualDesignComponent()
{
	if (!Z_Registration_Info_UClass_UVisualDesignComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UVisualDesignComponent.OuterSingleton, Z_Construct_UClass_UVisualDesignComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UVisualDesignComponent.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UVisualDesignComponent>()
{
	return UVisualDesignComponent::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UVisualDesignComponent);
UVisualDesignComponent::~UVisualDesignComponent() {}
// End Class UVisualDesignComponent

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EDesignVisualizationType_StaticEnum, TEXT("EDesignVisualizationType"), &Z_Registration_Info_UEnum_EDesignVisualizationType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 2455685326U) },
		{ EDesignComplexity_StaticEnum, TEXT("EDesignComplexity"), &Z_Registration_Info_UEnum_EDesignComplexity, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 4290637674U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FDesignTemplate::StaticStruct, Z_Construct_UScriptStruct_FDesignTemplate_Statics::NewStructOps, TEXT("DesignTemplate"), &Z_Registration_Info_UScriptStruct_DesignTemplate, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDesignTemplate), 2665605094U) },
		{ FDesignValidationRule::StaticStruct, Z_Construct_UScriptStruct_FDesignValidationRule_Statics::NewStructOps, TEXT("DesignValidationRule"), &Z_Registration_Info_UScriptStruct_DesignValidationRule, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDesignValidationRule), 3865219701U) },
		{ FDesignValidationResult::StaticStruct, Z_Construct_UScriptStruct_FDesignValidationResult_Statics::NewStructOps, TEXT("DesignValidationResult"), &Z_Registration_Info_UScriptStruct_DesignValidationResult, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FDesignValidationResult), 14622868U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UVisualDesignComponent, UVisualDesignComponent::StaticClass, TEXT("UVisualDesignComponent"), &Z_Registration_Info_UClass_UVisualDesignComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UVisualDesignComponent), 367621022U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_3734622697(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Design_VisualDesignTools_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
