#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "GameplayTagContainer.h"
#include "EnemyCharacter.generated.h"

class UBehaviorTreeComponent;
class UBlackboardComponent;
class UPawnSensingComponent;
class UWidgetComponent;

UENUM(BlueprintType)
enum class EEnemyState : uint8
{
	Idle			UMETA(DisplayName = "Idle"),
	Patrol			UMETA(DisplayName = "Patrol"),
	Alert			UMETA(DisplayName = "Alert"),
	Investigating	UMETA(DisplayName = "Investigating"),
	Chasing			UMETA(DisplayName = "Chasing"),
	Attacking		UMETA(DisplayName = "Attacking"),
	Stunned			UMETA(DisplayName = "Stunned"),
	Dead			UMETA(DisplayName = "Dead")
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyStateChanged, EEnemyState, OldState, EEnemyState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnEnemyDamaged, AEnemyCharacter*, Enemy, float, DamageAmount, AActor*, DamageSource);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnEnemyDeath, AEnemyCharacter*, Enemy, AActor*, Killer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlayerDetected, AEnemyCharacter*, Enemy, APawn*, DetectedPlayer);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerLost, AEnemyCharacter*, Enemy);

UCLASS(BlueprintType, Blueprintable)
class SLT_API AEnemyCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	AEnemyCharacter();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;

	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UPawnSensingComponent* PawnSensingComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UWidgetComponent* HealthBarWidget;

	// Enemy stats
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats", meta = (ClampMin = "0.0"))
	float MaxHealth = 100.0f;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Stats")
	float CurrentHealth = 100.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats", meta = (ClampMin = "0.0"))
	float AttackDamage = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats", meta = (ClampMin = "0.0"))
	float AttackRange = 150.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stats", meta = (ClampMin = "0.0"))
	float AttackCooldown = 2.0f;

	// AI Behavior
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI")
	class UBehaviorTree* BehaviorTree;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI", meta = (ClampMin = "0.0"))
	float SightRadius = 800.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI", meta = (ClampMin = "0.0"))
	float HearingRadius = 600.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI", meta = (ClampMin = "0.0"))
	float LosePlayerTime = 5.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI", meta = (ClampMin = "0.0"))
	float PatrolRadius = 500.0f;

	// State management
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	EEnemyState CurrentState = EEnemyState::Idle;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	bool bIsDead = false;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	APawn* TargetPlayer = nullptr;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	FVector LastKnownPlayerLocation = FVector::ZeroVector;

	// Patrol settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	TArray<FVector> PatrolPoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	bool bUseRandomPatrol = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol", meta = (ClampMin = "0.0"))
	float PatrolWaitTime = 3.0f;

	// Loot settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot")
	TArray<FName> LootTable;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float LootDropChance = 0.5f;

	// Gameplay tags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer EnemyTags;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyStateChanged OnEnemyStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyDamaged OnEnemyDamaged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnEnemyDeath OnEnemyDeath;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPlayerDetected OnPlayerDetected;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPlayerLost OnPlayerLost;

	// Main functions
	UFUNCTION(BlueprintCallable, Category = "Combat")
	virtual void TakeDamage(float DamageAmount, AActor* DamageSource = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Combat")
	virtual void Die(AActor* Killer = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Combat")
	virtual bool CanAttack() const;

	UFUNCTION(BlueprintCallable, Category = "Combat")
	virtual void Attack(AActor* Target);

	UFUNCTION(BlueprintCallable, Category = "AI")
	virtual void SetEnemyState(EEnemyState NewState);

	UFUNCTION(BlueprintCallable, Category = "AI")
	virtual void StartPatrol();

	UFUNCTION(BlueprintCallable, Category = "AI")
	virtual void StartChasing(APawn* Player);

	UFUNCTION(BlueprintCallable, Category = "AI")
	virtual void LosePlayer();

	UFUNCTION(BlueprintCallable, Category = "AI")
	virtual void Investigate(const FVector& Location);

	// Utility functions
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
	bool IsAlive() const { return !bIsDead && CurrentHealth > 0.0f; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "State")
	float GetHealthPercentage() const { return MaxHealth > 0.0f ? CurrentHealth / MaxHealth : 0.0f; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AI")
	bool HasLineOfSightToPlayer() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "AI")
	float GetDistanceToPlayer() const;

	UFUNCTION(BlueprintCallable, Category = "Loot")
	virtual void SpawnLoot();

protected:
	// Internal state
	float LastAttackTime = 0.0f;
	int32 CurrentPatrolIndex = 0;
	FVector HomeLocation;

	// Sensing callbacks
	UFUNCTION()
	void OnSeePawn(APawn* Pawn);

	UFUNCTION()
	void OnHearNoise(APawn* NoiseInstigator, const FVector& Location, float Volume);

	// Timer handles
	FTimerHandle LosePlayerTimerHandle;
	FTimerHandle PatrolTimerHandle;

	// Internal functions
	void InitializeAI();
	void UpdateHealthBar();
	FVector GetNextPatrolPoint();
	void OnLosePlayerTimer();

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStateChanged(EEnemyState OldState, EEnemyState NewState);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnDamageReceived(float DamageAmount, AActor* DamageSource);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnDeathEvent(AActor* Killer);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnAttackExecuted(AActor* Target);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPlayerSpotted(APawn* Player);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnInvestigationStarted(const FVector& Location);
};
