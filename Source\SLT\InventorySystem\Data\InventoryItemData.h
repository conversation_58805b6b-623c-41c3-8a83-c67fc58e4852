#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "InventoryItemData.generated.h"

class UTexture2D;
class UStaticMesh;
class AActor;

UENUM(BlueprintType)
enum class EItemType : uint8
{
	None			UMETA(DisplayName = "None"),
	Weapon			UMETA(DisplayName = "Weapon"),
	Ammo			UMETA(DisplayName = "Ammo"),
	Consumable		UMETA(DisplayName = "Consumable"),
	KeyItem			UMETA(DisplayName = "Key Item"),
	Treasure		UMETA(DisplayName = "Treasure"),
	Material		UMETA(DisplayName = "Material")
};

UENUM(BlueprintType)
enum class EItemRarity : uint8
{
	Common			UMETA(DisplayName = "Common"),
	Uncommon		UMETA(DisplayName = "Uncommon"),
	Rare			UMETA(DisplayName = "Rare"),
	Epic			UMETA(DisplayName = "Epic"),
	Legendary		UMETA(DisplayName = "Legendary")
};

USTRUCT(BlueprintType)
struct FInventoryItemData : public FTableRowBase
{
	GENERATED_BODY()

	FInventoryItemData()
	{
		ID = NAME_None;
		Name = FText::GetEmpty();
		Description = FText::GetEmpty();
		Icon = nullptr;
		Width = 1;
		Height = 1;
		bCanRotate = true;
		bCanStack = false;
		MaxStackSize = 1;
		PickupMesh = nullptr;
		ItemClass = nullptr;
		ItemType = EItemType::None;
		ItemRarity = EItemRarity::Common;
		Value = 0;
		Weight = 0.0f;
		bIsQuestItem = false;
		bCanBeDropped = true;
		bCanBeSold = true;
	}

	// Unique identifier for this item
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Data")
	FName ID;

	// Display name of the item
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Data")
	FText Name;

	// Description shown in tooltips
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Data")
	FText Description;

	// Icon displayed in inventory
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Data")
	TSoftObjectPtr<UTexture2D> Icon;

	// Grid dimensions
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid", meta = (ClampMin = "1", ClampMax = "10"))
	int32 Width;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid", meta = (ClampMin = "1", ClampMax = "10"))
	int32 Height;

	// Can this item be rotated in the grid?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Grid")
	bool bCanRotate;

	// Stacking properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stacking")
	bool bCanStack;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Stacking", meta = (ClampMin = "1", ClampMax = "999"))
	int32 MaxStackSize;

	// 3D mesh for world pickup
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World")
	TSoftObjectPtr<UStaticMesh> PickupMesh;

	// Actor class to spawn when using this item
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "World")
	TSoftClassPtr<AActor> ItemClass;

	// Item categorization
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	EItemType ItemType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	EItemRarity ItemRarity;

	// Gameplay tags for additional categorization
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	FGameplayTagContainer ItemTags;

	// Economic properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties", meta = (ClampMin = "0"))
	int32 Value;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties", meta = (ClampMin = "0.0"))
	float Weight;

	// Quest and interaction flags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsQuestItem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bCanBeDropped;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bCanBeSold;

	// Custom data for specific item types (weapons, consumables, etc.)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom Data")
	TMap<FString, FString> CustomProperties;
};

USTRUCT(BlueprintType)
struct FInventorySlot
{
	GENERATED_BODY()

	FInventorySlot()
	{
		ItemData = FInventoryItemData();
		Quantity = 0;
		bIsRotated = false;
		GridX = 0;
		GridY = 0;
		SlotID = FGuid::NewGuid();
	}

	// The item data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	FInventoryItemData ItemData;

	// Current quantity (for stackable items)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	int32 Quantity;

	// Is the item rotated in the grid?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	bool bIsRotated;

	// Grid position
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	int32 GridX;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	int32 GridY;

	// Unique identifier for this slot instance
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	FGuid SlotID;

	// Custom runtime data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Slot")
	TMap<FString, FString> RuntimeData;
};

USTRUCT(BlueprintType)
struct FInventorySlotSaveData
{
	GENERATED_BODY()

	FInventorySlotSaveData()
	{
		ItemID = NAME_None;
		Quantity = 0;
		bIsRotated = false;
		GridX = 0;
		GridY = 0;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FName ItemID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 Quantity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	bool bIsRotated;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 GridX;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 GridY;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FString> RuntimeData;
};
