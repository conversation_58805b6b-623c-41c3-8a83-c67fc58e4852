// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Enemies/EnemyCharacter.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AActor;
class AEnemyCharacter;
class APawn;
enum class EEnemyState : uint8;
#ifdef SLT_EnemyCharacter_generated_h
#error "EnemyCharacter.generated.h already included, missing '#pragma once' in EnemyCharacter.h"
#endif
#define SLT_EnemyCharacter_generated_h

#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_26_DELEGATE \
SLT_API void FOnEnemyStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyStateChanged, EEnemyState OldState, EEnemyState NewState);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_27_DELEGATE \
SLT_API void FOnEnemyDamaged_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDamaged, AEnemyCharacter* Enemy, float DamageAmount, AActor* DamageSource);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_28_DELEGATE \
SLT_API void FOnEnemyDeath_DelegateWrapper(const FMulticastScriptDelegate& OnEnemyDeath, AEnemyCharacter* Enemy, AActor* Killer);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_29_DELEGATE \
SLT_API void FOnPlayerDetected_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerDetected, AEnemyCharacter* Enemy, APawn* DetectedPlayer);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_30_DELEGATE \
SLT_API void FOnPlayerLost_DelegateWrapper(const FMulticastScriptDelegate& OnPlayerLost, AEnemyCharacter* Enemy);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnHearNoise); \
	DECLARE_FUNCTION(execOnSeePawn); \
	DECLARE_FUNCTION(execSpawnLoot); \
	DECLARE_FUNCTION(execGetDistanceToPlayer); \
	DECLARE_FUNCTION(execHasLineOfSightToPlayer); \
	DECLARE_FUNCTION(execGetHealthPercentage); \
	DECLARE_FUNCTION(execIsAlive); \
	DECLARE_FUNCTION(execInvestigate); \
	DECLARE_FUNCTION(execLosePlayer); \
	DECLARE_FUNCTION(execStartChasing); \
	DECLARE_FUNCTION(execStartPatrol); \
	DECLARE_FUNCTION(execSetEnemyState); \
	DECLARE_FUNCTION(execAttack); \
	DECLARE_FUNCTION(execCanAttack); \
	DECLARE_FUNCTION(execDie); \
	DECLARE_FUNCTION(execTakeDamage);


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAEnemyCharacter(); \
	friend struct Z_Construct_UClass_AEnemyCharacter_Statics; \
public: \
	DECLARE_CLASS(AEnemyCharacter, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(AEnemyCharacter)


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AEnemyCharacter(AEnemyCharacter&&); \
	AEnemyCharacter(const AEnemyCharacter&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AEnemyCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AEnemyCharacter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AEnemyCharacter) \
	NO_API virtual ~AEnemyCharacter();


#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_32_PROLOG
#define FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h_35_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class AEnemyCharacter>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Enemies_EnemyCharacter_h


#define FOREACH_ENUM_EENEMYSTATE(op) \
	op(EEnemyState::Idle) \
	op(EEnemyState::Patrol) \
	op(EEnemyState::Alert) \
	op(EEnemyState::Investigating) \
	op(EEnemyState::Chasing) \
	op(EEnemyState::Attacking) \
	op(EEnemyState::Stunned) \
	op(EEnemyState::Dead) 

enum class EEnemyState : uint8;
template<> struct TIsUEnumClass<EEnemyState> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EEnemyState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
