/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NATVIS:"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RenderCore\RenderCore.natvis"
/NATVIS:"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\MovieScene\MovieScene.natvis"
/NATVIS:"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\Niagara\Niagara.natvis"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\SLTEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.ValApi.Cpp20.InclOrderUnreal5_3.h.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.1.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.2.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.3.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.4.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.5.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.6.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Module.SLT.7.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\MyClass.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\SLT.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\AIBehaviorDesigner.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\AutomatedSetupWizard.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\DragDropLevelDesigner.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\VisualDesignTools.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\EnemyCharacter.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Interactable.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\SLTPlayerCharacter.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InventorySaveGame.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\LevelProgressionManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\LoadingManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ObjectPoolManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ResourceManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ShaderManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\StatisticsManager.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\WeaponSystem.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\ItemActor.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InteractionComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\InventoryGridComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\PuzzleActor.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\PuzzleComponent.cpp.obj"
"G:\Gamedev\SLT\Intermediate\Build\Win64\x64\UnrealEditor\Development\SLT\Default.rc2.res"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Slate\UnrealEditor-Slate.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SlateCore\UnrealEditor-SlateCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\ToolMenus\UnrealEditor-ToolMenus.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EditorStyle\UnrealEditor-EditorStyle.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EditorWidgets\UnrealEditor-EditorWidgets.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\UnrealEditor-UnrealEd.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RenderCore\UnrealEditor-RenderCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RHI\UnrealEditor-RHI.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\EngineSettings\UnrealEditor-EngineSettings.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\LevelSequence\UnrealEditor-LevelSequence.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\MovieScene\UnrealEditor-MovieScene.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AudioMixer\UnrealEditor-AudioMixer.lib"
"..\Plugins\Runtime\SignificanceManager\Intermediate\Build\Win64\x64\UnrealEditor\Development\SignificanceManager\UnrealEditor-SignificanceManager.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\BehaviorTreeEditor\UnrealEditor-BehaviorTreeEditor.lib"
"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\Niagara\UnrealEditor-Niagara.lib"
"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\NiagaraCore\UnrealEditor-NiagaraCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Projects\UnrealEditor-Projects.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\HTTP\UnrealEditor-HTTP.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Networking\UnrealEditor-Networking.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputCore\UnrealEditor-InputCore.lib"
"..\Plugins\EnhancedInput\Intermediate\Build\Win64\x64\UnrealEditor\Development\EnhancedInput\UnrealEditor-EnhancedInput.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UMG\UnrealEditor-UMG.lib"
"..\Plugins\Runtime\CommonUI\Intermediate\Build\Win64\x64\UnrealEditor\Development\CommonUI\UnrealEditor-CommonUI.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayTags\UnrealEditor-GameplayTags.lib"
"..\Plugins\Runtime\GameplayAbilities\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayAbilities\UnrealEditor-GameplayAbilities.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\GameplayTasks\UnrealEditor-GameplayTasks.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AIModule\UnrealEditor-AIModule.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\NavigationSystem\UnrealEditor-NavigationSystem.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\DeveloperSettings\UnrealEditor-DeveloperSettings.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Json\UnrealEditor-Json.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\JsonUtilities\UnrealEditor-JsonUtilities.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"G:\Gamedev\SLT\Binaries\Win64\UnrealEditor-SLT.dll"
/PDB:"G:\Gamedev\SLT\Binaries\Win64\UnrealEditor-SLT.pdb"
/ignore:4078