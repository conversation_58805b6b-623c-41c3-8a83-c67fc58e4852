#include "VisualDesignTools.h"
#include "Components/StaticMeshComponent.h"
#include "Components/WidgetComponent.h"
#include "Components/TextRenderComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/StaticMesh.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"

UVisualDesignComponent::UVisualDesignComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.1f;

	VisualizationType = EDesignVisualizationType::None;
	bShowInGame = false;
	bShowInEditor = true;
	VisualizationColor = FLinearColor::Green;
	VisualizationScale = 1.0f;
	CurrentTemplate = NAME_None;
	bAutoApplyTemplate = true;
	bEnableValidation = true;
	bAutoValidate = true;
	ValidationInterval = 1.0f;
	DesignComplexity = EDesignComplexity::Beginner;
	LastValidationTime = 0.0f;

	VisualizationMesh = nullptr;
	InfoWidget = nullptr;
	LabelText = nullptr;
	EffectComponent = nullptr;
}

void UVisualDesignComponent::BeginPlay()
{
	Super::BeginPlay();
	
	CreateVisualizationComponents();
	UpdateVisualization();
	
	if (bAutoValidate)
	{
		ValidateDesign();
	}
}

void UVisualDesignComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// Auto-validation
	if (bEnableValidation && bAutoValidate)
	{
		float CurrentTime = GetWorld()->GetTimeSeconds();
		if (CurrentTime - LastValidationTime >= ValidationInterval)
		{
			ValidateDesign();
			LastValidationTime = CurrentTime;
		}
	}

	// Update visualization in editor
	#if WITH_EDITOR
	if (bShowInEditor)
	{
		UpdateVisualization();
	}
	#endif
}

void UVisualDesignComponent::SetVisualizationType(EDesignVisualizationType NewType)
{
	if (VisualizationType != NewType)
	{
		EDesignVisualizationType OldType = VisualizationType;
		VisualizationType = NewType;
		
		UpdateVisualization();
		OnVisualizationChanged.Broadcast(GetOwner(), NewType, true);
	}
}

void UVisualDesignComponent::ApplyTemplate(FName TemplateID)
{
	if (TemplateID == NAME_None)
	{
		return;
	}

	FDesignTemplate Template = GetTemplate(TemplateID);
	if (Template.TemplateID == NAME_None)
	{
		UE_LOG(LogTemp, Warning, TEXT("Template %s not found"), *TemplateID.ToString());
		return;
	}

	FName OldTemplate = CurrentTemplate;
	CurrentTemplate = TemplateID;

	// Apply template properties
	for (const auto& PropertyPair : Template.DefaultProperties)
	{
		// This would set properties on the owner actor
		// Implementation depends on reflection system
	}

	// Update design complexity
	DesignComplexity = Template.Complexity;

	// Update tags
	DesignTags.AppendTags(Template.TemplateTags);

	OnTemplateApplied.Broadcast(GetOwner(), TemplateID);
	OnTemplateChanged(OldTemplate, TemplateID);

	// Re-validate after template application
	if (bEnableValidation)
	{
		ValidateDesign();
	}
}

FDesignValidationResult UVisualDesignComponent::ValidateDesign()
{
	FDesignValidationResult Result;
	Result.bIsValid = true;
	Result.ErrorCount = 0;
	Result.WarningCount = 0;
	Result.ValidationTime = GetWorld()->GetTimeSeconds();

	if (!bEnableValidation)
	{
		return Result;
	}

	// Get validation rules
	TArray<FDesignValidationRule> Rules = GetValidationRules();

	// Run each applicable rule
	for (const FDesignValidationRule& Rule : Rules)
	{
		if (!Rule.bIsEnabled)
		{
			continue;
		}

		// Check if rule applies to this actor
		bool bRuleApplies = false;
		
		if (Rule.ApplicableTags.Num() > 0)
		{
			bRuleApplies = DesignTags.HasAny(Rule.ApplicableTags);
		}
		
		if (!bRuleApplies && Rule.ApplicableClasses.Num() > 0)
		{
			for (TSubclassOf<AActor> ActorClass : Rule.ApplicableClasses)
			{
				if (GetOwner()->IsA(ActorClass))
				{
					bRuleApplies = true;
					break;
				}
			}
		}

		if (bRuleApplies)
		{
			RunValidationRule(Rule, Result);
		}
	}

	Result.bIsValid = (Result.ErrorCount == 0);
	LastValidationResult = Result;

	OnDesignValidated.Broadcast(GetOwner(), Result);
	OnValidationCompleted(Result);

	return Result;
}

void UVisualDesignComponent::AutoFixCommonIssues()
{
	// Snap to grid if not aligned
	SnapToGrid(100.0f);

	// Align to surface if floating
	AlignToSurface();

	// Fix common component issues
	if (AActor* Owner = GetOwner())
	{
		// Ensure proper collision settings
		if (UPrimitiveComponent* PrimComp = Owner->FindComponentByClass<UPrimitiveComponent>())
		{
			if (PrimComp->GetCollisionEnabled() == ECollisionEnabled::NoCollision)
			{
				PrimComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
			}
		}
	}

	// Re-validate after fixes
	ValidateDesign();
}

void UVisualDesignComponent::ShowDesignHelper()
{
	bShowInEditor = true;
	bShowInGame = true;
	UpdateVisualization();
}

void UVisualDesignComponent::HideDesignHelper()
{
	bShowInEditor = false;
	bShowInGame = false;
	UpdateVisualization();
}

TArray<FDesignTemplate> UVisualDesignComponent::GetAvailableTemplates() const
{
	TArray<FDesignTemplate> Templates;

	// Built-in templates
	FDesignTemplate InteractableTemplate;
	InteractableTemplate.TemplateID = TEXT("Interactable_Basic");
	InteractableTemplate.TemplateName = FText::FromString(TEXT("Basic Interactable"));
	InteractableTemplate.TemplateDescription = FText::FromString(TEXT("A simple interactable object with collision and interaction component"));
	InteractableTemplate.Complexity = EDesignComplexity::Beginner;
	InteractableTemplate.bIsBuiltIn = true;
	InteractableTemplate.EstimatedSetupTime = 2.0f;
	InteractableTemplate.Category = TEXT("Interaction");
	Templates.Add(InteractableTemplate);

	FDesignTemplate EnemySpawnTemplate;
	EnemySpawnTemplate.TemplateID = TEXT("EnemySpawn_Basic");
	EnemySpawnTemplate.TemplateName = FText::FromString(TEXT("Enemy Spawn Point"));
	EnemySpawnTemplate.TemplateDescription = FText::FromString(TEXT("A spawn point for enemies with patrol settings"));
	EnemySpawnTemplate.Complexity = EDesignComplexity::Intermediate;
	EnemySpawnTemplate.bIsBuiltIn = true;
	EnemySpawnTemplate.EstimatedSetupTime = 5.0f;
	EnemySpawnTemplate.Category = TEXT("AI");
	Templates.Add(EnemySpawnTemplate);

	FDesignTemplate PuzzleTemplate;
	PuzzleTemplate.TemplateID = TEXT("Puzzle_Sequence");
	PuzzleTemplate.TemplateName = FText::FromString(TEXT("Sequence Puzzle"));
	PuzzleTemplate.TemplateDescription = FText::FromString(TEXT("A multi-step sequence puzzle with visual feedback"));
	PuzzleTemplate.Complexity = EDesignComplexity::Advanced;
	PuzzleTemplate.bIsBuiltIn = true;
	PuzzleTemplate.EstimatedSetupTime = 10.0f;
	PuzzleTemplate.Category = TEXT("Puzzles");
	Templates.Add(PuzzleTemplate);

	return Templates;
}

FDesignTemplate UVisualDesignComponent::GetTemplate(FName TemplateID) const
{
	TArray<FDesignTemplate> Templates = GetAvailableTemplates();
	
	for (const FDesignTemplate& Template : Templates)
	{
		if (Template.TemplateID == TemplateID)
		{
			return Template;
		}
	}

	return FDesignTemplate();
}

bool UVisualDesignComponent::CreateCustomTemplate(const FDesignTemplate& Template)
{
	// This would save the template to a data table or file
	// For now, just log the creation
	UE_LOG(LogTemp, Log, TEXT("Created custom template: %s"), *Template.TemplateName.ToString());
	return true;
}

void UVisualDesignComponent::AddValidationRule(const FDesignValidationRule& Rule)
{
	// This would add to a global validation rule registry
	UE_LOG(LogTemp, Log, TEXT("Added validation rule: %s"), *Rule.RuleName.ToString());
}

void UVisualDesignComponent::RemoveValidationRule(FName RuleID)
{
	// This would remove from the global validation rule registry
	UE_LOG(LogTemp, Log, TEXT("Removed validation rule: %s"), *RuleID.ToString());
}

TArray<FDesignValidationRule> UVisualDesignComponent::GetValidationRules() const
{
	TArray<FDesignValidationRule> Rules;

	// Built-in validation rules
	FDesignValidationRule CollisionRule;
	CollisionRule.RuleID = TEXT("HasCollision");
	CollisionRule.RuleName = FText::FromString(TEXT("Has Collision Component"));
	CollisionRule.RuleDescription = FText::FromString(TEXT("Interactive objects should have collision components"));
	CollisionRule.bIsEnabled = true;
	CollisionRule.bIsWarning = false;
	CollisionRule.Priority = 1;
	Rules.Add(CollisionRule);

	FDesignValidationRule ScaleRule;
	ScaleRule.RuleID = TEXT("ReasonableScale");
	ScaleRule.RuleName = FText::FromString(TEXT("Reasonable Scale"));
	ScaleRule.RuleDescription = FText::FromString(TEXT("Objects should have reasonable scale values"));
	ScaleRule.bIsEnabled = true;
	ScaleRule.bIsWarning = true;
	ScaleRule.Priority = 2;
	Rules.Add(ScaleRule);

	return Rules;
}

void UVisualDesignComponent::SnapToGrid(float GridSize)
{
	if (AActor* Owner = GetOwner())
	{
		FVector CurrentLocation = Owner->GetActorLocation();
		FVector SnappedLocation;
		
		SnappedLocation.X = FMath::RoundToFloat(CurrentLocation.X / GridSize) * GridSize;
		SnappedLocation.Y = FMath::RoundToFloat(CurrentLocation.Y / GridSize) * GridSize;
		SnappedLocation.Z = CurrentLocation.Z; // Don't snap Z by default
		
		Owner->SetActorLocation(SnappedLocation);
	}
}

void UVisualDesignComponent::AlignToSurface()
{
	if (AActor* Owner = GetOwner())
	{
		FVector StartLocation = Owner->GetActorLocation();
		FVector EndLocation = StartLocation - FVector(0, 0, 1000.0f);
		
		FHitResult HitResult;
		FCollisionQueryParams QueryParams;
		QueryParams.AddIgnoredActor(Owner);
		
		if (GetWorld()->LineTraceSingleByChannel(HitResult, StartLocation, EndLocation, ECC_WorldStatic, QueryParams))
		{
			Owner->SetActorLocation(HitResult.Location);
		}
	}
}

void UVisualDesignComponent::CopyDesignSettings(UVisualDesignComponent* SourceComponent)
{
	if (!SourceComponent)
	{
		return;
	}

	VisualizationType = SourceComponent->VisualizationType;
	VisualizationColor = SourceComponent->VisualizationColor;
	VisualizationScale = SourceComponent->VisualizationScale;
	CurrentTemplate = SourceComponent->CurrentTemplate;
	DesignTags = SourceComponent->DesignTags;
	DesignComplexity = SourceComponent->DesignComplexity;
	DesignNotes = SourceComponent->DesignNotes;

	UpdateVisualization();
}

void UVisualDesignComponent::ExportDesignData(const FString& FilePath) const
{
	// This would export design data to JSON or other format
	UE_LOG(LogTemp, Log, TEXT("Exporting design data to: %s"), *FilePath);
}

bool UVisualDesignComponent::ImportDesignData(const FString& FilePath)
{
	// This would import design data from file
	UE_LOG(LogTemp, Log, TEXT("Importing design data from: %s"), *FilePath);
	return true;
}

void UVisualDesignComponent::CreateVisualizationComponents()
{
	if (!GetOwner())
	{
		return;
	}

	// Create visualization mesh component
	if (!VisualizationMesh)
	{
		VisualizationMesh = NewObject<UStaticMeshComponent>(GetOwner());
		VisualizationMesh->SetupAttachment(GetOwner()->GetRootComponent());
		VisualizationMesh->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		VisualizationMesh->SetCastShadow(false);
		VisualizationMesh->SetVisibility(false);
	}

	// Create label text component
	if (!LabelText)
	{
		LabelText = NewObject<UTextRenderComponent>(GetOwner());
		LabelText->SetupAttachment(GetOwner()->GetRootComponent());
		LabelText->SetText(FText::FromString(TEXT("Design Helper")));
		LabelText->SetTextRenderColor(FColor::White);
		LabelText->SetHorizontalAlignment(EHTA_Center);
		LabelText->SetVerticalAlignment(EVRTA_TextCenter);
		LabelText->SetVisibility(false);
	}
}

void UVisualDesignComponent::UpdateVisualization()
{
	bool bShouldShow = (bShowInEditor && GIsEditor) || (bShowInGame && !GIsEditor);

	if (VisualizationMesh)
	{
		VisualizationMesh->SetVisibility(bShouldShow && VisualizationType != EDesignVisualizationType::None);
		VisualizationMesh->SetRelativeScale3D(FVector(VisualizationScale));
	}

	if (LabelText)
	{
		LabelText->SetVisibility(bShouldShow);
		LabelText->SetTextRenderColor(VisualizationColor.ToFColor(true));
		
		// Update label text based on type
		FString LabelString = UEnum::GetValueAsString(VisualizationType);
		LabelText->SetText(FText::FromString(LabelString));
	}

	OnVisualizationUpdated();
}

void UVisualDesignComponent::UpdateInfoDisplay()
{
	// Update any info widgets or displays
	if (InfoWidget)
	{
		// Update widget with current design information
	}
}

bool UVisualDesignComponent::RunValidationRule(const FDesignValidationRule& Rule, FDesignValidationResult& Result)
{
	bool bRulePassed = true;

	// Example validation logic
	if (Rule.RuleID == TEXT("HasCollision"))
	{
		if (AActor* Owner = GetOwner())
		{
			UPrimitiveComponent* PrimComp = Owner->FindComponentByClass<UPrimitiveComponent>();
			if (!PrimComp || PrimComp->GetCollisionEnabled() == ECollisionEnabled::NoCollision)
			{
				bRulePassed = false;
				FText ErrorMsg = FText::FromString(TEXT("Object lacks proper collision component"));
				
				if (Rule.bIsWarning)
				{
					Result.WarningMessages.Add(ErrorMsg);
					Result.WarningCount++;
				}
				else
				{
					Result.ErrorMessages.Add(ErrorMsg);
					Result.ErrorCount++;
				}
			}
		}
	}
	else if (Rule.RuleID == TEXT("ReasonableScale"))
	{
		if (AActor* Owner = GetOwner())
		{
			FVector Scale = Owner->GetActorScale3D();
			if (Scale.X < 0.1f || Scale.X > 10.0f || Scale.Y < 0.1f || Scale.Y > 10.0f || Scale.Z < 0.1f || Scale.Z > 10.0f)
			{
				bRulePassed = false;
				FText WarningMsg = FText::FromString(TEXT("Object has unusual scale values"));
				Result.WarningMessages.Add(WarningMsg);
				Result.WarningCount++;
			}
		}
	}

	return bRulePassed;
}
