// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PuzzleSystem/Actors/PuzzleActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class APawn;
class APuzzleActor;
enum class EPuzzleState : uint8;
#ifdef SLT_PuzzleActor_generated_h
#error "PuzzleActor.generated.h already included, missing '#pragma once' in PuzzleActor.h"
#endif
#define SLT_PuzzleActor_generated_h

#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_60_DELEGATE \
static void FOnPuzzleActorSolved_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorSolved, APuzzleActor* PuzzleActor, APawn* SolvingPawn);


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_61_DELEGATE \
static void FOnPuzzleActorFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorFailed, APuzzleActor* PuzzleActor, APawn* FailingPawn);


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_62_DELEGATE \
static void FOnPuzzleActorReset_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleActorReset, APuzzleActor* PuzzleActor);


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execOnPuzzleReset); \
	DECLARE_FUNCTION(execOnPuzzleStateChanged); \
	DECLARE_FUNCTION(execOnPuzzleFailed); \
	DECLARE_FUNCTION(execOnPuzzleSolved); \
	DECLARE_FUNCTION(execGetPuzzleProgress); \
	DECLARE_FUNCTION(execIsPuzzleSolved); \
	DECLARE_FUNCTION(execResetPuzzle); \
	DECLARE_FUNCTION(execTryInputStep); \
	DECLARE_FUNCTION(execTryInputSolution);


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAPuzzleActor(); \
	friend struct Z_Construct_UClass_APuzzleActor_Statics; \
public: \
	DECLARE_CLASS(APuzzleActor, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(APuzzleActor) \
	virtual UObject* _getUObject() const override { return const_cast<APuzzleActor*>(this); }


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	APuzzleActor(APuzzleActor&&); \
	APuzzleActor(const APuzzleActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, APuzzleActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(APuzzleActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(APuzzleActor) \
	NO_API virtual ~APuzzleActor();


#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_11_PROLOG
#define FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h_14_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class APuzzleActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_PuzzleSystem_Actors_PuzzleActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
