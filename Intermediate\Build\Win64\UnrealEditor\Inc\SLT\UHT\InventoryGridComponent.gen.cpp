// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/InventorySystem/Components/InventoryGridComponent.h"
#include "SLT/InventorySystem/Data/InventoryItemData.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInventoryGridComponent() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FGuid();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector2D();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UClass* Z_Construct_UClass_UDataTable_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventoryGridComponent();
SLT_API UClass* Z_Construct_UClass_UInventoryGridComponent_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInventoryInterface_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventoryItemData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInventorySlot();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnInventoryItemAdded
struct Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryItemAdded_Parms
	{
		FInventorySlot AddedSlot;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AddedSlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AddedSlot;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_AddedSlot = { "AddedSlot", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryItemAdded_Parms, AddedSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AddedSlot_MetaData), NewProp_AddedSlot_MetaData) }; // 2390674128
void Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_SLT_eventOnInventoryItemAdded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnInventoryItemAdded_Parms), &Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_AddedSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryItemAdded__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemAdded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemAdded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryItemAdded_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemAdded, FInventorySlot const& AddedSlot, bool bSuccess)
{
	struct _Script_SLT_eventOnInventoryItemAdded_Parms
	{
		FInventorySlot AddedSlot;
		bool bSuccess;
	};
	_Script_SLT_eventOnInventoryItemAdded_Parms Parms;
	Parms.AddedSlot=AddedSlot;
	Parms.bSuccess=bSuccess ? true : false;
	OnInventoryItemAdded.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryItemAdded

// Begin Delegate FOnInventoryItemRemoved
struct Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryItemRemoved_Parms
	{
		FInventorySlot RemovedSlot;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RemovedSlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_RemovedSlot;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_RemovedSlot = { "RemovedSlot", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryItemRemoved_Parms, RemovedSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RemovedSlot_MetaData), NewProp_RemovedSlot_MetaData) }; // 2390674128
void Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_SLT_eventOnInventoryItemRemoved_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnInventoryItemRemoved_Parms), &Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_RemovedSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryItemRemoved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemRemoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemRemoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryItemRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemRemoved, FInventorySlot const& RemovedSlot, bool bSuccess)
{
	struct _Script_SLT_eventOnInventoryItemRemoved_Parms
	{
		FInventorySlot RemovedSlot;
		bool bSuccess;
	};
	_Script_SLT_eventOnInventoryItemRemoved_Parms Parms;
	Parms.RemovedSlot=RemovedSlot;
	Parms.bSuccess=bSuccess ? true : false;
	OnInventoryItemRemoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryItemRemoved

// Begin Delegate FOnInventoryItemMoved
struct Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryItemMoved_Parms
	{
		FInventorySlot MovedSlot;
		int32 NewX;
		int32 NewY;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MovedSlot_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_MovedSlot;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewY;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_MovedSlot = { "MovedSlot", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryItemMoved_Parms, MovedSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MovedSlot_MetaData), NewProp_MovedSlot_MetaData) }; // 2390674128
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_NewX = { "NewX", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryItemMoved_Parms, NewX), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_NewY = { "NewY", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryItemMoved_Parms, NewY), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_MovedSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_NewX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::NewProp_NewY,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryItemMoved__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemMoved_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::_Script_SLT_eventOnInventoryItemMoved_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryItemMoved_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemMoved, FInventorySlot const& MovedSlot, int32 NewX, int32 NewY)
{
	struct _Script_SLT_eventOnInventoryItemMoved_Parms
	{
		FInventorySlot MovedSlot;
		int32 NewX;
		int32 NewY;
	};
	_Script_SLT_eventOnInventoryItemMoved_Parms Parms;
	Parms.MovedSlot=MovedSlot;
	Parms.NewX=NewX;
	Parms.NewY=NewY;
	OnInventoryItemMoved.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryItemMoved

// Begin Delegate FOnInventoryGridChanged
struct Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInventoryGridChanged_Parms
	{
		UInventoryGridComponent* InventoryComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventoryComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InventoryComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::NewProp_InventoryComponent = { "InventoryComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInventoryGridChanged_Parms, InventoryComponent), Z_Construct_UClass_UInventoryGridComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventoryComponent_MetaData), NewProp_InventoryComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::NewProp_InventoryComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInventoryGridChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::_Script_SLT_eventOnInventoryGridChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::_Script_SLT_eventOnInventoryGridChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInventoryGridChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryGridChanged, UInventoryGridComponent* InventoryComponent)
{
	struct _Script_SLT_eventOnInventoryGridChanged_Parms
	{
		UInventoryGridComponent* InventoryComponent;
	};
	_Script_SLT_eventOnInventoryGridChanged_Parms Parms;
	Parms.InventoryComponent=InventoryComponent;
	OnInventoryGridChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInventoryGridChanged

// Begin Class UInventoryGridComponent Function CanPlaceItemAt
struct Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics
{
	struct InventoryGridComponent_eventCanPlaceItemAt_Parms
	{
		int32 X;
		int32 Y;
		FInventoryItemData ItemData;
		bool bRotated;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid management functions\n" },
#endif
		{ "CPP_Default_bRotated", "false" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid management functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static void NewProp_bRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRotated;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventCanPlaceItemAt_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventCanPlaceItemAt_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventCanPlaceItemAt_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
void Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_bRotated_SetBit(void* Obj)
{
	((InventoryGridComponent_eventCanPlaceItemAt_Parms*)Obj)->bRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_bRotated = { "bRotated", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventCanPlaceItemAt_Parms), &Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_bRotated_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventCanPlaceItemAt_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventCanPlaceItemAt_Parms), &Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_bRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "CanPlaceItemAt", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::InventoryGridComponent_eventCanPlaceItemAt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::InventoryGridComponent_eventCanPlaceItemAt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execCanPlaceItemAt)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_UBOOL(Z_Param_bRotated);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanPlaceItemAt(Z_Param_X,Z_Param_Y,Z_Param_Out_ItemData,Z_Param_bRotated);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function CanPlaceItemAt

// Begin Class UInventoryGridComponent Function DoesItemFitInGrid
struct Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics
{
	struct InventoryGridComponent_eventDoesItemFitInGrid_Parms
	{
		FInventoryItemData ItemData;
		bool bRotated;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "CPP_Default_bRotated", "false" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static void NewProp_bRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRotated;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventDoesItemFitInGrid_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
void Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_bRotated_SetBit(void* Obj)
{
	((InventoryGridComponent_eventDoesItemFitInGrid_Parms*)Obj)->bRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_bRotated = { "bRotated", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventDoesItemFitInGrid_Parms), &Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_bRotated_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventDoesItemFitInGrid_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventDoesItemFitInGrid_Parms), &Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_bRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "DoesItemFitInGrid", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::InventoryGridComponent_eventDoesItemFitInGrid_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::InventoryGridComponent_eventDoesItemFitInGrid_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execDoesItemFitInGrid)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_UBOOL(Z_Param_bRotated);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->DoesItemFitInGrid(Z_Param_Out_ItemData,Z_Param_bRotated);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function DoesItemFitInGrid

// Begin Class UInventoryGridComponent Function FindBestFitPosition
struct Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics
{
	struct InventoryGridComponent_eventFindBestFitPosition_Parms
	{
		FInventoryItemData ItemData;
		bool bAllowRotation;
		FVector2D ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "CPP_Default_bAllowRotation", "true" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static void NewProp_bAllowRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAllowRotation;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindBestFitPosition_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
void Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_bAllowRotation_SetBit(void* Obj)
{
	((InventoryGridComponent_eventFindBestFitPosition_Parms*)Obj)->bAllowRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_bAllowRotation = { "bAllowRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventFindBestFitPosition_Parms), &Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_bAllowRotation_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindBestFitPosition_Parms, ReturnValue), Z_Construct_UScriptStruct_FVector2D, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_bAllowRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "FindBestFitPosition", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::InventoryGridComponent_eventFindBestFitPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::InventoryGridComponent_eventFindBestFitPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execFindBestFitPosition)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_UBOOL(Z_Param_bAllowRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FVector2D*)Z_Param__Result=P_THIS->FindBestFitPosition(Z_Param_Out_ItemData,Z_Param_bAllowRotation);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function FindBestFitPosition

// Begin Class UInventoryGridComponent Function FindSlotAt
struct Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics
{
	struct InventoryGridComponent_eventFindSlotAt_Parms
	{
		int32 X;
		int32 Y;
		FInventorySlot OutSlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutSlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindSlotAt_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindSlotAt_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_OutSlot = { "OutSlot", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindSlotAt_Parms, OutSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(0, nullptr) }; // 2390674128
void Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventFindSlotAt_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventFindSlotAt_Parms), &Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_OutSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "FindSlotAt", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::InventoryGridComponent_eventFindSlotAt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::InventoryGridComponent_eventFindSlotAt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execFindSlotAt)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FInventorySlot,Z_Param_Out_OutSlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FindSlotAt(Z_Param_X,Z_Param_Y,Z_Param_Out_OutSlot);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function FindSlotAt

// Begin Class UInventoryGridComponent Function FindSlotByID
struct Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics
{
	struct InventoryGridComponent_eventFindSlotByID_Parms
	{
		FGuid SlotID;
		FInventorySlot OutSlot;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutSlot;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_SlotID = { "SlotID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindSlotByID_Parms, SlotID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotID_MetaData), NewProp_SlotID_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_OutSlot = { "OutSlot", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventFindSlotByID_Parms, OutSlot), Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(0, nullptr) }; // 2390674128
void Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventFindSlotByID_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventFindSlotByID_Parms), &Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_SlotID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_OutSlot,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "FindSlotByID", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::InventoryGridComponent_eventFindSlotByID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::InventoryGridComponent_eventFindSlotByID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execFindSlotByID)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_SlotID);
	P_GET_STRUCT_REF(FInventorySlot,Z_Param_Out_OutSlot);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->FindSlotByID(Z_Param_Out_SlotID,Z_Param_Out_OutSlot);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function FindSlotByID

// Begin Class UInventoryGridComponent Function GetItemDataByID
struct Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics
{
	struct InventoryGridComponent_eventGetItemDataByID_Parms
	{
		FName ItemID;
		FInventoryItemData OutItemData;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Database" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Item database functions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Item database functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_ItemID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_OutItemData;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ItemID = { "ItemID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetItemDataByID_Parms, ItemID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_OutItemData = { "OutItemData", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetItemDataByID_Parms, OutItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(0, nullptr) }; // 2976144554
void Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventGetItemDataByID_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventGetItemDataByID_Parms), &Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ItemID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_OutItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "GetItemDataByID", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::InventoryGridComponent_eventGetItemDataByID_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::InventoryGridComponent_eventGetItemDataByID_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execGetItemDataByID)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_ItemID);
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_OutItemData);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->GetItemDataByID(Z_Param_ItemID,Z_Param_Out_OutItemData);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function GetItemDataByID

// Begin Class UInventoryGridComponent Function GetItemDimensions
struct Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics
{
	struct InventoryGridComponent_eventGetItemDimensions_Parms
	{
		FInventoryItemData ItemData;
		bool bRotated;
		int32 OutWidth;
		int32 OutHeight;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static void NewProp_bRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRotated;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OutWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_OutHeight;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetItemDimensions_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
void Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_bRotated_SetBit(void* Obj)
{
	((InventoryGridComponent_eventGetItemDimensions_Parms*)Obj)->bRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_bRotated = { "bRotated", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventGetItemDimensions_Parms), &Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_bRotated_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_OutWidth = { "OutWidth", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetItemDimensions_Parms, OutWidth), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_OutHeight = { "OutHeight", nullptr, (EPropertyFlags)0x0010000000000180, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetItemDimensions_Parms, OutHeight), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_bRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_OutWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::NewProp_OutHeight,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "GetItemDimensions", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::InventoryGridComponent_eventGetItemDimensions_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::InventoryGridComponent_eventGetItemDimensions_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execGetItemDimensions)
{
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_UBOOL(Z_Param_bRotated);
	P_GET_PROPERTY_REF(FIntProperty,Z_Param_Out_OutWidth);
	P_GET_PROPERTY_REF(FIntProperty,Z_Param_Out_OutHeight);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->GetItemDimensions(Z_Param_Out_ItemData,Z_Param_bRotated,Z_Param_Out_OutWidth,Z_Param_Out_OutHeight);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function GetItemDimensions

// Begin Class UInventoryGridComponent Function GetSlotsInArea
struct Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics
{
	struct InventoryGridComponent_eventGetSlotsInArea_Parms
	{
		int32 X;
		int32 Y;
		int32 Width;
		int32 Height;
		TArray<FInventorySlot> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Width;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Height;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetSlotsInArea_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetSlotsInArea_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Width = { "Width", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetSlotsInArea_Parms, Width), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Height = { "Height", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetSlotsInArea_Parms, Height), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(0, nullptr) }; // 2390674128
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventGetSlotsInArea_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2390674128
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Width,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_Height,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "GetSlotsInArea", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::InventoryGridComponent_eventGetSlotsInArea_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::InventoryGridComponent_eventGetSlotsInArea_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execGetSlotsInArea)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_GET_PROPERTY(FIntProperty,Z_Param_Width);
	P_GET_PROPERTY(FIntProperty,Z_Param_Height);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FInventorySlot>*)Z_Param__Result=P_THIS->GetSlotsInArea(Z_Param_X,Z_Param_Y,Z_Param_Width,Z_Param_Height);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function GetSlotsInArea

// Begin Class UInventoryGridComponent Function IsValidGridPosition
struct Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics
{
	struct InventoryGridComponent_eventIsValidGridPosition_Parms
	{
		int32 X;
		int32 Y;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventIsValidGridPosition_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventIsValidGridPosition_Parms, Y), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventIsValidGridPosition_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventIsValidGridPosition_Parms), &Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "IsValidGridPosition", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::InventoryGridComponent_eventIsValidGridPosition_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::InventoryGridComponent_eventIsValidGridPosition_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execIsValidGridPosition)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsValidGridPosition(Z_Param_X,Z_Param_Y);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function IsValidGridPosition

// Begin Class UInventoryGridComponent Function LoadItemDatabase
struct Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Database" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "LoadItemDatabase", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execLoadItemDatabase)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadItemDatabase();
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function LoadItemDatabase

// Begin Class UInventoryGridComponent Function MoveItem
struct Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics
{
	struct InventoryGridComponent_eventMoveItem_Parms
	{
		FGuid SlotID;
		int32 NewX;
		int32 NewY;
		bool bNewRotation;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "CPP_Default_bNewRotation", "false" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotID;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewX;
	static const UECodeGen_Private::FIntPropertyParams NewProp_NewY;
	static void NewProp_bNewRotation_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bNewRotation;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_SlotID = { "SlotID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventMoveItem_Parms, SlotID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotID_MetaData), NewProp_SlotID_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_NewX = { "NewX", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventMoveItem_Parms, NewX), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_NewY = { "NewY", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventMoveItem_Parms, NewY), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_bNewRotation_SetBit(void* Obj)
{
	((InventoryGridComponent_eventMoveItem_Parms*)Obj)->bNewRotation = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_bNewRotation = { "bNewRotation", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventMoveItem_Parms), &Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_bNewRotation_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventMoveItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventMoveItem_Parms), &Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_SlotID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_NewX,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_NewY,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_bNewRotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "MoveItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::InventoryGridComponent_eventMoveItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::InventoryGridComponent_eventMoveItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_MoveItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_MoveItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execMoveItem)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_SlotID);
	P_GET_PROPERTY(FIntProperty,Z_Param_NewX);
	P_GET_PROPERTY(FIntProperty,Z_Param_NewY);
	P_GET_UBOOL(Z_Param_bNewRotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->MoveItem(Z_Param_Out_SlotID,Z_Param_NewX,Z_Param_NewY,Z_Param_bNewRotation);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function MoveItem

// Begin Class UInventoryGridComponent Function PlaceItemAt
struct Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics
{
	struct InventoryGridComponent_eventPlaceItemAt_Parms
	{
		int32 X;
		int32 Y;
		FInventoryItemData ItemData;
		int32 Quantity;
		bool bRotated;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "CPP_Default_bRotated", "false" },
		{ "CPP_Default_Quantity", "1" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_X;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Y;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ItemData;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Quantity;
	static void NewProp_bRotated_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRotated;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_X = { "X", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventPlaceItemAt_Parms, X), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_Y = { "Y", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventPlaceItemAt_Parms, Y), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ItemData = { "ItemData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventPlaceItemAt_Parms, ItemData), Z_Construct_UScriptStruct_FInventoryItemData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemData_MetaData), NewProp_ItemData_MetaData) }; // 2976144554
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_Quantity = { "Quantity", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventPlaceItemAt_Parms, Quantity), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_bRotated_SetBit(void* Obj)
{
	((InventoryGridComponent_eventPlaceItemAt_Parms*)Obj)->bRotated = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_bRotated = { "bRotated", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventPlaceItemAt_Parms), &Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_bRotated_SetBit, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventPlaceItemAt_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventPlaceItemAt_Parms), &Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_X,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_Y,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ItemData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_Quantity,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_bRotated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "PlaceItemAt", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::InventoryGridComponent_eventPlaceItemAt_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::InventoryGridComponent_eventPlaceItemAt_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execPlaceItemAt)
{
	P_GET_PROPERTY(FIntProperty,Z_Param_X);
	P_GET_PROPERTY(FIntProperty,Z_Param_Y);
	P_GET_STRUCT_REF(FInventoryItemData,Z_Param_Out_ItemData);
	P_GET_PROPERTY(FIntProperty,Z_Param_Quantity);
	P_GET_UBOOL(Z_Param_bRotated);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->PlaceItemAt(Z_Param_X,Z_Param_Y,Z_Param_Out_ItemData,Z_Param_Quantity,Z_Param_bRotated);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function PlaceItemAt

// Begin Class UInventoryGridComponent Function RotateItem
struct Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics
{
	struct InventoryGridComponent_eventRotateItem_Parms
	{
		FGuid SlotID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Grid" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SlotID_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_SlotID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_SlotID = { "SlotID", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InventoryGridComponent_eventRotateItem_Parms, SlotID), Z_Construct_UScriptStruct_FGuid, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SlotID_MetaData), NewProp_SlotID_MetaData) };
void Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InventoryGridComponent_eventRotateItem_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InventoryGridComponent_eventRotateItem_Parms), &Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_SlotID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInventoryGridComponent, nullptr, "RotateItem", nullptr, nullptr, Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::InventoryGridComponent_eventRotateItem_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::InventoryGridComponent_eventRotateItem_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInventoryGridComponent_RotateItem()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInventoryGridComponent_RotateItem_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInventoryGridComponent::execRotateItem)
{
	P_GET_STRUCT_REF(FGuid,Z_Param_Out_SlotID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->RotateItem(Z_Param_Out_SlotID);
	P_NATIVE_END;
}
// End Class UInventoryGridComponent Function RotateItem

// Begin Class UInventoryGridComponent
void UInventoryGridComponent::StaticRegisterNativesUInventoryGridComponent()
{
	UClass* Class = UInventoryGridComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanPlaceItemAt", &UInventoryGridComponent::execCanPlaceItemAt },
		{ "DoesItemFitInGrid", &UInventoryGridComponent::execDoesItemFitInGrid },
		{ "FindBestFitPosition", &UInventoryGridComponent::execFindBestFitPosition },
		{ "FindSlotAt", &UInventoryGridComponent::execFindSlotAt },
		{ "FindSlotByID", &UInventoryGridComponent::execFindSlotByID },
		{ "GetItemDataByID", &UInventoryGridComponent::execGetItemDataByID },
		{ "GetItemDimensions", &UInventoryGridComponent::execGetItemDimensions },
		{ "GetSlotsInArea", &UInventoryGridComponent::execGetSlotsInArea },
		{ "IsValidGridPosition", &UInventoryGridComponent::execIsValidGridPosition },
		{ "LoadItemDatabase", &UInventoryGridComponent::execLoadItemDatabase },
		{ "MoveItem", &UInventoryGridComponent::execMoveItem },
		{ "PlaceItemAt", &UInventoryGridComponent::execPlaceItemAt },
		{ "RotateItem", &UInventoryGridComponent::execRotateItem },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UInventoryGridComponent);
UClass* Z_Construct_UClass_UInventoryGridComponent_NoRegister()
{
	return UInventoryGridComponent::StaticClass();
}
struct Z_Construct_UClass_UInventoryGridComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "InventorySystem/Components/InventoryGridComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridWidth_MetaData[] = {
		{ "Category", "Grid Settings" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Grid dimensions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Grid dimensions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_GridHeight_MetaData[] = {
		{ "Category", "Grid Settings" },
		{ "ClampMax", "20" },
		{ "ClampMin", "1" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxWeight_MetaData[] = {
		{ "Category", "Inventory Settings" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Weight limits\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Weight limits" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ItemDatabase_MetaData[] = {
		{ "Category", "Data" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Item database reference\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Item database reference" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInventoryItemAdded_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInventoryItemRemoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInventoryItemMoved_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInventoryGridChanged_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InventorySlots_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal storage\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CachedItemDatabase_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Cached item database\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InventoryGridComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Cached item database" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridWidth;
	static const UECodeGen_Private::FIntPropertyParams NewProp_GridHeight;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxWeight;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_ItemDatabase;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInventoryItemAdded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInventoryItemRemoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInventoryItemMoved;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInventoryGridChanged;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InventorySlots_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InventorySlots;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_CachedItemDatabase;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UInventoryGridComponent_CanPlaceItemAt, "CanPlaceItemAt" }, // 3159989357
		{ &Z_Construct_UFunction_UInventoryGridComponent_DoesItemFitInGrid, "DoesItemFitInGrid" }, // 3822005938
		{ &Z_Construct_UFunction_UInventoryGridComponent_FindBestFitPosition, "FindBestFitPosition" }, // 1521654318
		{ &Z_Construct_UFunction_UInventoryGridComponent_FindSlotAt, "FindSlotAt" }, // 330429026
		{ &Z_Construct_UFunction_UInventoryGridComponent_FindSlotByID, "FindSlotByID" }, // 2251307441
		{ &Z_Construct_UFunction_UInventoryGridComponent_GetItemDataByID, "GetItemDataByID" }, // 4054724642
		{ &Z_Construct_UFunction_UInventoryGridComponent_GetItemDimensions, "GetItemDimensions" }, // 4098698896
		{ &Z_Construct_UFunction_UInventoryGridComponent_GetSlotsInArea, "GetSlotsInArea" }, // 1824293585
		{ &Z_Construct_UFunction_UInventoryGridComponent_IsValidGridPosition, "IsValidGridPosition" }, // 1244946340
		{ &Z_Construct_UFunction_UInventoryGridComponent_LoadItemDatabase, "LoadItemDatabase" }, // 2222325153
		{ &Z_Construct_UFunction_UInventoryGridComponent_MoveItem, "MoveItem" }, // 3291167354
		{ &Z_Construct_UFunction_UInventoryGridComponent_PlaceItemAt, "PlaceItemAt" }, // 150749272
		{ &Z_Construct_UFunction_UInventoryGridComponent_RotateItem, "RotateItem" }, // 4232254545
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static const UECodeGen_Private::FImplementedInterfaceParams InterfaceParams[];
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UInventoryGridComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_GridWidth = { "GridWidth", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, GridWidth), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridWidth_MetaData), NewProp_GridWidth_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_GridHeight = { "GridHeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, GridHeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_GridHeight_MetaData), NewProp_GridHeight_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_MaxWeight = { "MaxWeight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, MaxWeight), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxWeight_MetaData), NewProp_MaxWeight_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_ItemDatabase = { "ItemDatabase", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, ItemDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ItemDatabase_MetaData), NewProp_ItemDatabase_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemAdded = { "OnInventoryItemAdded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, OnInventoryItemAdded), Z_Construct_UDelegateFunction_SLT_OnInventoryItemAdded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInventoryItemAdded_MetaData), NewProp_OnInventoryItemAdded_MetaData) }; // 3635820906
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemRemoved = { "OnInventoryItemRemoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, OnInventoryItemRemoved), Z_Construct_UDelegateFunction_SLT_OnInventoryItemRemoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInventoryItemRemoved_MetaData), NewProp_OnInventoryItemRemoved_MetaData) }; // 4209088204
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemMoved = { "OnInventoryItemMoved", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, OnInventoryItemMoved), Z_Construct_UDelegateFunction_SLT_OnInventoryItemMoved__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInventoryItemMoved_MetaData), NewProp_OnInventoryItemMoved_MetaData) }; // 4080853256
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryGridChanged = { "OnInventoryGridChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, OnInventoryGridChanged), Z_Construct_UDelegateFunction_SLT_OnInventoryGridChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInventoryGridChanged_MetaData), NewProp_OnInventoryGridChanged_MetaData) }; // 2787530776
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_InventorySlots_Inner = { "InventorySlots", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FInventorySlot, METADATA_PARAMS(0, nullptr) }; // 2390674128
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_InventorySlots = { "InventorySlots", nullptr, (EPropertyFlags)0x0020080001000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, InventorySlots), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InventorySlots_MetaData), NewProp_InventorySlots_MetaData) }; // 2390674128
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_CachedItemDatabase = { "CachedItemDatabase", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInventoryGridComponent, CachedItemDatabase), Z_Construct_UClass_UDataTable_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CachedItemDatabase_MetaData), NewProp_CachedItemDatabase_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UInventoryGridComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_GridWidth,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_GridHeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_MaxWeight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_ItemDatabase,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemAdded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemRemoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryItemMoved,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_OnInventoryGridChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_InventorySlots_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_InventorySlots,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInventoryGridComponent_Statics::NewProp_CachedItemDatabase,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryGridComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UInventoryGridComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryGridComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FImplementedInterfaceParams Z_Construct_UClass_UInventoryGridComponent_Statics::InterfaceParams[] = {
	{ Z_Construct_UClass_UInventoryInterface_NoRegister, (int32)VTABLE_OFFSET(UInventoryGridComponent, IInventoryInterface), false },  // 3420551761
};
const UECodeGen_Private::FClassParams Z_Construct_UClass_UInventoryGridComponent_Statics::ClassParams = {
	&UInventoryGridComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UInventoryGridComponent_Statics::PropPointers,
	InterfaceParams,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryGridComponent_Statics::PropPointers),
	UE_ARRAY_COUNT(InterfaceParams),
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UInventoryGridComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UInventoryGridComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UInventoryGridComponent()
{
	if (!Z_Registration_Info_UClass_UInventoryGridComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UInventoryGridComponent.OuterSingleton, Z_Construct_UClass_UInventoryGridComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UInventoryGridComponent.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UInventoryGridComponent>()
{
	return UInventoryGridComponent::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UInventoryGridComponent);
UInventoryGridComponent::~UInventoryGridComponent() {}
// End Class UInventoryGridComponent

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UInventoryGridComponent, UInventoryGridComponent::StaticClass, TEXT("UInventoryGridComponent"), &Z_Registration_Info_UClass_UInventoryGridComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UInventoryGridComponent), 1052058804U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_123587994(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
