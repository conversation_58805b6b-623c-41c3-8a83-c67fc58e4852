#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "GameplayTagContainer.h"
#include "Interactable.generated.h"

class APawn;
class UInventoryGridComponent;

UENUM(BlueprintType)
enum class EInteractionType : uint8
{
	None			UMETA(DisplayName = "None"),
	Pickup			UMETA(DisplayName = "Pickup"),
	Use				UMETA(DisplayName = "Use"),
	Examine			UMETA(DisplayName = "Examine"),
	Open			UMETA(DisplayName = "Open"),
	Activate		UMETA(DisplayName = "Activate"),
	Talk			UMETA(DisplayName = "Talk"),
	Custom			UMETA(DisplayName = "Custom")
};

USTRUCT(BlueprintType)
struct FInteractionData
{
	GENERATED_BODY()

	FInteractionData()
	{
		InteractionType = EInteractionType::None;
		InteractionText = FText::GetEmpty();
		InteractionDuration = 0.0f;
		bRequiresItem = false;
		RequiredItemID = NAME_None;
		bCanInteractMultipleTimes = true;
		InteractionRange = 200.0f;
		bShowInteractionPrompt = true;
	}

	// Type of interaction
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	EInteractionType InteractionType;

	// Text to display in interaction prompt
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	FText InteractionText;

	// How long the interaction takes (0 = instant)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction", meta = (ClampMin = "0.0"))
	float InteractionDuration;

	// Does this interaction require a specific item?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	bool bRequiresItem;

	// Required item ID (if bRequiresItem is true)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements", meta = (EditCondition = "bRequiresItem"))
	FName RequiredItemID;

	// Required gameplay tags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	// Can this object be interacted with multiple times?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bCanInteractMultipleTimes;

	// Maximum interaction range
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction", meta = (ClampMin = "0.0"))
	float InteractionRange;

	// Should we show the interaction prompt UI?
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	bool bShowInteractionPrompt;

	// Custom interaction data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomData;
};

UINTERFACE(MinimalAPI, BlueprintType)
class UInteractable : public UInterface
{
	GENERATED_BODY()
};

/**
 * Interface for objects that can be interacted with by the player
 */
class SLT_API IInteractable
{
	GENERATED_BODY()

public:
	// Get interaction data for this object
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	FInteractionData GetInteractionData() const;
	virtual FInteractionData GetInteractionData_Implementation() const { return FInteractionData(); }

	// Check if this object can be interacted with by the given pawn
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	bool CanInteract(APawn* InteractingPawn) const;
	virtual bool CanInteract_Implementation(APawn* InteractingPawn) const { return true; }

	// Called when interaction begins
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void OnInteractionStart(APawn* InteractingPawn);
	virtual void OnInteractionStart_Implementation(APawn* InteractingPawn) {}

	// Called when interaction completes successfully
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void OnInteractionComplete(APawn* InteractingPawn);
	virtual void OnInteractionComplete_Implementation(APawn* InteractingPawn) {}

	// Called when interaction is cancelled or interrupted
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	void OnInteractionCancel(APawn* InteractingPawn);
	virtual void OnInteractionCancel_Implementation(APawn* InteractingPawn) {}

	// Get the world location for interaction prompts
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	FVector GetInteractionLocation() const;
	virtual FVector GetInteractionLocation_Implementation() const { return FVector::ZeroVector; }

	// Called to check if interaction requirements are met
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Interaction")
	bool CheckInteractionRequirements(APawn* InteractingPawn) const;
	virtual bool CheckInteractionRequirements_Implementation(APawn* InteractingPawn) const;
};
