// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InventorySystem/Actors/ItemActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class AItemActor;
class APawn;
struct FInventoryItemData;
#ifdef SLT_ItemActor_generated_h
#error "ItemActor.generated.h already included, missing '#pragma once' in ItemActor.h"
#endif
#define SLT_ItemActor_generated_h

#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_72_DELEGATE \
static void FOnItemPickedUp_DelegateWrapper(const FMulticastScriptDelegate& OnItemPickedUp, AItemActor* ItemActor, APawn* PickupPawn);


#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execGetItemID); \
	DECLARE_FUNCTION(execGetQuantity); \
	DECLARE_FUNCTION(execSetQuantity); \
	DECLARE_FUNCTION(execGetItemData); \
	DECLARE_FUNCTION(execInitializeFromItemData);


#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAItemActor(); \
	friend struct Z_Construct_UClass_AItemActor_Statics; \
public: \
	DECLARE_CLASS(AItemActor, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(AItemActor) \
	virtual UObject* _getUObject() const override { return const_cast<AItemActor*>(this); }


#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AItemActor(AItemActor&&); \
	AItemActor(const AItemActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AItemActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AItemActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AItemActor) \
	NO_API virtual ~AItemActor();


#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_13_PROLOG
#define FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class AItemActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_InventorySystem_Actors_ItemActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
