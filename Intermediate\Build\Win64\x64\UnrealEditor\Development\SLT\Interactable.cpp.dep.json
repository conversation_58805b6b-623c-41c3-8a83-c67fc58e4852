{"Version": "1.2", "Data": {"Source": "g:\\gamedev\\slt\\source\\slt\\core\\interfaces\\interactable.cpp", "ProvidedModule": "", "PCH": "g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\slteditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["g:\\gamedev\\slt\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\slt\\definitions.slt.h", "g:\\gamedev\\slt\\source\\slt\\core\\interfaces\\interactable.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\gameplaytags\\classes\\gameplaytagcontainer.h", "f:\\unreal\\ue_5.5\\engine\\source\\runtime\\core\\public\\misc\\comparisonutility.h", "f:\\unreal\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\gameplaytags\\uht\\gameplaytagcontainer.generated.h", "g:\\gamedev\\slt\\intermediate\\build\\win64\\unrealeditor\\inc\\slt\\uht\\interactable.generated.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}