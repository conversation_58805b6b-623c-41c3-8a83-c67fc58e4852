#pragma once

#include "CoreMinimal.h"
#include "GameFramework/SaveGame.h"
#include "InventorySystem/Data/InventoryItemData.h"
#include "PuzzleSystem/Components/PuzzleComponent.h"
#include "InventorySaveGame.generated.h"

USTRUCT(BlueprintType)
struct FPuzzleSaveData
{
	GENERATED_BODY()

	FPuzzleSaveData()
	{
		PuzzleID = NAME_None;
		bIsSolved = false;
		CurrentStepIndex = 0;
		AttemptCount = 0;
		PuzzleState = EPuzzleState::Inactive;
	}

	// Unique identifier for the puzzle
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FName PuzzleID;

	// Puzzle completion state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	bool bIsSolved;

	// Current progress
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 CurrentStepIndex;

	// Attempt tracking
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 AttemptCount;

	// Current state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	EPuzzleState PuzzleState;

	// Completed steps
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<bool> CompletedSteps;

	// Custom save data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FString> CustomSaveData;
};

USTRUCT(BlueprintType)
struct FPlayerSaveData
{
	GENERATED_BODY()

	FPlayerSaveData()
	{
		PlayerLocation = FVector::ZeroVector;
		PlayerRotation = FRotator::ZeroRotator;
		Health = 100.0f;
		MaxHealth = 100.0f;
		Experience = 0;
		Level = 1;
	}

	// Player transform
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FVector PlayerLocation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FRotator PlayerRotation;

	// Player stats
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	float Health;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	float MaxHealth;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 Experience;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	int32 Level;

	// Equipped items
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FName> EquippedItems; // Slot name -> Item ID

	// Custom player data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FString> CustomPlayerData;
};

USTRUCT(BlueprintType)
struct FWorldSaveData
{
	GENERATED_BODY()

	FWorldSaveData()
	{
		CurrentLevel = TEXT("");
		GameTime = 0.0f;
		PlayTime = 0.0f;
	}

	// World state
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	FString CurrentLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	float GameTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	float PlayTime;

	// Collected items in the world (by actor name or ID)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FString> CollectedWorldItems;

	// Destroyed actors (by name or ID)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FString> DestroyedActors;

	// Triggered events
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TArray<FString> TriggeredEvents;

	// Custom world data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Save Data")
	TMap<FString, FString> CustomWorldData;
};

UCLASS(BlueprintType)
class SLT_API UInventorySaveGame : public USaveGame
{
	GENERATED_BODY()

public:
	UInventorySaveGame();

	// Save file metadata
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metadata")
	FString SaveSlotName;

	UPROPERTY(VisibleAnywhere, Category = "Metadata")
	int32 UserIndex;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metadata")
	FDateTime SaveDateTime;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Metadata")
	FString GameVersion;

	// Inventory data
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	TArray<FInventorySlotSaveData> InventorySlots;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	int32 GridWidth;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	int32 GridHeight;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Inventory")
	float MaxWeight;

	// Puzzle data
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Puzzles")
	TArray<FPuzzleSaveData> PuzzleData;

	// Player data
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Player")
	FPlayerSaveData PlayerData;

	// World data
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "World")
	FWorldSaveData WorldData;

	// Save/Load functions
	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool SaveInventoryData(class UInventoryGridComponent* InventoryComponent);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool LoadInventoryData(class UInventoryGridComponent* InventoryComponent);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool SavePuzzleData(class UPuzzleComponent* PuzzleComponent, FName PuzzleID);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool LoadPuzzleData(class UPuzzleComponent* PuzzleComponent, FName PuzzleID);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool SavePlayerData(class ASLTPlayerCharacter* PlayerCharacter);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool LoadPlayerData(class ASLTPlayerCharacter* PlayerCharacter);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void SetCustomData(const FString& Key, const FString& Value);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	FString GetCustomData(const FString& Key, const FString& DefaultValue = TEXT("")) const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool HasCustomData(const FString& Key) const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void RemoveCustomData(const FString& Key);

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Save System")
	void UpdateMetadata(const FString& InSaveSlotName, int32 InUserIndex);

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool IsValidSaveData() const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void ClearAllData();

protected:
	// Custom data storage
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Custom Data")
	TMap<FString, FString> CustomSaveData;
};
