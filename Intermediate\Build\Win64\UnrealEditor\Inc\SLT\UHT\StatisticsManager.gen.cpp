// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/StatisticsManager.h"
#include "Runtime/Engine/Classes/Engine/GameInstance.h"
#include "Runtime/GameplayTags/Classes/GameplayTagContainer.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeStatisticsManager() {}

// Begin Cross Module References
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FDateTime();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FLinearColor();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTag();
GAMEPLAYTAGS_API UScriptStruct* Z_Construct_UScriptStruct_FGameplayTagContainer();
SLT_API UClass* Z_Construct_UClass_UStatisticsManager();
SLT_API UClass* Z_Construct_UClass_UStatisticsManager_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAchievementType();
SLT_API UEnum* Z_Construct_UEnum_SLT_EStatisticType();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FAchievementData();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FLeaderboardEntry();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPlayerStatistic();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum EStatisticType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EStatisticType;
static UEnum* EStatisticType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EStatisticType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EStatisticType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EStatisticType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EStatisticType"));
	}
	return Z_Registration_Info_UEnum_EStatisticType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EStatisticType>()
{
	return EStatisticType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EStatisticType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Average.DisplayName", "Average" },
		{ "Average.Name", "EStatisticType::Average" },
		{ "BlueprintType", "true" },
		{ "Counter.DisplayName", "Counter" },
		{ "Counter.Name", "EStatisticType::Counter" },
		{ "Custom.DisplayName", "Custom" },
		{ "Custom.Name", "EStatisticType::Custom" },
		{ "Maximum.DisplayName", "Maximum" },
		{ "Maximum.Name", "EStatisticType::Maximum" },
		{ "Minimum.DisplayName", "Minimum" },
		{ "Minimum.Name", "EStatisticType::Minimum" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
		{ "Percentage.DisplayName", "Percentage" },
		{ "Percentage.Name", "EStatisticType::Percentage" },
		{ "Timer.DisplayName", "Timer" },
		{ "Timer.Name", "EStatisticType::Timer" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EStatisticType::Counter", (int64)EStatisticType::Counter },
		{ "EStatisticType::Timer", (int64)EStatisticType::Timer },
		{ "EStatisticType::Maximum", (int64)EStatisticType::Maximum },
		{ "EStatisticType::Minimum", (int64)EStatisticType::Minimum },
		{ "EStatisticType::Average", (int64)EStatisticType::Average },
		{ "EStatisticType::Percentage", (int64)EStatisticType::Percentage },
		{ "EStatisticType::Custom", (int64)EStatisticType::Custom },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EStatisticType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EStatisticType",
	"EStatisticType",
	Z_Construct_UEnum_SLT_EStatisticType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EStatisticType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EStatisticType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EStatisticType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EStatisticType()
{
	if (!Z_Registration_Info_UEnum_EStatisticType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EStatisticType.InnerSingleton, Z_Construct_UEnum_SLT_EStatisticType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EStatisticType.InnerSingleton;
}
// End Enum EStatisticType

// Begin Enum EAchievementType
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAchievementType;
static UEnum* EAchievementType_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAchievementType.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAchievementType.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAchievementType, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAchievementType"));
	}
	return Z_Registration_Info_UEnum_EAchievementType.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAchievementType>()
{
	return EAchievementType_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAchievementType_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Hidden.DisplayName", "Hidden" },
		{ "Hidden.Name", "EAchievementType::Hidden" },
		{ "Milestone.DisplayName", "Milestone" },
		{ "Milestone.Name", "EAchievementType::Milestone" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
		{ "Platinum.DisplayName", "Platinum" },
		{ "Platinum.Name", "EAchievementType::Platinum" },
		{ "Progress.DisplayName", "Progress" },
		{ "Progress.Name", "EAchievementType::Progress" },
		{ "Rare.DisplayName", "Rare" },
		{ "Rare.Name", "EAchievementType::Rare" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAchievementType::Progress", (int64)EAchievementType::Progress },
		{ "EAchievementType::Milestone", (int64)EAchievementType::Milestone },
		{ "EAchievementType::Hidden", (int64)EAchievementType::Hidden },
		{ "EAchievementType::Rare", (int64)EAchievementType::Rare },
		{ "EAchievementType::Platinum", (int64)EAchievementType::Platinum },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAchievementType_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAchievementType",
	"EAchievementType",
	Z_Construct_UEnum_SLT_EAchievementType_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAchievementType_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAchievementType_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAchievementType_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAchievementType()
{
	if (!Z_Registration_Info_UEnum_EAchievementType.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAchievementType.InnerSingleton, Z_Construct_UEnum_SLT_EAchievementType_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAchievementType.InnerSingleton;
}
// End Enum EAchievementType

// Begin ScriptStruct FPlayerStatistic
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PlayerStatistic;
class UScriptStruct* FPlayerStatistic::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerStatistic.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PlayerStatistic.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPlayerStatistic, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PlayerStatistic"));
	}
	return Z_Registration_Info_UScriptStruct_PlayerStatistic.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPlayerStatistic>()
{
	return FPlayerStatistic::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPlayerStatistic_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatisticID_MetaData[] = {
		{ "Category", "Statistic" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatisticName_MetaData[] = {
		{ "Category", "Statistic" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatisticDescription_MetaData[] = {
		{ "Category", "Statistic" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatisticType_MetaData[] = {
		{ "Category", "Statistic" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentValue_MetaData[] = {
		{ "Category", "Values" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Values\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Values" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxValue_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinValue_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalValue_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_SampleCount_MetaData[] = {
		{ "Category", "Values" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsVisible_MetaData[] = {
		{ "Category", "Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Settings\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bTrackHistory_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUpdated_MetaData[] = {
		{ "Category", "Settings" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ValueHistory_MetaData[] = {
		{ "Category", "History" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// History (if tracking enabled)\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "History (if tracking enabled)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeHistory_MetaData[] = {
		{ "Category", "History" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Categories_MetaData[] = {
		{ "Category", "Categories" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Categories\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Categories" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomProperties_MetaData[] = {
		{ "Category", "Custom" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Custom properties\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Custom properties" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StatisticName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_StatisticDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_StatisticType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_StatisticType;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_CurrentValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TotalValue;
	static const UECodeGen_Private::FIntPropertyParams NewProp_SampleCount;
	static void NewProp_bIsVisible_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsVisible;
	static void NewProp_bTrackHistory_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bTrackHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LastUpdated;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ValueHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ValueHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_TimeHistory_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_TimeHistory;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Categories;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomProperties_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomProperties;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPlayerStatistic>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, StatisticID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatisticID_MetaData), NewProp_StatisticID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticName = { "StatisticName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, StatisticName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatisticName_MetaData), NewProp_StatisticName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticDescription = { "StatisticDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, StatisticDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatisticDescription_MetaData), NewProp_StatisticDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticType = { "StatisticType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, StatisticType), Z_Construct_UEnum_SLT_EStatisticType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatisticType_MetaData), NewProp_StatisticType_MetaData) }; // 929910636
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CurrentValue = { "CurrentValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, CurrentValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentValue_MetaData), NewProp_CurrentValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_MaxValue = { "MaxValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, MaxValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxValue_MetaData), NewProp_MaxValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_MinValue = { "MinValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, MinValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinValue_MetaData), NewProp_MinValue_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TotalValue = { "TotalValue", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, TotalValue), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalValue_MetaData), NewProp_TotalValue_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_SampleCount = { "SampleCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, SampleCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_SampleCount_MetaData), NewProp_SampleCount_MetaData) };
void Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bIsVisible_SetBit(void* Obj)
{
	((FPlayerStatistic*)Obj)->bIsVisible = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bIsVisible = { "bIsVisible", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlayerStatistic), &Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bIsVisible_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsVisible_MetaData), NewProp_bIsVisible_MetaData) };
void Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bTrackHistory_SetBit(void* Obj)
{
	((FPlayerStatistic*)Obj)->bTrackHistory = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bTrackHistory = { "bTrackHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPlayerStatistic), &Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bTrackHistory_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bTrackHistory_MetaData), NewProp_bTrackHistory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_LastUpdated = { "LastUpdated", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, LastUpdated), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUpdated_MetaData), NewProp_LastUpdated_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_ValueHistory_Inner = { "ValueHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_ValueHistory = { "ValueHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, ValueHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ValueHistory_MetaData), NewProp_ValueHistory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TimeHistory_Inner = { "TimeHistory", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TimeHistory = { "TimeHistory", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, TimeHistory), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeHistory_MetaData), NewProp_TimeHistory_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_Categories = { "Categories", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, Categories), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Categories_MetaData), NewProp_Categories_MetaData) }; // 3352185621
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties_ValueProp = { "CustomProperties", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties_Key_KeyProp = { "CustomProperties_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties = { "CustomProperties", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPlayerStatistic, CustomProperties), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomProperties_MetaData), NewProp_CustomProperties_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPlayerStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_StatisticType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CurrentValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_MaxValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_MinValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TotalValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_SampleCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bIsVisible,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_bTrackHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_LastUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_ValueHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_ValueHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TimeHistory_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_TimeHistory,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_Categories,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewProp_CustomProperties,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPlayerStatistic_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PlayerStatistic",
	Z_Construct_UScriptStruct_FPlayerStatistic_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerStatistic_Statics::PropPointers),
	sizeof(FPlayerStatistic),
	alignof(FPlayerStatistic),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPlayerStatistic_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPlayerStatistic_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPlayerStatistic()
{
	if (!Z_Registration_Info_UScriptStruct_PlayerStatistic.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PlayerStatistic.InnerSingleton, Z_Construct_UScriptStruct_FPlayerStatistic_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PlayerStatistic.InnerSingleton;
}
// End ScriptStruct FPlayerStatistic

// Begin ScriptStruct FAchievementData
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_AchievementData;
class UScriptStruct* FAchievementData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_AchievementData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_AchievementData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAchievementData, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("AchievementData"));
	}
	return Z_Registration_Info_UScriptStruct_AchievementData.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FAchievementData>()
{
	return FAchievementData::StaticStruct();
}
struct Z_Construct_UScriptStruct_FAchievementData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementID_MetaData[] = {
		{ "Category", "Achievement" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Identification\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Identification" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementName_MetaData[] = {
		{ "Category", "Achievement" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementDescription_MetaData[] = {
		{ "Category", "Achievement" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementType_MetaData[] = {
		{ "Category", "Achievement" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsUnlocked_MetaData[] = {
		{ "Category", "Status" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Status\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Status" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsHidden_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxProgress_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Points_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UnlockDate_MetaData[] = {
		{ "Category", "Status" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredStatistics_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Requirements\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Requirements" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequiredTags_MetaData[] = {
		{ "Category", "Requirements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// StatisticID -> Required Value\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "StatisticID -> Required Value" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementIcon_MetaData[] = {
		{ "Category", "Visual" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Visual\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Visual" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementColor_MetaData[] = {
		{ "Category", "Visual" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AchievementName;
	static const UECodeGen_Private::FTextPropertyParams NewProp_AchievementDescription;
	static const UECodeGen_Private::FBytePropertyParams NewProp_AchievementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AchievementType;
	static void NewProp_bIsUnlocked_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsUnlocked;
	static void NewProp_bIsHidden_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsHidden;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Points;
	static const UECodeGen_Private::FStructPropertyParams NewProp_UnlockDate;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_RequiredStatistics_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_RequiredStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_RequiredStatistics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_RequiredTags;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_AchievementIcon;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AchievementColor;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAchievementData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementID_MetaData), NewProp_AchievementID_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementName = { "AchievementName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementName_MetaData), NewProp_AchievementName_MetaData) };
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementDescription = { "AchievementDescription", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementDescription), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementDescription_MetaData), NewProp_AchievementDescription_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementType = { "AchievementType", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementType), Z_Construct_UEnum_SLT_EAchievementType, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementType_MetaData), NewProp_AchievementType_MetaData) }; // 1025751466
void Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsUnlocked_SetBit(void* Obj)
{
	((FAchievementData*)Obj)->bIsUnlocked = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsUnlocked = { "bIsUnlocked", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAchievementData), &Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsUnlocked_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsUnlocked_MetaData), NewProp_bIsUnlocked_MetaData) };
void Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsHidden_SetBit(void* Obj)
{
	((FAchievementData*)Obj)->bIsHidden = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsHidden = { "bIsHidden", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAchievementData), &Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsHidden_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsHidden_MetaData), NewProp_bIsHidden_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, Progress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_MaxProgress = { "MaxProgress", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, MaxProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxProgress_MetaData), NewProp_MaxProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_Points = { "Points", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, Points), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Points_MetaData), NewProp_Points_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_UnlockDate = { "UnlockDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, UnlockDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UnlockDate_MetaData), NewProp_UnlockDate_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics_ValueProp = { "RequiredStatistics", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FNamePropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics_Key_KeyProp = { "RequiredStatistics_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics = { "RequiredStatistics", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, RequiredStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredStatistics_MetaData), NewProp_RequiredStatistics_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredTags = { "RequiredTags", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, RequiredTags), Z_Construct_UScriptStruct_FGameplayTagContainer, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequiredTags_MetaData), NewProp_RequiredTags_MetaData) }; // 3352185621
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementIcon = { "AchievementIcon", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementIcon), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementIcon_MetaData), NewProp_AchievementIcon_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementColor = { "AchievementColor", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAchievementData, AchievementColor), Z_Construct_UScriptStruct_FLinearColor, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementColor_MetaData), NewProp_AchievementColor_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAchievementData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementDescription,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_bIsHidden,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_MaxProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_Points,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_UnlockDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_RequiredTags,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementIcon,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAchievementData_Statics::NewProp_AchievementColor,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAchievementData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAchievementData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"AchievementData",
	Z_Construct_UScriptStruct_FAchievementData_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAchievementData_Statics::PropPointers),
	sizeof(FAchievementData),
	alignof(FAchievementData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAchievementData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAchievementData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAchievementData()
{
	if (!Z_Registration_Info_UScriptStruct_AchievementData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_AchievementData.InnerSingleton, Z_Construct_UScriptStruct_FAchievementData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_AchievementData.InnerSingleton;
}
// End ScriptStruct FAchievementData

// Begin ScriptStruct FLeaderboardEntry
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LeaderboardEntry;
class UScriptStruct* FLeaderboardEntry::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LeaderboardEntry.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LeaderboardEntry.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLeaderboardEntry, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("LeaderboardEntry"));
	}
	return Z_Registration_Info_UScriptStruct_LeaderboardEntry.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FLeaderboardEntry>()
{
	return FLeaderboardEntry::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLeaderboardEntry_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerName_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Score_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rank_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayTime_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CompletionDate_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLocalPlayer_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CustomData_MetaData[] = {
		{ "Category", "Entry" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Score;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Rank;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PlayTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CompletionDate;
	static void NewProp_bIsLocalPlayer_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLocalPlayer;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CustomData_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_CustomData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLeaderboardEntry>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_PlayerName = { "PlayerName", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, PlayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerName_MetaData), NewProp_PlayerName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_Score = { "Score", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, Score), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Score_MetaData), NewProp_Score_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_Rank = { "Rank", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, Rank), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rank_MetaData), NewProp_Rank_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_PlayTime = { "PlayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, PlayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayTime_MetaData), NewProp_PlayTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CompletionDate = { "CompletionDate", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, CompletionDate), Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CompletionDate_MetaData), NewProp_CompletionDate_MetaData) };
void Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_bIsLocalPlayer_SetBit(void* Obj)
{
	((FLeaderboardEntry*)Obj)->bIsLocalPlayer = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_bIsLocalPlayer = { "bIsLocalPlayer", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLeaderboardEntry), &Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_bIsLocalPlayer_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLocalPlayer_MetaData), NewProp_bIsLocalPlayer_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData_ValueProp = { "CustomData", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData_Key_KeyProp = { "CustomData_Key", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData = { "CustomData", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLeaderboardEntry, CustomData), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CustomData_MetaData), NewProp_CustomData_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_PlayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_Score,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_Rank,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_PlayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CompletionDate,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_bIsLocalPlayer,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewProp_CustomData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"LeaderboardEntry",
	Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::PropPointers),
	sizeof(FLeaderboardEntry),
	alignof(FLeaderboardEntry),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLeaderboardEntry()
{
	if (!Z_Registration_Info_UScriptStruct_LeaderboardEntry.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LeaderboardEntry.InnerSingleton, Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LeaderboardEntry.InnerSingleton;
}
// End ScriptStruct FLeaderboardEntry

// Begin Delegate FOnStatisticUpdated
struct Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnStatisticUpdated_Parms
	{
		FName StatisticID;
		float NewValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnStatisticUpdated_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnStatisticUpdated_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::NewProp_NewValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnStatisticUpdated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::_Script_SLT_eventOnStatisticUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::_Script_SLT_eventOnStatisticUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnStatisticUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnStatisticUpdated, FName StatisticID, float NewValue)
{
	struct _Script_SLT_eventOnStatisticUpdated_Parms
	{
		FName StatisticID;
		float NewValue;
	};
	_Script_SLT_eventOnStatisticUpdated_Parms Parms;
	Parms.StatisticID=StatisticID;
	Parms.NewValue=NewValue;
	OnStatisticUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnStatisticUpdated

// Begin Delegate FOnAchievementUnlocked
struct Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnAchievementUnlocked_Parms
	{
		FName AchievementID;
		FAchievementData AchievementData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AchievementData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAchievementUnlocked_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_AchievementData = { "AchievementData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAchievementUnlocked_Parms, AchievementData), Z_Construct_UScriptStruct_FAchievementData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementData_MetaData), NewProp_AchievementData_MetaData) }; // 3257305814
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::NewProp_AchievementData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnAchievementUnlocked__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::_Script_SLT_eventOnAchievementUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::_Script_SLT_eventOnAchievementUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAchievementUnlocked_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementUnlocked, FName AchievementID, FAchievementData const& AchievementData)
{
	struct _Script_SLT_eventOnAchievementUnlocked_Parms
	{
		FName AchievementID;
		FAchievementData AchievementData;
	};
	_Script_SLT_eventOnAchievementUnlocked_Parms Parms;
	Parms.AchievementID=AchievementID;
	Parms.AchievementData=AchievementData;
	OnAchievementUnlocked.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAchievementUnlocked

// Begin Delegate FOnAchievementProgress
struct Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnAchievementProgress_Parms
	{
		FName AchievementID;
		float Progress;
		float MaxProgress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxProgress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAchievementProgress_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAchievementProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_MaxProgress = { "MaxProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAchievementProgress_Parms, MaxProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_Progress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::NewProp_MaxProgress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnAchievementProgress__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::_Script_SLT_eventOnAchievementProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::_Script_SLT_eventOnAchievementProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAchievementProgress_DelegateWrapper(const FMulticastScriptDelegate& OnAchievementProgress, FName AchievementID, float Progress, float MaxProgress)
{
	struct _Script_SLT_eventOnAchievementProgress_Parms
	{
		FName AchievementID;
		float Progress;
		float MaxProgress;
	};
	_Script_SLT_eventOnAchievementProgress_Parms Parms;
	Parms.AchievementID=AchievementID;
	Parms.Progress=Progress;
	Parms.MaxProgress=MaxProgress;
	OnAchievementProgress.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAchievementProgress

// Begin Delegate FOnLeaderboardUpdated
struct Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLeaderboardUpdated_Parms
	{
		FString LeaderboardName;
		TArray<FLeaderboardEntry> Entries;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Entries_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardName;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Entries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_Entries;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_LeaderboardName = { "LeaderboardName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLeaderboardUpdated_Parms, LeaderboardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardName_MetaData), NewProp_LeaderboardName_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_Entries_Inner = { "Entries", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLeaderboardEntry, METADATA_PARAMS(0, nullptr) }; // 2231317326
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_Entries = { "Entries", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLeaderboardUpdated_Parms, Entries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Entries_MetaData), NewProp_Entries_MetaData) }; // 2231317326
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_LeaderboardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_Entries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::NewProp_Entries,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLeaderboardUpdated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::_Script_SLT_eventOnLeaderboardUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::_Script_SLT_eventOnLeaderboardUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLeaderboardUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLeaderboardUpdated, const FString& LeaderboardName, TArray<FLeaderboardEntry> const& Entries)
{
	struct _Script_SLT_eventOnLeaderboardUpdated_Parms
	{
		FString LeaderboardName;
		TArray<FLeaderboardEntry> Entries;
	};
	_Script_SLT_eventOnLeaderboardUpdated_Parms Parms;
	Parms.LeaderboardName=LeaderboardName;
	Parms.Entries=Entries;
	OnLeaderboardUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLeaderboardUpdated

// Begin Class UStatisticsManager Function ClearLeaderboard
struct Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics
{
	struct StatisticsManager_eventClearLeaderboard_Parms
	{
		FString LeaderboardName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Leaderboards" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::NewProp_LeaderboardName = { "LeaderboardName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventClearLeaderboard_Parms, LeaderboardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardName_MetaData), NewProp_LeaderboardName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::NewProp_LeaderboardName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "ClearLeaderboard", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::StatisticsManager_eventClearLeaderboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::StatisticsManager_eventClearLeaderboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execClearLeaderboard)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearLeaderboard(Z_Param_LeaderboardName);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function ClearLeaderboard

// Begin Class UStatisticsManager Function ExportStatistics
struct Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics
{
	struct StatisticsManager_eventExportStatistics_Parms
	{
		FString FilePath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventExportStatistics_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::NewProp_FilePath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "ExportStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::StatisticsManager_eventExportStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x44020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::StatisticsManager_eventExportStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_ExportStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_ExportStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execExportStatistics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ExportStatistics(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function ExportStatistics

// Begin Class UStatisticsManager Function GetAchievementProgress
struct Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics
{
	struct StatisticsManager_eventGetAchievementProgress_Parms
	{
		FName AchievementID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetAchievementProgress_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetAchievementProgress_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetAchievementProgress", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::StatisticsManager_eventGetAchievementProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::StatisticsManager_eventGetAchievementProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetAchievementProgress)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AchievementID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetAchievementProgress(Z_Param_AchievementID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetAchievementProgress

// Begin Class UStatisticsManager Function GetAchievementsByType
struct Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics
{
	struct StatisticsManager_eventGetAchievementsByType_Parms
	{
		EAchievementType AchievementType;
		TArray<FAchievementData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_AchievementType_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_AchievementType;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_AchievementType_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_AchievementType = { "AchievementType", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetAchievementsByType_Parms, AchievementType), Z_Construct_UEnum_SLT_EAchievementType, METADATA_PARAMS(0, nullptr) }; // 1025751466
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAchievementData, METADATA_PARAMS(0, nullptr) }; // 3257305814
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetAchievementsByType_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3257305814
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_AchievementType_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_AchievementType,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetAchievementsByType", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::StatisticsManager_eventGetAchievementsByType_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::StatisticsManager_eventGetAchievementsByType_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetAchievementsByType)
{
	P_GET_ENUM(EAchievementType,Z_Param_AchievementType);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAchievementData>*)Z_Param__Result=P_THIS->GetAchievementsByType(EAchievementType(Z_Param_AchievementType));
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetAchievementsByType

// Begin Class UStatisticsManager Function GetLeaderboard
struct Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics
{
	struct StatisticsManager_eventGetLeaderboard_Parms
	{
		FString LeaderboardName;
		int32 MaxEntries;
		TArray<FLeaderboardEntry> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Leaderboards" },
		{ "CPP_Default_MaxEntries", "10" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxEntries;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_LeaderboardName = { "LeaderboardName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetLeaderboard_Parms, LeaderboardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardName_MetaData), NewProp_LeaderboardName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_MaxEntries = { "MaxEntries", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetLeaderboard_Parms, MaxEntries), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLeaderboardEntry, METADATA_PARAMS(0, nullptr) }; // 2231317326
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetLeaderboard_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 2231317326
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_LeaderboardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_MaxEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetLeaderboard", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::StatisticsManager_eventGetLeaderboard_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::StatisticsManager_eventGetLeaderboard_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetLeaderboard()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetLeaderboard_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetLeaderboard)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardName);
	P_GET_PROPERTY(FIntProperty,Z_Param_MaxEntries);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FLeaderboardEntry>*)Z_Param__Result=P_THIS->GetLeaderboard(Z_Param_LeaderboardName,Z_Param_MaxEntries);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetLeaderboard

// Begin Class UStatisticsManager Function GetPlayerRank
struct Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics
{
	struct StatisticsManager_eventGetPlayerRank_Parms
	{
		FString LeaderboardName;
		FString PlayerName;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Leaderboards" },
		{ "CPP_Default_PlayerName", "" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerName;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_LeaderboardName = { "LeaderboardName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetPlayerRank_Parms, LeaderboardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardName_MetaData), NewProp_LeaderboardName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_PlayerName = { "PlayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetPlayerRank_Parms, PlayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerName_MetaData), NewProp_PlayerName_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetPlayerRank_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_LeaderboardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_PlayerName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetPlayerRank", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::StatisticsManager_eventGetPlayerRank_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::StatisticsManager_eventGetPlayerRank_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetPlayerRank()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetPlayerRank_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetPlayerRank)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardName);
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPlayerRank(Z_Param_LeaderboardName,Z_Param_PlayerName);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetPlayerRank

// Begin Class UStatisticsManager Function GetPlaytimeStatistics
struct Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics
{
	struct StatisticsManager_eventGetPlaytimeStatistics_Parms
	{
		TMap<FString,float> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetPlaytimeStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetPlaytimeStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::StatisticsManager_eventGetPlaytimeStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::StatisticsManager_eventGetPlaytimeStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetPlaytimeStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,float>*)Z_Param__Result=P_THIS->GetPlaytimeStatistics();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetPlaytimeStatistics

// Begin Class UStatisticsManager Function GetStatistic
struct Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics
{
	struct StatisticsManager_eventGetStatistic_Parms
	{
		FName StatisticID;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatistic_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatistic_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetStatistic", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::StatisticsManager_eventGetStatistic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::StatisticsManager_eventGetStatistic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetStatistic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetStatistic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetStatistic)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetStatistic(Z_Param_StatisticID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetStatistic

// Begin Class UStatisticsManager Function GetStatisticData
struct Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics
{
	struct StatisticsManager_eventGetStatisticData_Parms
	{
		FName StatisticID;
		FPlayerStatistic ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatisticData_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatisticData_Parms, ReturnValue), Z_Construct_UScriptStruct_FPlayerStatistic, METADATA_PARAMS(0, nullptr) }; // 958180106
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetStatisticData", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::StatisticsManager_eventGetStatisticData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::StatisticsManager_eventGetStatisticData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetStatisticData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetStatisticData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetStatisticData)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FPlayerStatistic*)Z_Param__Result=P_THIS->GetStatisticData(Z_Param_StatisticID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetStatisticData

// Begin Class UStatisticsManager Function GetStatisticsByCategory
struct Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics
{
	struct StatisticsManager_eventGetStatisticsByCategory_Parms
	{
		FGameplayTag Category;
		TArray<FName> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Category;
	static const UECodeGen_Private::FNamePropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatisticsByCategory_Parms, Category), Z_Construct_UScriptStruct_FGameplayTag, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) }; // 1298103297
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetStatisticsByCategory_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetStatisticsByCategory", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::StatisticsManager_eventGetStatisticsByCategory_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::StatisticsManager_eventGetStatisticsByCategory_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetStatisticsByCategory)
{
	P_GET_STRUCT_REF(FGameplayTag,Z_Param_Out_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FName>*)Z_Param__Result=P_THIS->GetStatisticsByCategory(Z_Param_Out_Category);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetStatisticsByCategory

// Begin Class UStatisticsManager Function GetTotalAchievementPoints
struct Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics
{
	struct StatisticsManager_eventGetTotalAchievementPoints_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetTotalAchievementPoints_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetTotalAchievementPoints", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::StatisticsManager_eventGetTotalAchievementPoints_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::StatisticsManager_eventGetTotalAchievementPoints_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetTotalAchievementPoints)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetTotalAchievementPoints();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetTotalAchievementPoints

// Begin Class UStatisticsManager Function GetUnlockedAchievements
struct Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics
{
	struct StatisticsManager_eventGetUnlockedAchievements_Parms
	{
		TArray<FAchievementData> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAchievementData, METADATA_PARAMS(0, nullptr) }; // 3257305814
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetUnlockedAchievements_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) }; // 3257305814
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetUnlockedAchievements", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::StatisticsManager_eventGetUnlockedAchievements_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::StatisticsManager_eventGetUnlockedAchievements_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetUnlockedAchievements)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FAchievementData>*)Z_Param__Result=P_THIS->GetUnlockedAchievements();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetUnlockedAchievements

// Begin Class UStatisticsManager Function GetUsageStatistics
struct Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics
{
	struct StatisticsManager_eventGetUsageStatistics_Parms
	{
		TMap<FString,int32> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue_ValueProp = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue_Key_KeyProp = { "ReturnValue_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventGetUsageStatistics_Parms, ReturnValue), EMapPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "GetUsageStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::StatisticsManager_eventGetUsageStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::StatisticsManager_eventGetUsageStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execGetUsageStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TMap<FString,int32>*)Z_Param__Result=P_THIS->GetUsageStatistics();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function GetUsageStatistics

// Begin Class UStatisticsManager Function ImportStatistics
struct Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics
{
	struct StatisticsManager_eventImportStatistics_Parms
	{
		FString FilePath;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_FilePath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_FilePath;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_FilePath = { "FilePath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventImportStatistics_Parms, FilePath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_FilePath_MetaData), NewProp_FilePath_MetaData) };
void Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((StatisticsManager_eventImportStatistics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(StatisticsManager_eventImportStatistics_Parms), &Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_FilePath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "ImportStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::StatisticsManager_eventImportStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::StatisticsManager_eventImportStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_ImportStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_ImportStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execImportStatistics)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_FilePath);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ImportStatistics(Z_Param_FilePath);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function ImportStatistics

// Begin Class UStatisticsManager Function IsAchievementUnlocked
struct Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics
{
	struct StatisticsManager_eventIsAchievementUnlocked_Parms
	{
		FName AchievementID;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventIsAchievementUnlocked_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((StatisticsManager_eventIsAchievementUnlocked_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(StatisticsManager_eventIsAchievementUnlocked_Parms), &Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "IsAchievementUnlocked", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::StatisticsManager_eventIsAchievementUnlocked_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::StatisticsManager_eventIsAchievementUnlocked_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execIsAchievementUnlocked)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AchievementID);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsAchievementUnlocked(Z_Param_AchievementID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function IsAchievementUnlocked

// Begin Class UStatisticsManager Function LoadStatistics
struct Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics
{
	struct StatisticsManager_eventLoadStatistics_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((StatisticsManager_eventLoadStatistics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(StatisticsManager_eventLoadStatistics_Parms), &Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "LoadStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::StatisticsManager_eventLoadStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::StatisticsManager_eventLoadStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_LoadStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_LoadStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execLoadStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->LoadStatistics();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function LoadStatistics

// Begin Class UStatisticsManager Function OnNewPersonalBest
struct StatisticsManager_eventOnNewPersonalBest_Parms
{
	FName StatisticID;
	float NewValue;
	float OldValue;
};
static const FName NAME_UStatisticsManager_OnNewPersonalBest = FName(TEXT("OnNewPersonalBest"));
void UStatisticsManager::OnNewPersonalBest(FName StatisticID, float NewValue, float OldValue)
{
	StatisticsManager_eventOnNewPersonalBest_Parms Parms;
	Parms.StatisticID=StatisticID;
	Parms.NewValue=NewValue;
	Parms.OldValue=OldValue;
	UFunction* Func = FindFunctionChecked(NAME_UStatisticsManager_OnNewPersonalBest);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewValue;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OldValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventOnNewPersonalBest_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_NewValue = { "NewValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventOnNewPersonalBest_Parms, NewValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_OldValue = { "OldValue", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventOnNewPersonalBest_Parms, OldValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_NewValue,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::NewProp_OldValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "OnNewPersonalBest", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::PropPointers), sizeof(StatisticsManager_eventOnNewPersonalBest_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::Function_MetaDataParams) };
static_assert(sizeof(StatisticsManager_eventOnNewPersonalBest_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UStatisticsManager Function OnNewPersonalBest

// Begin Class UStatisticsManager Function OnStatisticsLoaded
static const FName NAME_UStatisticsManager_OnStatisticsLoaded = FName(TEXT("OnStatisticsLoaded"));
void UStatisticsManager::OnStatisticsLoaded()
{
	UFunction* Func = FindFunctionChecked(NAME_UStatisticsManager_OnStatisticsLoaded);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "OnStatisticsLoaded", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class UStatisticsManager Function OnStatisticsLoaded

// Begin Class UStatisticsManager Function RegisterAchievement
struct Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics
{
	struct StatisticsManager_eventRegisterAchievement_Parms
	{
		FName AchievementID;
		FAchievementData AchievementData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Achievement functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Achievement functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AchievementData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_AchievementData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventRegisterAchievement_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::NewProp_AchievementData = { "AchievementData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventRegisterAchievement_Parms, AchievementData), Z_Construct_UScriptStruct_FAchievementData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AchievementData_MetaData), NewProp_AchievementData_MetaData) }; // 3257305814
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::NewProp_AchievementData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "RegisterAchievement", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::StatisticsManager_eventRegisterAchievement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::StatisticsManager_eventRegisterAchievement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_RegisterAchievement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_RegisterAchievement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execRegisterAchievement)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AchievementID);
	P_GET_STRUCT_REF(FAchievementData,Z_Param_Out_AchievementData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterAchievement(Z_Param_AchievementID,Z_Param_Out_AchievementData);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function RegisterAchievement

// Begin Class UStatisticsManager Function RegisterStatistic
struct Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics
{
	struct StatisticsManager_eventRegisterStatistic_Parms
	{
		FName StatisticID;
		FPlayerStatistic StatisticData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StatisticData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FStructPropertyParams NewProp_StatisticData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventRegisterStatistic_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::NewProp_StatisticData = { "StatisticData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventRegisterStatistic_Parms, StatisticData), Z_Construct_UScriptStruct_FPlayerStatistic, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StatisticData_MetaData), NewProp_StatisticData_MetaData) }; // 958180106
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::NewProp_StatisticData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "RegisterStatistic", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::StatisticsManager_eventRegisterStatistic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::StatisticsManager_eventRegisterStatistic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_RegisterStatistic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_RegisterStatistic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execRegisterStatistic)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_GET_STRUCT_REF(FPlayerStatistic,Z_Param_Out_StatisticData);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterStatistic(Z_Param_StatisticID,Z_Param_Out_StatisticData);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function RegisterStatistic

// Begin Class UStatisticsManager Function ResetAllStatistics
struct Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "ResetAllStatistics", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execResetAllStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetAllStatistics();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function ResetAllStatistics

// Begin Class UStatisticsManager Function ResetStatistic
struct Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics
{
	struct StatisticsManager_eventResetStatistic_Parms
	{
		FName StatisticID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventResetStatistic_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::NewProp_StatisticID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "ResetStatistic", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::StatisticsManager_eventResetStatistic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::StatisticsManager_eventResetStatistic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_ResetStatistic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_ResetStatistic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execResetStatistic)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetStatistic(Z_Param_StatisticID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function ResetStatistic

// Begin Class UStatisticsManager Function SaveStatistics
struct Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics
{
	struct StatisticsManager_eventSaveStatistics_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Save System" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Save/Load functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Save/Load functions" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((StatisticsManager_eventSaveStatistics_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(StatisticsManager_eventSaveStatistics_Parms), &Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "SaveStatistics", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::StatisticsManager_eventSaveStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::StatisticsManager_eventSaveStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_SaveStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_SaveStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execSaveStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->SaveStatistics();
	P_NATIVE_END;
}
// End Class UStatisticsManager Function SaveStatistics

// Begin Class UStatisticsManager Function SetStatistic
struct Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics
{
	struct StatisticsManager_eventSetStatistic_Parms
	{
		FName StatisticID;
		float Value;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventSetStatistic_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventSetStatistic_Parms, Value), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::NewProp_Value,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "SetStatistic", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::StatisticsManager_eventSetStatistic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::StatisticsManager_eventSetStatistic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_SetStatistic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_SetStatistic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execSetStatistic)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetStatistic(Z_Param_StatisticID,Z_Param_Value);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function SetStatistic

// Begin Class UStatisticsManager Function SubmitScore
struct Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics
{
	struct StatisticsManager_eventSubmitScore_Parms
	{
		FString LeaderboardName;
		float Score;
		FString PlayerName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Leaderboards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Leaderboard functions\n" },
#endif
		{ "CPP_Default_PlayerName", "" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Leaderboard functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LeaderboardName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Score;
	static const UECodeGen_Private::FStrPropertyParams NewProp_PlayerName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_LeaderboardName = { "LeaderboardName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventSubmitScore_Parms, LeaderboardName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardName_MetaData), NewProp_LeaderboardName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_Score = { "Score", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventSubmitScore_Parms, Score), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_PlayerName = { "PlayerName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventSubmitScore_Parms, PlayerName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerName_MetaData), NewProp_PlayerName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_LeaderboardName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_Score,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::NewProp_PlayerName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "SubmitScore", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::StatisticsManager_eventSubmitScore_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::StatisticsManager_eventSubmitScore_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_SubmitScore()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_SubmitScore_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execSubmitScore)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LeaderboardName);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Score);
	P_GET_PROPERTY(FStrProperty,Z_Param_PlayerName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SubmitScore(Z_Param_LeaderboardName,Z_Param_Score,Z_Param_PlayerName);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function SubmitScore

// Begin Class UStatisticsManager Function TrackEvent
struct Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics
{
	struct StatisticsManager_eventTrackEvent_Parms
	{
		FString EventName;
		TMap<FString,FString> Parameters;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Analytics functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Analytics functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventName_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Parameters_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventName;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Parameters_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_Parameters;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_EventName = { "EventName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventTrackEvent_Parms, EventName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventName_MetaData), NewProp_EventName_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters_ValueProp = { "Parameters", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters_Key_KeyProp = { "Parameters_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters = { "Parameters", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventTrackEvent_Parms, Parameters), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Parameters_MetaData), NewProp_Parameters_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_EventName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::NewProp_Parameters,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "TrackEvent", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::StatisticsManager_eventTrackEvent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::StatisticsManager_eventTrackEvent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_TrackEvent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_TrackEvent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execTrackEvent)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_EventName);
	P_GET_TMAP_REF(FString,FString,Z_Param_Out_Parameters);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrackEvent(Z_Param_EventName,Z_Param_Out_Parameters);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function TrackEvent

// Begin Class UStatisticsManager Function TrackPlayerAction
struct Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics
{
	struct StatisticsManager_eventTrackPlayerAction_Parms
	{
		FString Action;
		FString Context;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Analytics" },
		{ "CPP_Default_Context", "" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Action_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Context_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Action;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Context;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::NewProp_Action = { "Action", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventTrackPlayerAction_Parms, Action), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Action_MetaData), NewProp_Action_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::NewProp_Context = { "Context", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventTrackPlayerAction_Parms, Context), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Context_MetaData), NewProp_Context_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::NewProp_Action,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::NewProp_Context,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "TrackPlayerAction", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::StatisticsManager_eventTrackPlayerAction_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::StatisticsManager_eventTrackPlayerAction_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execTrackPlayerAction)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Action);
	P_GET_PROPERTY(FStrProperty,Z_Param_Context);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->TrackPlayerAction(Z_Param_Action,Z_Param_Context);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function TrackPlayerAction

// Begin Class UStatisticsManager Function UnlockAchievement
struct Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics
{
	struct StatisticsManager_eventUnlockAchievement_Parms
	{
		FName AchievementID;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventUnlockAchievement_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::NewProp_AchievementID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "UnlockAchievement", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::StatisticsManager_eventUnlockAchievement_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::StatisticsManager_eventUnlockAchievement_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_UnlockAchievement()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_UnlockAchievement_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execUnlockAchievement)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AchievementID);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnlockAchievement(Z_Param_AchievementID);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function UnlockAchievement

// Begin Class UStatisticsManager Function UpdateAchievementProgress
struct Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics
{
	struct StatisticsManager_eventUpdateAchievementProgress_Parms
	{
		FName AchievementID;
		float Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Achievements" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_AchievementID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::NewProp_AchievementID = { "AchievementID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventUpdateAchievementProgress_Parms, AchievementID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventUpdateAchievementProgress_Parms, Progress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::NewProp_AchievementID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "UpdateAchievementProgress", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::StatisticsManager_eventUpdateAchievementProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::StatisticsManager_eventUpdateAchievementProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execUpdateAchievementProgress)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_AchievementID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Progress);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateAchievementProgress(Z_Param_AchievementID,Z_Param_Progress);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function UpdateAchievementProgress

// Begin Class UStatisticsManager Function UpdateStatistic
struct Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics
{
	struct StatisticsManager_eventUpdateStatistic_Parms
	{
		FName StatisticID;
		float Value;
		bool bIsIncrement;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Statistics" },
		{ "CPP_Default_bIsIncrement", "true" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FNamePropertyParams NewProp_StatisticID;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_Value;
	static void NewProp_bIsIncrement_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsIncrement;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FNamePropertyParams Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_StatisticID = { "StatisticID", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventUpdateStatistic_Parms, StatisticID), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_Value = { "Value", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(StatisticsManager_eventUpdateStatistic_Parms, Value), METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_bIsIncrement_SetBit(void* Obj)
{
	((StatisticsManager_eventUpdateStatistic_Parms*)Obj)->bIsIncrement = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_bIsIncrement = { "bIsIncrement", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(StatisticsManager_eventUpdateStatistic_Parms), &Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_bIsIncrement_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_StatisticID,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_Value,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::NewProp_bIsIncrement,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UStatisticsManager, nullptr, "UpdateStatistic", nullptr, nullptr, Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::PropPointers), sizeof(Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::StatisticsManager_eventUpdateStatistic_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::Function_MetaDataParams), Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::StatisticsManager_eventUpdateStatistic_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UStatisticsManager_UpdateStatistic()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UStatisticsManager_UpdateStatistic_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UStatisticsManager::execUpdateStatistic)
{
	P_GET_PROPERTY(FNameProperty,Z_Param_StatisticID);
	P_GET_PROPERTY(FFloatProperty,Z_Param_Value);
	P_GET_UBOOL(Z_Param_bIsIncrement);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateStatistic(Z_Param_StatisticID,Z_Param_Value,Z_Param_bIsIncrement);
	P_NATIVE_END;
}
// End Class UStatisticsManager Function UpdateStatistic

// Begin Class UStatisticsManager
void UStatisticsManager::StaticRegisterNativesUStatisticsManager()
{
	UClass* Class = UStatisticsManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "ClearLeaderboard", &UStatisticsManager::execClearLeaderboard },
		{ "ExportStatistics", &UStatisticsManager::execExportStatistics },
		{ "GetAchievementProgress", &UStatisticsManager::execGetAchievementProgress },
		{ "GetAchievementsByType", &UStatisticsManager::execGetAchievementsByType },
		{ "GetLeaderboard", &UStatisticsManager::execGetLeaderboard },
		{ "GetPlayerRank", &UStatisticsManager::execGetPlayerRank },
		{ "GetPlaytimeStatistics", &UStatisticsManager::execGetPlaytimeStatistics },
		{ "GetStatistic", &UStatisticsManager::execGetStatistic },
		{ "GetStatisticData", &UStatisticsManager::execGetStatisticData },
		{ "GetStatisticsByCategory", &UStatisticsManager::execGetStatisticsByCategory },
		{ "GetTotalAchievementPoints", &UStatisticsManager::execGetTotalAchievementPoints },
		{ "GetUnlockedAchievements", &UStatisticsManager::execGetUnlockedAchievements },
		{ "GetUsageStatistics", &UStatisticsManager::execGetUsageStatistics },
		{ "ImportStatistics", &UStatisticsManager::execImportStatistics },
		{ "IsAchievementUnlocked", &UStatisticsManager::execIsAchievementUnlocked },
		{ "LoadStatistics", &UStatisticsManager::execLoadStatistics },
		{ "RegisterAchievement", &UStatisticsManager::execRegisterAchievement },
		{ "RegisterStatistic", &UStatisticsManager::execRegisterStatistic },
		{ "ResetAllStatistics", &UStatisticsManager::execResetAllStatistics },
		{ "ResetStatistic", &UStatisticsManager::execResetStatistic },
		{ "SaveStatistics", &UStatisticsManager::execSaveStatistics },
		{ "SetStatistic", &UStatisticsManager::execSetStatistic },
		{ "SubmitScore", &UStatisticsManager::execSubmitScore },
		{ "TrackEvent", &UStatisticsManager::execTrackEvent },
		{ "TrackPlayerAction", &UStatisticsManager::execTrackPlayerAction },
		{ "UnlockAchievement", &UStatisticsManager::execUnlockAchievement },
		{ "UpdateAchievementProgress", &UStatisticsManager::execUpdateAchievementProgress },
		{ "UpdateStatistic", &UStatisticsManager::execUpdateStatistic },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UStatisticsManager);
UClass* Z_Construct_UClass_UStatisticsManager_NoRegister()
{
	return UStatisticsManager::StaticClass();
}
struct Z_Construct_UClass_UStatisticsManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/StatisticsManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerStatistics_MetaData[] = {
		{ "Category", "Statistics" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PlayerAchievements_MetaData[] = {
		{ "Category", "Achievements" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Achievements storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Achievements storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LeaderboardEntries_MetaData[] = {
		{ "Category", "Leaderboards" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Leaderboards storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Leaderboards storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnStatisticUpdated_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAchievementUnlocked_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAchievementProgress_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLeaderboardUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventTimestamps_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal tracking\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EventCounts_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/StatisticsManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerStatistics_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PlayerStatistics_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerStatistics;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PlayerAchievements_ValueProp;
	static const UECodeGen_Private::FNamePropertyParams NewProp_PlayerAchievements_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PlayerAchievements;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LeaderboardEntries_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LeaderboardEntries;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnStatisticUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAchievementUnlocked;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAchievementProgress;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLeaderboardUpdated;
	static const UECodeGen_Private::FStructPropertyParams NewProp_EventTimestamps_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventTimestamps_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventTimestamps;
	static const UECodeGen_Private::FIntPropertyParams NewProp_EventCounts_ValueProp;
	static const UECodeGen_Private::FStrPropertyParams NewProp_EventCounts_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_EventCounts;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UStatisticsManager_ClearLeaderboard, "ClearLeaderboard" }, // 2991887046
		{ &Z_Construct_UFunction_UStatisticsManager_ExportStatistics, "ExportStatistics" }, // 448791333
		{ &Z_Construct_UFunction_UStatisticsManager_GetAchievementProgress, "GetAchievementProgress" }, // 1064158815
		{ &Z_Construct_UFunction_UStatisticsManager_GetAchievementsByType, "GetAchievementsByType" }, // 3371005583
		{ &Z_Construct_UFunction_UStatisticsManager_GetLeaderboard, "GetLeaderboard" }, // 82615244
		{ &Z_Construct_UFunction_UStatisticsManager_GetPlayerRank, "GetPlayerRank" }, // 425457801
		{ &Z_Construct_UFunction_UStatisticsManager_GetPlaytimeStatistics, "GetPlaytimeStatistics" }, // 4172945171
		{ &Z_Construct_UFunction_UStatisticsManager_GetStatistic, "GetStatistic" }, // 3309045242
		{ &Z_Construct_UFunction_UStatisticsManager_GetStatisticData, "GetStatisticData" }, // 2074394351
		{ &Z_Construct_UFunction_UStatisticsManager_GetStatisticsByCategory, "GetStatisticsByCategory" }, // 799181558
		{ &Z_Construct_UFunction_UStatisticsManager_GetTotalAchievementPoints, "GetTotalAchievementPoints" }, // 563731066
		{ &Z_Construct_UFunction_UStatisticsManager_GetUnlockedAchievements, "GetUnlockedAchievements" }, // 2115104027
		{ &Z_Construct_UFunction_UStatisticsManager_GetUsageStatistics, "GetUsageStatistics" }, // 3844461152
		{ &Z_Construct_UFunction_UStatisticsManager_ImportStatistics, "ImportStatistics" }, // 187619645
		{ &Z_Construct_UFunction_UStatisticsManager_IsAchievementUnlocked, "IsAchievementUnlocked" }, // 1252091645
		{ &Z_Construct_UFunction_UStatisticsManager_LoadStatistics, "LoadStatistics" }, // 1537328312
		{ &Z_Construct_UFunction_UStatisticsManager_OnNewPersonalBest, "OnNewPersonalBest" }, // 1796837444
		{ &Z_Construct_UFunction_UStatisticsManager_OnStatisticsLoaded, "OnStatisticsLoaded" }, // 4097075706
		{ &Z_Construct_UFunction_UStatisticsManager_RegisterAchievement, "RegisterAchievement" }, // 3532696854
		{ &Z_Construct_UFunction_UStatisticsManager_RegisterStatistic, "RegisterStatistic" }, // 2654020607
		{ &Z_Construct_UFunction_UStatisticsManager_ResetAllStatistics, "ResetAllStatistics" }, // 4108056377
		{ &Z_Construct_UFunction_UStatisticsManager_ResetStatistic, "ResetStatistic" }, // 1857058270
		{ &Z_Construct_UFunction_UStatisticsManager_SaveStatistics, "SaveStatistics" }, // 567630079
		{ &Z_Construct_UFunction_UStatisticsManager_SetStatistic, "SetStatistic" }, // 992625362
		{ &Z_Construct_UFunction_UStatisticsManager_SubmitScore, "SubmitScore" }, // 1069947449
		{ &Z_Construct_UFunction_UStatisticsManager_TrackEvent, "TrackEvent" }, // 3228709731
		{ &Z_Construct_UFunction_UStatisticsManager_TrackPlayerAction, "TrackPlayerAction" }, // 1127280136
		{ &Z_Construct_UFunction_UStatisticsManager_UnlockAchievement, "UnlockAchievement" }, // 1306069952
		{ &Z_Construct_UFunction_UStatisticsManager_UpdateAchievementProgress, "UpdateAchievementProgress" }, // 3322426942
		{ &Z_Construct_UFunction_UStatisticsManager_UpdateStatistic, "UpdateStatistic" }, // 779236619
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UStatisticsManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics_ValueProp = { "PlayerStatistics", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FPlayerStatistic, METADATA_PARAMS(0, nullptr) }; // 958180106
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics_Key_KeyProp = { "PlayerStatistics_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics = { "PlayerStatistics", nullptr, (EPropertyFlags)0x0010000001020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, PlayerStatistics), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerStatistics_MetaData), NewProp_PlayerStatistics_MetaData) }; // 958180106
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements_ValueProp = { "PlayerAchievements", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FAchievementData, METADATA_PARAMS(0, nullptr) }; // 3257305814
const UECodeGen_Private::FNamePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements_Key_KeyProp = { "PlayerAchievements_Key", nullptr, (EPropertyFlags)0x0000000000020001, UECodeGen_Private::EPropertyGenFlags::Name, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements = { "PlayerAchievements", nullptr, (EPropertyFlags)0x0010000001020015, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, PlayerAchievements), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PlayerAchievements_MetaData), NewProp_PlayerAchievements_MetaData) }; // 3257305814
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_LeaderboardEntries_Inner = { "LeaderboardEntries", nullptr, (EPropertyFlags)0x0000000000020000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLeaderboardEntry, METADATA_PARAMS(0, nullptr) }; // 2231317326
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_LeaderboardEntries = { "LeaderboardEntries", nullptr, (EPropertyFlags)0x0010000001020015, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, LeaderboardEntries), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LeaderboardEntries_MetaData), NewProp_LeaderboardEntries_MetaData) }; // 2231317326
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnStatisticUpdated = { "OnStatisticUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, OnStatisticUpdated), Z_Construct_UDelegateFunction_SLT_OnStatisticUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnStatisticUpdated_MetaData), NewProp_OnStatisticUpdated_MetaData) }; // 599233312
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnAchievementUnlocked = { "OnAchievementUnlocked", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, OnAchievementUnlocked), Z_Construct_UDelegateFunction_SLT_OnAchievementUnlocked__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAchievementUnlocked_MetaData), NewProp_OnAchievementUnlocked_MetaData) }; // 1221955062
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnAchievementProgress = { "OnAchievementProgress", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, OnAchievementProgress), Z_Construct_UDelegateFunction_SLT_OnAchievementProgress__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAchievementProgress_MetaData), NewProp_OnAchievementProgress_MetaData) }; // 691570293
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnLeaderboardUpdated = { "OnLeaderboardUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, OnLeaderboardUpdated), Z_Construct_UDelegateFunction_SLT_OnLeaderboardUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLeaderboardUpdated_MetaData), NewProp_OnLeaderboardUpdated_MetaData) }; // 2881139291
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps_ValueProp = { "EventTimestamps", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FDateTime, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps_Key_KeyProp = { "EventTimestamps_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps = { "EventTimestamps", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, EventTimestamps), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventTimestamps_MetaData), NewProp_EventTimestamps_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts_ValueProp = { "EventCounts", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts_Key_KeyProp = { "EventCounts_Key", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts = { "EventCounts", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UStatisticsManager, EventCounts), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EventCounts_MetaData), NewProp_EventCounts_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UStatisticsManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerStatistics,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_PlayerAchievements,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_LeaderboardEntries_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_LeaderboardEntries,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnStatisticUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnAchievementUnlocked,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnAchievementProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_OnLeaderboardUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventTimestamps,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UStatisticsManager_Statics::NewProp_EventCounts,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UStatisticsManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UStatisticsManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UStatisticsManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UStatisticsManager_Statics::ClassParams = {
	&UStatisticsManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UStatisticsManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UStatisticsManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UStatisticsManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UStatisticsManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UStatisticsManager()
{
	if (!Z_Registration_Info_UClass_UStatisticsManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UStatisticsManager.OuterSingleton, Z_Construct_UClass_UStatisticsManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UStatisticsManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UStatisticsManager>()
{
	return UStatisticsManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UStatisticsManager);
UStatisticsManager::~UStatisticsManager() {}
// End Class UStatisticsManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ EStatisticType_StaticEnum, TEXT("EStatisticType"), &Z_Registration_Info_UEnum_EStatisticType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 929910636U) },
		{ EAchievementType_StaticEnum, TEXT("EAchievementType"), &Z_Registration_Info_UEnum_EAchievementType, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 1025751466U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FPlayerStatistic::StaticStruct, Z_Construct_UScriptStruct_FPlayerStatistic_Statics::NewStructOps, TEXT("PlayerStatistic"), &Z_Registration_Info_UScriptStruct_PlayerStatistic, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPlayerStatistic), 958180106U) },
		{ FAchievementData::StaticStruct, Z_Construct_UScriptStruct_FAchievementData_Statics::NewStructOps, TEXT("AchievementData"), &Z_Registration_Info_UScriptStruct_AchievementData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAchievementData), 3257305814U) },
		{ FLeaderboardEntry::StaticStruct, Z_Construct_UScriptStruct_FLeaderboardEntry_Statics::NewStructOps, TEXT("LeaderboardEntry"), &Z_Registration_Info_UScriptStruct_LeaderboardEntry, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLeaderboardEntry), 2231317326U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UStatisticsManager, UStatisticsManager::StaticClass, TEXT("UStatisticsManager"), &Z_Registration_Info_UClass_UStatisticsManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UStatisticsManager), 402592285U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_4144516865(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_StatisticsManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
