// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Player/SLTPlayerCharacter.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FInventorySlot;
#ifdef SLT_SLTPlayerCharacter_generated_h
#error "SLTPlayerCharacter.generated.h already included, missing '#pragma once' in SLTPlayerCharacter.h"
#endif
#define SLT_SLTPlayerCharacter_generated_h

#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_82_DELEGATE \
static void FOnInventoryToggled_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryToggled, bool bIsOpen);


#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execIsInventoryOpen); \
	DECLARE_FUNCTION(execCloseInventory); \
	DECLARE_FUNCTION(execOpenInventory); \
	DECLARE_FUNCTION(execToggleInventory);


#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesASLTPlayerCharacter(); \
	friend struct Z_Construct_UClass_ASLTPlayerCharacter_Statics; \
public: \
	DECLARE_CLASS(ASLTPlayerCharacter, ACharacter, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(ASLTPlayerCharacter) \
	virtual UObject* _getUObject() const override { return const_cast<ASLTPlayerCharacter*>(this); }


#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ASLTPlayerCharacter(ASLTPlayerCharacter&&); \
	ASLTPlayerCharacter(const ASLTPlayerCharacter&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ASLTPlayerCharacter); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ASLTPlayerCharacter); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ASLTPlayerCharacter) \
	NO_API virtual ~ASLTPlayerCharacter();


#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_19_PROLOG
#define FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h_22_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class ASLTPlayerCharacter>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Player_SLTPlayerCharacter_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
