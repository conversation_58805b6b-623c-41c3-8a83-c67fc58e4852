#include "PuzzleComponent.h"
#include "../../InventorySystem/Components/InventoryGridComponent.h"
#include "GameFramework/Pawn.h"
#include "Engine/World.h"
#include "TimerManager.h"

UPuzzleComponent::UPuzzleComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	
	PuzzleType = EPuzzleType::Sequence;
	bRequireSequentialCompletion = true;
	bAllowReset = true;
	MaxAttempts = 0;
	ResetDelayAfterFailure = 2.0f;
	CurrentState = EPuzzleState::Inactive;
	CurrentStepIndex = 0;
	AttemptCount = 0;
	bIsSolved = false;
}

void UPuzzleComponent::BeginPlay()
{
	Super::BeginPlay();
	
	// Auto-activate if we have steps configured
	if (PuzzleSteps.Num() > 0)
	{
		ActivatePuzzle();
	}
}

void UPuzzleComponent::SetPuzzleState(EPuzzleState NewState)
{
	if (CurrentState == NewState)
	{
		return;
	}

	EPuzzleState OldState = CurrentState;
	CurrentState = NewState;
	OnPuzzleStateChanged.Broadcast(OldState, NewState);
}

void UPuzzleComponent::ActivatePuzzle()
{
	if (CurrentState == EPuzzleState::Solved || CurrentState == EPuzzleState::Locked)
	{
		return;
	}

	SetPuzzleState(EPuzzleState::Active);
	OnPuzzleActivated();
}

void UPuzzleComponent::DeactivatePuzzle()
{
	if (CurrentState == EPuzzleState::Solved)
	{
		return; // Don't deactivate solved puzzles
	}

	SetPuzzleState(EPuzzleState::Inactive);
	OnPuzzleDeactivated();
}

void UPuzzleComponent::ResetPuzzle(bool bForceReset)
{
	if (!bAllowReset && !bForceReset)
	{
		return;
	}

	// Clear timer if active
	if (ResetTimerHandle.IsValid())
	{
		GetWorld()->GetTimerManager().ClearTimer(ResetTimerHandle);
		ResetTimerHandle.Invalidate();
	}

	// Reset all steps
	for (FPuzzleStep& Step : PuzzleSteps)
	{
		Step.bIsCompleted = false;
	}

	CurrentStepIndex = 0;
	bIsSolved = false;

	if (bForceReset)
	{
		AttemptCount = 0;
	}

	SetPuzzleState(EPuzzleState::Active);
	OnPuzzleReset.Broadcast(bForceReset);
}

bool UPuzzleComponent::TrySolvePuzzle(FName SolutionCode, APawn* SolvingPawn)
{
	if (!CanAttemptPuzzle())
	{
		return false;
	}

	AttemptCount++;

	// For simple solution code puzzles
	if (PuzzleType == EPuzzleType::Combination && PuzzleSteps.Num() == 1)
	{
		FPuzzleStep& Step = PuzzleSteps[0];
		if (Step.StepValue == SolutionCode.ToString())
		{
			SolvePuzzle(SolvingPawn);
			return true;
		}
		else
		{
			FailPuzzle(SolvingPawn);
			return false;
		}
	}

	return false;
}

bool UPuzzleComponent::TryCompleteStep(int32 StepIndex, const FString& StepValue, APawn* InteractingPawn)
{
	if (!CanAttemptPuzzle() || StepIndex < 0 || StepIndex >= PuzzleSteps.Num())
	{
		return false;
	}

	FPuzzleStep& Step = PuzzleSteps[StepIndex];

	// Check if step is already completed
	if (Step.bIsCompleted)
	{
		return true;
	}

	// Check if we need to complete steps sequentially
	if (bRequireSequentialCompletion && StepIndex != CurrentStepIndex)
	{
		return false;
	}

	// Check step requirements
	if (!CheckStepRequirements(Step, InteractingPawn))
	{
		OnStepAttempted(StepIndex, StepValue, false);
		return false;
	}

	// Check if the step value matches
	bool bStepSuccess = (Step.StepValue == StepValue);
	
	OnStepAttempted(StepIndex, StepValue, bStepSuccess);

	if (bStepSuccess)
	{
		Step.bIsCompleted = true;
		OnPuzzleStepCompleted.Broadcast(StepIndex, Step);

		// Move to next step if sequential
		if (bRequireSequentialCompletion)
		{
			CurrentStepIndex++;
		}

		// Check if puzzle is complete
		bool bAllStepsCompleted = true;
		for (const FPuzzleStep& CheckStep : PuzzleSteps)
		{
			if (!CheckStep.bIsCompleted)
			{
				bAllStepsCompleted = false;
				break;
			}
		}

		if (bAllStepsCompleted)
		{
			SolvePuzzle(InteractingPawn);
		}
		else
		{
			SetPuzzleState(EPuzzleState::InProgress);
		}

		return true;
	}
	else
	{
		AttemptCount++;
		
		// Check if we've exceeded max attempts
		if (MaxAttempts > 0 && AttemptCount >= MaxAttempts)
		{
			FailPuzzle(InteractingPawn);
		}

		return false;
	}
}

bool UPuzzleComponent::TryCompleteCurrentStep(const FString& StepValue, APawn* InteractingPawn)
{
	return TryCompleteStep(CurrentStepIndex, StepValue, InteractingPawn);
}

void UPuzzleComponent::SolvePuzzle(APawn* SolvingPawn)
{
	bIsSolved = true;
	SetPuzzleState(EPuzzleState::Solved);
	OnPuzzleSolved.Broadcast(SolvingPawn);
}

void UPuzzleComponent::FailPuzzle(APawn* FailingPawn)
{
	SetPuzzleState(EPuzzleState::Failed);
	OnPuzzleFailed.Broadcast(FailingPawn);

	// Auto-reset after delay if allowed
	if (bAllowReset && ResetDelayAfterFailure > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(
			ResetTimerHandle,
			this,
			&UPuzzleComponent::OnFailureResetTimer,
			ResetDelayAfterFailure,
			false
		);
	}
}

void UPuzzleComponent::OnFailureResetTimer()
{
	ResetPuzzle(false);
}

bool UPuzzleComponent::CanAttemptPuzzle() const
{
	if (CurrentState == EPuzzleState::Solved || CurrentState == EPuzzleState::Locked)
	{
		return false;
	}

	if (MaxAttempts > 0 && AttemptCount >= MaxAttempts)
	{
		return false;
	}

	return true;
}

int32 UPuzzleComponent::GetRemainingAttempts() const
{
	if (MaxAttempts <= 0)
	{
		return -1; // Unlimited
	}

	return FMath::Max(0, MaxAttempts - AttemptCount);
}

float UPuzzleComponent::GetCompletionPercentage() const
{
	if (PuzzleSteps.Num() == 0)
	{
		return 0.0f;
	}

	int32 CompletedSteps = 0;
	for (const FPuzzleStep& Step : PuzzleSteps)
	{
		if (Step.bIsCompleted)
		{
			CompletedSteps++;
		}
	}

	return (float)CompletedSteps / (float)PuzzleSteps.Num();
}

FPuzzleStep UPuzzleComponent::GetCurrentStep() const
{
	if (CurrentStepIndex >= 0 && CurrentStepIndex < PuzzleSteps.Num())
	{
		return PuzzleSteps[CurrentStepIndex];
	}

	return FPuzzleStep();
}

TArray<FPuzzleStep> UPuzzleComponent::GetCompletedSteps() const
{
	TArray<FPuzzleStep> CompletedSteps;
	
	for (const FPuzzleStep& Step : PuzzleSteps)
	{
		if (Step.bIsCompleted)
		{
			CompletedSteps.Add(Step);
		}
	}

	return CompletedSteps;
}

bool UPuzzleComponent::IsStepCompleted(int32 StepIndex) const
{
	if (StepIndex >= 0 && StepIndex < PuzzleSteps.Num())
	{
		return PuzzleSteps[StepIndex].bIsCompleted;
	}

	return false;
}

bool UPuzzleComponent::CanCompleteStep(int32 StepIndex, APawn* InteractingPawn) const
{
	if (!CanAttemptPuzzle() || StepIndex < 0 || StepIndex >= PuzzleSteps.Num())
	{
		return false;
	}

	const FPuzzleStep& Step = PuzzleSteps[StepIndex];

	// Check if step is already completed
	if (Step.bIsCompleted)
	{
		return false;
	}

	// Check sequential requirement
	if (bRequireSequentialCompletion && StepIndex != CurrentStepIndex)
	{
		return false;
	}

	// Check step requirements
	return CheckStepRequirements(Step, InteractingPawn);
}

void UPuzzleComponent::SetStepCompleted(int32 StepIndex, bool bCompleted)
{
	if (StepIndex >= 0 && StepIndex < PuzzleSteps.Num())
	{
		PuzzleSteps[StepIndex].bIsCompleted = bCompleted;
	}
}

bool UPuzzleComponent::CheckStepRequirements(const FPuzzleStep& Step, APawn* InteractingPawn) const
{
	// Check item requirements
	if (Step.bRequiresItem && Step.RequiredItemID != NAME_None)
	{
		if (!InteractingPawn)
		{
			return false;
		}

		UInventoryGridComponent* InventoryComponent = InteractingPawn->FindComponentByClass<UInventoryGridComponent>();
		if (!InventoryComponent || !InventoryComponent->HasItem(Step.RequiredItemID))
		{
			return false;
		}
	}

	// Check gameplay tag requirements
	if (Step.RequiredTags.Num() > 0)
	{
		// TODO: Implement gameplay tag checking
		// This would require a gameplay tag component on the pawn
	}

	return true;
}
