#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "../../InventorySystem/Data/InventoryItemData.h"
#include "InventoryInterface.generated.h"

// Forward declarations
struct FInventorySlot;
struct FInventoryItemData;
enum class EItemType : uint8;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInventoryChanged, const FInventorySlot&, ChangedSlot, bool, bWasAdded);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInventoryOpened, bool, bIsOpen);

UINTERFACE(MinimalAPI, BlueprintType)
class UInventoryInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * Interface for inventory management functionality
 */
class SLT_API IInventoryInterface
{
	GENERATED_BODY()

public:
	// Add an item to the inventory
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool AddItem(const FInventoryItemData& ItemData, int32 Quantity = 1, FInventorySlot& OutSlot);
	virtual bool AddItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity, FInventorySlot& OutSlot) { return false; }

	// Remove an item from the inventory
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool RemoveItem(FName ItemID, int32 Quantity = 1);
	virtual bool RemoveItem_Implementation(FName ItemID, int32 Quantity) { return false; }

	// Remove item by slot ID
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool RemoveItemBySlotID(const FGuid& SlotID);
	virtual bool RemoveItemBySlotID_Implementation(const FGuid& SlotID) { return false; }

	// Check if inventory has a specific item
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool HasItem(FName ItemID) const;
	virtual bool HasItem_Implementation(FName ItemID) const { return false; }

	// Get quantity of a specific item
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	int32 GetItemQuantity(FName ItemID) const;
	virtual int32 GetItemQuantity_Implementation(FName ItemID) const { return 0; }

	// Get all items in inventory
	// UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	// TArray<FInventorySlot> GetAllItems() const;
	// virtual TArray<FInventorySlot> GetAllItems_Implementation() const { return TArray<FInventorySlot>(); }

	// Get items by type
	// UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	// TArray<FInventorySlot> GetItemsByType(EItemType ItemType) const;
	// virtual TArray<FInventorySlot> GetItemsByType_Implementation(EItemType ItemType) const { return TArray<FInventorySlot>(); }

	// Check if inventory is full
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool IsInventoryFull() const;
	virtual bool IsInventoryFull_Implementation() const { return false; }

	// Get available space for an item
	// UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	// bool CanFitItem(const FInventoryItemData& ItemData, int32 Quantity = 1) const;
	// virtual bool CanFitItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity) const { return false; }

	// Clear all items from inventory
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	void ClearInventory();
	virtual void ClearInventory_Implementation() {}

	// Get inventory capacity
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	int32 GetInventoryCapacity() const;
	virtual int32 GetInventoryCapacity_Implementation() const { return 0; }

	// Get current inventory weight
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	float GetCurrentWeight() const;
	virtual float GetCurrentWeight_Implementation() const { return 0.0f; }

	// Get maximum inventory weight
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	float GetMaxWeight() const;
	virtual float GetMaxWeight_Implementation() const { return 0.0f; }

	// Use an item from inventory
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool UseItem(FName ItemID, int32 Quantity = 1);
	virtual bool UseItem_Implementation(FName ItemID, int32 Quantity) { return false; }

	// Drop an item from inventory
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Inventory")
	bool DropItem(FName ItemID, int32 Quantity = 1, const FVector& DropLocation = FVector::ZeroVector);
	virtual bool DropItem_Implementation(FName ItemID, int32 Quantity, const FVector& DropLocation) { return false; }
};
