#include "InventoryGridComponent.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"

UInventoryGridComponent::UInventoryGridComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	
	// Default grid settings
	GridWidth = 8;
	GridHeight = 6;
	MaxWeight = 100.0f;
	
	CachedItemDatabase = nullptr;
}

void UInventoryGridComponent::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeGrid();
	LoadItemDatabase();
}

void UInventoryGridComponent::InitializeGrid()
{
	// Initialize the grid state array
	GridState.Empty();
	GridState.SetNum(GridHeight);
	
	for (int32 Y = 0; Y < GridHeight; Y++)
	{
		GridState[Y].SetNum(GridWidth);
		for (int32 X = 0; X < GridWidth; X++)
		{
			GridState[Y][X] = FGuid(); // Empty slot
		}
	}
}

void UInventoryGridComponent::LoadItemDatabase()
{
	if (ItemDatabase.IsValid())
	{
		CachedItemDatabase = ItemDatabase.LoadSynchronous();
	}
}

bool UInventoryGridComponent::GetItemDataByID(FName ItemID, FInventoryItemData& OutItemData) const
{
	if (!CachedItemDatabase)
	{
		return false;
	}

	FInventoryItemData* ItemData = CachedItemDatabase->FindRow<FInventoryItemData>(ItemID, TEXT("GetItemDataByID"));
	if (ItemData)
	{
		OutItemData = *ItemData;
		return true;
	}

	return false;
}

bool UInventoryGridComponent::IsValidGridPosition(int32 X, int32 Y) const
{
	return X >= 0 && X < GridWidth && Y >= 0 && Y < GridHeight;
}

void UInventoryGridComponent::GetItemDimensions(const FInventoryItemData& ItemData, bool bRotated, int32& OutWidth, int32& OutHeight) const
{
	if (bRotated && ItemData.bCanRotate)
	{
		OutWidth = ItemData.Height;
		OutHeight = ItemData.Width;
	}
	else
	{
		OutWidth = ItemData.Width;
		OutHeight = ItemData.Height;
	}
}

bool UInventoryGridComponent::CanPlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, bool bRotated) const
{
	int32 ItemWidth, ItemHeight;
	GetItemDimensions(ItemData, bRotated, ItemWidth, ItemHeight);

	// Check if the item fits within grid bounds
	if (X + ItemWidth > GridWidth || Y + ItemHeight > GridHeight)
	{
		return false;
	}

	// Check if the area is free
	return IsAreaFree(X, Y, ItemWidth, ItemHeight);
}

bool UInventoryGridComponent::IsAreaFree(int32 X, int32 Y, int32 Width, int32 Height, const FGuid& IgnoreSlotID) const
{
	for (int32 CheckY = Y; CheckY < Y + Height; CheckY++)
	{
		for (int32 CheckX = X; CheckX < X + Width; CheckX++)
		{
			if (!IsValidGridPosition(CheckX, CheckY))
			{
				return false;
			}

			const FGuid& SlotID = GridState[CheckY][CheckX];
			if (SlotID.IsValid() && SlotID != IgnoreSlotID)
			{
				return false;
			}
		}
	}
	return true;
}

void UInventoryGridComponent::OccupyGridArea(int32 X, int32 Y, int32 Width, int32 Height, const FGuid& SlotID)
{
	for (int32 OccupyY = Y; OccupyY < Y + Height; OccupyY++)
	{
		for (int32 OccupyX = X; OccupyX < X + Width; OccupyX++)
		{
			if (IsValidGridPosition(OccupyX, OccupyY))
			{
				GridState[OccupyY][OccupyX] = SlotID;
			}
		}
	}
}

void UInventoryGridComponent::FreeGridArea(int32 X, int32 Y, int32 Width, int32 Height)
{
	for (int32 FreeY = Y; FreeY < Y + Height; FreeY++)
	{
		for (int32 FreeX = X; FreeX < X + Width; FreeX++)
		{
			if (IsValidGridPosition(FreeX, FreeY))
			{
				GridState[FreeY][FreeX] = FGuid();
			}
		}
	}
}

bool UInventoryGridComponent::PlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, int32 Quantity, bool bRotated)
{
	if (!CanPlaceItemAt(X, Y, ItemData, bRotated))
	{
		return false;
	}

	// Create new inventory slot
	FInventorySlot NewSlot;
	NewSlot.ItemData = ItemData;
	NewSlot.Quantity = Quantity;
	NewSlot.bIsRotated = bRotated;
	NewSlot.GridX = X;
	NewSlot.GridY = Y;
	NewSlot.SlotID = FGuid::NewGuid();

	// Add to inventory
	InventorySlots.Add(NewSlot);

	// Update grid state
	int32 ItemWidth, ItemHeight;
	GetItemDimensions(ItemData, bRotated, ItemWidth, ItemHeight);
	OccupyGridArea(X, Y, ItemWidth, ItemHeight, NewSlot.SlotID);

	// Broadcast event
	OnInventoryItemAdded.Broadcast(NewSlot, true);
	OnInventoryGridChanged.Broadcast(this);

	return true;
}

FVector2D UInventoryGridComponent::FindBestFitPosition(const FInventoryItemData& ItemData, bool bAllowRotation) const
{
	// Try normal orientation first
	for (int32 Y = 0; Y < GridHeight; Y++)
	{
		for (int32 X = 0; X < GridWidth; X++)
		{
			if (CanPlaceItemAt(X, Y, ItemData, false))
			{
				return FVector2D(X, Y);
			}
		}
	}

	// Try rotated orientation if allowed
	if (bAllowRotation && ItemData.bCanRotate)
	{
		for (int32 Y = 0; Y < GridHeight; Y++)
		{
			for (int32 X = 0; X < GridWidth; X++)
			{
				if (CanPlaceItemAt(X, Y, ItemData, true))
				{
					return FVector2D(X, Y);
				}
			}
		}
	}

	return FVector2D(-1, -1); // No valid position found
}

FInventorySlot* UInventoryGridComponent::FindSlotByID(const FGuid& SlotID)
{
	for (FInventorySlot& Slot : InventorySlots)
	{
		if (Slot.SlotID == SlotID)
		{
			return &Slot;
		}
	}
	return nullptr;
}

FInventorySlot* UInventoryGridComponent::FindSlotAt(int32 X, int32 Y)
{
	if (!IsValidGridPosition(X, Y))
	{
		return nullptr;
	}

	const FGuid& SlotID = GridState[Y][X];
	if (SlotID.IsValid())
	{
		return FindSlotByID(SlotID);
	}

	return nullptr;
}

FInventorySlot* UInventoryGridComponent::FindSlotByItemID(FName ItemID)
{
	for (FInventorySlot& Slot : InventorySlots)
	{
		if (Slot.ItemData.ID == ItemID)
		{
			return &Slot;
		}
	}
	return nullptr;
}

bool UInventoryGridComponent::DoesItemFitInGrid(const FInventoryItemData& ItemData, bool bRotated) const
{
	int32 ItemWidth, ItemHeight;
	GetItemDimensions(ItemData, bRotated, ItemWidth, ItemHeight);

	return ItemWidth <= GridWidth && ItemHeight <= GridHeight;
}

// IInventoryInterface Implementation

bool UInventoryGridComponent::AddItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity, FInventorySlot& OutSlot)
{
	// Check if item can stack with existing items
	if (ItemData.bCanStack)
	{
		FInventorySlot* ExistingSlot = FindSlotByItemID(ItemData.ID);
		if (ExistingSlot && ExistingSlot->Quantity + Quantity <= ItemData.MaxStackSize)
		{
			ExistingSlot->Quantity += Quantity;
			OutSlot = *ExistingSlot;
			OnInventoryItemAdded.Broadcast(*ExistingSlot, true);
			OnInventoryGridChanged.Broadcast(this);
			return true;
		}
	}

	// Find best position for new item
	FVector2D BestPosition = FindBestFitPosition(ItemData, true);
	if (BestPosition.X < 0 || BestPosition.Y < 0)
	{
		// No space available
		OnInventoryItemAdded.Broadcast(FInventorySlot(), false);
		return false;
	}

	// Determine if we need to rotate the item
	bool bShouldRotate = false;
	if (!CanPlaceItemAt(BestPosition.X, BestPosition.Y, ItemData, false) && ItemData.bCanRotate)
	{
		bShouldRotate = true;
	}

	// Place the item
	bool bSuccess = PlaceItemAt(BestPosition.X, BestPosition.Y, ItemData, Quantity, bShouldRotate);
	if (bSuccess)
	{
		// Find the slot we just created
		FInventorySlot* NewSlot = FindSlotAt(BestPosition.X, BestPosition.Y);
		if (NewSlot)
		{
			OutSlot = *NewSlot;
		}
	}

	return bSuccess;
}

bool UInventoryGridComponent::RemoveItem_Implementation(FName ItemID, int32 Quantity)
{
	FInventorySlot* Slot = FindSlotByItemID(ItemID);
	if (!Slot)
	{
		return false;
	}

	if (Slot->Quantity <= Quantity)
	{
		// Remove entire slot
		return RemoveItemBySlotID_Implementation(Slot->SlotID);
	}
	else
	{
		// Reduce quantity
		Slot->Quantity -= Quantity;
		OnInventoryItemRemoved.Broadcast(*Slot, true);
		OnInventoryGridChanged.Broadcast(this);
		return true;
	}
}

bool UInventoryGridComponent::RemoveItemBySlotID_Implementation(const FGuid& SlotID)
{
	for (int32 i = 0; i < InventorySlots.Num(); i++)
	{
		if (InventorySlots[i].SlotID == SlotID)
		{
			FInventorySlot RemovedSlot = InventorySlots[i];

			// Free grid area
			int32 ItemWidth, ItemHeight;
			GetItemDimensions(RemovedSlot.ItemData, RemovedSlot.bIsRotated, ItemWidth, ItemHeight);
			FreeGridArea(RemovedSlot.GridX, RemovedSlot.GridY, ItemWidth, ItemHeight);

			// Remove from array
			InventorySlots.RemoveAt(i);

			// Broadcast event
			OnInventoryItemRemoved.Broadcast(RemovedSlot, true);
			OnInventoryGridChanged.Broadcast(this);

			return true;
		}
	}
	return false;
}

bool UInventoryGridComponent::HasItem_Implementation(FName ItemID) const
{
	for (const FInventorySlot& Slot : InventorySlots)
	{
		if (Slot.ItemData.ID == ItemID)
		{
			return true;
		}
	}
	return false;
}

int32 UInventoryGridComponent::GetItemQuantity_Implementation(FName ItemID) const
{
	int32 TotalQuantity = 0;
	for (const FInventorySlot& Slot : InventorySlots)
	{
		if (Slot.ItemData.ID == ItemID)
		{
			TotalQuantity += Slot.Quantity;
		}
	}
	return TotalQuantity;
}

TArray<FInventorySlot> UInventoryGridComponent::GetAllItems_Implementation() const
{
	return InventorySlots;
}

TArray<FInventorySlot> UInventoryGridComponent::GetItemsByType_Implementation(EItemType ItemType) const
{
	TArray<FInventorySlot> FilteredItems;
	for (const FInventorySlot& Slot : InventorySlots)
	{
		if (Slot.ItemData.ItemType == ItemType)
		{
			FilteredItems.Add(Slot);
		}
	}
	return FilteredItems;
}

bool UInventoryGridComponent::IsInventoryFull_Implementation() const
{
	// Check if there's any free space in the grid
	for (int32 Y = 0; Y < GridHeight; Y++)
	{
		for (int32 X = 0; X < GridWidth; X++)
		{
			if (!GridState[Y][X].IsValid())
			{
				return false; // Found at least one free slot
			}
		}
	}
	return true;
}

bool UInventoryGridComponent::CanFitItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity) const
{
	// Check if item can stack with existing items
	if (ItemData.bCanStack)
	{
		for (const FInventorySlot& Slot : InventorySlots)
		{
			if (Slot.ItemData.ID == ItemData.ID)
			{
				int32 AvailableSpace = ItemData.MaxStackSize - Slot.Quantity;
				if (AvailableSpace >= Quantity)
				{
					return true;
				}
				Quantity -= AvailableSpace;
				if (Quantity <= 0)
				{
					return true;
				}
			}
		}
	}

	// Check if we can fit new slots
	FVector2D BestPosition = FindBestFitPosition(ItemData, true);
	return BestPosition.X >= 0 && BestPosition.Y >= 0;
}

void UInventoryGridComponent::ClearInventory_Implementation()
{
	InventorySlots.Empty();
	InitializeGrid();
	OnInventoryGridChanged.Broadcast(this);
}

int32 UInventoryGridComponent::GetInventoryCapacity_Implementation() const
{
	return GridWidth * GridHeight;
}

float UInventoryGridComponent::GetCurrentWeight_Implementation() const
{
	float TotalWeight = 0.0f;
	for (const FInventorySlot& Slot : InventorySlots)
	{
		TotalWeight += Slot.ItemData.Weight * Slot.Quantity;
	}
	return TotalWeight;
}

float UInventoryGridComponent::GetMaxWeight_Implementation() const
{
	return MaxWeight;
}

bool UInventoryGridComponent::UseItem_Implementation(FName ItemID, int32 Quantity)
{
	// Basic implementation - just remove the item
	// This can be extended with item-specific use logic
	return RemoveItem_Implementation(ItemID, Quantity);
}

bool UInventoryGridComponent::DropItem_Implementation(FName ItemID, int32 Quantity, const FVector& DropLocation)
{
	// Get item data
	FInventoryItemData ItemData;
	if (!GetItemDataByID(ItemID, ItemData))
	{
		return false;
	}

	// Check if item can be dropped
	if (!ItemData.bCanBeDropped)
	{
		return false;
	}

	// Remove from inventory
	if (!RemoveItem_Implementation(ItemID, Quantity))
	{
		return false;
	}

	// TODO: Spawn item actor in world at DropLocation
	// This would require access to the world and item actor classes

	return true;
}

// Additional grid management functions

bool UInventoryGridComponent::MoveItem(const FGuid& SlotID, int32 NewX, int32 NewY, bool bNewRotation)
{
	FInventorySlot* Slot = FindSlotByID(SlotID);
	if (!Slot)
	{
		return false;
	}

	// Store old position and dimensions
	int32 OldX = Slot->GridX;
	int32 OldY = Slot->GridY;
	bool OldRotation = Slot->bIsRotated;
	int32 OldWidth, OldHeight;
	GetItemDimensions(Slot->ItemData, OldRotation, OldWidth, OldHeight);

	// Check if new position is valid
	if (!CanPlaceItemAt(NewX, NewY, Slot->ItemData, bNewRotation))
	{
		// Check if we can place it ignoring the current item's position
		if (!IsAreaFree(NewX, NewY,
			bNewRotation && Slot->ItemData.bCanRotate ? Slot->ItemData.Height : Slot->ItemData.Width,
			bNewRotation && Slot->ItemData.bCanRotate ? Slot->ItemData.Width : Slot->ItemData.Height,
			SlotID))
		{
			return false;
		}
	}

	// Free old position
	FreeGridArea(OldX, OldY, OldWidth, OldHeight);

	// Update slot data
	Slot->GridX = NewX;
	Slot->GridY = NewY;
	Slot->bIsRotated = bNewRotation;

	// Occupy new position
	int32 NewWidth, NewHeight;
	GetItemDimensions(Slot->ItemData, bNewRotation, NewWidth, NewHeight);
	OccupyGridArea(NewX, NewY, NewWidth, NewHeight, SlotID);

	// Broadcast event
	OnInventoryItemMoved.Broadcast(*Slot, NewX, NewY);
	OnInventoryGridChanged.Broadcast(this);

	return true;
}

bool UInventoryGridComponent::RotateItem(const FGuid& SlotID)
{
	FInventorySlot* Slot = FindSlotByID(SlotID);
	if (!Slot || !Slot->ItemData.bCanRotate)
	{
		return false;
	}

	return MoveItem(SlotID, Slot->GridX, Slot->GridY, !Slot->bIsRotated);
}

TArray<FInventorySlot*> UInventoryGridComponent::GetSlotsInArea(int32 X, int32 Y, int32 Width, int32 Height) const
{
	TArray<FInventorySlot*> SlotsInArea;
	TSet<FGuid> ProcessedSlots;

	for (int32 CheckY = Y; CheckY < Y + Height; CheckY++)
	{
		for (int32 CheckX = X; CheckX < X + Width; CheckX++)
		{
			if (IsValidGridPosition(CheckX, CheckY))
			{
				const FGuid& SlotID = GridState[CheckY][CheckX];
				if (SlotID.IsValid() && !ProcessedSlots.Contains(SlotID))
				{
					FInventorySlot* Slot = const_cast<UInventoryGridComponent*>(this)->FindSlotByID(SlotID);
					if (Slot)
					{
						SlotsInArea.Add(Slot);
						ProcessedSlots.Add(SlotID);
					}
				}
			}
		}
	}

	return SlotsInArea;
}
