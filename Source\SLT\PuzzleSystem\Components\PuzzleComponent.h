#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "GameplayTagContainer.h"
#include "PuzzleComponent.generated.h"

class APawn;

UENUM(BlueprintType)
enum class EPuzzleType : uint8
{
	None				UMETA(DisplayName = "None"),
	Sequence			UMETA(DisplayName = "Sequence"),
	Combination			UMETA(DisplayName = "Combination"),
	Pattern				UMETA(DisplayName = "Pattern"),
	ItemPlacement		UMETA(DisplayName = "Item Placement"),
	Alignment			UMETA(DisplayName = "Alignment"),
	Custom				UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EPuzzleState : uint8
{
	Inactive			UMETA(DisplayName = "Inactive"),
	Active				UMETA(DisplayName = "Active"),
	InProgress			UMETA(DisplayName = "In Progress"),
	Solved				UMETA(DisplayName = "Solved"),
	Failed				UMETA(DisplayName = "Failed"),
	Locked				UMETA(DisplayName = "Locked")
};

USTRUCT(BlueprintType)
struct FPuzzleStep
{
	GENERATED_BODY()

	FPuzzleStep()
	{
		StepID = NAME_None;
		StepValue = TEXT("");
		bIsCompleted = false;
		RequiredItemID = NAME_None;
		bRequiresItem = false;
	}

	// Unique identifier for this step
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	FName StepID;

	// The value or action for this step
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	FString StepValue;

	// Whether this step has been completed
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Step")
	bool bIsCompleted;

	// Required item for this step (optional)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	bool bRequiresItem;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements", meta = (EditCondition = "bRequiresItem"))
	FName RequiredItemID;

	// Required gameplay tags
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	// Custom step data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomData;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPuzzleStateChanged, EPuzzleState, OldState, EPuzzleState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPuzzleSolved, APawn*, SolvingPawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPuzzleFailed, APawn*, FailingPawn);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPuzzleStepCompleted, int32, StepIndex, const FPuzzleStep&, CompletedStep);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPuzzleReset, bool, bWasForced);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UPuzzleComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UPuzzleComponent();

protected:
	virtual void BeginPlay() override;

public:
	// Puzzle configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	EPuzzleType PuzzleType = EPuzzleType::Sequence;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	FText PuzzleName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	FText PuzzleDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	TArray<FPuzzleStep> PuzzleSteps;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	bool bRequireSequentialCompletion = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	bool bAllowReset = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	int32 MaxAttempts = 0; // 0 = unlimited

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Puzzle Settings")
	float ResetDelayAfterFailure = 2.0f;

	// State tracking
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	EPuzzleState CurrentState = EPuzzleState::Inactive;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	int32 CurrentStepIndex = 0;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	int32 AttemptCount = 0;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "State")
	bool bIsSolved = false;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleStateChanged OnPuzzleStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleSolved OnPuzzleSolved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleFailed OnPuzzleFailed;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleStepCompleted OnPuzzleStepCompleted;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleReset OnPuzzleReset;

	// Main puzzle functions
	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool TrySolvePuzzle(FName SolutionCode, APawn* SolvingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool TryCompleteStep(int32 StepIndex, const FString& StepValue, APawn* InteractingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool TryCompleteCurrentStep(const FString& StepValue, APawn* InteractingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void ActivatePuzzle();

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void DeactivatePuzzle();

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void ResetPuzzle(bool bForceReset = false);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void SolvePuzzle(APawn* SolvingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void FailPuzzle(APawn* FailingPawn = nullptr);

	// State queries
	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	bool IsPuzzleSolved() const { return bIsSolved; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	bool IsPuzzleActive() const { return CurrentState == EPuzzleState::Active || CurrentState == EPuzzleState::InProgress; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	bool CanAttemptPuzzle() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	int32 GetRemainingAttempts() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	float GetCompletionPercentage() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	FPuzzleStep GetCurrentStep() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Puzzle")
	TArray<FPuzzleStep> GetCompletedSteps() const;

	// Step management
	UFUNCTION(BlueprintCallable, Category = "Steps")
	bool IsStepCompleted(int32 StepIndex) const;

	UFUNCTION(BlueprintCallable, Category = "Steps")
	bool CanCompleteStep(int32 StepIndex, APawn* InteractingPawn = nullptr) const;

	UFUNCTION(BlueprintCallable, Category = "Steps")
	void SetStepCompleted(int32 StepIndex, bool bCompleted = true);

protected:
	// Internal functions
	void SetPuzzleState(EPuzzleState NewState);
	bool CheckStepRequirements(const FPuzzleStep& Step, APawn* InteractingPawn) const;
	void OnFailureResetTimer();

	// Timer handle for reset delay
	FTimerHandle ResetTimerHandle;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPuzzleActivated();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPuzzleDeactivated();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStepAttempted(int32 StepIndex, const FString& AttemptedValue, bool bSuccess);
};
