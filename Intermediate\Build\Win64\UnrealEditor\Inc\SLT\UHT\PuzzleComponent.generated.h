// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "PuzzleSystem/Components/PuzzleComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class APawn;
enum class EPuzzleState : uint8;
struct FPuzzleStep;
#ifdef SLT_PuzzleComponent_generated_h
#error "PuzzleComponent.generated.h already included, missing '#pragma once' in PuzzleComponent.h"
#endif
#define SLT_PuzzleComponent_generated_h

#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_36_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FPuzzleStep_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FPuzzleStep>();

#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_75_DELEGATE \
SLT_API void FOnPuzzleStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleStateChanged, EPuzzleState OldState, EPuzzleState NewState);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_76_DELEGATE \
SLT_API void FOnPuzzleSolved_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleSolved, APawn* SolvingPawn);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_77_DELEGATE \
SLT_API void FOnPuzzleFailed_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleFailed, APawn* FailingPawn);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_78_DELEGATE \
SLT_API void FOnPuzzleStepCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleStepCompleted, int32 StepIndex, FPuzzleStep const& CompletedStep);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_79_DELEGATE \
SLT_API void FOnPuzzleReset_DelegateWrapper(const FMulticastScriptDelegate& OnPuzzleReset, bool bWasForced);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetStepCompleted); \
	DECLARE_FUNCTION(execCanCompleteStep); \
	DECLARE_FUNCTION(execIsStepCompleted); \
	DECLARE_FUNCTION(execGetCompletedSteps); \
	DECLARE_FUNCTION(execGetCurrentStep); \
	DECLARE_FUNCTION(execGetCompletionPercentage); \
	DECLARE_FUNCTION(execGetRemainingAttempts); \
	DECLARE_FUNCTION(execCanAttemptPuzzle); \
	DECLARE_FUNCTION(execIsPuzzleActive); \
	DECLARE_FUNCTION(execIsPuzzleSolved); \
	DECLARE_FUNCTION(execFailPuzzle); \
	DECLARE_FUNCTION(execSolvePuzzle); \
	DECLARE_FUNCTION(execResetPuzzle); \
	DECLARE_FUNCTION(execDeactivatePuzzle); \
	DECLARE_FUNCTION(execActivatePuzzle); \
	DECLARE_FUNCTION(execTryCompleteCurrentStep); \
	DECLARE_FUNCTION(execTryCompleteStep); \
	DECLARE_FUNCTION(execTrySolvePuzzle);


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUPuzzleComponent(); \
	friend struct Z_Construct_UClass_UPuzzleComponent_Statics; \
public: \
	DECLARE_CLASS(UPuzzleComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UPuzzleComponent)


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UPuzzleComponent(UPuzzleComponent&&); \
	UPuzzleComponent(const UPuzzleComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UPuzzleComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UPuzzleComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UPuzzleComponent) \
	NO_API virtual ~UPuzzleComponent();


#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_81_PROLOG
#define FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h_84_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UPuzzleComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_PuzzleSystem_Components_PuzzleComponent_h


#define FOREACH_ENUM_EPUZZLETYPE(op) \
	op(EPuzzleType::None) \
	op(EPuzzleType::Sequence) \
	op(EPuzzleType::Combination) \
	op(EPuzzleType::Pattern) \
	op(EPuzzleType::ItemPlacement) \
	op(EPuzzleType::Alignment) \
	op(EPuzzleType::Custom) 

enum class EPuzzleType : uint8;
template<> struct TIsUEnumClass<EPuzzleType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EPuzzleType>();

#define FOREACH_ENUM_EPUZZLESTATE(op) \
	op(EPuzzleState::Inactive) \
	op(EPuzzleState::Active) \
	op(EPuzzleState::InProgress) \
	op(EPuzzleState::Solved) \
	op(EPuzzleState::Failed) \
	op(EPuzzleState::Locked) 

enum class EPuzzleState : uint8;
template<> struct TIsUEnumClass<EPuzzleState> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EPuzzleState>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
