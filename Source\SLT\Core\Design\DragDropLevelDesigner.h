#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "DragDropLevelDesigner.generated.h"

class UStaticMeshComponent;
class UBoxComponent;
class UWidgetComponent;

UENUM(BlueprintType)
enum class EDragDropCategory : uint8
{
	Environment		UMETA(DisplayName = "Environment"),
	Interactive		UMETA(DisplayName = "Interactive"),
	Enemies			UMETA(DisplayName = "Enemies"),
	Items			UMETA(DisplayName = "Items"),
	Puzzles			UMETA(DisplayName = "Puzzles"),
	Triggers		UMETA(DisplayName = "Triggers"),
	Spawners		UMETA(DisplayName = "Spawners"),
	Decorative		UMETA(DisplayName = "Decorative"),
	Lighting		UMETA(DisplayName = "Lighting"),
	Audio			UMETA(DisplayName = "Audio"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class ESnapMode : uint8
{
	None			UMETA(DisplayName = "None"),
	Grid			UMETA(DisplayName = "Grid"),
	Surface			UMETA(DisplayName = "Surface"),
	Object			UMETA(DisplayName = "Object"),
	Smart			UMETA(DisplayName = "Smart")
};

UENUM(BlueprintType)
enum class EPlacementMode : uint8
{
	Single			UMETA(DisplayName = "Single"),
	Paint			UMETA(DisplayName = "Paint"),
	Line			UMETA(DisplayName = "Line"),
	Circle			UMETA(DisplayName = "Circle"),
	Rectangle		UMETA(DisplayName = "Rectangle"),
	Random			UMETA(DisplayName = "Random")
};

USTRUCT(BlueprintType)
struct FDragDropAsset : public FTableRowBase
{
	GENERATED_BODY()

	FDragDropAsset()
	{
		AssetID = NAME_None;
		AssetName = FText::GetEmpty();
		Category = EDragDropCategory::Environment;
		bIsBuiltIn = false;
		bRequiresSetup = false;
		PlacementCost = 0;
		MaxInstances = -1;
		PreviewMesh = nullptr;
		ActorClass = nullptr;
	}

	// Asset identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
	FName AssetID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
	FText AssetName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
	FText AssetDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset")
	EDragDropCategory Category;

	// Asset properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsBuiltIn;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bRequiresSetup;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	int32 PlacementCost;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	int32 MaxInstances; // -1 for unlimited

	// Visual assets
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UStaticMesh> PreviewMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UTexture2D> PreviewIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FLinearColor PreviewColor = FLinearColor::White;

	// Spawning data
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	TSubclassOf<AActor> ActorClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	FVector DefaultScale = FVector::OneVector;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	bool bRandomizeRotation = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawning")
	bool bRandomizeScale = false;

	// Placement rules
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	ESnapMode PreferredSnapMode = ESnapMode::Grid;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float MinDistanceFromOthers = 0.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	TArray<EDragDropCategory> ConflictingCategories;

	// Tags and metadata
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer AssetTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	TArray<FString> SearchKeywords;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;
};

USTRUCT(BlueprintType)
struct FPlacementSettings
{
	GENERATED_BODY()

	FPlacementSettings()
	{
		PlacementMode = EPlacementMode::Single;
		SnapMode = ESnapMode::Grid;
		GridSize = 100.0f;
		PaintRadius = 500.0f;
		PaintDensity = 0.5f;
		bAlignToSurface = true;
		bRandomizeRotation = false;
		bRandomizeScale = false;
		ScaleVariation = 0.1f;
		RotationVariation = 15.0f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	EPlacementMode PlacementMode;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	ESnapMode SnapMode;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float GridSize;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float PaintRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float PaintDensity;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	bool bAlignToSurface;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	bool bRandomizeRotation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	bool bRandomizeScale;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float ScaleVariation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Placement")
	float RotationVariation;
};

USTRUCT(BlueprintType)
struct FLevelDesignStats
{
	GENERATED_BODY()

	FLevelDesignStats()
	{
		TotalActorsPlaced = 0;
		ActorsByCategory = TMap<EDragDropCategory, int32>();
		DesignTime = 0.0f;
		LastModified = FDateTime::Now();
	}

	UPROPERTY(BlueprintReadOnly, Category = "Stats")
	int32 TotalActorsPlaced;

	UPROPERTY(BlueprintReadOnly, Category = "Stats")
	TMap<EDragDropCategory, int32> ActorsByCategory;

	UPROPERTY(BlueprintReadOnly, Category = "Stats")
	float DesignTime;

	UPROPERTY(BlueprintReadOnly, Category = "Stats")
	FDateTime LastModified;

	UPROPERTY(BlueprintReadOnly, Category = "Stats")
	TArray<FString> RecentActions;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnActorPlaced, AActor*, PlacedActor, FName, AssetID, const FVector&, Location);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnActorRemoved, AActor*, RemovedActor, FName, AssetID);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSelectionChanged, const TArray<AActor*>&, SelectedActors, bool, bMultiSelect);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPlacementModeChanged, EPlacementMode, OldMode, EPlacementMode, NewMode);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UDragDropLevelDesigner : public UActorComponent
{
	GENERATED_BODY()

public:
	UDragDropLevelDesigner();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Asset database
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Assets")
	TSoftObjectPtr<UDataTable> AssetDatabase;

	// Current settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	FPlacementSettings PlacementSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bDesignModeActive = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bShowPreview = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bShowGrid = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bEnableUndo = true;

	// Current state
	UPROPERTY(BlueprintReadOnly, Category = "State")
	FName CurrentAssetID = NAME_None;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	TArray<AActor*> SelectedActors;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	FLevelDesignStats DesignStats;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnActorPlaced OnActorPlaced;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnActorRemoved OnActorRemoved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnSelectionChanged OnSelectionChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPlacementModeChanged OnPlacementModeChanged;

	// Main design functions
	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void SetDesignModeActive(bool bActive);

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void SelectAssetForPlacement(FName AssetID);

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	AActor* PlaceActorAtLocation(const FVector& Location, const FRotator& Rotation = FRotator::ZeroRotator);

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void RemoveSelectedActors();

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void DuplicateSelectedActors();

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void GroupSelectedActors();

	UFUNCTION(BlueprintCallable, Category = "Level Design")
	void UngroupSelectedActors();

	// Selection functions
	UFUNCTION(BlueprintCallable, Category = "Selection")
	void SelectActor(AActor* Actor, bool bAddToSelection = false);

	UFUNCTION(BlueprintCallable, Category = "Selection")
	void SelectActorsInArea(const FVector& Center, float Radius);

	UFUNCTION(BlueprintCallable, Category = "Selection")
	void SelectActorsByCategory(EDragDropCategory Category);

	UFUNCTION(BlueprintCallable, Category = "Selection")
	void ClearSelection();

	UFUNCTION(BlueprintCallable, Category = "Selection")
	void InvertSelection();

	// Placement mode functions
	UFUNCTION(BlueprintCallable, Category = "Placement")
	void SetPlacementMode(EPlacementMode NewMode);

	UFUNCTION(BlueprintCallable, Category = "Placement")
	void SetSnapMode(ESnapMode NewMode);

	UFUNCTION(BlueprintCallable, Category = "Placement")
	FVector SnapLocationToGrid(const FVector& Location) const;

	UFUNCTION(BlueprintCallable, Category = "Placement")
	FVector SnapLocationToSurface(const FVector& Location) const;

	// Asset functions
	UFUNCTION(BlueprintCallable, Category = "Assets")
	TArray<FDragDropAsset> GetAssetsByCategory(EDragDropCategory Category) const;

	UFUNCTION(BlueprintCallable, Category = "Assets")
	FDragDropAsset GetAssetData(FName AssetID) const;

	UFUNCTION(BlueprintCallable, Category = "Assets")
	TArray<FDragDropAsset> SearchAssets(const FString& SearchTerm) const;

	UFUNCTION(BlueprintCallable, Category = "Assets")
	bool CanPlaceAsset(FName AssetID, const FVector& Location) const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SaveLevelLayout(const FString& LayoutName);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool LoadLevelLayout(const FString& LayoutName);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ClearLevel();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void OptimizeLevel();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	FLevelDesignStats GetDesignStatistics() const;

	// Undo/Redo functions
	UFUNCTION(BlueprintCallable, Category = "Undo")
	void Undo();

	UFUNCTION(BlueprintCallable, Category = "Undo")
	void Redo();

	UFUNCTION(BlueprintCallable, Category = "Undo")
	bool CanUndo() const;

	UFUNCTION(BlueprintCallable, Category = "Undo")
	bool CanRedo() const;

protected:
	// Internal state
	UPROPERTY()
	UDataTable* CachedAssetDatabase;

	UPROPERTY()
	TArray<AActor*> PlacedActors;

	UPROPERTY()
	AActor* PreviewActor;

	// Undo/Redo system
	UPROPERTY()
	TArray<FString> UndoStack;

	UPROPERTY()
	TArray<FString> RedoStack;

	UPROPERTY()
	int32 MaxUndoSteps = 50;

	// Internal functions
	void LoadAssetDatabase();
	AActor* CreatePreviewActor(FName AssetID);
	void UpdatePreviewActor(const FVector& Location, const FRotator& Rotation);
	void DestroyPreviewActor();
	bool ValidatePlacement(FName AssetID, const FVector& Location) const;
	void RecordUndoAction(const FString& Action);
	void UpdateDesignStats();
	void DrawDebugGrid();
	void UpdatePreviewActorLocation();

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnDesignModeToggled(bool bInIsActive);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnAssetSelected(FName AssetID, const FDragDropAsset& AssetData);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnLevelLayoutChanged();
};
