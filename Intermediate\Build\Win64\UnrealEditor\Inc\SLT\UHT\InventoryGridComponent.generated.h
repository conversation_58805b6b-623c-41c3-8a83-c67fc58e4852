// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InventorySystem/Components/InventoryGridComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UInventoryGridComponent;
struct FGuid;
struct FInventoryItemData;
struct FInventorySlot;
#ifdef SLT_InventoryGridComponent_generated_h
#error "InventoryGridComponent.generated.h already included, missing '#pragma once' in InventoryGridComponent.h"
#endif
#define SLT_InventoryGridComponent_generated_h

#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_10_DELEGATE \
SLT_API void FOnInventoryItemAdded_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemAdded, FInventorySlot const& AddedSlot, bool bSuccess);


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_11_DELEGATE \
SLT_API void FOnInventoryItemRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemRemoved, FInventorySlot const& RemovedSlot, bool bSuccess);


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_12_DELEGATE \
SLT_API void FOnInventoryItemMoved_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryItemMoved, FInventorySlot const& MovedSlot, int32 NewX, int32 NewY);


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_13_DELEGATE \
SLT_API void FOnInventoryGridChanged_DelegateWrapper(const FMulticastScriptDelegate& OnInventoryGridChanged, UInventoryGridComponent* InventoryComponent);


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execDoesItemFitInGrid); \
	DECLARE_FUNCTION(execGetItemDimensions); \
	DECLARE_FUNCTION(execLoadItemDatabase); \
	DECLARE_FUNCTION(execGetItemDataByID); \
	DECLARE_FUNCTION(execFindBestFitPosition); \
	DECLARE_FUNCTION(execIsValidGridPosition); \
	DECLARE_FUNCTION(execGetSlotsInArea); \
	DECLARE_FUNCTION(execFindSlotAt); \
	DECLARE_FUNCTION(execFindSlotByID); \
	DECLARE_FUNCTION(execRotateItem); \
	DECLARE_FUNCTION(execMoveItem); \
	DECLARE_FUNCTION(execPlaceItemAt); \
	DECLARE_FUNCTION(execCanPlaceItemAt);


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUInventoryGridComponent(); \
	friend struct Z_Construct_UClass_UInventoryGridComponent_Statics; \
public: \
	DECLARE_CLASS(UInventoryGridComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UInventoryGridComponent) \
	virtual UObject* _getUObject() const override { return const_cast<UInventoryGridComponent*>(this); }


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UInventoryGridComponent(UInventoryGridComponent&&); \
	UInventoryGridComponent(const UInventoryGridComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UInventoryGridComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UInventoryGridComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UInventoryGridComponent) \
	NO_API virtual ~UInventoryGridComponent();


#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_15_PROLOG
#define FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h_18_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UInventoryGridComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_InventorySystem_Components_InventoryGridComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
