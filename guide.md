# Resident Evil 4-style Inventory System in Unreal Engine (C++)

This guide covers a full-featured, modular, and designer-friendly inventory and gameplay system inspired by **Resident Evil 4**, using **Unreal Engine 5**, **C++**, **Enhanced Input**, **Common UI**, and more.

---

## 🧱 Project Setup

### 1. Create a New Project
- Use the **Third Person C++ template**.
- Enable plugins: `Common UI`, `Enhanced Input`, `Gameplay Tags`, `UMG`, `Gameplay Ability System`, and `Developer Tools`.

### 2. Folder Structure
```
Source/
├── Core/
│   ├── Player/
│   ├── Enemies/
│   ├── Interfaces/
│   └── Systems/
├── InventorySystem/
│   ├── Data/
│   ├── Components/
│   ├── UI/
│   └── Actors/
├── PuzzleSystem/
│   ├── Components/
│   ├── Actors/
│   └── Interfaces/
```

---

## 👤 Base Player Controller & Character

### `AMyPlayerCharacter`
```cpp
class AMyPlayerCharacter : public ACharacter {
    GENERATED_BODY()

public:
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
    class UCameraComponent* CameraComponent;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
    class USpringArmComponent* SpringArm;

    UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
    class UInventoryGridComponent* InventoryComponent;

    virtual void SetupPlayerInputComponent(UInputComponent* PlayerInputComponent) override;
};
```

### Controls
- WASD: Move
- Mouse: Look
- Left Click: Fire
- Right Click: Aim
- Tab: Open Inventory
- E: Interact
- R: Rotate Item

---

## 📦 Inventory System (Attaché Case)

### `FInventoryItemData.h`
```cpp
USTRUCT(BlueprintType)
struct FInventoryItemData {
    GENERATED_BODY()
    FName ID;
    FText Name;
    UTexture2D* Icon;
    int32 Width;
    int32 Height;
    bool bCanRotate;
    UStaticMesh* PickupMesh;
    TSubclassOf<AActor> ItemClass;
};
```

### Grid Management - `UInventoryGridComponent`
```cpp
bool CanPlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, bool bRotated);
bool PlaceItemAt(int32 X, int32 Y, const FInventoryItemData& ItemData, bool bRotated);
void RemoveItem(FName ID);
```

---

## 🧩 Puzzle System

### `UPuzzleComponent`
```cpp
UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class UPuzzleComponent : public UActorComponent {
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable)
    bool TrySolvePuzzle(FName SolutionCode);

    UPROPERTY(EditAnywhere)
    TArray<FName> CorrectSequence;
};
```

### `APuzzleActor`
```cpp
UCLASS()
class APuzzleActor : public AActor {
    GENERATED_BODY()

public:
    UPROPERTY(VisibleAnywhere)
    UPuzzleComponent* PuzzleComponent;
};
```

### Puzzle Types
- Combination Locks
- Pressure Plates
- Object Alignment (e.g., rotate statues)

---

## 🎮 Gameplay Loop

### Loop Breakdown
1. **Explore**: Move through the level, encounter enemies and puzzles.
2. **Combat**: Fight using third-person aiming, ammo management, and strategic positioning.
3. **Loot**: Collect items and weapons, place them in your attaché case.
4. **Manage Inventory**: Rotate, rearrange, and discard items to optimize space.
5. **Interact**: Solve puzzles using objects from your inventory or environmental cues.
6. **Progress**: Unlock doors, access new areas, continue exploration.

---

## 😈 Enemy System

### `AEnemyCharacter`
```cpp
class AEnemyCharacter : public ACharacter {
    GENERATED_BODY()

public:
    UFUNCTION(BlueprintCallable)
    void TakeDamage(float Amount);

    UFUNCTION(BlueprintCallable)
    void Patrol();

    UFUNCTION(BlueprintCallable)
    void ChasePlayer();
};
```

### Behavior
- AIController with Behavior Tree
- Blackboard: `PlayerLocation`, `HasLOS`, `IsAlerted`
- States: Idle → Patrol → Alert → Attack → Dead

---

## 🖱️ Enhanced Input Setup

### Input Actions
- `IA_Move`, `IA_Look`, `IA_Aim`, `IA_Fire`, `IA_Interact`, `IA_OpenInventory`, `IA_RotateItem`

### Input Mapping Context
Assign actions to input keys and bind them in `SetupPlayerInputComponent()`

---

## 🎨 Common UI - Inventory

### Widgets
- `WBP_InventoryRoot`: Fullscreen container
- `WBP_Grid`: Grid widget
- `WBP_ItemSlot`: Individual grid item (supports rotation/drag)
- `WBP_Tooltip`: Context display for selected items

---

## 💾 Save/Load System

### `UInventorySaveData`
```cpp
USTRUCT(BlueprintType)
struct FInventorySlotSaveData {
    GENERATED_BODY()
    FName ItemID;
    int32 X;
    int32 Y;
    bool bRotated;
};

UCLASS()
class UInventorySaveData : public USaveGame {
    GENERATED_BODY()
    TArray<FInventorySlotSaveData> SavedItems;
};
```

### Puzzle and Player State
- Store puzzle progress as bools or enums
- Save equipped items and ammo count

---

## 🛠 Designer Features

- Place `AItemActor`, `AEnemyCharacter`, and `APuzzleActor` directly into the world.
- Inventory component can be added to any pawn or actor.
- Puzzle logic exposed via Blueprint Interfaces.
- Common UI provides modular widget creation.
- Enhanced Input supports remappable controls.
- Full gameplay loop editable in editor.

---

## 🧪 Tips

- Use Gameplay Tags for categorizing items and enemy behaviors.
- Add audio/visual feedback for grid interactions.
- Make use of Construction Scripts for snapping puzzle elements.
- Organize grid data in Blueprints via preview grid overlays.
- Let designers trigger game state events via `Level Blueprint` + `Interfaces`.

---

Let me know if you want example levels, behavior trees, or UMG visual blueprints next.
