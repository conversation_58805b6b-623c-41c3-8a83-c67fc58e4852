#include "BaseEnemyCharacter.h"
#include "Perception/PawnSensingComponent.h"
#include "Components/WidgetComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Engine/DataTable.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "EnemyComponents/EnemyAIComponent.h"
#include "EnemyComponents/EnemyStateComponent.h"
#include "EnemyComponents/EnemyAttackComponent.h"
#include "EnemyComponents/EnemyMovementComponent.h"

ABaseEnemyCharacter::ABaseEnemyCharacter()
{
	PrimaryActorTick.bCanEverTick = true;

	// Initialize components
	PawnSensingComponent = CreateDefaultSubobject<UPawnSensingComponent>(TEXT("PawnSensingComponent"));
	HealthBarWidget = CreateDefaultSubobject<UWidgetComponent>(TEXT("HealthBarWidget"));
	AIComponent = CreateDefaultSubobject<UEnemyAIComponent>(TEXT("AIComponent"));
	StateComponent = CreateDefaultSubobject<UEnemyStateComponent>(TEXT("StateComponent"));
	AttackComponent = CreateDefaultSubobject<UEnemyAttackComponent>(TEXT("AttackComponent"));
	MovementComponent = CreateDefaultSubobject<UEnemyMovementComponent>(TEXT("MovementComponent"));

	// Configure pawn sensing
	PawnSensingComponent->bOnlySensePlayers = true;
	PawnSensingComponent->bHearNoises = true;
	PawnSensingComponent->bSeePawns = true;
	PawnSensingComponent->SightRadius = 800.0f;
	PawnSensingComponent->HearingThreshold = 600.0f;
	PawnSensingComponent->LOSHearingThreshold = 1200.0f;
	PawnSensingComponent->PeripheralVisionAngle = 90.0f;

	// Configure health bar widget
	HealthBarWidget->SetupAttachment(RootComponent);
	HealthBarWidget->SetRelativeLocation(FVector(0, 0, 100));
	HealthBarWidget->SetWidgetSpace(EWidgetSpace::Screen);
	HealthBarWidget->SetDrawSize(FVector2D(100, 20));
	HealthBarWidget->SetVisibility(false);

	// Configure collision
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Pawn, ECR_Block);
	GetCapsuleComponent()->SetCollisionResponseToChannel(ECC_Vehicle, ECR_Block);

	// Initialize default values
	CurrentHealth = 100.0f;
	CurrentState = EBaseEnemyState::Idle;
	TargetPlayer = nullptr;
	bIsPlayerDetected = false;

	// Set default enemy configuration
	EnemyConfig.EnemyType = EEnemyType::BasicZombie;
	EnemyConfig.Difficulty = EEnemyDifficulty::Normal;
	EnemyConfig.DifficultyMultiplier = 1.0f;
}

void ABaseEnemyCharacter::BeginPlay()
{
	Super::BeginPlay();

	// Bind sensing events
	if (PawnSensingComponent)
	{
		PawnSensingComponent->OnSeePawn.AddDynamic(this, &ABaseEnemyCharacter::OnPlayerSeen);
		PawnSensingComponent->OnHearNoise.AddDynamic(this, &ABaseEnemyCharacter::OnPlayerHeard);
	}

	// Initialize enemy
	InitializeEnemy();
}

void ABaseEnemyCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update health bar visibility based on player proximity
	if (HealthBarWidget && TargetPlayer)
	{
		float DistanceToPlayer = FVector::Dist(GetActorLocation(), TargetPlayer->GetActorLocation());
		bool bShouldShowHealthBar = bIsPlayerDetected && DistanceToPlayer < 1500.0f && CurrentHealth < CurrentStats.MaxHealth;
		HealthBarWidget->SetVisibility(bShouldShowHealthBar);
	}
}

void ABaseEnemyCharacter::InitializeEnemy()
{
	// Load stats from database
	LoadStatsFromDatabase();

	// Apply difficulty scaling
	ApplyDifficultyScaling();

	// Set initial health
	CurrentHealth = CurrentStats.MaxHealth;

	// Configure movement speed
	if (GetCharacterMovement())
	{
		GetCharacterMovement()->MaxWalkSpeed = CurrentStats.MovementSpeed;
	}

	// Configure pawn sensing ranges
	if (PawnSensingComponent)
	{
		PawnSensingComponent->SightRadius = CurrentStats.SightRange;
		PawnSensingComponent->HearingThreshold = CurrentStats.HearingRange;
	}

	// Initialize components
	if (AIComponent)
	{
		AIComponent->InitializeAI(this);
	}

	if (StateComponent)
	{
		StateComponent->InitializeState(this);
	}

	if (AttackComponent)
	{
		AttackComponent->InitializeAttack(this);
	}

	if (MovementComponent)
	{
		MovementComponent->InitializeMovement(this);
	}

	// Update health bar
	UpdateHealthBar();

	// Call Blueprint event
	OnEnemyInitialized();

	UE_LOG(LogTemp, Log, TEXT("Enemy %s initialized with type %s"), 
		*GetName(), *UEnum::GetValueAsString(EnemyConfig.EnemyType));
}

void ABaseEnemyCharacter::SetEnemyState(EBaseEnemyState NewState)
{
	if (CurrentState != NewState)
	{
		EBaseEnemyState OldState = CurrentState;
		CurrentState = NewState;

		// Notify components
		if (StateComponent)
		{
			StateComponent->OnStateChanged(OldState, NewState);
		}

		if (AIComponent)
		{
			AIComponent->OnStateChanged(OldState, NewState);
		}

		// Broadcast events
		OnEnemyStateChanged.Broadcast(OldState, NewState);
		OnStateChangedBP(OldState, NewState);

		UE_LOG(LogTemp, Log, TEXT("Enemy %s state changed from %s to %s"), 
			*GetName(), *UEnum::GetValueAsString(OldState), *UEnum::GetValueAsString(NewState));
	}
}

float ABaseEnemyCharacter::TakeDamage(float DamageAmount, struct FDamageEvent const& DamageEvent, class AController* EventInstigator, AActor* DamageCauser)
{
	if (CurrentState == EBaseEnemyState::Dead)
	{
		return 0.0f;
	}

	float ActualDamage = Super::TakeDamage(DamageAmount, DamageEvent, EventInstigator, DamageCauser);
	
	CurrentHealth = FMath::Max(0.0f, CurrentHealth - ActualDamage);

	// Update health bar
	UpdateHealthBar();

	// Broadcast damage events
	OnEnemyDamaged.Broadcast(ActualDamage, DamageCauser);
	OnEnemyHealthChanged.Broadcast(CurrentHealth, CurrentStats.MaxHealth);
	OnDamagedBP(ActualDamage, DamageCauser);

	// Check for death
	if (CurrentHealth <= 0.0f)
	{
		Die();
	}
	else
	{
		// React to damage
		if (DamageCauser && DamageCauser->IsA<APawn>())
		{
			SetTarget(Cast<APawn>(DamageCauser));
			SetEnemyState(EBaseEnemyState::Chasing);
		}

		// Show health bar when damaged
		if (HealthBarWidget)
		{
			HealthBarWidget->SetVisibility(true);
		}
	}

	return ActualDamage;
}

void ABaseEnemyCharacter::Die()
{
	if (CurrentState == EBaseEnemyState::Dead)
	{
		return;
	}

	SetEnemyState(EBaseEnemyState::Dead);

	// Disable collision
	GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::NoCollision);

	// Stop movement
	if (GetCharacterMovement())
	{
		GetCharacterMovement()->DisableMovement();
	}

	// Hide health bar
	if (HealthBarWidget)
	{
		HealthBarWidget->SetVisibility(false);
	}

	// Spawn loot
	SpawnLoot();

	// Broadcast death events
	OnEnemyDeath.Broadcast(this);
	OnDeathBP();

	// Handle respawning
	if (EnemyConfig.bRespawns)
	{
		GetWorldTimerManager().SetTimer(RespawnTimerHandle, [this]()
		{
			// Reset enemy state
			CurrentHealth = CurrentStats.MaxHealth;
			SetEnemyState(EBaseEnemyState::Idle);
			
			// Re-enable collision and movement
			GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
			if (GetCharacterMovement())
			{
				GetCharacterMovement()->SetDefaultMovementMode();
			}
			
			UpdateHealthBar();
			
		}, EnemyConfig.RespawnTime, false);
	}
	else
	{
		// Destroy after a delay
		GetWorldTimerManager().SetTimer(RespawnTimerHandle, [this]()
		{
			Destroy();
		}, 5.0f, false);
	}

	UE_LOG(LogTemp, Log, TEXT("Enemy %s died"), *GetName());
}

void ABaseEnemyCharacter::Stun(float Duration)
{
	if (CurrentState == EBaseEnemyState::Dead)
	{
		return;
	}

	SetEnemyState(EBaseEnemyState::Stunned);

	// Disable movement temporarily
	if (GetCharacterMovement())
	{
		GetCharacterMovement()->DisableMovement();
	}

	// Clear existing stun timer
	GetWorldTimerManager().ClearTimer(StunTimerHandle);

	// Set timer to recover from stun
	GetWorldTimerManager().SetTimer(StunTimerHandle, [this]()
	{
		if (GetCharacterMovement())
		{
			GetCharacterMovement()->SetDefaultMovementMode();
		}
		
		// Return to appropriate state
		if (bIsPlayerDetected && TargetPlayer)
		{
			SetEnemyState(EBaseEnemyState::Chasing);
		}
		else
		{
			SetEnemyState(EBaseEnemyState::Idle);
		}
		
	}, Duration, false);

	OnStunnedBP(Duration);

	UE_LOG(LogTemp, Log, TEXT("Enemy %s stunned for %.1f seconds"), *GetName(), Duration);
}

void ABaseEnemyCharacter::SetTarget(APawn* NewTarget)
{
	if (TargetPlayer != NewTarget)
	{
		TargetPlayer = NewTarget;
		bIsPlayerDetected = (NewTarget != nullptr);

		if (bIsPlayerDetected)
		{
			OnEnemyPlayerDetected.Broadcast(NewTarget);
			OnPlayerDetectedBP(NewTarget);
		}
		else
		{
			OnEnemyPlayerLost.Broadcast();
			OnPlayerLostBP();
		}

		// Notify AI component
		if (AIComponent)
		{
			AIComponent->OnTargetChanged(NewTarget);
		}
	}
}

void ABaseEnemyCharacter::LoseTarget()
{
	SetTarget(nullptr);
	
	// Return to patrol or idle state
	SetEnemyState(EBaseEnemyState::Patrolling);
}

void ABaseEnemyCharacter::ApplyDifficultyScaling()
{
	float Multiplier = CalculateDifficultyMultiplier() * EnemyConfig.DifficultyMultiplier;

	CurrentStats.MaxHealth *= Multiplier;
	CurrentStats.AttackDamage *= Multiplier;
	CurrentStats.MovementSpeed *= FMath::Clamp(Multiplier, 0.5f, 2.0f); // Limit speed scaling
	CurrentStats.SightRange *= FMath::Clamp(Multiplier, 0.8f, 1.5f); // Limit sight scaling
	CurrentStats.AttackCooldown /= FMath::Clamp(Multiplier, 0.5f, 2.0f); // Faster attacks on higher difficulty
}

void ABaseEnemyCharacter::LoadStatsFromDatabase()
{
	if (EnemyConfig.bUseCustomStats)
	{
		CurrentStats = EnemyConfig.CustomStats;
		return;
	}

	if (!EnemyStatsDatabase.IsValid())
	{
		// Use default stats based on enemy type
		switch (EnemyConfig.EnemyType)
		{
			case EEnemyType::BasicZombie:
				CurrentStats.MaxHealth = 100.0f;
				CurrentStats.MovementSpeed = 200.0f;
				CurrentStats.AttackDamage = 20.0f;
				break;
			case EEnemyType::FastRunner:
				CurrentStats.MaxHealth = 60.0f;
				CurrentStats.MovementSpeed = 450.0f;
				CurrentStats.AttackDamage = 15.0f;
				break;
			case EEnemyType::HeavyBrute:
				CurrentStats.MaxHealth = 300.0f;
				CurrentStats.MovementSpeed = 150.0f;
				CurrentStats.AttackDamage = 50.0f;
				break;
			default:
				// Use default values from struct
				break;
		}
		return;
	}

	// Load from data table
	UDataTable* StatsTable = EnemyStatsDatabase.LoadSynchronous();
	if (StatsTable)
	{
		FString EnemyTypeName = UEnum::GetValueAsString(EnemyConfig.EnemyType);
		if (FEnemyStats* FoundStats = StatsTable->FindRow<FEnemyStats>(FName(*EnemyTypeName), TEXT("LoadEnemyStats")))
		{
			CurrentStats = *FoundStats;
		}
	}
}

void ABaseEnemyCharacter::UpdateHealthBar()
{
	if (HealthBarWidget && HealthBarWidget->GetWidget())
	{
		// This would update the health bar widget
		// Implementation depends on the specific widget class used
	}
}

void ABaseEnemyCharacter::SpawnLoot()
{
	if (CurrentStats.LootDropChance <= 0.0f || CurrentStats.PossibleLootItems.Num() == 0)
	{
		return;
	}

	if (FMath::RandRange(0.0f, 1.0f) <= CurrentStats.LootDropChance)
	{
		// Randomly select a loot item
		FName LootItemID = CurrentStats.PossibleLootItems[FMath::RandRange(0, CurrentStats.PossibleLootItems.Num() - 1)];
		
		// Spawn loot item in the world
		// This would create an ItemActor with the selected loot
		UE_LOG(LogTemp, Log, TEXT("Enemy %s dropped loot: %s"), *GetName(), *LootItemID.ToString());
	}
}

float ABaseEnemyCharacter::CalculateDifficultyMultiplier() const
{
	switch (EnemyConfig.Difficulty)
	{
		case EEnemyDifficulty::Easy:
			return 0.7f;
		case EEnemyDifficulty::Normal:
			return 1.0f;
		case EEnemyDifficulty::Hard:
			return 1.4f;
		case EEnemyDifficulty::Nightmare:
			return 2.0f;
		case EEnemyDifficulty::Adaptive:
			// This would calculate based on player performance
			return 1.0f;
		default:
			return 1.0f;
	}
}

void ABaseEnemyCharacter::OnPlayerSeen(APawn* SeenPawn)
{
	if (SeenPawn && SeenPawn->IsA<APawn>() && CurrentState != EBaseEnemyState::Dead)
	{
		SetTarget(SeenPawn);
		SetEnemyState(EBaseEnemyState::Chasing);
	}
}

void ABaseEnemyCharacter::OnPlayerHeard(APawn* HeardPawn, const FVector& Location, float Volume)
{
	if (HeardPawn && CurrentState != EBaseEnemyState::Dead && !bIsPlayerDetected)
	{
		// Investigate the sound
		SetEnemyState(EBaseEnemyState::Investigating);
		
		// Notify AI component about the sound location
		if (AIComponent)
		{
			AIComponent->InvestigateLocation(Location);
		}
	}
}
