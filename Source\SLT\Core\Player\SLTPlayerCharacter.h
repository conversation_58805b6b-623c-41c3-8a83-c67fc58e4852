#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "InputActionValue.h"
#include "Core/Interfaces/InventoryInterface.h"
#include "SLTPlayerCharacter.generated.h"

class USpringArmComponent;
class UCameraComponent;
class UInputMappingContext;
class UInputAction;
class UInventoryGridComponent;
class UInteractionComponent;
class UEnhancedInputComponent;

UCLASS(config=Game)
class SLT_API ASLTPlayerCharacter : public ACharacter, public IInventoryInterface
{
	GENERATED_BODY()

public:
	ASLTPlayer<PERSON>haracter();

protected:
	virtual void BeginPlay() override;

public:
	virtual void Tick(float DeltaTime) override;
	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	// Camera components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera", meta = (AllowPrivateAccess = "true"))
	USpringArmComponent* CameraBoom;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera", meta = (AllowPrivateAccess = "true"))
	UCameraComponent* FollowCamera;

	// System components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Systems", meta = (AllowPrivateAccess = "true"))
	UInventoryGridComponent* InventoryComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Systems", meta = (AllowPrivateAccess = "true"))
	UInteractionComponent* InteractionComponent;

	// Input Mapping Context
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputMappingContext* DefaultMappingContext;

	// Input Actions
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* JumpAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* MoveAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* LookAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* InteractAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* OpenInventoryAction;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Input", meta = (AllowPrivateAccess = "true"))
	UInputAction* RotateItemAction;

	// Inventory UI
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
	TSubclassOf<class UUserWidget> InventoryWidgetClass;

	// Events
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInventoryToggled, bool, bIsOpen);
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnInventoryToggled OnInventoryToggled;

	// Public functions
	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void ToggleInventory();

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void OpenInventory();

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	void CloseInventory();

	UFUNCTION(BlueprintCallable, Category = "Inventory")
	bool IsInventoryOpen() const { return bIsInventoryOpen; }

	// Getters for components
	FORCEINLINE USpringArmComponent* GetCameraBoom() const { return CameraBoom; }
	FORCEINLINE UCameraComponent* GetFollowCamera() const { return FollowCamera; }
	FORCEINLINE UInventoryGridComponent* GetInventoryComponent() const { return InventoryComponent; }
	FORCEINLINE UInteractionComponent* GetInteractionComponent() const { return InteractionComponent; }

	// IInventoryInterface implementation (delegates to InventoryComponent)
	virtual bool AddItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity, FInventorySlot& OutSlot) override;
	virtual bool RemoveItem_Implementation(FName ItemID, int32 Quantity) override;
	virtual bool RemoveItemBySlotID_Implementation(const FGuid& SlotID) override;
	virtual bool HasItem_Implementation(FName ItemID) const override;
	virtual int32 GetItemQuantity_Implementation(FName ItemID) const override;
	virtual TArray<FInventorySlot> GetAllItems_Implementation() const override;
	virtual TArray<FInventorySlot> GetItemsByType_Implementation(EItemType ItemType) const override;
	virtual bool IsInventoryFull_Implementation() const override;
	virtual bool CanFitItem_Implementation(const FInventoryItemData& ItemData, int32 Quantity) const override;
	virtual void ClearInventory_Implementation() override;
	virtual int32 GetInventoryCapacity_Implementation() const override;
	virtual float GetCurrentWeight_Implementation() const override;
	virtual float GetMaxWeight_Implementation() const override;
	virtual bool UseItem_Implementation(FName ItemID, int32 Quantity) override;
	virtual bool DropItem_Implementation(FName ItemID, int32 Quantity, const FVector& DropLocation) override;

protected:
	// Input handlers
	void Move(const FInputActionValue& Value);
	void Look(const FInputActionValue& Value);
	void Interact(const FInputActionValue& Value);
	void OpenInventoryInput(const FInputActionValue& Value);
	void RotateItem(const FInputActionValue& Value);

	// Internal state
	UPROPERTY()
	bool bIsInventoryOpen = false;

	UPROPERTY()
	UUserWidget* InventoryWidget = nullptr;

	// Helper functions
	void CreateInventoryWidget();
	void DestroyInventoryWidget();

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnInventoryOpened();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnInventoryClosed();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnItemPickedUp(const FInventorySlot& PickedUpSlot);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnItemUsed(FName ItemID, int32 Quantity);
};
