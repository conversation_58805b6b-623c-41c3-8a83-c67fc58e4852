#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "GameplayTagContainer.h"
#include "StatisticsManager.generated.h"

UENUM(BlueprintType)
enum class EStatisticType : uint8
{
	Counter			UMETA(DisplayName = "Counter"),
	Timer			UMETA(DisplayName = "Timer"),
	Maximum			UMETA(DisplayName = "Maximum"),
	Minimum			UMETA(DisplayName = "Minimum"),
	Average			UMETA(DisplayName = "Average"),
	Percentage		UMETA(DisplayName = "Percentage"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EAchievementType : uint8
{
	Progress		UMETA(DisplayName = "Progress"),
	Milestone		UMETA(DisplayName = "Milestone"),
	Hidden			UMETA(DisplayName = "Hidden"),
	Rare			UMETA(DisplayName = "Rare"),
	Platinum		UMETA(DisplayName = "Platinum")
};

USTRUCT(BlueprintType)
struct FPlayerStatistic
{
	GENERATED_BODY()

	FPlayerStatistic()
	{
		StatisticID = NAME_None;
		StatisticName = FText::GetEmpty();
		StatisticType = EStatisticType::Counter;
		CurrentValue = 0.0f;
		MaxValue = 0.0f;
		MinValue = 0.0f;
		TotalValue = 0.0f;
		SampleCount = 0;
		bIsVisible = true;
		bTrackHistory = false;
		LastUpdated = FDateTime::Now();
	}

	// Identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistic")
	FName StatisticID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistic")
	FText StatisticName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistic")
	FText StatisticDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Statistic")
	EStatisticType StatisticType;

	// Values
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float CurrentValue;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float MaxValue;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float MinValue;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	float TotalValue;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Values")
	int32 SampleCount;

	// Settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bIsVisible;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	bool bTrackHistory;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Settings")
	FDateTime LastUpdated;

	// History (if tracking enabled)
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "History")
	TArray<float> ValueHistory;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "History")
	TArray<FDateTime> TimeHistory;

	// Categories
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Categories")
	FGameplayTagContainer Categories;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomProperties;
};

USTRUCT(BlueprintType)
struct FAchievementData
{
	GENERATED_BODY()

	FAchievementData()
	{
		AchievementID = NAME_None;
		AchievementName = FText::GetEmpty();
		AchievementType = EAchievementType::Progress;
		bIsUnlocked = false;
		bIsHidden = false;
		Progress = 0.0f;
		MaxProgress = 100.0f;
		Points = 10;
		UnlockDate = FDateTime();
		RequiredStatistics = TMap<FName, float>();
	}

	// Identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Achievement")
	FName AchievementID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Achievement")
	FText AchievementName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Achievement")
	FText AchievementDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Achievement")
	EAchievementType AchievementType;

	// Status
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	bool bIsUnlocked;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	bool bIsHidden;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	float Progress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	float MaxProgress;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	int32 Points;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Status")
	FDateTime UnlockDate;

	// Requirements
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	TMap<FName, float> RequiredStatistics; // StatisticID -> Required Value

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Requirements")
	FGameplayTagContainer RequiredTags;

	// Visual
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	TSoftObjectPtr<UTexture2D> AchievementIcon;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	FLinearColor AchievementColor;
};

USTRUCT(BlueprintType)
struct FLeaderboardEntry
{
	GENERATED_BODY()

	FLeaderboardEntry()
	{
		PlayerName = TEXT("");
		Score = 0.0f;
		Rank = 0;
		PlayTime = 0.0f;
		CompletionDate = FDateTime::Now();
		bIsLocalPlayer = false;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	FString PlayerName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	float Score;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	int32 Rank;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	float PlayTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	FDateTime CompletionDate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	bool bIsLocalPlayer;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
	TMap<FString, FString> CustomData;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStatisticUpdated, FName, StatisticID, float, NewValue);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAchievementUnlocked, FName, AchievementID, const FAchievementData&, AchievementData);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnAchievementProgress, FName, AchievementID, float, Progress, float, MaxProgress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLeaderboardUpdated, const FString&, LeaderboardName, const TArray<FLeaderboardEntry>&, Entries);

UCLASS(BlueprintType, Blueprintable)
class SLT_API UStatisticsManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	UStatisticsManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Statistics storage
	UPROPERTY(SaveGame, VisibleAnywhere, BlueprintReadOnly, Category = "Statistics")
	TMap<FName, FPlayerStatistic> PlayerStatistics;

	// Achievements storage
	UPROPERTY(SaveGame, VisibleAnywhere, BlueprintReadOnly, Category = "Achievements")
	TMap<FName, FAchievementData> PlayerAchievements;

	// Leaderboards storage
	UPROPERTY(SaveGame, VisibleAnywhere, BlueprintReadOnly, Category = "Leaderboards")
	TArray<FLeaderboardEntry> LeaderboardEntries;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnStatisticUpdated OnStatisticUpdated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAchievementUnlocked OnAchievementUnlocked;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAchievementProgress OnAchievementProgress;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLeaderboardUpdated OnLeaderboardUpdated;

	// Statistics functions
	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void RegisterStatistic(FName StatisticID, const FPlayerStatistic& StatisticData);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void UpdateStatistic(FName StatisticID, float Value, bool bIsIncrement = true);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void SetStatistic(FName StatisticID, float Value);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	float GetStatistic(FName StatisticID) const;

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	FPlayerStatistic GetStatisticData(FName StatisticID) const;

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	TArray<FName> GetStatisticsByCategory(const FGameplayTag& Category) const;

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void ResetStatistic(FName StatisticID);

	UFUNCTION(BlueprintCallable, Category = "Statistics")
	void ResetAllStatistics();

	// Achievement functions
	UFUNCTION(BlueprintCallable, Category = "Achievements")
	void RegisterAchievement(FName AchievementID, const FAchievementData& AchievementData);

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	void UnlockAchievement(FName AchievementID);

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	void UpdateAchievementProgress(FName AchievementID, float Progress);

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	bool IsAchievementUnlocked(FName AchievementID) const;

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	float GetAchievementProgress(FName AchievementID) const;

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	TArray<FAchievementData> GetUnlockedAchievements() const;

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	TArray<FAchievementData> GetAchievementsByType(EAchievementType AchievementType) const;

	UFUNCTION(BlueprintCallable, Category = "Achievements")
	int32 GetTotalAchievementPoints() const;

	// Leaderboard functions
	UFUNCTION(BlueprintCallable, Category = "Leaderboards")
	void SubmitScore(const FString& LeaderboardName, float Score, const FString& PlayerName = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "Leaderboards")
	TArray<FLeaderboardEntry> GetLeaderboard(const FString& LeaderboardName, int32 MaxEntries = 10) const;

	UFUNCTION(BlueprintCallable, Category = "Leaderboards")
	int32 GetPlayerRank(const FString& LeaderboardName, const FString& PlayerName = TEXT("")) const;

	UFUNCTION(BlueprintCallable, Category = "Leaderboards")
	void ClearLeaderboard(const FString& LeaderboardName);

	// Analytics functions
	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void TrackEvent(const FString& EventName, const TMap<FString, FString>& Parameters);

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	void TrackPlayerAction(const FString& Action, const FString& Context = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	TMap<FString, float> GetPlaytimeStatistics() const;

	UFUNCTION(BlueprintCallable, Category = "Analytics")
	TMap<FString, int32> GetUsageStatistics() const;

	// Save/Load functions
	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool SaveStatistics();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool LoadStatistics();

	UFUNCTION(BlueprintCallable, Category = "Save System")
	void ExportStatistics(const FString& FilePath) const;

	UFUNCTION(BlueprintCallable, Category = "Save System")
	bool ImportStatistics(const FString& FilePath);

protected:
	// Internal tracking
	UPROPERTY()
	TMap<FString, FDateTime> EventTimestamps;

	UPROPERTY()
	TMap<FString, int32> EventCounts;

	// Internal functions
	void CheckAchievementRequirements(FName StatisticID);
	void UpdateAchievementProgressInternal(FName AchievementID);
	void SortLeaderboard(const FString& LeaderboardName);
	FString GetDefaultPlayerName() const;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnStatisticsLoaded();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnNewPersonalBest(FName StatisticID, float NewValue, float OldValue);
};
