#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "../BaseEnemyCharacter.h"
#include "EnemyAIComponent.generated.h"

class UBehaviorTreeComponent;
class UBlackboardComponent;
class AAIController;

UENUM(BlueprintType)
enum class EAIBehaviorPattern : uint8
{
	Aggressive		UMETA(DisplayName = "Aggressive"),
	Defensive		UMETA(DisplayName = "Defensive"),
	Cautious		UMETA(DisplayName = "Cautious"),
	Territorial		UMETA(DisplayName = "Territorial"),
	Pack			UMETA(DisplayName = "Pack"),
	Ambush			UMETA(DisplayName = "Ambush"),
	Patrol			UMETA(DisplayName = "Patrol"),
	Guard			UMETA(DisplayName = "Guard"),
	Custom			UMETA(DisplayName = "Custom")
};

USTRUCT(BlueprintType)
struct FAIBehaviorSettings
{
	GENERATED_BODY()

	FAIBehaviorSettings()
	{
		BehaviorPattern = EAIBehaviorPattern::Aggressive;
		AggressionLevel = 0.7f;
		CautiousLevel = 0.3f;
		GroupCoordination = 0.5f;
		TerritorialRadius = 500.0f;
		PatrolSpeed = 200.0f;
		ChaseSpeed = 400.0f;
		AttackDistance = 150.0f;
		FleeHealthThreshold = 0.2f;
		CallForHelpRange = 800.0f;
		bCanFlank = false;
		bCanTakeCover = false;
		bCanCallForHelp = true;
		bCanRetreat = true;
		bUsesWeapons = false;
		ReactionTime = 0.5f;
		DecisionUpdateRate = 0.2f;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	EAIBehaviorPattern BehaviorPattern;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float AggressionLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float CautiousLevel;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float GroupCoordination;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Territory")
	float TerritorialRadius;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float PatrolSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Movement")
	float ChaseSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat")
	float AttackDistance;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Combat", meta = (ClampMin = "0.0", ClampMax = "1.0"))
	float FleeHealthThreshold;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Group")
	float CallForHelpRange;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanFlank;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanTakeCover;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanCallForHelp;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanRetreat;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bUsesWeapons;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float ReactionTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Timing")
	float DecisionUpdateRate;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, float> CustomParameters;
};

USTRUCT(BlueprintType)
struct FPatrolPoint
{
	GENERATED_BODY()

	FPatrolPoint()
	{
		Location = FVector::ZeroVector;
		WaitTime = 2.0f;
		bLookAround = true;
		LookDirection = FVector::ForwardVector;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	FVector Location;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	float WaitTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	bool bLookAround;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	FVector LookDirection;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	FGameplayTagContainer PatrolTags;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAIDecisionMade, const FString&, DecisionDescription);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAITargetChanged, APawn*, OldTarget, APawn*, NewTarget);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAIBehaviorChanged, EAIBehaviorPattern, NewBehavior);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UEnemyAIComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UEnemyAIComponent();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// AI Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
	FAIBehaviorSettings BehaviorSettings;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
	TSoftObjectPtr<UBehaviorTree> BehaviorTree;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Configuration")
	TSoftObjectPtr<UBlackboardAsset> BlackboardAsset;

	// Patrol Configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	TArray<FPatrolPoint> PatrolPoints;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	bool bLoopPatrol = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Patrol")
	bool bRandomizePatrol = false;

	// Current AI State
	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	APawn* CurrentTarget;

	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	FVector LastKnownTargetLocation;

	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	FVector InvestigationLocation;

	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	int32 CurrentPatrolIndex = 0;

	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	float LastDecisionTime = 0.0f;

	UPROPERTY(BlueprintReadOnly, Category = "AI State")
	bool bIsInCombat = false;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAIDecisionMade OnAIDecisionMade;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAITargetChanged OnAITargetChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAIBehaviorChanged OnAIBehaviorChanged;

	// Main AI Functions
	UFUNCTION(BlueprintCallable, Category = "AI")
	void InitializeAI(ABaseEnemyCharacter* OwnerEnemy);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void OnStateChanged(EEnemyState OldState, EEnemyState NewState);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void OnTargetChanged(APawn* NewTarget);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void InvestigateLocation(const FVector& Location);

	UFUNCTION(BlueprintCallable, Category = "AI")
	void CallForHelp();

	UFUNCTION(BlueprintCallable, Category = "AI")
	void SetBehaviorPattern(EAIBehaviorPattern NewPattern);

	// Decision Making
	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	void MakeDecision();

	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	bool ShouldAttack() const;

	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	bool ShouldFlee() const;

	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	bool ShouldCallForHelp() const;

	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	FVector GetFlankingPosition() const;

	UFUNCTION(BlueprintCallable, Category = "AI Decision")
	FVector GetCoverPosition() const;

	// Patrol Functions
	UFUNCTION(BlueprintCallable, Category = "Patrol")
	void StartPatrol();

	UFUNCTION(BlueprintCallable, Category = "Patrol")
	void NextPatrolPoint();

	UFUNCTION(BlueprintCallable, Category = "Patrol")
	FVector GetCurrentPatrolTarget() const;

	UFUNCTION(BlueprintCallable, Category = "Patrol")
	void AddPatrolPoint(const FVector& Location, float WaitTime = 2.0f);

	UFUNCTION(BlueprintCallable, Category = "Patrol")
	void ClearPatrolPoints();

	// Utility Functions
	UFUNCTION(BlueprintCallable, Category = "AI Utility")
	float GetDistanceToTarget() const;

	UFUNCTION(BlueprintCallable, Category = "AI Utility")
	bool HasLineOfSightToTarget() const;

	UFUNCTION(BlueprintCallable, Category = "AI Utility")
	TArray<ABaseEnemyCharacter*> GetNearbyAllies(float Range = 1000.0f) const;

	UFUNCTION(BlueprintCallable, Category = "AI Utility")
	void UpdateBlackboardValues();

	// Blueprint Events
	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnAIInitialized();

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnTargetAcquired(APawn* Target);

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnTargetLost();

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnInvestigationStarted(const FVector& Location);

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnPatrolPointReached(int32 PatrolIndex);

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnCombatStarted();

	UFUNCTION(BlueprintImplementableEvent, Category = "AI Events")
	void OnCombatEnded();

protected:
	// Internal references
	UPROPERTY()
	ABaseEnemyCharacter* OwnerEnemyCharacter;

	UPROPERTY()
	AAIController* AIController;

	UPROPERTY()
	UBehaviorTreeComponent* BehaviorTreeComponent;

	UPROPERTY()
	UBlackboardComponent* BlackboardComponent;

	// Internal state
	FTimerHandle DecisionTimerHandle;
	FTimerHandle PatrolTimerHandle;

	// Internal functions
	void SetupBehaviorTree();
	void ConfigureBehaviorForPattern();
	void UpdatePatrol();
	void ProcessCombatDecision();
	void ProcessPatrolDecision();
	void ProcessInvestigationDecision();
	FVector FindRandomPatrolLocation() const;
	bool IsLocationSafe(const FVector& Location) const;
};
