#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "../../Core/Interfaces/Interactable.h"
#include "PuzzleSystem/Components/PuzzleComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "PuzzleActor.generated.h"

UCLASS(BlueprintType, Blueprintable)
class SLT_API APuzzleActor : public AActor, public IInteractable
{
	GENERATED_BODY()
	
public:	
	APuzzleActor();

protected:
	virtual void BeginPlay() override;

public:
	// Components
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* MeshComponent;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UBoxComponent* InteractionBox;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UPuzzleComponent* PuzzleComponent;

	// Interaction settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	FText InteractionPrompt;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bUseCustomPrompt = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bShowPuzzleProgress = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Interaction")
	bool bRequireInteractionToStart = true;

	// Visual feedback
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual")
	bool bChangeColorOnSolved = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bChangeColorOnSolved"))
	FLinearColor SolvedColor = FLinearColor::Green;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bChangeColorOnSolved"))
	FLinearColor UnsolvedColor = FLinearColor::White;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Visual", meta = (EditCondition = "bChangeColorOnSolved"))
	FLinearColor FailedColor = FLinearColor::Red;

	// Events
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPuzzleActorSolved, APuzzleActor*, PuzzleActor, APawn*, SolvingPawn);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPuzzleActorFailed, APuzzleActor*, PuzzleActor, APawn*, FailingPawn);
	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPuzzleActorReset, APuzzleActor*, PuzzleActor);

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleActorSolved OnPuzzleActorSolved;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleActorFailed OnPuzzleActorFailed;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPuzzleActorReset OnPuzzleActorReset;

	// Public functions
	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool TryInputSolution(const FString& Solution, APawn* InteractingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool TryInputStep(const FString& StepValue, APawn* InteractingPawn = nullptr);

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	void ResetPuzzle();

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	bool IsPuzzleSolved() const;

	UFUNCTION(BlueprintCallable, Category = "Puzzle")
	float GetPuzzleProgress() const;

	// IInteractable interface
	virtual FInteractionData GetInteractionData_Implementation() const override;
	virtual bool CanInteract_Implementation(APawn* InteractingPawn) const override;
	virtual void OnInteractionStart_Implementation(APawn* InteractingPawn) override;
	virtual void OnInteractionComplete_Implementation(APawn* InteractingPawn) override;
	virtual void OnInteractionCancel_Implementation(APawn* InteractingPawn) override;
	virtual FVector GetInteractionLocation_Implementation() const override;

protected:
	// Internal state
	UPROPERTY()
	UMaterialInstanceDynamic* DynamicMaterial;

	// Event handlers
	UFUNCTION()
	void OnPuzzleSolved(APawn* SolvingPawn);

	UFUNCTION()
	void OnPuzzleFailed(APawn* FailingPawn);

	UFUNCTION()
	void OnPuzzleStateChanged(EPuzzleState OldState, EPuzzleState NewState);

	UFUNCTION()
	void OnPuzzleReset(bool bWasForced);

	// Visual updates
	void UpdateVisualState();
	void SetMeshColor(const FLinearColor& Color);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPuzzleInteracted(APawn* InteractingPawn);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnPuzzleVisualUpdate(EPuzzleState PuzzleState);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnSolutionAttempted(const FString& AttemptedSolution, bool bWasCorrect);
};
