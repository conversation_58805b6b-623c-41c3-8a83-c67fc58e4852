// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Core/Systems/ShaderManager.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UMaterialInstanceDynamic;
class UPrimitiveComponent;
enum class EItemRarity : uint8;
enum class EPostProcessType : uint8;
enum class EShaderVariant : uint8;
struct FLinearColor;
#ifdef SLT_ShaderManager_generated_h
#error "ShaderManager.generated.h already included, missing '#pragma once' in ShaderManager.h"
#endif
#define SLT_ShaderManager_generated_h

#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_41_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FShaderVariantData_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FShaderVariantData>();

#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_116_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FShaderPostProcessSettings_Statics; \
	SLT_API static class UScriptStruct* StaticStruct();


template<> SLT_API UScriptStruct* StaticStruct<struct FShaderPostProcessSettings>();

#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_169_DELEGATE \
SLT_API void FOnShaderVariantApplied_DelegateWrapper(const FMulticastScriptDelegate& OnShaderVariantApplied, UPrimitiveComponent* Component, EShaderVariant VariantType);


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_170_DELEGATE \
SLT_API void FOnPostProcessApplied_DelegateWrapper(const FMulticastScriptDelegate& OnPostProcessApplied, EPostProcessType ProcessType, float BlendWeight);


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_171_DELEGATE \
SLT_API void FOnPostProcessRemoved_DelegateWrapper(const FMulticastScriptDelegate& OnPostProcessRemoved, EPostProcessType ProcessType);


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateShaderAnimations); \
	DECLARE_FUNCTION(execStopShaderAnimation); \
	DECLARE_FUNCTION(execStartShaderAnimation); \
	DECLARE_FUNCTION(execSetShaderQuality); \
	DECLARE_FUNCTION(execGetAvailableVariants); \
	DECLARE_FUNCTION(execCacheShaderVariants); \
	DECLARE_FUNCTION(execPrecompileShaderVariants); \
	DECLARE_FUNCTION(execGetGlobalVectorParameter); \
	DECLARE_FUNCTION(execGetGlobalScalarParameter); \
	DECLARE_FUNCTION(execSetGlobalVectorParameter); \
	DECLARE_FUNCTION(execSetGlobalScalarParameter); \
	DECLARE_FUNCTION(execIsPostProcessActive); \
	DECLARE_FUNCTION(execClearAllPostProcesses); \
	DECLARE_FUNCTION(execRemovePostProcess); \
	DECLARE_FUNCTION(execApplyPostProcess); \
	DECLARE_FUNCTION(execApplyItemRarityShader); \
	DECLARE_FUNCTION(execCreateDynamicMaterial); \
	DECLARE_FUNCTION(execRemoveShaderVariant); \
	DECLARE_FUNCTION(execApplyShaderVariant);


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_CALLBACK_WRAPPERS
#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUShaderManager(); \
	friend struct Z_Construct_UClass_UShaderManager_Statics; \
public: \
	DECLARE_CLASS(UShaderManager, UGameInstanceSubsystem, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/SLT"), NO_API) \
	DECLARE_SERIALIZER(UShaderManager)


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UShaderManager(UShaderManager&&); \
	UShaderManager(const UShaderManager&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UShaderManager); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UShaderManager); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UShaderManager) \
	NO_API virtual ~UShaderManager();


#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_173_PROLOG
#define FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_CALLBACK_WRAPPERS \
	FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_INCLASS_NO_PURE_DECLS \
	FID_SLT_Source_SLT_Core_Systems_ShaderManager_h_176_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> SLT_API UClass* StaticClass<class UShaderManager>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_SLT_Source_SLT_Core_Systems_ShaderManager_h


#define FOREACH_ENUM_ESHADERVARIANT(op) \
	op(EShaderVariant::Default) \
	op(EShaderVariant::Highlighted) \
	op(EShaderVariant::Selected) \
	op(EShaderVariant::Disabled) \
	op(EShaderVariant::Damaged) \
	op(EShaderVariant::Rare) \
	op(EShaderVariant::Epic) \
	op(EShaderVariant::Legendary) \
	op(EShaderVariant::Interactive) \
	op(EShaderVariant::Locked) 

enum class EShaderVariant : uint8;
template<> struct TIsUEnumClass<EShaderVariant> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EShaderVariant>();

#define FOREACH_ENUM_EPOSTPROCESSTYPE(op) \
	op(EPostProcessType::None) \
	op(EPostProcessType::Interaction) \
	op(EPostProcessType::Inventory) \
	op(EPostProcessType::Combat) \
	op(EPostProcessType::Puzzle) \
	op(EPostProcessType::Transition) \
	op(EPostProcessType::Death) 

enum class EPostProcessType : uint8;
template<> struct TIsUEnumClass<EPostProcessType> { enum { Value = true }; };
template<> SLT_API UEnum* StaticEnum<EPostProcessType>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
