#include "AIBehaviorDesigner.h"
#include "Engine/DataTable.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BlackboardAsset.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Kismet/GameplayStatics.h"

UAIBehaviorDesigner::UAIBehaviorDesigner()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.5f;

	CurrentTemplateID = NAME_None;
	BehaviorType = EAIBehaviorType::Patrol;
	Personality = EAIPersonality::Aggressive;
	DifficultyLevel = EAIDifficultyLevel::Normal;
	bAutoConfigureFromTemplate = true;
	bAdaptToDifficulty = true;
	bLearnFromPlayer = false;
	AdaptationRate = 0.1f;

	CachedTemplateDatabase = nullptr;
	CachedAIController = nullptr;
	CachedBehaviorTreeComponent = nullptr;
	CachedBlackboardComponent = nullptr;
}

void UAIBehaviorDesigner::BeginPlay()
{
	Super::BeginPlay();
	
	LoadTemplateDatabase();
	CacheAIComponents();
	
	if (bAutoConfigureFromTemplate && CurrentTemplateID != NAME_None)
	{
		ApplyBehaviorTemplate(CurrentTemplateID);
	}
}

void UAIBehaviorDesigner::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (BehaviorState.bIsActive)
	{
		ProcessParameterModifiers(DeltaTime);
		UpdateBehaviorParameters();
		
		// Update state timing
		BehaviorState.StateStartTime += DeltaTime;
	}
}

void UAIBehaviorDesigner::ApplyBehaviorTemplate(FName TemplateID)
{
	if (TemplateID == NAME_None)
	{
		return;
	}

	FAIBehaviorTemplate Template = GetBehaviorTemplate(TemplateID);
	if (Template.TemplateID == NAME_None)
	{
		UE_LOG(LogTemp, Warning, TEXT("Behavior template %s not found"), *TemplateID.ToString());
		return;
	}

	FName OldTemplateID = CurrentTemplateID;
	CurrentTemplateID = TemplateID;

	// Apply template settings
	SetBehaviorType(Template.BehaviorType);
	SetPersonality(Template.Personality);
	SetDifficultyLevel(Template.DifficultyLevel);

	// Apply template parameters
	ApplyTemplateParameters(Template);

	// Load behavior tree if specified
	if (Template.BehaviorTree.IsValid() && CachedAIController)
	{
		UBehaviorTree* BehaviorTree = Template.BehaviorTree.LoadSynchronous();
		if (BehaviorTree && CachedBehaviorTreeComponent)
		{
			CachedBehaviorTreeComponent->StartTree(*BehaviorTree);
		}
	}

	// Load blackboard if specified
	if (Template.BlackboardAsset.IsValid() && CachedBlackboardComponent)
	{
		UBlackboardAsset* BlackboardAsset = Template.BlackboardAsset.LoadSynchronous();
		if (BlackboardAsset)
		{
			CachedBlackboardComponent->InitializeBlackboard(*BlackboardAsset);
		}
	}

	OnBehaviorTemplateApplied.Broadcast(TemplateID, Template);
	
	UE_LOG(LogTemp, Log, TEXT("Applied behavior template: %s"), *Template.TemplateName.ToString());
}

void UAIBehaviorDesigner::SetBehaviorType(EAIBehaviorType NewBehavior)
{
	if (BehaviorType != NewBehavior)
	{
		EAIBehaviorType OldBehavior = BehaviorType;
		BehaviorType = NewBehavior;
		BehaviorState.CurrentBehavior = NewBehavior;
		BehaviorState.LastStateChange = GetWorld()->GetTimeSeconds();
		BehaviorState.StateChangeCount++;

		OnBehaviorChanged.Broadcast(OldBehavior, NewBehavior, GetOwner());
		
		UE_LOG(LogTemp, Log, TEXT("AI behavior changed from %s to %s"), 
			*UEnum::GetValueAsString(OldBehavior), *UEnum::GetValueAsString(NewBehavior));
	}
}

void UAIBehaviorDesigner::SetPersonality(EAIPersonality NewPersonality)
{
	if (Personality != NewPersonality)
	{
		EAIPersonality OldPersonality = Personality;
		Personality = NewPersonality;
		BehaviorState.CurrentPersonality = NewPersonality;

		OnPersonalityChanged.Broadcast(OldPersonality, NewPersonality);
		
		// Auto-adjust parameters based on personality
		AutoConfigureForPersonality(NewPersonality);
	}
}

void UAIBehaviorDesigner::SetDifficultyLevel(EAIDifficultyLevel NewDifficulty)
{
	if (DifficultyLevel != NewDifficulty)
	{
		EAIDifficultyLevel OldDifficulty = DifficultyLevel;
		DifficultyLevel = NewDifficulty;
		BehaviorState.CurrentDifficulty = NewDifficulty;

		OnDifficultyChanged.Broadcast(OldDifficulty, NewDifficulty);
		
		// Auto-adjust parameters based on difficulty
		if (bAdaptToDifficulty)
		{
			AutoConfigureForDifficulty(NewDifficulty);
		}
	}
}

void UAIBehaviorDesigner::ActivateBehavior()
{
	if (!BehaviorState.bIsActive)
	{
		BehaviorState.bIsActive = true;
		BehaviorState.StateStartTime = 0.0f;
		
		OnBehaviorActivated(BehaviorType);
		
		UE_LOG(LogTemp, Log, TEXT("AI behavior activated: %s"), *UEnum::GetValueAsString(BehaviorType));
	}
}

void UAIBehaviorDesigner::DeactivateBehavior()
{
	if (BehaviorState.bIsActive)
	{
		BehaviorState.bIsActive = false;
		
		OnBehaviorDeactivated(BehaviorType);
		
		UE_LOG(LogTemp, Log, TEXT("AI behavior deactivated: %s"), *UEnum::GetValueAsString(BehaviorType));
	}
}

TArray<FAIBehaviorTemplate> UAIBehaviorDesigner::GetAvailableTemplates() const
{
	TArray<FAIBehaviorTemplate> Templates;
	
	if (!CachedTemplateDatabase)
	{
		return Templates;
	}

	TArray<FName> RowNames = CachedTemplateDatabase->GetRowNames();
	for (FName RowName : RowNames)
	{
		if (FAIBehaviorTemplate* TemplateData = CachedTemplateDatabase->FindRow<FAIBehaviorTemplate>(RowName, TEXT("GetAvailableTemplates")))
		{
			Templates.Add(*TemplateData);
		}
	}
	
	return Templates;
}

FAIBehaviorTemplate UAIBehaviorDesigner::GetBehaviorTemplate(FName TemplateID) const
{
	if (CachedTemplateDatabase)
	{
		if (FAIBehaviorTemplate* TemplateData = CachedTemplateDatabase->FindRow<FAIBehaviorTemplate>(TemplateID, TEXT("GetBehaviorTemplate")))
		{
			return *TemplateData;
		}
	}
	return FAIBehaviorTemplate();
}

TArray<FAIBehaviorTemplate> UAIBehaviorDesigner::GetTemplatesByType(EAIBehaviorType BehaviorType) const
{
	TArray<FAIBehaviorTemplate> FilteredTemplates;
	TArray<FAIBehaviorTemplate> AllTemplates = GetAvailableTemplates();
	
	for (const FAIBehaviorTemplate& Template : AllTemplates)
	{
		if (Template.BehaviorType == BehaviorType)
		{
			FilteredTemplates.Add(Template);
		}
	}
	
	return FilteredTemplates;
}

TArray<FAIBehaviorTemplate> UAIBehaviorDesigner::GetTemplatesByPersonality(EAIPersonality Personality) const
{
	TArray<FAIBehaviorTemplate> FilteredTemplates;
	TArray<FAIBehaviorTemplate> AllTemplates = GetAvailableTemplates();
	
	for (const FAIBehaviorTemplate& Template : AllTemplates)
	{
		if (Template.Personality == Personality)
		{
			FilteredTemplates.Add(Template);
		}
	}
	
	return FilteredTemplates;
}

bool UAIBehaviorDesigner::CreateCustomTemplate(const FAIBehaviorTemplate& Template)
{
	// This would save the template to the database
	UE_LOG(LogTemp, Log, TEXT("Created custom AI behavior template: %s"), *Template.TemplateName.ToString());
	return true;
}

void UAIBehaviorDesigner::AutoConfigureForDifficulty(EAIDifficultyLevel TargetDifficulty)
{
	float DifficultyMultiplier = CalculateDifficultyMultiplier();
	
	// Adjust parameters based on difficulty
	SetBehaviorParameter(TEXT("SightRadius"), 800.0f * DifficultyMultiplier);
	SetBehaviorParameter(TEXT("HearingRadius"), 600.0f * DifficultyMultiplier);
	SetBehaviorParameter(TEXT("MovementSpeed"), 300.0f * DifficultyMultiplier);
	SetBehaviorParameter(TEXT("AttackDamage"), 25.0f * DifficultyMultiplier);
	SetBehaviorParameter(TEXT("AttackCooldown"), 2.0f / DifficultyMultiplier);
	SetBehaviorParameter(TEXT("AlertDuration"), 10.0f * DifficultyMultiplier);
	
	UE_LOG(LogTemp, Log, TEXT("Auto-configured AI for difficulty: %s (multiplier: %.2f)"), 
		*UEnum::GetValueAsString(TargetDifficulty), DifficultyMultiplier);
}

void UAIBehaviorDesigner::AutoConfigureForScenario(const FString& ScenarioName)
{
	// This would configure AI based on predefined scenarios
	UE_LOG(LogTemp, Log, TEXT("Auto-configuring AI for scenario: %s"), *ScenarioName);
}

void UAIBehaviorDesigner::AdaptToPlayerBehavior(const TMap<FString, float>& PlayerStats)
{
	if (!bLearnFromPlayer)
	{
		return;
	}

	// Analyze player behavior and adapt AI accordingly
	if (const float* PlayerAccuracy = PlayerStats.Find(TEXT("Accuracy")))
	{
		if (*PlayerAccuracy > 0.8f)
		{
			// Player is very accurate, make AI more evasive
			ApplyParameterModifier(TEXT("MovementSpeed"), 1.2f, 30.0f);
			ApplyParameterModifier(TEXT("TakeCoverChance"), 1.5f, 30.0f);
		}
	}

	if (const float* PlayerAggression = PlayerStats.Find(TEXT("Aggression")))
	{
		if (*PlayerAggression > 0.7f)
		{
			// Player is aggressive, make AI more defensive
			ApplyParameterModifier(TEXT("AttackRange"), 1.3f, 30.0f);
			ApplyParameterModifier(TEXT("RetreatThreshold"), 0.8f, 30.0f);
		}
	}

	UE_LOG(LogTemp, Log, TEXT("AI adapted to player behavior"));
}

void UAIBehaviorDesigner::ResetToDefaults()
{
	// Clear all parameter modifiers
	ParameterModifiers.Empty();
	ModifierTimers.Empty();
	
	// Reset to default template if available
	if (CurrentTemplateID != NAME_None)
	{
		ApplyBehaviorTemplate(CurrentTemplateID);
	}
	
	UE_LOG(LogTemp, Log, TEXT("AI behavior reset to defaults"));
}

void UAIBehaviorDesigner::SetBehaviorParameter(const FString& ParameterName, float Value)
{
	float OldValue = GetBehaviorParameter(ParameterName);
	BehaviorState.BehaviorParameters.Add(ParameterName, Value);
	
	// Update blackboard if available
	if (CachedBlackboardComponent)
	{
		FName KeyName = FName(*ParameterName);
		if (CachedBlackboardComponent->GetBlackboardAsset()->GetKeyID(KeyName) != FBlackboard::InvalidKey)
		{
			CachedBlackboardComponent->SetValueAsFloat(KeyName, Value);
		}
	}
	
	OnParameterChanged(ParameterName, OldValue, Value);
}

float UAIBehaviorDesigner::GetBehaviorParameter(const FString& ParameterName) const
{
	if (const float* Value = BehaviorState.BehaviorParameters.Find(ParameterName))
	{
		return *Value;
	}
	return 0.0f;
}

void UAIBehaviorDesigner::ApplyParameterModifier(const FString& ModifierName, float Multiplier, float Duration)
{
	ParameterModifiers.Add(ModifierName, Multiplier);
	
	if (Duration > 0.0f)
	{
		ModifierTimers.Add(ModifierName, Duration);
	}
	
	UE_LOG(LogTemp, Log, TEXT("Applied parameter modifier %s: %.2fx for %.1fs"), 
		*ModifierName, Multiplier, Duration);
}

void UAIBehaviorDesigner::RemoveParameterModifier(const FString& ModifierName)
{
	ParameterModifiers.Remove(ModifierName);
	ModifierTimers.Remove(ModifierName);
	
	UE_LOG(LogTemp, Log, TEXT("Removed parameter modifier: %s"), *ModifierName);
}

void UAIBehaviorDesigner::ValidateBehaviorSetup()
{
	TArray<FString> Issues = GetSetupIssues();
	
	if (Issues.Num() == 0)
	{
		UE_LOG(LogTemp, Log, TEXT("AI behavior setup is valid"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("AI behavior setup has %d issues:"), Issues.Num());
		for (const FString& Issue : Issues)
		{
			UE_LOG(LogTemp, Warning, TEXT("  - %s"), *Issue);
		}
	}
}

TArray<FString> UAIBehaviorDesigner::GetSetupIssues() const
{
	TArray<FString> Issues;
	
	if (!CachedAIController)
	{
		Issues.Add(TEXT("No AI Controller found"));
	}
	
	if (!CachedBehaviorTreeComponent)
	{
		Issues.Add(TEXT("No Behavior Tree Component found"));
	}
	
	if (!CachedBlackboardComponent)
	{
		Issues.Add(TEXT("No Blackboard Component found"));
	}
	
	if (CurrentTemplateID == NAME_None)
	{
		Issues.Add(TEXT("No behavior template selected"));
	}
	
	return Issues;
}

void UAIBehaviorDesigner::AutoFixCommonIssues()
{
	// This would automatically fix common setup issues
	UE_LOG(LogTemp, Log, TEXT("Auto-fixing AI behavior setup issues..."));
}

void UAIBehaviorDesigner::ExportBehaviorConfiguration(const FString& FilePath) const
{
	// This would export the current configuration to a file
	UE_LOG(LogTemp, Log, TEXT("Exporting AI behavior configuration to: %s"), *FilePath);
}

bool UAIBehaviorDesigner::ImportBehaviorConfiguration(const FString& FilePath)
{
	// This would import configuration from a file
	UE_LOG(LogTemp, Log, TEXT("Importing AI behavior configuration from: %s"), *FilePath);
	return true;
}

void UAIBehaviorDesigner::LoadTemplateDatabase()
{
	if (BehaviorTemplateDatabase.IsValid())
	{
		CachedTemplateDatabase = BehaviorTemplateDatabase.LoadSynchronous();
	}
}

void UAIBehaviorDesigner::CacheAIComponents()
{
	if (AActor* Owner = GetOwner())
	{
		CachedAIController = Cast<AAIController>(Owner->GetInstigatorController());
		
		if (CachedAIController)
		{
			CachedBehaviorTreeComponent = CachedAIController->FindComponentByClass<UBehaviorTreeComponent>();
			CachedBlackboardComponent = CachedAIController->FindComponentByClass<UBlackboardComponent>();
		}
	}
}

void UAIBehaviorDesigner::ApplyTemplateParameters(const FAIBehaviorTemplate& Template)
{
	SetBehaviorParameter(TEXT("SightRadius"), Template.SightRadius);
	SetBehaviorParameter(TEXT("HearingRadius"), Template.HearingRadius);
	SetBehaviorParameter(TEXT("MovementSpeed"), Template.MovementSpeed);
	SetBehaviorParameter(TEXT("AttackRange"), Template.AttackRange);
	SetBehaviorParameter(TEXT("AttackDamage"), Template.AttackDamage);
	SetBehaviorParameter(TEXT("AttackCooldown"), Template.AttackCooldown);
	SetBehaviorParameter(TEXT("PatrolRadius"), Template.PatrolRadius);
	SetBehaviorParameter(TEXT("AlertDuration"), Template.AlertDuration);
	SetBehaviorParameter(TEXT("InvestigationTime"), Template.InvestigationTime);
	
	// Apply custom parameters
	for (const auto& CustomParam : Template.CustomParameters)
	{
		float Value = FCString::Atof(*CustomParam.Value);
		SetBehaviorParameter(CustomParam.Key, Value);
	}
}

void UAIBehaviorDesigner::UpdateBehaviorParameters()
{
	// Apply parameter modifiers
	for (const auto& ParamPair : BehaviorState.BehaviorParameters)
	{
		float BaseValue = ParamPair.Value;
		float ModifiedValue = BaseValue;
		
		// Apply all relevant modifiers
		for (const auto& ModifierPair : ParameterModifiers)
		{
			if (ModifierPair.Key.Contains(ParamPair.Key))
			{
				ModifiedValue *= ModifierPair.Value;
			}
		}
		
		// Update blackboard if value changed
		if (ModifiedValue != BaseValue && CachedBlackboardComponent)
		{
			FName KeyName = FName(*ParamPair.Key);
			if (CachedBlackboardComponent->GetBlackboardAsset()->GetKeyID(KeyName) != FBlackboard::InvalidKey)
			{
				CachedBlackboardComponent->SetValueAsFloat(KeyName, ModifiedValue);
			}
		}
	}
}

void UAIBehaviorDesigner::ProcessParameterModifiers(float DeltaTime)
{
	// Update modifier timers
	TArray<FString> ExpiredModifiers;
	
	for (auto& TimerPair : ModifierTimers)
	{
		TimerPair.Value -= DeltaTime;
		if (TimerPair.Value <= 0.0f)
		{
			ExpiredModifiers.Add(TimerPair.Key);
		}
	}
	
	// Remove expired modifiers
	for (const FString& ModifierName : ExpiredModifiers)
	{
		RemoveParameterModifier(ModifierName);
	}
}

float UAIBehaviorDesigner::CalculateDifficultyMultiplier() const
{
	switch (DifficultyLevel)
	{
		case EAIDifficultyLevel::Beginner:
			return 0.5f;
		case EAIDifficultyLevel::Easy:
			return 0.75f;
		case EAIDifficultyLevel::Normal:
			return 1.0f;
		case EAIDifficultyLevel::Hard:
			return 1.25f;
		case EAIDifficultyLevel::Expert:
			return 1.5f;
		case EAIDifficultyLevel::Nightmare:
			return 2.0f;
		case EAIDifficultyLevel::Adaptive:
			// This would calculate based on player performance
			return 1.0f;
		default:
			return 1.0f;
	}
}

void UAIBehaviorDesigner::AutoConfigureForPersonality(EAIPersonality NewPersonality)
{
	switch (NewPersonality)
	{
		case EAIPersonality::Aggressive:
			ApplyParameterModifier(TEXT("AttackRange"), 1.2f);
			ApplyParameterModifier(TEXT("AttackCooldown"), 0.8f);
			break;
		case EAIPersonality::Defensive:
			ApplyParameterModifier(TEXT("AttackRange"), 0.8f);
			ApplyParameterModifier(TEXT("SightRadius"), 1.2f);
			break;
		case EAIPersonality::Cautious:
			ApplyParameterModifier(TEXT("MovementSpeed"), 0.8f);
			ApplyParameterModifier(TEXT("InvestigationTime"), 1.5f);
			break;
		case EAIPersonality::Reckless:
			ApplyParameterModifier(TEXT("MovementSpeed"), 1.3f);
			ApplyParameterModifier(TEXT("InvestigationTime"), 0.5f);
			break;
		default:
			break;
	}
}
