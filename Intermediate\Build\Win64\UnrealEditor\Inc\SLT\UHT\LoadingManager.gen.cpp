// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/LoadingManager.h"
#include "Runtime/Engine/Classes/Engine/GameInstance.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeLoadingManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FSoftObjectPath();
ENGINE_API UClass* Z_Construct_UClass_UGameInstanceSubsystem();
ENGINE_API UClass* Z_Construct_UClass_UTexture2D_NoRegister();
SLT_API UClass* Z_Construct_UClass_ULoadingManager();
SLT_API UClass* Z_Construct_UClass_ULoadingManager_NoRegister();
SLT_API UEnum* Z_Construct_UEnum_SLT_EAssetPriority();
SLT_API UEnum* Z_Construct_UEnum_SLT_ELoadingState();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FAssetLoadRequest();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FLoadingProgress();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FLoadingTip();
UMG_API UClass* Z_Construct_UClass_UUserWidget_NoRegister();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Enum ELoadingState
static FEnumRegistrationInfo Z_Registration_Info_UEnum_ELoadingState;
static UEnum* ELoadingState_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_ELoadingState.OuterSingleton)
	{
		Z_Registration_Info_UEnum_ELoadingState.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_ELoadingState, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ELoadingState"));
	}
	return Z_Registration_Info_UEnum_ELoadingState.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<ELoadingState>()
{
	return ELoadingState_StaticEnum();
}
struct Z_Construct_UEnum_SLT_ELoadingState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "Complete.DisplayName", "Complete" },
		{ "Complete.Name", "ELoadingState::Complete" },
		{ "Failed.DisplayName", "Failed" },
		{ "Failed.Name", "ELoadingState::Failed" },
		{ "Finalizing.DisplayName", "Finalizing" },
		{ "Finalizing.Name", "ELoadingState::Finalizing" },
		{ "Idle.DisplayName", "Idle" },
		{ "Idle.Name", "ELoadingState::Idle" },
		{ "Loading.DisplayName", "Loading" },
		{ "Loading.Name", "ELoadingState::Loading" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
		{ "Preparing.DisplayName", "Preparing" },
		{ "Preparing.Name", "ELoadingState::Preparing" },
		{ "Streaming.DisplayName", "Streaming" },
		{ "Streaming.Name", "ELoadingState::Streaming" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "ELoadingState::Idle", (int64)ELoadingState::Idle },
		{ "ELoadingState::Preparing", (int64)ELoadingState::Preparing },
		{ "ELoadingState::Loading", (int64)ELoadingState::Loading },
		{ "ELoadingState::Streaming", (int64)ELoadingState::Streaming },
		{ "ELoadingState::Finalizing", (int64)ELoadingState::Finalizing },
		{ "ELoadingState::Complete", (int64)ELoadingState::Complete },
		{ "ELoadingState::Failed", (int64)ELoadingState::Failed },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_ELoadingState_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"ELoadingState",
	"ELoadingState",
	Z_Construct_UEnum_SLT_ELoadingState_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ELoadingState_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_ELoadingState_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_ELoadingState_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_ELoadingState()
{
	if (!Z_Registration_Info_UEnum_ELoadingState.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_ELoadingState.InnerSingleton, Z_Construct_UEnum_SLT_ELoadingState_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_ELoadingState.InnerSingleton;
}
// End Enum ELoadingState

// Begin Enum EAssetPriority
static FEnumRegistrationInfo Z_Registration_Info_UEnum_EAssetPriority;
static UEnum* EAssetPriority_StaticEnum()
{
	if (!Z_Registration_Info_UEnum_EAssetPriority.OuterSingleton)
	{
		Z_Registration_Info_UEnum_EAssetPriority.OuterSingleton = GetStaticEnum(Z_Construct_UEnum_SLT_EAssetPriority, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("EAssetPriority"));
	}
	return Z_Registration_Info_UEnum_EAssetPriority.OuterSingleton;
}
template<> SLT_API UEnum* StaticEnum<EAssetPriority>()
{
	return EAssetPriority_StaticEnum();
}
struct Z_Construct_UEnum_SLT_EAssetPriority_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Enum_MetaDataParams[] = {
		{ "Background.DisplayName", "Background" },
		{ "Background.Name", "EAssetPriority::Background" },
		{ "BlueprintType", "true" },
		{ "Critical.DisplayName", "Critical" },
		{ "Critical.Name", "EAssetPriority::Critical" },
		{ "High.DisplayName", "High" },
		{ "High.Name", "EAssetPriority::High" },
		{ "Low.DisplayName", "Low" },
		{ "Low.Name", "EAssetPriority::Low" },
		{ "Medium.DisplayName", "Medium" },
		{ "Medium.Name", "EAssetPriority::Medium" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static constexpr UECodeGen_Private::FEnumeratorParam Enumerators[] = {
		{ "EAssetPriority::Critical", (int64)EAssetPriority::Critical },
		{ "EAssetPriority::High", (int64)EAssetPriority::High },
		{ "EAssetPriority::Medium", (int64)EAssetPriority::Medium },
		{ "EAssetPriority::Low", (int64)EAssetPriority::Low },
		{ "EAssetPriority::Background", (int64)EAssetPriority::Background },
	};
	static const UECodeGen_Private::FEnumParams EnumParams;
};
const UECodeGen_Private::FEnumParams Z_Construct_UEnum_SLT_EAssetPriority_Statics::EnumParams = {
	(UObject*(*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	"EAssetPriority",
	"EAssetPriority",
	Z_Construct_UEnum_SLT_EAssetPriority_Statics::Enumerators,
	RF_Public|RF_Transient|RF_MarkAsNative,
	UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAssetPriority_Statics::Enumerators),
	EEnumFlags::None,
	(uint8)UEnum::ECppForm::EnumClass,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UEnum_SLT_EAssetPriority_Statics::Enum_MetaDataParams), Z_Construct_UEnum_SLT_EAssetPriority_Statics::Enum_MetaDataParams)
};
UEnum* Z_Construct_UEnum_SLT_EAssetPriority()
{
	if (!Z_Registration_Info_UEnum_EAssetPriority.InnerSingleton)
	{
		UECodeGen_Private::ConstructUEnum(Z_Registration_Info_UEnum_EAssetPriority.InnerSingleton, Z_Construct_UEnum_SLT_EAssetPriority_Statics::EnumParams);
	}
	return Z_Registration_Info_UEnum_EAssetPriority.InnerSingleton;
}
// End Enum EAssetPriority

// Begin ScriptStruct FLoadingTip
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LoadingTip;
class UScriptStruct* FLoadingTip::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingTip.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LoadingTip.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLoadingTip, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("LoadingTip"));
	}
	return Z_Registration_Info_UScriptStruct_LoadingTip.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FLoadingTip>()
{
	return FLoadingTip::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLoadingTip_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TipText_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsContextual_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinDisplayTime_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TipImage_MetaData[] = {
		{ "Category", "Tip" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FTextPropertyParams NewProp_TipText;
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static void NewProp_bIsContextual_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsContextual;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinDisplayTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_TipImage;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLoadingTip>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FTextPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_TipText = { "TipText", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Text, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingTip, TipText), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TipText_MetaData), NewProp_TipText_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingTip, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
void Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_bIsContextual_SetBit(void* Obj)
{
	((FLoadingTip*)Obj)->bIsContextual = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_bIsContextual = { "bIsContextual", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FLoadingTip), &Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_bIsContextual_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsContextual_MetaData), NewProp_bIsContextual_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_MinDisplayTime = { "MinDisplayTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingTip, MinDisplayTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinDisplayTime_MetaData), NewProp_MinDisplayTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingTip, Priority), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) };
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_TipImage = { "TipImage", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingTip, TipImage), Z_Construct_UClass_UTexture2D_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TipImage_MetaData), NewProp_TipImage_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLoadingTip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_TipText,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_bIsContextual,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_MinDisplayTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingTip_Statics::NewProp_TipImage,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingTip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLoadingTip_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"LoadingTip",
	Z_Construct_UScriptStruct_FLoadingTip_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingTip_Statics::PropPointers),
	sizeof(FLoadingTip),
	alignof(FLoadingTip),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingTip_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLoadingTip_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLoadingTip()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingTip.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LoadingTip.InnerSingleton, Z_Construct_UScriptStruct_FLoadingTip_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LoadingTip.InnerSingleton;
}
// End ScriptStruct FLoadingTip

// Begin ScriptStruct FAssetLoadRequest
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_AssetLoadRequest;
class UScriptStruct* FAssetLoadRequest::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FAssetLoadRequest, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("AssetLoadRequest"));
	}
	return Z_Registration_Info_UScriptStruct_AssetLoadRequest.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FAssetLoadRequest>()
{
	return FAssetLoadRequest::StaticStruct();
}
struct Z_Construct_UScriptStruct_FAssetLoadRequest_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Priority_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsBlocking_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TimeoutSeconds_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RetryCount_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPreloadDependencies_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_RequestID_MetaData[] = {
		{ "Category", "Request" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static void NewProp_bIsBlocking_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsBlocking;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_TimeoutSeconds;
	static const UECodeGen_Private::FIntPropertyParams NewProp_RetryCount;
	static void NewProp_bPreloadDependencies_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPreloadDependencies;
	static const UECodeGen_Private::FStrPropertyParams NewProp_RequestID;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FAssetLoadRequest>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, Priority), Z_Construct_UEnum_SLT_EAssetPriority, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Priority_MetaData), NewProp_Priority_MetaData) }; // 3646486783
void Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsBlocking_SetBit(void* Obj)
{
	((FAssetLoadRequest*)Obj)->bIsBlocking = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsBlocking = { "bIsBlocking", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAssetLoadRequest), &Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsBlocking_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsBlocking_MetaData), NewProp_bIsBlocking_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_TimeoutSeconds = { "TimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, TimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TimeoutSeconds_MetaData), NewProp_TimeoutSeconds_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_RetryCount = { "RetryCount", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, RetryCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RetryCount_MetaData), NewProp_RetryCount_MetaData) };
void Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bPreloadDependencies_SetBit(void* Obj)
{
	((FAssetLoadRequest*)Obj)->bPreloadDependencies = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bPreloadDependencies = { "bPreloadDependencies", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FAssetLoadRequest), &Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bPreloadDependencies_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPreloadDependencies_MetaData), NewProp_bPreloadDependencies_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_RequestID = { "RequestID", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FAssetLoadRequest, RequestID), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_RequestID_MetaData), NewProp_RequestID_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_AssetPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_Priority,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bIsBlocking,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_TimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_RetryCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_bPreloadDependencies,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewProp_RequestID,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"AssetLoadRequest",
	Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::PropPointers),
	sizeof(FAssetLoadRequest),
	alignof(FAssetLoadRequest),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FAssetLoadRequest()
{
	if (!Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton, Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_AssetLoadRequest.InnerSingleton;
}
// End ScriptStruct FAssetLoadRequest

// Begin ScriptStruct FLoadingProgress
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_LoadingProgress;
class UScriptStruct* FLoadingProgress::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingProgress.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_LoadingProgress.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FLoadingProgress, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("LoadingProgress"));
	}
	return Z_Registration_Info_UScriptStruct_LoadingProgress.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FLoadingProgress>()
{
	return FLoadingProgress::StaticStruct();
}
struct Z_Construct_UScriptStruct_FLoadingProgress_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentState_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OverallProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_StreamingProgress_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadedAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_TotalAssets_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentAssetName_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EstimatedTimeRemaining_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingStartTime_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingMessages_MetaData[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_CurrentState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_CurrentState;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_OverallProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AssetProgress;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_StreamingProgress;
	static const UECodeGen_Private::FIntPropertyParams NewProp_LoadedAssets;
	static const UECodeGen_Private::FIntPropertyParams NewProp_TotalAssets;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentAssetName;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_EstimatedTimeRemaining;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LoadingStartTime;
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoadingMessages_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LoadingMessages;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FLoadingProgress>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentState = { "CurrentState", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, CurrentState), Z_Construct_UEnum_SLT_ELoadingState, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentState_MetaData), NewProp_CurrentState_MetaData) }; // 353074094
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_OverallProgress = { "OverallProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, OverallProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OverallProgress_MetaData), NewProp_OverallProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_AssetProgress = { "AssetProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, AssetProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetProgress_MetaData), NewProp_AssetProgress_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_StreamingProgress = { "StreamingProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, StreamingProgress), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_StreamingProgress_MetaData), NewProp_StreamingProgress_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadedAssets = { "LoadedAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, LoadedAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadedAssets_MetaData), NewProp_LoadedAssets_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_TotalAssets = { "TotalAssets", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, TotalAssets), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_TotalAssets_MetaData), NewProp_TotalAssets_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentAssetName = { "CurrentAssetName", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, CurrentAssetName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentAssetName_MetaData), NewProp_CurrentAssetName_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_EstimatedTimeRemaining = { "EstimatedTimeRemaining", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, EstimatedTimeRemaining), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EstimatedTimeRemaining_MetaData), NewProp_EstimatedTimeRemaining_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingStartTime = { "LoadingStartTime", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, LoadingStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingStartTime_MetaData), NewProp_LoadingStartTime_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingMessages_Inner = { "LoadingMessages", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingMessages = { "LoadingMessages", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FLoadingProgress, LoadingMessages), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingMessages_MetaData), NewProp_LoadingMessages_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FLoadingProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentState,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_OverallProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_AssetProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_StreamingProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadedAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_TotalAssets,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_CurrentAssetName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_EstimatedTimeRemaining,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingMessages_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewProp_LoadingMessages,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FLoadingProgress_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"LoadingProgress",
	Z_Construct_UScriptStruct_FLoadingProgress_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingProgress_Statics::PropPointers),
	sizeof(FLoadingProgress),
	alignof(FLoadingProgress),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FLoadingProgress_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FLoadingProgress_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FLoadingProgress()
{
	if (!Z_Registration_Info_UScriptStruct_LoadingProgress.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_LoadingProgress.InnerSingleton, Z_Construct_UScriptStruct_FLoadingProgress_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_LoadingProgress.InnerSingleton;
}
// End ScriptStruct FLoadingProgress

// Begin Delegate FOnLoadingStateChanged
struct Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLoadingStateChanged_Parms
	{
		ELoadingState NewState;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FBytePropertyParams NewProp_NewState_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_NewState;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FBytePropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::NewProp_NewState = { "NewState", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLoadingStateChanged_Parms, NewState), Z_Construct_UEnum_SLT_ELoadingState, METADATA_PARAMS(0, nullptr) }; // 353074094
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::NewProp_NewState_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::NewProp_NewState,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLoadingStateChanged__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnLoadingStateChanged_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::_Script_SLT_eventOnLoadingStateChanged_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingStateChanged_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingStateChanged, ELoadingState NewState)
{
	struct _Script_SLT_eventOnLoadingStateChanged_Parms
	{
		ELoadingState NewState;
	};
	_Script_SLT_eventOnLoadingStateChanged_Parms Parms;
	Parms.NewState=NewState;
	OnLoadingStateChanged.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLoadingStateChanged

// Begin Delegate FOnLoadingProgressUpdated
struct Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLoadingProgressUpdated_Parms
	{
		FLoadingProgress Progress;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Progress_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_Progress;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::NewProp_Progress = { "Progress", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLoadingProgressUpdated_Parms, Progress), Z_Construct_UScriptStruct_FLoadingProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Progress_MetaData), NewProp_Progress_MetaData) }; // 926561320
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::NewProp_Progress,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLoadingProgressUpdated__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::_Script_SLT_eventOnLoadingProgressUpdated_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::_Script_SLT_eventOnLoadingProgressUpdated_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingProgressUpdated_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingProgressUpdated, FLoadingProgress const& Progress)
{
	struct _Script_SLT_eventOnLoadingProgressUpdated_Parms
	{
		FLoadingProgress Progress;
	};
	_Script_SLT_eventOnLoadingProgressUpdated_Parms Parms;
	Parms.Progress=Progress;
	OnLoadingProgressUpdated.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLoadingProgressUpdated

// Begin Delegate FOnAssetLoaded
struct Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnAssetLoaded_Parms
	{
		FString AssetPath;
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPath;
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnAssetLoaded_Parms, AssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
void Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_SLT_eventOnAssetLoaded_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnAssetLoaded_Parms), &Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_AssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnAssetLoaded__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::_Script_SLT_eventOnAssetLoaded_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::_Script_SLT_eventOnAssetLoaded_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnAssetLoaded_DelegateWrapper(const FMulticastScriptDelegate& OnAssetLoaded, const FString& AssetPath, bool bSuccess)
{
	struct _Script_SLT_eventOnAssetLoaded_Parms
	{
		FString AssetPath;
		bool bSuccess;
	};
	_Script_SLT_eventOnAssetLoaded_Parms Parms;
	Parms.AssetPath=AssetPath;
	Parms.bSuccess=bSuccess ? true : false;
	OnAssetLoaded.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnAssetLoaded

// Begin Delegate FOnLoadingComplete
struct Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLoadingComplete_Parms
	{
		bool bSuccess;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bSuccess_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bSuccess;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit(void* Obj)
{
	((_Script_SLT_eventOnLoadingComplete_Parms*)Obj)->bSuccess = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::NewProp_bSuccess = { "bSuccess", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnLoadingComplete_Parms), &Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::NewProp_bSuccess_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::NewProp_bSuccess,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLoadingComplete__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::_Script_SLT_eventOnLoadingComplete_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::_Script_SLT_eventOnLoadingComplete_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingComplete_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingComplete, bool bSuccess)
{
	struct _Script_SLT_eventOnLoadingComplete_Parms
	{
		bool bSuccess;
	};
	_Script_SLT_eventOnLoadingComplete_Parms Parms;
	Parms.bSuccess=bSuccess ? true : false;
	OnLoadingComplete.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLoadingComplete

// Begin Delegate FOnLoadingError
struct Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnLoadingError_Parms
	{
		FString ErrorMessage;
		FString AssetPath;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ErrorMessage_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ErrorMessage;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::NewProp_ErrorMessage = { "ErrorMessage", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLoadingError_Parms, ErrorMessage), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ErrorMessage_MetaData), NewProp_ErrorMessage_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnLoadingError_Parms, AssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::NewProp_ErrorMessage,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::NewProp_AssetPath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnLoadingError__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::_Script_SLT_eventOnLoadingError_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::_Script_SLT_eventOnLoadingError_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnLoadingError_DelegateWrapper(const FMulticastScriptDelegate& OnLoadingError, const FString& ErrorMessage, const FString& AssetPath)
{
	struct _Script_SLT_eventOnLoadingError_Parms
	{
		FString ErrorMessage;
		FString AssetPath;
	};
	_Script_SLT_eventOnLoadingError_Parms Parms;
	Parms.ErrorMessage=ErrorMessage;
	Parms.AssetPath=AssetPath;
	OnLoadingError.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnLoadingError

// Begin Class ULoadingManager Function AddLoadingMessage
struct Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics
{
	struct LoadingManager_eventAddLoadingMessage_Parms
	{
		FString Message;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Message_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Message;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::NewProp_Message = { "Message", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventAddLoadingMessage_Parms, Message), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Message_MetaData), NewProp_Message_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::NewProp_Message,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "AddLoadingMessage", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::LoadingManager_eventAddLoadingMessage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::LoadingManager_eventAddLoadingMessage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_AddLoadingMessage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_AddLoadingMessage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execAddLoadingMessage)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Message);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->AddLoadingMessage(Z_Param_Message);
	P_NATIVE_END;
}
// End Class ULoadingManager Function AddLoadingMessage

// Begin Class ULoadingManager Function ClearAssetCache
struct Z_Construct_UFunction_ULoadingManager_ClearAssetCache_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_ClearAssetCache_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "ClearAssetCache", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_ClearAssetCache_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_ClearAssetCache_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_ClearAssetCache()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_ClearAssetCache_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execClearAssetCache)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAssetCache();
	P_NATIVE_END;
}
// End Class ULoadingManager Function ClearAssetCache

// Begin Class ULoadingManager Function GetEstimatedTimeRemaining
struct Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics
{
	struct LoadingManager_eventGetEstimatedTimeRemaining_Parms
	{
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventGetEstimatedTimeRemaining_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "GetEstimatedTimeRemaining", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::LoadingManager_eventGetEstimatedTimeRemaining_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::LoadingManager_eventGetEstimatedTimeRemaining_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execGetEstimatedTimeRemaining)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetEstimatedTimeRemaining();
	P_NATIVE_END;
}
// End Class ULoadingManager Function GetEstimatedTimeRemaining

// Begin Class ULoadingManager Function GetMemoryUsage
struct Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics
{
	struct LoadingManager_eventGetMemoryUsage_Parms
	{
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventGetMemoryUsage_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "GetMemoryUsage", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::LoadingManager_eventGetMemoryUsage_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::LoadingManager_eventGetMemoryUsage_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_GetMemoryUsage()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_GetMemoryUsage_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execGetMemoryUsage)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetMemoryUsage();
	P_NATIVE_END;
}
// End Class ULoadingManager Function GetMemoryUsage

// Begin Class ULoadingManager Function GetRandomLoadingTip
struct Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics
{
	struct LoadingManager_eventGetRandomLoadingTip_Parms
	{
		FString Category;
		FLoadingTip ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading Screen" },
		{ "CPP_Default_Category", "" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Category_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_Category;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::NewProp_Category = { "Category", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventGetRandomLoadingTip_Parms, Category), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Category_MetaData), NewProp_Category_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventGetRandomLoadingTip_Parms, ReturnValue), Z_Construct_UScriptStruct_FLoadingTip, METADATA_PARAMS(0, nullptr) }; // 2669823834
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::NewProp_Category,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "GetRandomLoadingTip", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::LoadingManager_eventGetRandomLoadingTip_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::LoadingManager_eventGetRandomLoadingTip_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execGetRandomLoadingTip)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_Category);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FLoadingTip*)Z_Param__Result=P_THIS->GetRandomLoadingTip(Z_Param_Category);
	P_NATIVE_END;
}
// End Class ULoadingManager Function GetRandomLoadingTip

// Begin Class ULoadingManager Function HideLoadingScreen
struct Z_Construct_UFunction_ULoadingManager_HideLoadingScreen_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading Screen" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_HideLoadingScreen_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "HideLoadingScreen", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_HideLoadingScreen_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_HideLoadingScreen_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_HideLoadingScreen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_HideLoadingScreen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execHideLoadingScreen)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->HideLoadingScreen();
	P_NATIVE_END;
}
// End Class ULoadingManager Function HideLoadingScreen

// Begin Class ULoadingManager Function IsCurrentlyLoading
struct Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics
{
	struct LoadingManager_eventIsCurrentlyLoading_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LoadingManager_eventIsCurrentlyLoading_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LoadingManager_eventIsCurrentlyLoading_Parms), &Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "IsCurrentlyLoading", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::LoadingManager_eventIsCurrentlyLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::LoadingManager_eventIsCurrentlyLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execIsCurrentlyLoading)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsCurrentlyLoading();
	P_NATIVE_END;
}
// End Class ULoadingManager Function IsCurrentlyLoading

// Begin Class ULoadingManager Function IsLevelStreamed
struct Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics
{
	struct LoadingManager_eventIsLevelStreamed_Parms
	{
		FString LevelName;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelName;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventIsLevelStreamed_Parms, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
void Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((LoadingManager_eventIsLevelStreamed_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LoadingManager_eventIsLevelStreamed_Parms), &Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_LevelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "IsLevelStreamed", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::LoadingManager_eventIsLevelStreamed_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::LoadingManager_eventIsLevelStreamed_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_IsLevelStreamed()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_IsLevelStreamed_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execIsLevelStreamed)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LevelName);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsLevelStreamed(Z_Param_LevelName);
	P_NATIVE_END;
}
// End Class ULoadingManager Function IsLevelStreamed

// Begin Class ULoadingManager Function LoadAssetAsync
struct Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics
{
	struct FTopLevelAssetPath
	{
		FName PackageName;
		FName AssetName;
	};

	struct LoadingManager_eventLoadAssetAsync_Parms
	{
		FSoftObjectPath AssetPath;
		EAssetPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "CPP_Default_Priority", "Medium" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssetAsync_Parms, AssetPath), Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssetAsync_Parms, Priority), Z_Construct_UEnum_SLT_EAssetPriority, METADATA_PARAMS(0, nullptr) }; // 3646486783
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_AssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "LoadAssetAsync", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::LoadingManager_eventLoadAssetAsync_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::LoadingManager_eventLoadAssetAsync_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_LoadAssetAsync()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_LoadAssetAsync_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execLoadAssetAsync)
{
	P_GET_STRUCT_REF(FSoftObjectPath,Z_Param_Out_AssetPath);
	P_GET_ENUM(EAssetPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssetAsync(Z_Param_Out_AssetPath,EAssetPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// End Class ULoadingManager Function LoadAssetAsync

// Begin Class ULoadingManager Function LoadAssetBundle
struct Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics
{
	struct LoadingManager_eventLoadAssetBundle_Parms
	{
		FString BundleName;
		EAssetPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset loading functions\n" },
#endif
		{ "CPP_Default_Priority", "Medium" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset loading functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_BundleName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_BundleName;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_BundleName = { "BundleName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssetBundle_Parms, BundleName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_BundleName_MetaData), NewProp_BundleName_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssetBundle_Parms, Priority), Z_Construct_UEnum_SLT_EAssetPriority, METADATA_PARAMS(0, nullptr) }; // 3646486783
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_BundleName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "LoadAssetBundle", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::LoadingManager_eventLoadAssetBundle_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::LoadingManager_eventLoadAssetBundle_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_LoadAssetBundle()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_LoadAssetBundle_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execLoadAssetBundle)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_BundleName);
	P_GET_ENUM(EAssetPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssetBundle(Z_Param_BundleName,EAssetPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// End Class ULoadingManager Function LoadAssetBundle

// Begin Class ULoadingManager Function LoadAssets
struct Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics
{
	struct FTopLevelAssetPath
	{
		FName PackageName;
		FName AssetName;
	};

	struct LoadingManager_eventLoadAssets_Parms
	{
		TArray<FSoftObjectPath> AssetPaths;
		EAssetPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "CPP_Default_Priority", "Medium" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssets_Parms, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventLoadAssets_Parms, Priority), Z_Construct_UEnum_SLT_EAssetPriority, METADATA_PARAMS(0, nullptr) }; // 3646486783
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_AssetPaths,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "LoadAssets", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::LoadingManager_eventLoadAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::LoadingManager_eventLoadAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_LoadAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_LoadAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execLoadAssets)
{
	P_GET_TARRAY_REF(FSoftObjectPath,Z_Param_Out_AssetPaths);
	P_GET_ENUM(EAssetPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->LoadAssets(Z_Param_Out_AssetPaths,EAssetPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// End Class ULoadingManager Function LoadAssets

// Begin Class ULoadingManager Function OnAssetLoadStarted
struct LoadingManager_eventOnAssetLoadStarted_Parms
{
	FString AssetPath;
};
static const FName NAME_ULoadingManager_OnAssetLoadStarted = FName(TEXT("OnAssetLoadStarted"));
void ULoadingManager::OnAssetLoadStarted(const FString& AssetPath)
{
	LoadingManager_eventOnAssetLoadStarted_Parms Parms;
	Parms.AssetPath=AssetPath;
	UFunction* Func = FindFunctionChecked(NAME_ULoadingManager_OnAssetLoadStarted);
	ProcessEvent(Func,&Parms);
}
struct Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventOnAssetLoadStarted_Parms, AssetPath), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::NewProp_AssetPath,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "OnAssetLoadStarted", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::PropPointers), sizeof(LoadingManager_eventOnAssetLoadStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::Function_MetaDataParams) };
static_assert(sizeof(LoadingManager_eventOnAssetLoadStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ULoadingManager Function OnAssetLoadStarted

// Begin Class ULoadingManager Function OnLoadingScreenHidden
static const FName NAME_ULoadingManager_OnLoadingScreenHidden = FName(TEXT("OnLoadingScreenHidden"));
void ULoadingManager::OnLoadingScreenHidden()
{
	UFunction* Func = FindFunctionChecked(NAME_ULoadingManager_OnLoadingScreenHidden);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "OnLoadingScreenHidden", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ULoadingManager Function OnLoadingScreenHidden

// Begin Class ULoadingManager Function OnLoadingScreenShown
static const FName NAME_ULoadingManager_OnLoadingScreenShown = FName(TEXT("OnLoadingScreenShown"));
void ULoadingManager::OnLoadingScreenShown()
{
	UFunction* Func = FindFunctionChecked(NAME_ULoadingManager_OnLoadingScreenShown);
	ProcessEvent(Func,NULL);
}
struct Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Blueprint events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Blueprint events" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "OnLoadingScreenShown", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x08080800, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown_Statics::FuncParams);
	}
	return ReturnFunction;
}
// End Class ULoadingManager Function OnLoadingScreenShown

// Begin Class ULoadingManager Function OptimizeMemory
struct Z_Construct_UFunction_ULoadingManager_OptimizeMemory_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_OptimizeMemory_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "OptimizeMemory", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_OptimizeMemory_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_OptimizeMemory_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_OptimizeMemory()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_OptimizeMemory_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execOptimizeMemory)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OptimizeMemory();
	P_NATIVE_END;
}
// End Class ULoadingManager Function OptimizeMemory

// Begin Class ULoadingManager Function PreloadAssets
struct Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics
{
	struct FTopLevelAssetPath
	{
		FName PackageName;
		FName AssetName;
	};

	struct LoadingManager_eventPreloadAssets_Parms
	{
		TArray<FSoftObjectPath> AssetPaths;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventPreloadAssets_Parms, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::NewProp_AssetPaths,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "PreloadAssets", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::LoadingManager_eventPreloadAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::LoadingManager_eventPreloadAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_PreloadAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_PreloadAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execPreloadAssets)
{
	P_GET_TARRAY_REF(FSoftObjectPath,Z_Param_Out_AssetPaths);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PreloadAssets(Z_Param_Out_AssetPaths);
	P_NATIVE_END;
}
// End Class ULoadingManager Function PreloadAssets

// Begin Class ULoadingManager Function SetAssetPriority
struct Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics
{
	struct FTopLevelAssetPath
	{
		FName PackageName;
		FName AssetName;
	};

	struct LoadingManager_eventSetAssetPriority_Parms
	{
		FSoftObjectPath AssetPath;
		EAssetPriority Priority;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Utility" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Utility functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Utility functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPath_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPath;
	static const UECodeGen_Private::FBytePropertyParams NewProp_Priority_Underlying;
	static const UECodeGen_Private::FEnumPropertyParams NewProp_Priority;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_AssetPath = { "AssetPath", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventSetAssetPriority_Parms, AssetPath), Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPath_MetaData), NewProp_AssetPath_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_Priority_Underlying = { "UnderlyingType", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, nullptr, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FEnumPropertyParams Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_Priority = { "Priority", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Enum, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventSetAssetPriority_Parms, Priority), Z_Construct_UEnum_SLT_EAssetPriority, METADATA_PARAMS(0, nullptr) }; // 3646486783
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_AssetPath,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_Priority_Underlying,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::NewProp_Priority,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "SetAssetPriority", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::LoadingManager_eventSetAssetPriority_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::LoadingManager_eventSetAssetPriority_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_SetAssetPriority()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_SetAssetPriority_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execSetAssetPriority)
{
	P_GET_STRUCT_REF(FSoftObjectPath,Z_Param_Out_AssetPath);
	P_GET_ENUM(EAssetPriority,Z_Param_Priority);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetAssetPriority(Z_Param_Out_AssetPath,EAssetPriority(Z_Param_Priority));
	P_NATIVE_END;
}
// End Class ULoadingManager Function SetAssetPriority

// Begin Class ULoadingManager Function ShowLoadingScreen
struct Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading Screen" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Loading screen functions\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading screen functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "ShowLoadingScreen", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execShowLoadingScreen)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ShowLoadingScreen();
	P_NATIVE_END;
}
// End Class ULoadingManager Function ShowLoadingScreen

// Begin Class ULoadingManager Function StartLoading
struct Z_Construct_UFunction_ULoadingManager_StartLoading_Statics
{
	struct LoadingManager_eventStartLoading_Parms
	{
		FString LoadingContext;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main loading functions\n" },
#endif
		{ "CPP_Default_LoadingContext", "" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main loading functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingContext_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LoadingContext;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::NewProp_LoadingContext = { "LoadingContext", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventStartLoading_Parms, LoadingContext), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingContext_MetaData), NewProp_LoadingContext_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::NewProp_LoadingContext,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "StartLoading", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::LoadingManager_eventStartLoading_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::LoadingManager_eventStartLoading_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_StartLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_StartLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execStartLoading)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LoadingContext);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartLoading(Z_Param_LoadingContext);
	P_NATIVE_END;
}
// End Class ULoadingManager Function StartLoading

// Begin Class ULoadingManager Function StopLoading
struct Z_Construct_UFunction_ULoadingManager_StopLoading_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Loading" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_StopLoading_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "StopLoading", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StopLoading_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_StopLoading_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_ULoadingManager_StopLoading()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_StopLoading_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execStopLoading)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StopLoading();
	P_NATIVE_END;
}
// End Class ULoadingManager Function StopLoading

// Begin Class ULoadingManager Function StreamLevel
struct Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics
{
	struct LoadingManager_eventStreamLevel_Parms
	{
		FString LevelName;
		bool bShouldBlock;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Level streaming functions\n" },
#endif
		{ "CPP_Default_bShouldBlock", "false" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Level streaming functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelName;
	static void NewProp_bShouldBlock_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShouldBlock;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventStreamLevel_Parms, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
void Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_bShouldBlock_SetBit(void* Obj)
{
	((LoadingManager_eventStreamLevel_Parms*)Obj)->bShouldBlock = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_bShouldBlock = { "bShouldBlock", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(LoadingManager_eventStreamLevel_Parms), &Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_bShouldBlock_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_LevelName,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::NewProp_bShouldBlock,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "StreamLevel", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::LoadingManager_eventStreamLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::LoadingManager_eventStreamLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_StreamLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_StreamLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execStreamLevel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LevelName);
	P_GET_UBOOL(Z_Param_bShouldBlock);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StreamLevel(Z_Param_LevelName,Z_Param_bShouldBlock);
	P_NATIVE_END;
}
// End Class ULoadingManager Function StreamLevel

// Begin Class ULoadingManager Function UnloadAssets
struct Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics
{
	struct FTopLevelAssetPath
	{
		FName PackageName;
		FName AssetName;
	};

	struct LoadingManager_eventUnloadAssets_Parms
	{
		TArray<FSoftObjectPath> AssetPaths;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Assets" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetPaths_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_AssetPaths_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetPaths;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::NewProp_AssetPaths_Inner = { "AssetPaths", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FSoftObjectPath, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::NewProp_AssetPaths = { "AssetPaths", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventUnloadAssets_Parms, AssetPaths), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetPaths_MetaData), NewProp_AssetPaths_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::NewProp_AssetPaths_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::NewProp_AssetPaths,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "UnloadAssets", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::LoadingManager_eventUnloadAssets_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::LoadingManager_eventUnloadAssets_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_UnloadAssets()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_UnloadAssets_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execUnloadAssets)
{
	P_GET_TARRAY_REF(FSoftObjectPath,Z_Param_Out_AssetPaths);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnloadAssets(Z_Param_Out_AssetPaths);
	P_NATIVE_END;
}
// End Class ULoadingManager Function UnloadAssets

// Begin Class ULoadingManager Function UnstreamLevel
struct Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics
{
	struct LoadingManager_eventUnstreamLevel_Parms
	{
		FString LevelName;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Streaming" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LevelName_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_LevelName;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::NewProp_LevelName = { "LevelName", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventUnstreamLevel_Parms, LevelName), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LevelName_MetaData), NewProp_LevelName_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::NewProp_LevelName,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "UnstreamLevel", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::LoadingManager_eventUnstreamLevel_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::LoadingManager_eventUnstreamLevel_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_UnstreamLevel()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_UnstreamLevel_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execUnstreamLevel)
{
	P_GET_PROPERTY(FStrProperty,Z_Param_LevelName);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UnstreamLevel(Z_Param_LevelName);
	P_NATIVE_END;
}
// End Class ULoadingManager Function UnstreamLevel

// Begin Class ULoadingManager Function UpdateProgress
struct Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics
{
	struct LoadingManager_eventUpdateProgress_Parms
	{
		float NewProgress;
		FString CurrentTask;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Progress" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Progress functions\n" },
#endif
		{ "CPP_Default_CurrentTask", "" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Progress functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentTask_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_NewProgress;
	static const UECodeGen_Private::FStrPropertyParams NewProp_CurrentTask;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::NewProp_NewProgress = { "NewProgress", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventUpdateProgress_Parms, NewProgress), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::NewProp_CurrentTask = { "CurrentTask", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(LoadingManager_eventUpdateProgress_Parms, CurrentTask), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentTask_MetaData), NewProp_CurrentTask_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::NewProp_NewProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::NewProp_CurrentTask,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_ULoadingManager, nullptr, "UpdateProgress", nullptr, nullptr, Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::PropPointers), sizeof(Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::LoadingManager_eventUpdateProgress_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::Function_MetaDataParams), Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::LoadingManager_eventUpdateProgress_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_ULoadingManager_UpdateProgress()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_ULoadingManager_UpdateProgress_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(ULoadingManager::execUpdateProgress)
{
	P_GET_PROPERTY(FFloatProperty,Z_Param_NewProgress);
	P_GET_PROPERTY(FStrProperty,Z_Param_CurrentTask);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->UpdateProgress(Z_Param_NewProgress,Z_Param_CurrentTask);
	P_NATIVE_END;
}
// End Class ULoadingManager Function UpdateProgress

// Begin Class ULoadingManager
void ULoadingManager::StaticRegisterNativesULoadingManager()
{
	UClass* Class = ULoadingManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "AddLoadingMessage", &ULoadingManager::execAddLoadingMessage },
		{ "ClearAssetCache", &ULoadingManager::execClearAssetCache },
		{ "GetEstimatedTimeRemaining", &ULoadingManager::execGetEstimatedTimeRemaining },
		{ "GetMemoryUsage", &ULoadingManager::execGetMemoryUsage },
		{ "GetRandomLoadingTip", &ULoadingManager::execGetRandomLoadingTip },
		{ "HideLoadingScreen", &ULoadingManager::execHideLoadingScreen },
		{ "IsCurrentlyLoading", &ULoadingManager::execIsCurrentlyLoading },
		{ "IsLevelStreamed", &ULoadingManager::execIsLevelStreamed },
		{ "LoadAssetAsync", &ULoadingManager::execLoadAssetAsync },
		{ "LoadAssetBundle", &ULoadingManager::execLoadAssetBundle },
		{ "LoadAssets", &ULoadingManager::execLoadAssets },
		{ "OptimizeMemory", &ULoadingManager::execOptimizeMemory },
		{ "PreloadAssets", &ULoadingManager::execPreloadAssets },
		{ "SetAssetPriority", &ULoadingManager::execSetAssetPriority },
		{ "ShowLoadingScreen", &ULoadingManager::execShowLoadingScreen },
		{ "StartLoading", &ULoadingManager::execStartLoading },
		{ "StopLoading", &ULoadingManager::execStopLoading },
		{ "StreamLevel", &ULoadingManager::execStreamLevel },
		{ "UnloadAssets", &ULoadingManager::execUnloadAssets },
		{ "UnstreamLevel", &ULoadingManager::execUnstreamLevel },
		{ "UpdateProgress", &ULoadingManager::execUpdateProgress },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(ULoadingManager);
UClass* Z_Construct_UClass_ULoadingManager_NoRegister()
{
	return ULoadingManager::StaticClass();
}
struct Z_Construct_UClass_ULoadingManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/LoadingManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingScreenWidgetClass_MetaData[] = {
		{ "Category", "Loading Screen" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Loading screen configuration\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Loading screen configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingTips_MetaData[] = {
		{ "Category", "Loading Screen" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MinLoadingScreenTime_MetaData[] = {
		{ "Category", "Loading Screen" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowProgressBar_MetaData[] = {
		{ "Category", "Loading Screen" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bShowLoadingTips_MetaData[] = {
		{ "Category", "Loading Screen" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetBundleNames_MetaData[] = {
		{ "Category", "Asset Bundling" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Asset bundling configuration\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Asset bundling configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxConcurrentLoads_MetaData[] = {
		{ "Category", "Asset Bundling" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_AssetTimeoutSeconds_MetaData[] = {
		{ "Category", "Asset Bundling" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentProgress_MetaData[] = {
		{ "Category", "State" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current state\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsLoading_MetaData[] = {
		{ "Category", "State" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingStateChanged_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingProgressUpdated_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnAssetLoaded_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingComplete_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnLoadingError_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingScreenWidget_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal state\n" },
#endif
		{ "EditInline", "true" },
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LoadingQueue_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/LoadingManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_LoadingScreenWidgetClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoadingTips_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LoadingTips;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MinLoadingScreenTime;
	static void NewProp_bShowProgressBar_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowProgressBar;
	static void NewProp_bShowLoadingTips_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bShowLoadingTips;
	static const UECodeGen_Private::FStrPropertyParams NewProp_AssetBundleNames_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_AssetBundleNames;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxConcurrentLoads;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_AssetTimeoutSeconds;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentProgress;
	static void NewProp_bIsLoading_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsLoading;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingStateChanged;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingProgressUpdated;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnAssetLoaded;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingComplete;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnLoadingError;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_LoadingScreenWidget;
	static const UECodeGen_Private::FStructPropertyParams NewProp_LoadingQueue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_LoadingQueue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_ULoadingManager_AddLoadingMessage, "AddLoadingMessage" }, // 3299782448
		{ &Z_Construct_UFunction_ULoadingManager_ClearAssetCache, "ClearAssetCache" }, // 3275188557
		{ &Z_Construct_UFunction_ULoadingManager_GetEstimatedTimeRemaining, "GetEstimatedTimeRemaining" }, // 1986086028
		{ &Z_Construct_UFunction_ULoadingManager_GetMemoryUsage, "GetMemoryUsage" }, // 631559288
		{ &Z_Construct_UFunction_ULoadingManager_GetRandomLoadingTip, "GetRandomLoadingTip" }, // 358568195
		{ &Z_Construct_UFunction_ULoadingManager_HideLoadingScreen, "HideLoadingScreen" }, // 1824214969
		{ &Z_Construct_UFunction_ULoadingManager_IsCurrentlyLoading, "IsCurrentlyLoading" }, // 66002754
		{ &Z_Construct_UFunction_ULoadingManager_IsLevelStreamed, "IsLevelStreamed" }, // 3038144426
		{ &Z_Construct_UFunction_ULoadingManager_LoadAssetAsync, "LoadAssetAsync" }, // 3798385501
		{ &Z_Construct_UFunction_ULoadingManager_LoadAssetBundle, "LoadAssetBundle" }, // 3333908870
		{ &Z_Construct_UFunction_ULoadingManager_LoadAssets, "LoadAssets" }, // 2381984917
		{ &Z_Construct_UFunction_ULoadingManager_OnAssetLoadStarted, "OnAssetLoadStarted" }, // 274049979
		{ &Z_Construct_UFunction_ULoadingManager_OnLoadingScreenHidden, "OnLoadingScreenHidden" }, // 1731537583
		{ &Z_Construct_UFunction_ULoadingManager_OnLoadingScreenShown, "OnLoadingScreenShown" }, // 3498147111
		{ &Z_Construct_UFunction_ULoadingManager_OptimizeMemory, "OptimizeMemory" }, // 3128363919
		{ &Z_Construct_UFunction_ULoadingManager_PreloadAssets, "PreloadAssets" }, // 1950691110
		{ &Z_Construct_UFunction_ULoadingManager_SetAssetPriority, "SetAssetPriority" }, // 3884098235
		{ &Z_Construct_UFunction_ULoadingManager_ShowLoadingScreen, "ShowLoadingScreen" }, // 352180413
		{ &Z_Construct_UFunction_ULoadingManager_StartLoading, "StartLoading" }, // 956762291
		{ &Z_Construct_UFunction_ULoadingManager_StopLoading, "StopLoading" }, // 1766097913
		{ &Z_Construct_UFunction_ULoadingManager_StreamLevel, "StreamLevel" }, // 408247363
		{ &Z_Construct_UFunction_ULoadingManager_UnloadAssets, "UnloadAssets" }, // 2081133503
		{ &Z_Construct_UFunction_ULoadingManager_UnstreamLevel, "UnstreamLevel" }, // 564967888
		{ &Z_Construct_UFunction_ULoadingManager_UpdateProgress, "UpdateProgress" }, // 790332426
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<ULoadingManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingScreenWidgetClass = { "LoadingScreenWidgetClass", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, LoadingScreenWidgetClass), Z_Construct_UClass_UClass, Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingScreenWidgetClass_MetaData), NewProp_LoadingScreenWidgetClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingTips_Inner = { "LoadingTips", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FLoadingTip, METADATA_PARAMS(0, nullptr) }; // 2669823834
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingTips = { "LoadingTips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, LoadingTips), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingTips_MetaData), NewProp_LoadingTips_MetaData) }; // 2669823834
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_MinLoadingScreenTime = { "MinLoadingScreenTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, MinLoadingScreenTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MinLoadingScreenTime_MetaData), NewProp_MinLoadingScreenTime_MetaData) };
void Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowProgressBar_SetBit(void* Obj)
{
	((ULoadingManager*)Obj)->bShowProgressBar = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowProgressBar = { "bShowProgressBar", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ULoadingManager), &Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowProgressBar_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowProgressBar_MetaData), NewProp_bShowProgressBar_MetaData) };
void Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowLoadingTips_SetBit(void* Obj)
{
	((ULoadingManager*)Obj)->bShowLoadingTips = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowLoadingTips = { "bShowLoadingTips", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ULoadingManager), &Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowLoadingTips_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bShowLoadingTips_MetaData), NewProp_bShowLoadingTips_MetaData) };
const UECodeGen_Private::FStrPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetBundleNames_Inner = { "AssetBundleNames", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetBundleNames = { "AssetBundleNames", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, AssetBundleNames), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetBundleNames_MetaData), NewProp_AssetBundleNames_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_MaxConcurrentLoads = { "MaxConcurrentLoads", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, MaxConcurrentLoads), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxConcurrentLoads_MetaData), NewProp_MaxConcurrentLoads_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetTimeoutSeconds = { "AssetTimeoutSeconds", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, AssetTimeoutSeconds), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_AssetTimeoutSeconds_MetaData), NewProp_AssetTimeoutSeconds_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_CurrentProgress = { "CurrentProgress", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, CurrentProgress), Z_Construct_UScriptStruct_FLoadingProgress, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentProgress_MetaData), NewProp_CurrentProgress_MetaData) }; // 926561320
void Z_Construct_UClass_ULoadingManager_Statics::NewProp_bIsLoading_SetBit(void* Obj)
{
	((ULoadingManager*)Obj)->bIsLoading = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_bIsLoading = { "bIsLoading", nullptr, (EPropertyFlags)0x0010000000000014, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ULoadingManager), &Z_Construct_UClass_ULoadingManager_Statics::NewProp_bIsLoading_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsLoading_MetaData), NewProp_bIsLoading_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingStateChanged = { "OnLoadingStateChanged", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, OnLoadingStateChanged), Z_Construct_UDelegateFunction_SLT_OnLoadingStateChanged__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingStateChanged_MetaData), NewProp_OnLoadingStateChanged_MetaData) }; // 883518635
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingProgressUpdated = { "OnLoadingProgressUpdated", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, OnLoadingProgressUpdated), Z_Construct_UDelegateFunction_SLT_OnLoadingProgressUpdated__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingProgressUpdated_MetaData), NewProp_OnLoadingProgressUpdated_MetaData) }; // 1940953734
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnAssetLoaded = { "OnAssetLoaded", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, OnAssetLoaded), Z_Construct_UDelegateFunction_SLT_OnAssetLoaded__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnAssetLoaded_MetaData), NewProp_OnAssetLoaded_MetaData) }; // 3660826667
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingComplete = { "OnLoadingComplete", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, OnLoadingComplete), Z_Construct_UDelegateFunction_SLT_OnLoadingComplete__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingComplete_MetaData), NewProp_OnLoadingComplete_MetaData) }; // 2527641215
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingError = { "OnLoadingError", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, OnLoadingError), Z_Construct_UDelegateFunction_SLT_OnLoadingError__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnLoadingError_MetaData), NewProp_OnLoadingError_MetaData) }; // 2162196610
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingScreenWidget = { "LoadingScreenWidget", nullptr, (EPropertyFlags)0x0020080000080008, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, LoadingScreenWidget), Z_Construct_UClass_UUserWidget_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingScreenWidget_MetaData), NewProp_LoadingScreenWidget_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingQueue_Inner = { "LoadingQueue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FAssetLoadRequest, METADATA_PARAMS(0, nullptr) }; // 3444184009
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingQueue = { "LoadingQueue", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ULoadingManager, LoadingQueue), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LoadingQueue_MetaData), NewProp_LoadingQueue_MetaData) }; // 3444184009
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_ULoadingManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingScreenWidgetClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingTips_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingTips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_MinLoadingScreenTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowProgressBar,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_bShowLoadingTips,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetBundleNames_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetBundleNames,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_MaxConcurrentLoads,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_AssetTimeoutSeconds,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_CurrentProgress,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_bIsLoading,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingStateChanged,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingProgressUpdated,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnAssetLoaded,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingComplete,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_OnLoadingError,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingScreenWidget,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingQueue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_ULoadingManager_Statics::NewProp_LoadingQueue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ULoadingManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_ULoadingManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UGameInstanceSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_ULoadingManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_ULoadingManager_Statics::ClassParams = {
	&ULoadingManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_ULoadingManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_ULoadingManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_ULoadingManager_Statics::Class_MetaDataParams), Z_Construct_UClass_ULoadingManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_ULoadingManager()
{
	if (!Z_Registration_Info_UClass_ULoadingManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_ULoadingManager.OuterSingleton, Z_Construct_UClass_ULoadingManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_ULoadingManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<ULoadingManager>()
{
	return ULoadingManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(ULoadingManager);
ULoadingManager::~ULoadingManager() {}
// End Class ULoadingManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics
{
	static constexpr FEnumRegisterCompiledInInfo EnumInfo[] = {
		{ ELoadingState_StaticEnum, TEXT("ELoadingState"), &Z_Registration_Info_UEnum_ELoadingState, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 353074094U) },
		{ EAssetPriority_StaticEnum, TEXT("EAssetPriority"), &Z_Registration_Info_UEnum_EAssetPriority, CONSTRUCT_RELOAD_VERSION_INFO(FEnumReloadVersionInfo, 3646486783U) },
	};
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FLoadingTip::StaticStruct, Z_Construct_UScriptStruct_FLoadingTip_Statics::NewStructOps, TEXT("LoadingTip"), &Z_Registration_Info_UScriptStruct_LoadingTip, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLoadingTip), 2669823834U) },
		{ FAssetLoadRequest::StaticStruct, Z_Construct_UScriptStruct_FAssetLoadRequest_Statics::NewStructOps, TEXT("AssetLoadRequest"), &Z_Registration_Info_UScriptStruct_AssetLoadRequest, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FAssetLoadRequest), 3444184009U) },
		{ FLoadingProgress::StaticStruct, Z_Construct_UScriptStruct_FLoadingProgress_Statics::NewStructOps, TEXT("LoadingProgress"), &Z_Registration_Info_UScriptStruct_LoadingProgress, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FLoadingProgress), 926561320U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_ULoadingManager, ULoadingManager::StaticClass, TEXT("ULoadingManager"), &Z_Registration_Info_UClass_ULoadingManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(ULoadingManager), 30237735U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_308023518(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::ScriptStructInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::EnumInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_LoadingManager_h_Statics::EnumInfo));
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
