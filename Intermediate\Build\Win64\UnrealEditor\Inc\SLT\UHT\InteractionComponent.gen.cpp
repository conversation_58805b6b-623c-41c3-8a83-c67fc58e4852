// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/InventorySystem/Components/InteractionComponent.h"
#include "Runtime/Engine/Classes/Engine/TimerHandle.h"
#include "SLT/Core/Interfaces/Interactable.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeInteractionComponent() {}

// Begin Cross Module References
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_APawn_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UActorComponent();
ENGINE_API UEnum* Z_Construct_UEnum_Engine_EObjectTypeQuery();
ENGINE_API UScriptStruct* Z_Construct_UScriptStruct_FTimerHandle();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UEnhancedInputComponent_NoRegister();
ENHANCEDINPUT_API UClass* Z_Construct_UClass_UInputAction_NoRegister();
SLT_API UClass* Z_Construct_UClass_UInteractionComponent();
SLT_API UClass* Z_Construct_UClass_UInteractionComponent_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FInteractionData();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnInteractionStarted
struct Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInteractionStarted_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractableActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::NewProp_InteractableActor = { "InteractableActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionStarted_Parms, InteractableActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionStarted_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::NewProp_InteractableActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInteractionStarted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::_Script_SLT_eventOnInteractionStarted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::_Script_SLT_eventOnInteractionStarted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInteractionStarted_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionStarted, AActor* InteractableActor, APawn* InteractingPawn)
{
	struct _Script_SLT_eventOnInteractionStarted_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
	_Script_SLT_eventOnInteractionStarted_Parms Parms;
	Parms.InteractableActor=InteractableActor;
	Parms.InteractingPawn=InteractingPawn;
	OnInteractionStarted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInteractionStarted

// Begin Delegate FOnInteractionCompleted
struct Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInteractionCompleted_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractableActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::NewProp_InteractableActor = { "InteractableActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionCompleted_Parms, InteractableActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionCompleted_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::NewProp_InteractableActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInteractionCompleted__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::_Script_SLT_eventOnInteractionCompleted_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::_Script_SLT_eventOnInteractionCompleted_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInteractionCompleted_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionCompleted, AActor* InteractableActor, APawn* InteractingPawn)
{
	struct _Script_SLT_eventOnInteractionCompleted_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
	_Script_SLT_eventOnInteractionCompleted_Parms Parms;
	Parms.InteractableActor=InteractableActor;
	Parms.InteractingPawn=InteractingPawn;
	OnInteractionCompleted.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInteractionCompleted

// Begin Delegate FOnInteractionCancelled
struct Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInteractionCancelled_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractableActor;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractingPawn;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::NewProp_InteractableActor = { "InteractableActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionCancelled_Parms, InteractableActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::NewProp_InteractingPawn = { "InteractingPawn", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractionCancelled_Parms, InteractingPawn), Z_Construct_UClass_APawn_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::NewProp_InteractableActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::NewProp_InteractingPawn,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInteractionCancelled__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::_Script_SLT_eventOnInteractionCancelled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::_Script_SLT_eventOnInteractionCancelled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInteractionCancelled_DelegateWrapper(const FMulticastScriptDelegate& OnInteractionCancelled, AActor* InteractableActor, APawn* InteractingPawn)
{
	struct _Script_SLT_eventOnInteractionCancelled_Parms
	{
		AActor* InteractableActor;
		APawn* InteractingPawn;
	};
	_Script_SLT_eventOnInteractionCancelled_Parms Parms;
	Parms.InteractableActor=InteractableActor;
	Parms.InteractingPawn=InteractingPawn;
	OnInteractionCancelled.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInteractionCancelled

// Begin Delegate FOnInteractableFound
struct Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnInteractableFound_Parms
	{
		AActor* InteractableActor;
		FInteractionData InteractionData;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionData_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_InteractableActor;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionData;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::NewProp_InteractableActor = { "InteractableActor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractableFound_Parms, InteractableActor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::NewProp_InteractionData = { "InteractionData", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnInteractableFound_Parms, InteractionData), Z_Construct_UScriptStruct_FInteractionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionData_MetaData), NewProp_InteractionData_MetaData) }; // 3029509838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::NewProp_InteractableActor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::NewProp_InteractionData,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInteractableFound__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::_Script_SLT_eventOnInteractableFound_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::_Script_SLT_eventOnInteractableFound_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInteractableFound_DelegateWrapper(const FMulticastScriptDelegate& OnInteractableFound, AActor* InteractableActor, FInteractionData const& InteractionData)
{
	struct _Script_SLT_eventOnInteractableFound_Parms
	{
		AActor* InteractableActor;
		FInteractionData InteractionData;
	};
	_Script_SLT_eventOnInteractableFound_Parms Parms;
	Parms.InteractableActor=InteractableActor;
	Parms.InteractionData=InteractionData;
	OnInteractableFound.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnInteractableFound

// Begin Delegate FOnInteractableLost
struct Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnInteractableLost__DelegateSignature", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnInteractableLost_DelegateWrapper(const FMulticastScriptDelegate& OnInteractableLost)
{
	OnInteractableLost.ProcessMulticastDelegate<UObject>(NULL);
}
// End Delegate FOnInteractableLost

// Begin Class UInteractionComponent Function CancelInteraction
struct Z_Construct_UFunction_UInteractionComponent_CancelInteraction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_CancelInteraction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "CancelInteraction", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_CancelInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_CancelInteraction_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UInteractionComponent_CancelInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_CancelInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execCancelInteraction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CancelInteraction();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function CancelInteraction

// Begin Class UInteractionComponent Function CanInteractWith
struct Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics
{
	struct InteractionComponent_eventCanInteractWith_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Detection" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventCanInteractWith_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InteractionComponent_eventCanInteractWith_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InteractionComponent_eventCanInteractWith_Parms), &Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "CanInteractWith", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::InteractionComponent_eventCanInteractWith_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::InteractionComponent_eventCanInteractWith_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_CanInteractWith()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_CanInteractWith_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execCanInteractWith)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanInteractWith(Z_Param_Actor);
	P_NATIVE_END;
}
// End Class UInteractionComponent Function CanInteractWith

// Begin Class UInteractionComponent Function FindBestInteractable
struct Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics
{
	struct InteractionComponent_eventFindBestInteractable_Parms
	{
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Detection" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventFindBestInteractable_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "FindBestInteractable", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::InteractionComponent_eventFindBestInteractable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::InteractionComponent_eventFindBestInteractable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_FindBestInteractable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_FindBestInteractable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execFindBestInteractable)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->FindBestInteractable();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function FindBestInteractable

// Begin Class UInteractionComponent Function FindInteractablesInRange
struct Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics
{
	struct InteractionComponent_eventFindInteractablesInRange_Parms
	{
		TArray<AActor*> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Detection" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Detection functions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Detection functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventFindInteractablesInRange_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "FindInteractablesInRange", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::InteractionComponent_eventFindInteractablesInRange_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::InteractionComponent_eventFindInteractablesInRange_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execFindInteractablesInRange)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<AActor*>*)Z_Param__Result=P_THIS->FindInteractablesInRange();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function FindInteractablesInRange

// Begin Class UInteractionComponent Function GetCurrentInteractable
struct Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics
{
	struct InteractionComponent_eventGetCurrentInteractable_Parms
	{
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventGetCurrentInteractable_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "GetCurrentInteractable", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::InteractionComponent_eventGetCurrentInteractable_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::InteractionComponent_eventGetCurrentInteractable_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execGetCurrentInteractable)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->GetCurrentInteractable();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function GetCurrentInteractable

// Begin Class UInteractionComponent Function GetCurrentInteractionData
struct Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics
{
	struct InteractionComponent_eventGetCurrentInteractionData_Parms
	{
		FInteractionData ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventGetCurrentInteractionData_Parms, ReturnValue), Z_Construct_UScriptStruct_FInteractionData, METADATA_PARAMS(0, nullptr) }; // 3029509838
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "GetCurrentInteractionData", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::InteractionComponent_eventGetCurrentInteractionData_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::InteractionComponent_eventGetCurrentInteractionData_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execGetCurrentInteractionData)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(FInteractionData*)Z_Param__Result=P_THIS->GetCurrentInteractionData();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function GetCurrentInteractionData

// Begin Class UInteractionComponent Function IsInteracting
struct Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics
{
	struct InteractionComponent_eventIsInteracting_Parms
	{
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((InteractionComponent_eventIsInteracting_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(InteractionComponent_eventIsInteracting_Parms), &Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "IsInteracting", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::InteractionComponent_eventIsInteracting_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::InteractionComponent_eventIsInteracting_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_IsInteracting()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_IsInteracting_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execIsInteracting)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->IsInteracting();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function IsInteracting

// Begin Class UInteractionComponent Function SetupInputComponent
struct Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics
{
	struct InteractionComponent_eventSetupInputComponent_Parms
	{
		UEnhancedInputComponent* EnhancedInputComponent;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Setup" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Setup functions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Setup functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_EnhancedInputComponent_MetaData[] = {
		{ "EditInline", "true" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_EnhancedInputComponent;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::NewProp_EnhancedInputComponent = { "EnhancedInputComponent", nullptr, (EPropertyFlags)0x0010000000080080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(InteractionComponent_eventSetupInputComponent_Parms, EnhancedInputComponent), Z_Construct_UClass_UEnhancedInputComponent_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_EnhancedInputComponent_MetaData), NewProp_EnhancedInputComponent_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::NewProp_EnhancedInputComponent,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "SetupInputComponent", nullptr, nullptr, Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::PropPointers), sizeof(Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::InteractionComponent_eventSetupInputComponent_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::InteractionComponent_eventSetupInputComponent_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UInteractionComponent_SetupInputComponent()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_SetupInputComponent_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execSetupInputComponent)
{
	P_GET_OBJECT(UEnhancedInputComponent,Z_Param_EnhancedInputComponent);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetupInputComponent(Z_Param_EnhancedInputComponent);
	P_NATIVE_END;
}
// End Class UInteractionComponent Function SetupInputComponent

// Begin Class UInteractionComponent Function StartInteraction
struct Z_Construct_UFunction_UInteractionComponent_StartInteraction_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Interaction" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main interaction functions\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main interaction functions" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UInteractionComponent_StartInteraction_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UInteractionComponent, nullptr, "StartInteraction", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UInteractionComponent_StartInteraction_Statics::Function_MetaDataParams), Z_Construct_UFunction_UInteractionComponent_StartInteraction_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UInteractionComponent_StartInteraction()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UInteractionComponent_StartInteraction_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UInteractionComponent::execStartInteraction)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->StartInteraction();
	P_NATIVE_END;
}
// End Class UInteractionComponent Function StartInteraction

// Begin Class UInteractionComponent
void UInteractionComponent::StaticRegisterNativesUInteractionComponent()
{
	UClass* Class = UInteractionComponent::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CancelInteraction", &UInteractionComponent::execCancelInteraction },
		{ "CanInteractWith", &UInteractionComponent::execCanInteractWith },
		{ "FindBestInteractable", &UInteractionComponent::execFindBestInteractable },
		{ "FindInteractablesInRange", &UInteractionComponent::execFindInteractablesInRange },
		{ "GetCurrentInteractable", &UInteractionComponent::execGetCurrentInteractable },
		{ "GetCurrentInteractionData", &UInteractionComponent::execGetCurrentInteractionData },
		{ "IsInteracting", &UInteractionComponent::execIsInteracting },
		{ "SetupInputComponent", &UInteractionComponent::execSetupInputComponent },
		{ "StartInteraction", &UInteractionComponent::execStartInteraction },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UInteractionComponent);
UClass* Z_Construct_UClass_UInteractionComponent_NoRegister()
{
	return UInteractionComponent::StaticClass();
}
struct Z_Construct_UClass_UInteractionComponent_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintSpawnableComponent", "" },
		{ "BlueprintType", "true" },
		{ "ClassGroupNames", "Custom" },
		{ "IncludePath", "InventorySystem/Components/InteractionComponent.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionRange_MetaData[] = {
		{ "Category", "Interaction Settings" },
		{ "ClampMin", "0.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Interaction settings\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Interaction settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionSphereRadius_MetaData[] = {
		{ "Category", "Interaction Settings" },
		{ "ClampMin", "0.0" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bUseLineTrace_MetaData[] = {
		{ "Category", "Interaction Settings" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bRequireLineOfSight_MetaData[] = {
		{ "Category", "Interaction Settings" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionObjectTypes_MetaData[] = {
		{ "Category", "Interaction Settings" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractAction_MetaData[] = {
		{ "Category", "Input" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Input settings\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Input settings" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInteractionStarted_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInteractionCompleted_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInteractionCancelled_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInteractableFound_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnInteractableLost_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentInteractable_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Current interaction state\n" },
#endif
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Current interaction state" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_CurrentInteractionData_MetaData[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsInteracting_MetaData[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionStartTime_MetaData[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InteractionTimerHandle_MetaData[] = {
		{ "ModuleRelativePath", "InventorySystem/Components/InteractionComponent.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionRange;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionSphereRadius;
	static void NewProp_bUseLineTrace_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bUseLineTrace;
	static void NewProp_bRequireLineOfSight_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bRequireLineOfSight;
	static const UECodeGen_Private::FBytePropertyParams NewProp_InteractionObjectTypes_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_InteractionObjectTypes;
	static const UECodeGen_Private::FSoftObjectPropertyParams NewProp_InteractAction;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInteractionStarted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInteractionCompleted;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInteractionCancelled;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInteractableFound;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnInteractableLost;
	static const UECodeGen_Private::FWeakObjectPropertyParams NewProp_CurrentInteractable;
	static const UECodeGen_Private::FStructPropertyParams NewProp_CurrentInteractionData;
	static void NewProp_bIsInteracting_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsInteracting;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_InteractionStartTime;
	static const UECodeGen_Private::FStructPropertyParams NewProp_InteractionTimerHandle;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UInteractionComponent_CancelInteraction, "CancelInteraction" }, // 987283302
		{ &Z_Construct_UFunction_UInteractionComponent_CanInteractWith, "CanInteractWith" }, // 1268832312
		{ &Z_Construct_UFunction_UInteractionComponent_FindBestInteractable, "FindBestInteractable" }, // 2667551204
		{ &Z_Construct_UFunction_UInteractionComponent_FindInteractablesInRange, "FindInteractablesInRange" }, // 3478231568
		{ &Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractable, "GetCurrentInteractable" }, // 3199097354
		{ &Z_Construct_UFunction_UInteractionComponent_GetCurrentInteractionData, "GetCurrentInteractionData" }, // 460628464
		{ &Z_Construct_UFunction_UInteractionComponent_IsInteracting, "IsInteracting" }, // 2515115083
		{ &Z_Construct_UFunction_UInteractionComponent_SetupInputComponent, "SetupInputComponent" }, // 4245540337
		{ &Z_Construct_UFunction_UInteractionComponent_StartInteraction, "StartInteraction" }, // 1421402401
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UInteractionComponent>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionRange = { "InteractionRange", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractionRange), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionRange_MetaData), NewProp_InteractionRange_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionSphereRadius = { "InteractionSphereRadius", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractionSphereRadius), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionSphereRadius_MetaData), NewProp_InteractionSphereRadius_MetaData) };
void Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bUseLineTrace_SetBit(void* Obj)
{
	((UInteractionComponent*)Obj)->bUseLineTrace = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bUseLineTrace = { "bUseLineTrace", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UInteractionComponent), &Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bUseLineTrace_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bUseLineTrace_MetaData), NewProp_bUseLineTrace_MetaData) };
void Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bRequireLineOfSight_SetBit(void* Obj)
{
	((UInteractionComponent*)Obj)->bRequireLineOfSight = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bRequireLineOfSight = { "bRequireLineOfSight", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UInteractionComponent), &Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bRequireLineOfSight_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bRequireLineOfSight_MetaData), NewProp_bRequireLineOfSight_MetaData) };
const UECodeGen_Private::FBytePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionObjectTypes_Inner = { "InteractionObjectTypes", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Byte, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UEnum_Engine_EObjectTypeQuery, METADATA_PARAMS(0, nullptr) }; // 1798967895
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionObjectTypes = { "InteractionObjectTypes", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractionObjectTypes), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionObjectTypes_MetaData), NewProp_InteractionObjectTypes_MetaData) }; // 1798967895
const UECodeGen_Private::FSoftObjectPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractAction = { "InteractAction", nullptr, (EPropertyFlags)0x0014000000000005, UECodeGen_Private::EPropertyGenFlags::SoftObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractAction), Z_Construct_UClass_UInputAction_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractAction_MetaData), NewProp_InteractAction_MetaData) };
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionStarted = { "OnInteractionStarted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, OnInteractionStarted), Z_Construct_UDelegateFunction_SLT_OnInteractionStarted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInteractionStarted_MetaData), NewProp_OnInteractionStarted_MetaData) }; // 4210863536
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionCompleted = { "OnInteractionCompleted", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, OnInteractionCompleted), Z_Construct_UDelegateFunction_SLT_OnInteractionCompleted__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInteractionCompleted_MetaData), NewProp_OnInteractionCompleted_MetaData) }; // 3961569444
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionCancelled = { "OnInteractionCancelled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, OnInteractionCancelled), Z_Construct_UDelegateFunction_SLT_OnInteractionCancelled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInteractionCancelled_MetaData), NewProp_OnInteractionCancelled_MetaData) }; // 3448765388
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractableFound = { "OnInteractableFound", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, OnInteractableFound), Z_Construct_UDelegateFunction_SLT_OnInteractableFound__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInteractableFound_MetaData), NewProp_OnInteractableFound_MetaData) }; // 2047706731
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractableLost = { "OnInteractableLost", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, OnInteractableLost), Z_Construct_UDelegateFunction_SLT_OnInteractableLost__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnInteractableLost_MetaData), NewProp_OnInteractableLost_MetaData) }; // 3563908284
const UECodeGen_Private::FWeakObjectPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_CurrentInteractable = { "CurrentInteractable", nullptr, (EPropertyFlags)0x0024080000000000, UECodeGen_Private::EPropertyGenFlags::WeakObject, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, CurrentInteractable), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentInteractable_MetaData), NewProp_CurrentInteractable_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_CurrentInteractionData = { "CurrentInteractionData", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, CurrentInteractionData), Z_Construct_UScriptStruct_FInteractionData, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_CurrentInteractionData_MetaData), NewProp_CurrentInteractionData_MetaData) }; // 3029509838
void Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bIsInteracting_SetBit(void* Obj)
{
	((UInteractionComponent*)Obj)->bIsInteracting = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bIsInteracting = { "bIsInteracting", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UInteractionComponent), &Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bIsInteracting_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsInteracting_MetaData), NewProp_bIsInteracting_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionStartTime = { "InteractionStartTime", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractionStartTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionStartTime_MetaData), NewProp_InteractionStartTime_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionTimerHandle = { "InteractionTimerHandle", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UInteractionComponent, InteractionTimerHandle), Z_Construct_UScriptStruct_FTimerHandle, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InteractionTimerHandle_MetaData), NewProp_InteractionTimerHandle_MetaData) }; // 756291145
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UInteractionComponent_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionRange,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionSphereRadius,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bUseLineTrace,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bRequireLineOfSight,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionObjectTypes_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionObjectTypes,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractAction,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionStarted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionCompleted,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractionCancelled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractableFound,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_OnInteractableLost,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_CurrentInteractable,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_CurrentInteractionData,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_bIsInteracting,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionStartTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UInteractionComponent_Statics::NewProp_InteractionTimerHandle,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInteractionComponent_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UInteractionComponent_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UActorComponent,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UInteractionComponent_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UInteractionComponent_Statics::ClassParams = {
	&UInteractionComponent::StaticClass,
	"Engine",
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UInteractionComponent_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UInteractionComponent_Statics::PropPointers),
	0,
	0x00B000A4u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UInteractionComponent_Statics::Class_MetaDataParams), Z_Construct_UClass_UInteractionComponent_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UInteractionComponent()
{
	if (!Z_Registration_Info_UClass_UInteractionComponent.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UInteractionComponent.OuterSingleton, Z_Construct_UClass_UInteractionComponent_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UInteractionComponent.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UInteractionComponent>()
{
	return UInteractionComponent::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UInteractionComponent);
UInteractionComponent::~UInteractionComponent() {}
// End Class UInteractionComponent

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_Statics
{
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UInteractionComponent, UInteractionComponent::StaticClass, TEXT("UInteractionComponent"), &Z_Registration_Info_UClass_UInteractionComponent, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UInteractionComponent), 3501714029U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_3893679471(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_InventorySystem_Components_InteractionComponent_h_Statics::ClassInfo),
	nullptr, 0,
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
