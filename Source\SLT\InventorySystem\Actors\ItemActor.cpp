#include "ItemActor.h"
#include "InventorySystem/Components/InventoryGridComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/DataTable.h"
#include "Engine/Engine.h"
#include "GameFramework/Pawn.h"

AItemActor::AItemActor()
{
	PrimaryActorTick.bCanEverTick = true;

	// Create root component
	RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

	// Create mesh component
	MeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("MeshComponent"));
	MeshComponent->SetupAttachment(RootComponent);
	MeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	MeshComponent->SetGenerateOverlapEvents(false);

	// Create interaction sphere
	InteractionSphere = CreateDefaultSubobject<USphereComponent>(TEXT("InteractionSphere"));
	InteractionSphere->SetupAttachment(RootComponent);
	InteractionSphere->SetSphereRadius(100.0f);
	InteractionSphere->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	InteractionSphere->SetCollisionObjectType(ECollisionChannel::ECC_WorldDynamic);
	InteractionSphere->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	InteractionSphere->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
	InteractionSphere->SetGenerateOverlapEvents(true);

	// Default settings
	Quantity = 1;
	bAutoPickup = true;
	bDestroyOnPickup = true;
	bEnableFloating = true;
	FloatAmplitude = 10.0f;
	FloatSpeed = 2.0f;
	bEnableRotation = true;
	RotationSpeed = 45.0f;
}

void AItemActor::BeginPlay()
{
	Super::BeginPlay();
	
	InitialLocation = GetActorLocation();
	LoadItemDataFromDatabase();
}

void AItemActor::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (bEnableFloating)
	{
		UpdateFloatingAnimation(DeltaTime);
	}

	if (bEnableRotation)
	{
		UpdateRotationAnimation(DeltaTime);
	}
}

void AItemActor::UpdateFloatingAnimation(float DeltaTime)
{
	FloatTimeAccumulator += DeltaTime * FloatSpeed;
	
	float FloatOffset = FMath::Sin(FloatTimeAccumulator) * FloatAmplitude;
	FVector NewLocation = InitialLocation + FVector(0.0f, 0.0f, FloatOffset);
	
	SetActorLocation(NewLocation);
}

void AItemActor::UpdateRotationAnimation(float DeltaTime)
{
	FRotator CurrentRotation = GetActorRotation();
	CurrentRotation.Yaw += RotationSpeed * DeltaTime;
	SetActorRotation(CurrentRotation);
}

bool AItemActor::InitializeFromItemData(FName InItemID, int32 InQuantity)
{
	ItemID = InItemID;
	Quantity = FMath::Max(1, InQuantity);
	
	LoadItemDataFromDatabase();
	
	if (bItemDataCached)
	{
		UpdateMeshFromItemData();
		OnItemInitialized();
		return true;
	}
	
	return false;
}

void AItemActor::LoadItemDataFromDatabase()
{
	if (ItemID == NAME_None)
	{
		return;
	}

	UDataTable* Database = ItemDatabase.LoadSynchronous();
	if (!Database)
	{
		UE_LOG(LogTemp, Warning, TEXT("ItemActor: No item database set for item %s"), *ItemID.ToString());
		return;
	}

	FInventoryItemData* ItemData = Database->FindRow<FInventoryItemData>(ItemID, TEXT("LoadItemDataFromDatabase"));
	if (ItemData)
	{
		CachedItemData = *ItemData;
		bItemDataCached = true;
		UpdateMeshFromItemData();
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("ItemActor: Item %s not found in database"), *ItemID.ToString());
	}
}

void AItemActor::UpdateMeshFromItemData()
{
	if (!bItemDataCached || !MeshComponent)
	{
		return;
	}

	// Load and set the pickup mesh
	if (CachedItemData.PickupMesh.IsValid())
	{
		UStaticMesh* Mesh = CachedItemData.PickupMesh.LoadSynchronous();
		if (Mesh)
		{
			MeshComponent->SetStaticMesh(Mesh);
		}
	}
}

bool AItemActor::GetItemData(FInventoryItemData& OutItemData) const
{
	if (bItemDataCached)
	{
		OutItemData = CachedItemData;
		return true;
	}
	return false;
}

void AItemActor::SetQuantity(int32 NewQuantity)
{
	Quantity = FMath::Max(1, NewQuantity);
}

FInteractionData AItemActor::GetInteractionData_Implementation() const
{
	FInteractionData InteractionData;
	InteractionData.InteractionType = EInteractionType::Pickup;
	InteractionData.InteractionDuration = 0.0f; // Instant pickup
	InteractionData.bCanInteractMultipleTimes = false;
	InteractionData.InteractionRange = 150.0f;
	InteractionData.bShowInteractionPrompt = true;

	// Set interaction text
	if (bUseCustomInteractionText && !CustomInteractionText.IsEmpty())
	{
		InteractionData.InteractionText = CustomInteractionText;
	}
	else if (bItemDataCached)
	{
		FText QuantityText = (Quantity > 1) ? 
			FText::Format(FText::FromString(TEXT(" ({0})")), FText::AsNumber(Quantity)) : 
			FText::GetEmpty();
		
		InteractionData.InteractionText = FText::Format(
			FText::FromString(TEXT("Pick up {0}{1}")),
			CachedItemData.Name,
			QuantityText
		);
	}
	else
	{
		InteractionData.InteractionText = FText::FromString(TEXT("Pick up item"));
	}

	return InteractionData;
}

bool AItemActor::CanInteract_Implementation(APawn* InteractingPawn) const
{
	if (!InteractingPawn)
	{
		return false;
	}

	// Check if the pawn has an inventory component
	UInventoryGridComponent* InventoryComponent = InteractingPawn->FindComponentByClass<UInventoryGridComponent>();
	if (!InventoryComponent)
	{
		return false;
	}

	// Check if the item can fit in the inventory
	if (bItemDataCached)
	{
		return InventoryComponent->CanFitItem(CachedItemData, Quantity);
	}

	return true;
}

void AItemActor::OnInteractionStart_Implementation(APawn* InteractingPawn)
{
	// For instant pickup, this is called but immediately followed by OnInteractionComplete
}

void AItemActor::OnInteractionComplete_Implementation(APawn* InteractingPawn)
{
	if (!InteractingPawn)
	{
		return;
	}

	bool bPickupSuccess = TryAddToInventory(InteractingPawn);
	
	OnPickupAttempt(InteractingPawn, bPickupSuccess);
	
	if (bPickupSuccess)
	{
		OnItemPickedUp.Broadcast(this, InteractingPawn);
		
		if (bDestroyOnPickup)
		{
			Destroy();
		}
	}
}

void AItemActor::OnInteractionCancel_Implementation(APawn* InteractingPawn)
{
	// Nothing to do for item pickup cancellation
}

FVector AItemActor::GetInteractionLocation_Implementation() const
{
	return GetActorLocation();
}

bool AItemActor::TryAddToInventory(APawn* InteractingPawn)
{
	if (!InteractingPawn || !bItemDataCached)
	{
		return false;
	}

	UInventoryGridComponent* InventoryComponent = InteractingPawn->FindComponentByClass<UInventoryGridComponent>();
	if (!InventoryComponent)
	{
		UE_LOG(LogTemp, Warning, TEXT("ItemActor: Pawn %s has no inventory component"), *InteractingPawn->GetName());
		return false;
	}

	FInventorySlot OutSlot;
	bool bSuccess = InventoryComponent->AddItem(CachedItemData, Quantity, OutSlot);
	
	if (!bSuccess)
	{
		UE_LOG(LogTemp, Warning, TEXT("ItemActor: Failed to add item %s to inventory"), *ItemID.ToString());
	}

	return bSuccess;
}
