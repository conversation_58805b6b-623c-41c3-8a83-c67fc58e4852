#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "BehaviorTree/BehaviorTree.h"
// #include "BehaviorTree/BlackboardAsset.h"
#include "AIBehaviorDesigner.generated.h"

class UBlackboardComponent;
class UBehaviorTreeComponent;
class AAIController;

UENUM(BlueprintType)
enum class EAIBehaviorType : uint8
{
	Patrol			UMETA(DisplayName = "Patrol"),
	Guard			UMETA(DisplayName = "Guard"),
	Hunt			UMETA(DisplayName = "Hunt"),
	Flee			UMETA(DisplayName = "Flee"),
	Investigate		UMETA(DisplayName = "Investigate"),
	Follow			UMETA(DisplayName = "Follow"),
	Wander			UMETA(DisplayName = "Wander"),
	Ambush			UMETA(DisplayName = "Ambush"),
	Swarm			UMETA(DisplayName = "Swarm"),
	Custom			UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EAIPersonality : uint8
{
	Aggressive		UMETA(DisplayName = "Aggressive"),
	Defensive		UMETA(DisplayName = "Defensive"),
	Cautious		UMETA(DisplayName = "Cautious"),
	Reckless		UMETA(DisplayName = "Reckless"),
	Intelligent		UMETA(DisplayName = "Intelligent"),
	Cowardly		UMETA(DisplayName = "Cowardly"),
	Territorial		UMETA(DisplayName = "Territorial"),
	Pack			UMETA(DisplayName = "Pack"),
	Lone			UMETA(DisplayName = "Lone"),
	Adaptive		UMETA(DisplayName = "Adaptive")
};

UENUM(BlueprintType)
enum class EAIDifficultyLevel : uint8
{
	Beginner		UMETA(DisplayName = "Beginner"),
	Easy			UMETA(DisplayName = "Easy"),
	Normal			UMETA(DisplayName = "Normal"),
	Hard			UMETA(DisplayName = "Hard"),
	Expert			UMETA(DisplayName = "Expert"),
	Nightmare		UMETA(DisplayName = "Nightmare"),
	Adaptive		UMETA(DisplayName = "Adaptive")
};

USTRUCT(BlueprintType)
struct FAIBehaviorTemplate : public FTableRowBase
{
	GENERATED_BODY()

	FAIBehaviorTemplate()
	{
		TemplateID = NAME_None;
		TemplateName = FText::GetEmpty();
		BehaviorType = EAIBehaviorType::Patrol;
		Personality = EAIPersonality::Aggressive;
		DifficultyLevel = EAIDifficultyLevel::Normal;
		bIsBuiltIn = false;
		bRequiresSetup = false;
		EstimatedSetupTime = 5.0f;
		BehaviorTree = nullptr;
		BlackboardAsset = nullptr;
	}

	// Template identification
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FName TemplateID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FText TemplateName;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	FText TemplateDescription;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	EAIBehaviorType BehaviorType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	EAIPersonality Personality;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Template")
	EAIDifficultyLevel DifficultyLevel;

	// Template properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bIsBuiltIn;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	bool bRequiresSetup;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties")
	float EstimatedSetupTime;

	// AI assets
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Assets")
	TSoftObjectPtr<UBehaviorTree> BehaviorTree;

	// UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "AI Assets")
	// TSoftObjectPtr<UBlackboardAsset> BlackboardAsset;

	// Behavior parameters
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float SightRadius = 800.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float HearingRadius = 600.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float MovementSpeed = 300.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float AttackRange = 150.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float AttackDamage = 25.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float AttackCooldown = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float PatrolRadius = 500.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float AlertDuration = 10.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Parameters")
	float InvestigationTime = 5.0f;

	// Advanced settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanCallForHelp = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanUseWeapons = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanTakeCover = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanFlank = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bCanRetreat = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Advanced")
	bool bLearnsFromPlayer = false;

	// Group behavior
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Group")
	bool bSupportsGroupBehavior = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Group")
	float GroupCommunicationRange = 1000.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Group")
	int32 MaxGroupSize = 5;

	// Tags and categorization
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	FGameplayTagContainer BehaviorTags;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tags")
	TArray<FString> SearchKeywords;

	// Custom properties
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
	TMap<FString, FString> CustomParameters;
};

USTRUCT(BlueprintType)
struct FAIBehaviorState
{
	GENERATED_BODY()

	FAIBehaviorState()
	{
		CurrentBehavior = EAIBehaviorType::Patrol;
		CurrentPersonality = EAIPersonality::Aggressive;
		CurrentDifficulty = EAIDifficultyLevel::Normal;
		bIsActive = false;
		StateStartTime = 0.0f;
		LastStateChange = 0.0f;
		StateChangeCount = 0;
	}

	UPROPERTY(BlueprintReadOnly, Category = "State")
	EAIBehaviorType CurrentBehavior;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	EAIPersonality CurrentPersonality;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	EAIDifficultyLevel CurrentDifficulty;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	bool bIsActive;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	float StateStartTime;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	float LastStateChange;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	int32 StateChangeCount;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	TMap<FString, float> BehaviorParameters;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	TArray<FString> ActiveModifiers;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnBehaviorChanged, EAIBehaviorType, OldBehavior, EAIBehaviorType, NewBehavior, AActor*, AIActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPersonalityChanged, EAIPersonality, OldPersonality, EAIPersonality, NewPersonality);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDifficultyChanged, EAIDifficultyLevel, OldDifficulty, EAIDifficultyLevel, NewDifficulty);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnBehaviorTemplateApplied, FName, TemplateID, const FAIBehaviorTemplate&, Template);

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), BlueprintType, Blueprintable)
class SLT_API UAIBehaviorDesigner : public UActorComponent
{
	GENERATED_BODY()

public:
	UAIBehaviorDesigner();

protected:
	virtual void BeginPlay() override;

public:
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	// Template database
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Templates")
	TSoftObjectPtr<UDataTable> BehaviorTemplateDatabase;

	// Current behavior configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	FName CurrentTemplateID = NAME_None;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	EAIBehaviorType BehaviorType = EAIBehaviorType::Patrol;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	EAIPersonality Personality = EAIPersonality::Aggressive;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Behavior")
	EAIDifficultyLevel DifficultyLevel = EAIDifficultyLevel::Normal;

	// Behavior state
	UPROPERTY(BlueprintReadOnly, Category = "State")
	FAIBehaviorState BehaviorState;

	// Auto-configuration settings
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Config")
	bool bAutoConfigureFromTemplate = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Config")
	bool bAdaptToDifficulty = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Config")
	bool bLearnFromPlayer = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto Config")
	float AdaptationRate = 0.1f;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnBehaviorChanged OnBehaviorChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnPersonalityChanged OnPersonalityChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnDifficultyChanged OnDifficultyChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnBehaviorTemplateApplied OnBehaviorTemplateApplied;

	// Main behavior functions
	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void ApplyBehaviorTemplate(FName TemplateID);

	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void SetBehaviorType(EAIBehaviorType NewBehavior);

	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void SetPersonality(EAIPersonality NewPersonality);

	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void SetDifficultyLevel(EAIDifficultyLevel NewDifficulty);

	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void ActivateBehavior();

	UFUNCTION(BlueprintCallable, Category = "AI Behavior")
	void DeactivateBehavior();

	// Template functions
	UFUNCTION(BlueprintCallable, Category = "Templates")
	TArray<FAIBehaviorTemplate> GetAvailableTemplates() const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	FAIBehaviorTemplate GetBehaviorTemplate(FName TemplateID) const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	TArray<FAIBehaviorTemplate> GetTemplatesByType(EAIBehaviorType BehaviorType) const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	TArray<FAIBehaviorTemplate> GetTemplatesByPersonality(EAIPersonality Personality) const;

	UFUNCTION(BlueprintCallable, Category = "Templates")
	bool CreateCustomTemplate(const FAIBehaviorTemplate& Template);

	// Auto-configuration functions
	UFUNCTION(BlueprintCallable, Category = "Auto Config")
	void AutoConfigureForDifficulty(EAIDifficultyLevel TargetDifficulty);

	UFUNCTION(BlueprintCallable, Category = "Auto Config")
	void AutoConfigureForScenario(const FString& ScenarioName);

	UFUNCTION(BlueprintCallable, Category = "Auto Config")
	void AdaptToPlayerBehavior(const TMap<FString, float>& PlayerStats);

	UFUNCTION(BlueprintCallable, Category = "Auto Config")
	void ResetToDefaults();

	// Parameter functions
	UFUNCTION(BlueprintCallable, Category = "Parameters")
	void SetBehaviorParameter(const FString& ParameterName, float Value);

	UFUNCTION(BlueprintCallable, Category = "Parameters")
	float GetBehaviorParameter(const FString& ParameterName) const;

	UFUNCTION(BlueprintCallable, Category = "Parameters")
	void ApplyParameterModifier(const FString& ModifierName, float Multiplier, float Duration = -1.0f);

	UFUNCTION(BlueprintCallable, Category = "Parameters")
	void RemoveParameterModifier(const FString& ModifierName);

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ValidateBehaviorSetup();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	TArray<FString> GetSetupIssues() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void AutoFixCommonIssues();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ExportBehaviorConfiguration(const FString& FilePath) const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	bool ImportBehaviorConfiguration(const FString& FilePath);

protected:
	// Internal state
	UPROPERTY()
	UDataTable* CachedTemplateDatabase;

	UPROPERTY()
	AAIController* CachedAIController;

	UPROPERTY()
	UBehaviorTreeComponent* CachedBehaviorTreeComponent;

	UPROPERTY()
	UBlackboardComponent* CachedBlackboardComponent;

	UPROPERTY()
	TMap<FString, float> ParameterModifiers;

	UPROPERTY()
	TMap<FString, float> ModifierTimers;

	// Internal functions
	void LoadTemplateDatabase();
	void CacheAIComponents();
	void ApplyTemplateParameters(const FAIBehaviorTemplate& Template);
	void UpdateBehaviorParameters();
	void ProcessParameterModifiers(float DeltaTime);
	float CalculateDifficultyMultiplier() const;
	void AutoConfigureForPersonality(EAIPersonality NewPersonality);

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnBehaviorActivated(EAIBehaviorType BehaviorType);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnBehaviorDeactivated(EAIBehaviorType BehaviorType);

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnParameterChanged(const FString& ParameterName, float OldValue, float NewValue);
};
