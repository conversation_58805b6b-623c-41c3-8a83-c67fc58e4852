#include "ShaderManager.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/World.h"
#include "Engine/Engine.h"

UShaderManager::UShaderManager()
{
	// Initialize default shader variants
	FShaderVariantData DefaultVariant;
	DefaultVariant.VariantType = EShaderVariant::Default;
	DefaultVariant.EmissiveColor = FLinearColor::White;
	DefaultVariant.EmissiveIntensity = 0.0f;
	ShaderVariants.Add(EShaderVariant::Default, DefaultVariant);

	FShaderVariantData HighlightedVariant;
	HighlightedVariant.VariantType = EShaderVariant::Highlighted;
	HighlightedVariant.EmissiveColor = FLinearColor::Yellow;
	HighlightedVariant.EmissiveIntensity = 2.0f;
	HighlightedVariant.bEnableAnimation = true;
	HighlightedVariant.PulseFrequency = 2.0f;
	ShaderVariants.Add(EShaderVariant::Highlighted, HighlightedVariant);

	FShaderVariantData SelectedVariant;
	SelectedVariant.VariantType = EShaderVariant::Selected;
	SelectedVariant.EmissiveColor = FLinearColor::Green;
	SelectedVariant.EmissiveIntensity = 3.0f;
	ShaderVariants.Add(EShaderVariant::Selected, SelectedVariant);

	FShaderVariantData RareVariant;
	RareVariant.VariantType = EShaderVariant::Rare;
	RareVariant.EmissiveColor = FLinearColor::Blue;
	RareVariant.EmissiveIntensity = 1.5f;
	ShaderVariants.Add(EShaderVariant::Rare, RareVariant);

	FShaderVariantData EpicVariant;
	EpicVariant.VariantType = EShaderVariant::Epic;
	EpicVariant.EmissiveColor = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Purple
	EpicVariant.EmissiveIntensity = 2.0f;
	ShaderVariants.Add(EShaderVariant::Epic, EpicVariant);

	FShaderVariantData LegendaryVariant;
	LegendaryVariant.VariantType = EShaderVariant::Legendary;
	LegendaryVariant.EmissiveColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Orange
	LegendaryVariant.EmissiveIntensity = 3.0f;
	LegendaryVariant.bEnableAnimation = true;
	LegendaryVariant.PulseFrequency = 1.5f;
	ShaderVariants.Add(EShaderVariant::Legendary, LegendaryVariant);
}

void UShaderManager::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);
	LoadParameterCollection();
	OnShaderSystemInitialized();
}

void UShaderManager::Deinitialize()
{
	ClearAllPostProcesses();
	ComponentMaterials.Empty();
	ComponentVariants.Empty();
	AnimationTimers.Empty();
	Super::Deinitialize();
}

void UShaderManager::LoadParameterCollection()
{
	if (GlobalParameterCollection.IsValid())
	{
		CachedParameterCollection = GlobalParameterCollection.LoadSynchronous();
	}
}

bool UShaderManager::ApplyShaderVariant(UPrimitiveComponent* Component, EShaderVariant VariantType)
{
	if (!Component)
	{
		return false;
	}

	const FShaderVariantData* VariantData = ShaderVariants.Find(VariantType);
	if (!VariantData)
	{
		return false;
	}

	// Create or get dynamic material instance
	UMaterialInstanceDynamic* DynamicMaterial = CreateDynamicMaterial(Component, VariantType);
	if (!DynamicMaterial)
	{
		return false;
	}

	// Apply variant parameters
	DynamicMaterial->SetVectorParameterValue(TEXT("EmissiveColor"), VariantData->EmissiveColor);
	DynamicMaterial->SetScalarParameterValue(TEXT("EmissiveIntensity"), VariantData->EmissiveIntensity);
	DynamicMaterial->SetScalarParameterValue(TEXT("Metallic"), VariantData->Metallic);
	DynamicMaterial->SetScalarParameterValue(TEXT("Roughness"), VariantData->Roughness);
	DynamicMaterial->SetScalarParameterValue(TEXT("Opacity"), VariantData->Opacity);

	// Apply custom parameters
	if (VariantData->bUseCustomParameters)
	{
		for (const auto& ScalarParam : VariantData->ScalarParameters)
		{
			DynamicMaterial->SetScalarParameterValue(FName(*ScalarParam.Key), ScalarParam.Value);
		}

		for (const auto& VectorParam : VariantData->VectorParameters)
		{
			DynamicMaterial->SetVectorParameterValue(FName(*VectorParam.Key), VectorParam.Value);
		}

		for (const auto& TextureParam : VariantData->TextureParameters)
		{
			if (UTexture* Texture = TextureParam.Value.LoadSynchronous())
			{
				DynamicMaterial->SetTextureParameterValue(FName(*TextureParam.Key), Texture);
			}
		}
	}

	// Set material on component
	Component->SetMaterial(0, DynamicMaterial);

	// Store variant type
	ComponentVariants.Add(Component, VariantType);

	// Start animation if enabled
	if (VariantData->bEnableAnimation)
	{
		StartShaderAnimation(Component, VariantType);
	}

	OnShaderVariantApplied.Broadcast(Component, VariantType);
	OnMaterialParametersChanged(Component, VariantType);

	return true;
}

bool UShaderManager::RemoveShaderVariant(UPrimitiveComponent* Component)
{
	if (!Component)
	{
		return false;
	}

	// Remove from tracking
	ComponentMaterials.Remove(Component);
	ComponentVariants.Remove(Component);
	AnimationTimers.Remove(Component);

	// Reset to original material (this would need to be stored when first applying)
	// For now, we'll apply the default variant
	return ApplyShaderVariant(Component, EShaderVariant::Default);
}

UMaterialInstanceDynamic* UShaderManager::CreateDynamicMaterial(UPrimitiveComponent* Component, EShaderVariant VariantType)
{
	if (!Component)
	{
		return nullptr;
	}

	// Check if we already have a dynamic material for this component
	if (UMaterialInstanceDynamic** ExistingMaterial = ComponentMaterials.Find(Component))
	{
		return *ExistingMaterial;
	}

	// Get variant data
	const FShaderVariantData* VariantData = ShaderVariants.Find(VariantType);
	if (!VariantData)
	{
		return nullptr;
	}

	// Determine base material
	UMaterialInterface* BaseMaterial = nullptr;
	if (VariantData->OverrideMaterial.IsValid())
	{
		BaseMaterial = VariantData->OverrideMaterial.LoadSynchronous();
	}
	else if (VariantData->BaseMaterial.IsValid())
	{
		BaseMaterial = VariantData->BaseMaterial.LoadSynchronous();
	}
	else
	{
		// Use component's current material
		BaseMaterial = Component->GetMaterial(0);
	}

	if (!BaseMaterial)
	{
		return nullptr;
	}

	// Create dynamic material instance
	UMaterialInstanceDynamic* DynamicMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, Component);
	if (DynamicMaterial)
	{
		ComponentMaterials.Add(Component, DynamicMaterial);
	}

	return DynamicMaterial;
}

void UShaderManager::ApplyItemRarityShader(UPrimitiveComponent* Component, EItemRarity Rarity)
{
	EShaderVariant VariantType = GetRarityVariant(Rarity);
	ApplyShaderVariant(Component, VariantType);
}

bool UShaderManager::ApplyPostProcess(EPostProcessType ProcessType, float BlendWeight, float Duration)
{
	const FPostProcessSettings* Settings = PostProcessSettings.Find(ProcessType);
	if (!Settings)
	{
		return false;
	}

	// This would integrate with UE's post process system
	// For now, we'll just track active post processes
	ActivePostProcesses.Add(ProcessType);
	OnPostProcessApplied.Broadcast(ProcessType, BlendWeight);

	// Set up auto-removal timer if duration is specified
	if (Duration > 0.0f && Settings->bAutoRemove)
	{
		FTimerHandle TimerHandle;
		if (UWorld* World = GetWorld())
		{
			World->GetTimerManager().SetTimer(TimerHandle, [this, ProcessType]()
			{
				RemovePostProcess(ProcessType);
			}, Duration, false);
		}
	}

	return true;
}

bool UShaderManager::RemovePostProcess(EPostProcessType ProcessType)
{
	if (ActivePostProcesses.Contains(ProcessType))
	{
		ActivePostProcesses.Remove(ProcessType);
		OnPostProcessRemoved.Broadcast(ProcessType);
		return true;
	}
	return false;
}

void UShaderManager::ClearAllPostProcesses()
{
	TArray<EPostProcessType> ProcessesToRemove;
	for (EPostProcessType ProcessType : ActivePostProcesses)
	{
		ProcessesToRemove.Add(ProcessType);
	}

	for (EPostProcessType ProcessType : ProcessesToRemove)
	{
		RemovePostProcess(ProcessType);
	}
}

bool UShaderManager::IsPostProcessActive(EPostProcessType ProcessType) const
{
	return ActivePostProcesses.Contains(ProcessType);
}

void UShaderManager::SetGlobalScalarParameter(const FString& ParameterName, float Value)
{
	if (CachedParameterCollection)
	{
		if (UWorld* World = GetWorld())
		{
			World->GetParameterCollectionInstance(CachedParameterCollection)->SetScalarParameterValue(FName(*ParameterName), Value);
		}
	}
}

void UShaderManager::SetGlobalVectorParameter(const FString& ParameterName, const FLinearColor& Value)
{
	if (CachedParameterCollection)
	{
		if (UWorld* World = GetWorld())
		{
			World->GetParameterCollectionInstance(CachedParameterCollection)->SetVectorParameterValue(FName(*ParameterName), Value);
		}
	}
}

float UShaderManager::GetGlobalScalarParameter(const FString& ParameterName) const
{
	if (CachedParameterCollection)
	{
		if (UWorld* World = GetWorld())
		{
			float Value = 0.0f;
			World->GetParameterCollectionInstance(CachedParameterCollection)->GetScalarParameterValue(FName(*ParameterName), Value);
			return Value;
		}
	}
	return 0.0f;
}

FLinearColor UShaderManager::GetGlobalVectorParameter(const FString& ParameterName) const
{
	if (CachedParameterCollection)
	{
		if (UWorld* World = GetWorld())
		{
			FLinearColor Value = FLinearColor::Black;
			World->GetParameterCollectionInstance(CachedParameterCollection)->GetVectorParameterValue(FName(*ParameterName), Value);
			return Value;
		}
	}
	return FLinearColor::Black;
}

void UShaderManager::PrecompileShaderVariants()
{
	// This would trigger shader compilation for all variants
	// Implementation would depend on UE's shader compilation system
	UE_LOG(LogTemp, Log, TEXT("Precompiling shader variants..."));
}

void UShaderManager::CacheShaderVariants()
{
	// Cache frequently used shader variants
	for (const auto& VariantPair : ShaderVariants)
	{
		const FShaderVariantData& VariantData = VariantPair.Value;
		if (VariantData.BaseMaterial.IsValid())
		{
			VariantData.BaseMaterial.LoadSynchronous();
		}
		if (VariantData.OverrideMaterial.IsValid())
		{
			VariantData.OverrideMaterial.LoadSynchronous();
		}
	}
}

TArray<EShaderVariant> UShaderManager::GetAvailableVariants() const
{
	TArray<EShaderVariant> Variants;
	ShaderVariants.GetKeys(Variants);
	return Variants;
}

void UShaderManager::SetShaderQuality(int32 QualityLevel)
{
	// Adjust shader complexity based on quality level
	SetGlobalScalarParameter(TEXT("ShaderQuality"), (float)QualityLevel);
}

void UShaderManager::StartShaderAnimation(UPrimitiveComponent* Component, EShaderVariant VariantType)
{
	if (Component)
	{
		AnimationTimers.Add(Component, 0.0f);
	}
}

void UShaderManager::StopShaderAnimation(UPrimitiveComponent* Component)
{
	if (Component)
	{
		AnimationTimers.Remove(Component);
	}
}

void UShaderManager::UpdateShaderAnimations(float DeltaTime)
{
	for (auto& AnimationPair : AnimationTimers)
	{
		UPrimitiveComponent* Component = AnimationPair.Key;
		float& Timer = AnimationPair.Value;
		
		if (IsValid(Component))
		{
			Timer += DeltaTime;
			UpdateAnimatedMaterial(Component, Timer);
		}
	}

	// Clean up invalid components
	for (auto It = AnimationTimers.CreateIterator(); It; ++It)
	{
		if (!IsValid(It->Key))
		{
			It.RemoveCurrent();
		}
	}
}

FLinearColor UShaderManager::GetRarityColor(EItemRarity Rarity) const
{
	switch (Rarity)
	{
		case EItemRarity::Common:
			return FLinearColor::White;
		case EItemRarity::Uncommon:
			return FLinearColor::Green;
		case EItemRarity::Rare:
			return FLinearColor::Blue;
		case EItemRarity::Epic:
			return FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Purple
		case EItemRarity::Legendary:
			return FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Orange
		default:
			return FLinearColor::White;
	}
}

EShaderVariant UShaderManager::GetRarityVariant(EItemRarity Rarity) const
{
	switch (Rarity)
	{
		case EItemRarity::Common:
			return EShaderVariant::Default;
		case EItemRarity::Uncommon:
			return EShaderVariant::Default;
		case EItemRarity::Rare:
			return EShaderVariant::Rare;
		case EItemRarity::Epic:
			return EShaderVariant::Epic;
		case EItemRarity::Legendary:
			return EShaderVariant::Legendary;
		default:
			return EShaderVariant::Default;
	}
}

void UShaderManager::UpdateAnimatedMaterial(UPrimitiveComponent* Component, float Timer)
{
	if (!Component)
	{
		return;
	}

	EShaderVariant* VariantType = ComponentVariants.Find(Component);
	if (!VariantType)
	{
		return;
	}

	const FShaderVariantData* VariantData = ShaderVariants.Find(*VariantType);
	if (!VariantData || !VariantData->bEnableAnimation)
	{
		return;
	}

	UMaterialInstanceDynamic** DynamicMaterial = ComponentMaterials.Find(Component);
	if (!DynamicMaterial || !*DynamicMaterial)
	{
		return;
	}

	// Calculate pulsing effect
	float PulseValue = FMath::Sin(Timer * VariantData->PulseFrequency * 2.0f * PI) * 0.5f + 0.5f;
	float AnimatedIntensity = VariantData->EmissiveIntensity * (0.5f + PulseValue * 0.5f);

	// Apply animated parameters
	(*DynamicMaterial)->SetScalarParameterValue(TEXT("EmissiveIntensity"), AnimatedIntensity);
	(*DynamicMaterial)->SetScalarParameterValue(TEXT("AnimationTime"), Timer);
}
