#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Engine/StreamableManager.h"
#include "Engine/AssetManager.h"
#include "LoadingManager.generated.h"

class UUserWidget;
class UTexture2D;
class ULevelStreaming;

UENUM(BlueprintType)
enum class ELoadingState : uint8
{
	Idle			UMETA(DisplayName = "Idle"),
	Preparing		UMETA(DisplayName = "Preparing"),
	Loading			UMETA(DisplayName = "Loading"),
	Streaming		UMETA(DisplayName = "Streaming"),
	Finalizing		UMETA(DisplayName = "Finalizing"),
	Complete		UMETA(DisplayName = "Complete"),
	Failed			UMETA(DisplayName = "Failed")
};

UENUM(BlueprintType)
enum class EAssetPriority : uint8
{
	Critical		UMETA(DisplayName = "Critical"),
	High			UMETA(DisplayName = "High"),
	Medium			UMETA(DisplayName = "Medium"),
	Low				UMETA(DisplayName = "Low"),
	Background		UMETA(DisplayName = "Background")
};

USTRUCT(BlueprintType)
struct FLoadingTip
{
	GENERATED_BODY()

	FLoadingTip()
	{
		TipText = FText::GetEmpty();
		Category = TEXT("");
		bIsContextual = false;
		MinDisplayTime = 3.0f;
		Priority = 0;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	FText TipText;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	FString Category;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	bool bIsContextual;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	float MinDisplayTime;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	int32 Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tip")
	TSoftObjectPtr<UTexture2D> TipImage;
};

USTRUCT(BlueprintType)
struct FAssetLoadRequest
{
	GENERATED_BODY()

	FAssetLoadRequest()
	{
		Priority = EAssetPriority::Medium;
		bIsBlocking = false;
		TimeoutSeconds = 30.0f;
		RetryCount = 3;
		bPreloadDependencies = true;
	}

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	TArray<FSoftObjectPath> AssetPaths;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	EAssetPriority Priority;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	bool bIsBlocking;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	float TimeoutSeconds;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	int32 RetryCount;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	bool bPreloadDependencies;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	FString RequestID;
};

USTRUCT(BlueprintType)
struct FLoadingProgress
{
	GENERATED_BODY()

	FLoadingProgress()
	{
		CurrentState = ELoadingState::Idle;
		OverallProgress = 0.0f;
		AssetProgress = 0.0f;
		StreamingProgress = 0.0f;
		LoadedAssets = 0;
		TotalAssets = 0;
		CurrentAssetName = TEXT("");
		EstimatedTimeRemaining = 0.0f;
		LoadingStartTime = 0.0f;
	}

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	ELoadingState CurrentState;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	float OverallProgress;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	float AssetProgress;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	float StreamingProgress;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	int32 LoadedAssets;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	int32 TotalAssets;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	FString CurrentAssetName;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	float EstimatedTimeRemaining;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	float LoadingStartTime;

	UPROPERTY(BlueprintReadOnly, Category = "Progress")
	TArray<FString> LoadingMessages;
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLoadingStateChanged, ELoadingState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLoadingProgressUpdated, const FLoadingProgress&, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssetLoaded, const FString&, AssetPath, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLoadingComplete, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnLoadingError, const FString&, ErrorMessage, const FString&, AssetPath);

UCLASS(BlueprintType, Blueprintable)
class SLT_API ULoadingManager : public UGameInstanceSubsystem
{
	GENERATED_BODY()

public:
	ULoadingManager();

	// USubsystem interface
	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	// Loading screen configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loading Screen")
	TSubclassOf<UUserWidget> LoadingScreenWidgetClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loading Screen")
	TArray<FLoadingTip> LoadingTips;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loading Screen")
	float MinLoadingScreenTime = 2.0f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loading Screen")
	bool bShowProgressBar = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loading Screen")
	bool bShowLoadingTips = true;

	// Asset bundling configuration
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset Bundling")
	TArray<FString> AssetBundleNames;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset Bundling")
	int32 MaxConcurrentLoads = 5;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Asset Bundling")
	float AssetTimeoutSeconds = 30.0f;

	// Current state
	UPROPERTY(BlueprintReadOnly, Category = "State")
	FLoadingProgress CurrentProgress;

	UPROPERTY(BlueprintReadOnly, Category = "State")
	bool bIsLoading = false;

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingStateChanged OnLoadingStateChanged;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingProgressUpdated OnLoadingProgressUpdated;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnAssetLoaded OnAssetLoaded;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingComplete OnLoadingComplete;

	UPROPERTY(BlueprintAssignable, Category = "Events")
	FOnLoadingError OnLoadingError;

	// Main loading functions
	UFUNCTION(BlueprintCallable, Category = "Loading")
	void StartLoading(const FString& LoadingContext = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "Loading")
	void StopLoading();

	UFUNCTION(BlueprintCallable, Category = "Loading")
	bool IsCurrentlyLoading() const { return bIsLoading; }

	// Asset loading functions
	UFUNCTION(BlueprintCallable, Category = "Assets")
	void LoadAssetBundle(const FString& BundleName, EAssetPriority Priority = EAssetPriority::Medium);

	UFUNCTION(BlueprintCallable, Category = "Assets")
	void LoadAssets(const TArray<FSoftObjectPath>& AssetPaths, EAssetPriority Priority = EAssetPriority::Medium);

	UFUNCTION(BlueprintCallable, Category = "Assets")
	void LoadAssetAsync(const FSoftObjectPath& AssetPath, EAssetPriority Priority = EAssetPriority::Medium);

	UFUNCTION(BlueprintCallable, Category = "Assets")
	void PreloadAssets(const TArray<FSoftObjectPath>& AssetPaths);

	UFUNCTION(BlueprintCallable, Category = "Assets")
	void UnloadAssets(const TArray<FSoftObjectPath>& AssetPaths);

	// Level streaming functions
	UFUNCTION(BlueprintCallable, Category = "Streaming")
	void StreamLevel(const FString& LevelName, bool bShouldBlock = false);

	UFUNCTION(BlueprintCallable, Category = "Streaming")
	void UnstreamLevel(const FString& LevelName);

	UFUNCTION(BlueprintCallable, Category = "Streaming")
	bool IsLevelStreamed(const FString& LevelName) const;

	// Loading screen functions
	UFUNCTION(BlueprintCallable, Category = "Loading Screen")
	void ShowLoadingScreen();

	UFUNCTION(BlueprintCallable, Category = "Loading Screen")
	void HideLoadingScreen();

	UFUNCTION(BlueprintCallable, Category = "Loading Screen")
	FLoadingTip GetRandomLoadingTip(const FString& Category = TEXT("")) const;

	// Progress functions
	UFUNCTION(BlueprintCallable, Category = "Progress")
	void UpdateProgress(float NewProgress, const FString& CurrentTask = TEXT(""));

	UFUNCTION(BlueprintCallable, Category = "Progress")
	void AddLoadingMessage(const FString& Message);

	UFUNCTION(BlueprintCallable, Category = "Progress")
	float GetEstimatedTimeRemaining() const;

	// Utility functions
	UFUNCTION(BlueprintCallable, Category = "Utility")
	void SetAssetPriority(const FSoftObjectPath& AssetPath, EAssetPriority Priority);

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void ClearAssetCache();

	UFUNCTION(BlueprintCallable, Category = "Utility")
	int32 GetMemoryUsage() const;

	UFUNCTION(BlueprintCallable, Category = "Utility")
	void OptimizeMemory();

protected:
	// Internal state
	UPROPERTY()
	UUserWidget* LoadingScreenWidget;

	UPROPERTY()
	TArray<FAssetLoadRequest> LoadingQueue;

	// Active handles (not exposed to Blueprint)
	TArray<TSharedPtr<FStreamableHandle>> ActiveHandles;

	// Streamable manager
	FStreamableManager StreamableManager;

	// Internal functions
	void ProcessLoadingQueue();
	void OnAssetLoadComplete(const FString& AssetPath, bool bSuccess);
	void UpdateLoadingState(ELoadingState NewState);
	void CalculateProgress();
	float CalculateEstimatedTime() const;

	// Blueprint events
	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnLoadingScreenShown();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnLoadingScreenHidden();

	UFUNCTION(BlueprintImplementableEvent, Category = "Events")
	void OnAssetLoadStarted(const FString& AssetPath);
};
