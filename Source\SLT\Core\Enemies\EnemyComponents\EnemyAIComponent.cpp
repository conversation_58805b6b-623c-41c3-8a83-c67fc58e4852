#include "EnemyAIComponent.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/BehaviorTree.h"
#include "Engine/World.h"
#include "TimerManager.h"
#include "Kismet/GameplayStatics.h"
#include "NavigationSystem.h"
#include "DrawDebugHelpers.h"

UEnemyAIComponent::UEnemyAIComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	PrimaryComponentTick.TickInterval = 0.1f;

	// Initialize default behavior settings
	BehaviorSettings.BehaviorPattern = EAIBehaviorPattern::Aggressive;
	BehaviorSettings.AggressionLevel = 0.7f;
	BehaviorSettings.CautiousLevel = 0.3f;
	BehaviorSettings.GroupCoordination = 0.5f;
	BehaviorSettings.TerritorialRadius = 500.0f;
	BehaviorSettings.PatrolSpeed = 200.0f;
	BehaviorSettings.ChaseSpeed = 400.0f;
	BehaviorSettings.AttackDistance = 150.0f;
	BehaviorSettings.FleeHealthThreshold = 0.2f;
	BehaviorSettings.CallForHelpRange = 800.0f;
	BehaviorSettings.ReactionTime = 0.5f;
	BehaviorSettings.DecisionUpdateRate = 0.2f;

	CurrentTarget = nullptr;
	LastKnownTargetLocation = FVector::ZeroVector;
	InvestigationLocation = FVector::ZeroVector;
	CurrentPatrolIndex = 0;
	LastDecisionTime = 0.0f;
	bIsInCombat = false;
	bLoopPatrol = true;
	bRandomizePatrol = false;

	OwnerEnemyCharacter = nullptr;
	AIController = nullptr;
	BehaviorTreeComponent = nullptr;
	BlackboardComponent = nullptr;
}

void UEnemyAIComponent::BeginPlay()
{
	Super::BeginPlay();

	// Start decision making timer
	GetWorld()->GetTimerManager().SetTimer(DecisionTimerHandle, this, &UEnemyAIComponent::MakeDecision, 
		BehaviorSettings.DecisionUpdateRate, true);
}

void UEnemyAIComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	// Update blackboard values
	UpdateBlackboardValues();

	// Update last known target location
	if (CurrentTarget && IsValid(CurrentTarget))
	{
		LastKnownTargetLocation = CurrentTarget->GetActorLocation();
	}
}

void UEnemyAIComponent::InitializeAI(ABaseEnemyCharacter* OwnerEnemy)
{
	OwnerEnemyCharacter = OwnerEnemy;
	
	if (!OwnerEnemyCharacter)
	{
		UE_LOG(LogTemp, Error, TEXT("EnemyAIComponent: No owner enemy character provided"));
		return;
	}

	// Get AI controller
	AIController = Cast<AAIController>(OwnerEnemyCharacter->GetController());
	if (!AIController)
	{
		UE_LOG(LogTemp, Error, TEXT("EnemyAIComponent: Owner does not have an AI controller"));
		return;
	}

	// Get behavior tree component
	BehaviorTreeComponent = AIController->FindComponentByClass<UBehaviorTreeComponent>();
	BlackboardComponent = AIController->FindComponentByClass<UBlackboardComponent>();

	// Setup behavior tree
	SetupBehaviorTree();

	// Configure behavior for the selected pattern
	ConfigureBehaviorForPattern();

	// Generate patrol points if none are set
	if (PatrolPoints.Num() == 0)
	{
		// Create a simple patrol around the spawn location
		FVector SpawnLocation = OwnerEnemyCharacter->GetActorLocation();
		AddPatrolPoint(SpawnLocation + FVector(300, 0, 0));
		AddPatrolPoint(SpawnLocation + FVector(0, 300, 0));
		AddPatrolPoint(SpawnLocation + FVector(-300, 0, 0));
		AddPatrolPoint(SpawnLocation + FVector(0, -300, 0));
	}

	OnAIInitialized();

	UE_LOG(LogTemp, Log, TEXT("EnemyAIComponent initialized for %s with pattern %s"), 
		*OwnerEnemyCharacter->GetName(), *UEnum::GetValueAsString(BehaviorSettings.BehaviorPattern));
}

void UEnemyAIComponent::OnStateChanged(EBaseEnemyState OldState, EBaseEnemyState NewState)
{
	switch (NewState)
	{
		case EBaseEnemyState::Patrolling:
			bIsInCombat = false;
			StartPatrol();
			OnCombatEnded();
			break;

		case EBaseEnemyState::Chasing:
		case EBaseEnemyState::Attacking:
			if (!bIsInCombat)
			{
				bIsInCombat = true;
				OnCombatStarted();
			}
			break;

		case EBaseEnemyState::Investigating:
			bIsInCombat = false;
			break;

		case EBaseEnemyState::Dead:
			bIsInCombat = false;
			GetWorld()->GetTimerManager().ClearTimer(DecisionTimerHandle);
			GetWorld()->GetTimerManager().ClearTimer(PatrolTimerHandle);
			break;
	}
}

void UEnemyAIComponent::OnTargetChanged(APawn* NewTarget)
{
	APawn* OldTarget = CurrentTarget;
	CurrentTarget = NewTarget;

	if (CurrentTarget)
	{
		LastKnownTargetLocation = CurrentTarget->GetActorLocation();
		OnTargetAcquired(CurrentTarget);
	}
	else
	{
		OnTargetLost();
	}

	OnAITargetChanged.Broadcast(OldTarget, NewTarget);
}

void UEnemyAIComponent::InvestigateLocation(const FVector& Location)
{
	InvestigationLocation = Location;
	OnInvestigationStarted(Location);
	
	UE_LOG(LogTemp, Log, TEXT("EnemyAIComponent: %s investigating location %s"), 
		*GetOwner()->GetName(), *Location.ToString());
}

void UEnemyAIComponent::CallForHelp()
{
	if (!BehaviorSettings.bCanCallForHelp)
	{
		return;
	}

	TArray<ABaseEnemyCharacter*> NearbyAllies = GetNearbyAllies(BehaviorSettings.CallForHelpRange);
	
	for (ABaseEnemyCharacter* Ally : NearbyAllies)
	{
		if (Ally && Ally != OwnerEnemyCharacter)
		{
			// Alert the ally to our target
			if (CurrentTarget)
			{
				Ally->SetTarget(CurrentTarget);
				Ally->SetEnemyState(EEnemyState::Chasing);
			}
		}
	}

	OnAIDecisionMade.Broadcast(FString::Printf(TEXT("Called for help - alerted %d allies"), NearbyAllies.Num()));
}

void UEnemyAIComponent::SetBehaviorPattern(EAIBehaviorPattern NewPattern)
{
	if (BehaviorSettings.BehaviorPattern != NewPattern)
	{
		BehaviorSettings.BehaviorPattern = NewPattern;
		ConfigureBehaviorForPattern();
		OnAIBehaviorChanged.Broadcast(NewPattern);
	}
}

void UEnemyAIComponent::MakeDecision()
{
	if (!OwnerEnemyCharacter || OwnerEnemyCharacter->CurrentState == EBaseEnemyState::Dead)
	{
		return;
	}

	LastDecisionTime = GetWorld()->GetTimeSeconds();

	switch (OwnerEnemyCharacter->CurrentState)
	{
		case EBaseEnemyState::Idle:
		case EBaseEnemyState::Patrolling:
			ProcessPatrolDecision();
			break;

		case EBaseEnemyState::Chasing:
		case EBaseEnemyState::Attacking:
			ProcessCombatDecision();
			break;

		case EBaseEnemyState::Investigating:
			ProcessInvestigationDecision();
			break;
	}
}

bool UEnemyAIComponent::ShouldAttack() const
{
	if (!CurrentTarget || !OwnerEnemyCharacter)
	{
		return false;
	}

	float DistanceToTarget = GetDistanceToTarget();
	return DistanceToTarget <= BehaviorSettings.AttackDistance && HasLineOfSightToTarget();
}

bool UEnemyAIComponent::ShouldFlee() const
{
	if (!OwnerEnemyCharacter || !BehaviorSettings.bCanRetreat)
	{
		return false;
	}

	float HealthPercentage = OwnerEnemyCharacter->CurrentHealth / OwnerEnemyCharacter->CurrentStats.MaxHealth;
	return HealthPercentage <= BehaviorSettings.FleeHealthThreshold;
}

bool UEnemyAIComponent::ShouldCallForHelp() const
{
	if (!BehaviorSettings.bCanCallForHelp || !CurrentTarget)
	{
		return false;
	}

	// Call for help when health is low or when outnumbered
	float HealthPercentage = OwnerEnemyCharacter->CurrentHealth / OwnerEnemyCharacter->CurrentStats.MaxHealth;
	return HealthPercentage < 0.5f || GetNearbyAllies().Num() == 0;
}

FVector UEnemyAIComponent::GetFlankingPosition() const
{
	if (!CurrentTarget || !OwnerEnemyCharacter)
	{
		return FVector::ZeroVector;
	}

	FVector TargetLocation = CurrentTarget->GetActorLocation();
	FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();
	FVector DirectionToTarget = (TargetLocation - OwnerLocation).GetSafeNormal();
	
	// Get a position 90 degrees to the side
	FVector FlankDirection = FVector::CrossProduct(DirectionToTarget, FVector::UpVector).GetSafeNormal();
	FVector FlankPosition = TargetLocation + FlankDirection * 300.0f;

	return FlankPosition;
}

FVector UEnemyAIComponent::GetCoverPosition() const
{
	if (!CurrentTarget || !OwnerEnemyCharacter)
	{
		return FVector::ZeroVector;
	}

	// Simple cover finding - move away from target
	FVector TargetLocation = CurrentTarget->GetActorLocation();
	FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();
	FVector AwayDirection = (OwnerLocation - TargetLocation).GetSafeNormal();
	
	return OwnerLocation + AwayDirection * 500.0f;
}

void UEnemyAIComponent::StartPatrol()
{
	if (PatrolPoints.Num() == 0)
	{
		return;
	}

	CurrentPatrolIndex = 0;
	UpdatePatrol();
}

void UEnemyAIComponent::NextPatrolPoint()
{
	if (PatrolPoints.Num() == 0)
	{
		return;
	}

	if (bRandomizePatrol)
	{
		CurrentPatrolIndex = FMath::RandRange(0, PatrolPoints.Num() - 1);
	}
	else
	{
		CurrentPatrolIndex++;
		if (CurrentPatrolIndex >= PatrolPoints.Num())
		{
			if (bLoopPatrol)
			{
				CurrentPatrolIndex = 0;
			}
			else
			{
				CurrentPatrolIndex = PatrolPoints.Num() - 1;
			}
		}
	}

	UpdatePatrol();
	OnPatrolPointReached(CurrentPatrolIndex);
}

FVector UEnemyAIComponent::GetCurrentPatrolTarget() const
{
	if (PatrolPoints.IsValidIndex(CurrentPatrolIndex))
	{
		return PatrolPoints[CurrentPatrolIndex].Location;
	}
	return FVector::ZeroVector;
}

void UEnemyAIComponent::AddPatrolPoint(const FVector& Location, float WaitTime)
{
	FPatrolPoint NewPoint;
	NewPoint.Location = Location;
	NewPoint.WaitTime = WaitTime;
	PatrolPoints.Add(NewPoint);
}

void UEnemyAIComponent::ClearPatrolPoints()
{
	PatrolPoints.Empty();
	CurrentPatrolIndex = 0;
}

float UEnemyAIComponent::GetDistanceToTarget() const
{
	if (!CurrentTarget || !OwnerEnemyCharacter)
	{
		return FLT_MAX;
	}

	return FVector::Dist(OwnerEnemyCharacter->GetActorLocation(), CurrentTarget->GetActorLocation());
}

bool UEnemyAIComponent::HasLineOfSightToTarget() const
{
	if (!CurrentTarget || !OwnerEnemyCharacter)
	{
		return false;
	}

	FVector StartLocation = OwnerEnemyCharacter->GetActorLocation();
	FVector EndLocation = CurrentTarget->GetActorLocation();

	FHitResult HitResult;
	FCollisionQueryParams QueryParams;
	QueryParams.AddIgnoredActor(OwnerEnemyCharacter);
	QueryParams.AddIgnoredActor(CurrentTarget);

	bool bHit = GetWorld()->LineTraceSingleByChannel(HitResult, StartLocation, EndLocation, ECC_Visibility, QueryParams);
	return !bHit;
}

TArray<ABaseEnemyCharacter*> UEnemyAIComponent::GetNearbyAllies(float Range) const
{
	TArray<ABaseEnemyCharacter*> NearbyAllies;

	if (!OwnerEnemyCharacter)
	{
		return NearbyAllies;
	}

	TArray<AActor*> FoundActors;
	UGameplayStatics::GetAllActorsOfClass(GetWorld(), ABaseEnemyCharacter::StaticClass(), FoundActors);

	FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();

	for (AActor* Actor : FoundActors)
	{
		ABaseEnemyCharacter* Enemy = Cast<ABaseEnemyCharacter>(Actor);
		if (Enemy && Enemy != OwnerEnemyCharacter)
		{
			float Distance = FVector::Dist(OwnerLocation, Enemy->GetActorLocation());
			if (Distance <= Range)
			{
				NearbyAllies.Add(Enemy);
			}
		}
	}

	return NearbyAllies;
}

void UEnemyAIComponent::UpdateBlackboardValues()
{
	if (!BlackboardComponent)
	{
		return;
	}

	// Update common blackboard keys
	BlackboardComponent->SetValueAsObject(TEXT("TargetActor"), CurrentTarget);
	BlackboardComponent->SetValueAsVector(TEXT("LastKnownTargetLocation"), LastKnownTargetLocation);
	BlackboardComponent->SetValueAsVector(TEXT("InvestigationLocation"), InvestigationLocation);
	BlackboardComponent->SetValueAsVector(TEXT("PatrolTarget"), GetCurrentPatrolTarget());
	BlackboardComponent->SetValueAsBool(TEXT("IsInCombat"), bIsInCombat);
	BlackboardComponent->SetValueAsBool(TEXT("HasTarget"), CurrentTarget != nullptr);
	BlackboardComponent->SetValueAsBool(TEXT("ShouldFlee"), ShouldFlee());
	BlackboardComponent->SetValueAsBool(TEXT("CanAttack"), ShouldAttack());
}

void UEnemyAIComponent::SetupBehaviorTree()
{
	if (!AIController)
	{
		return;
	}

	// Load and start behavior tree if specified
	if (BehaviorTree.IsValid())
	{
		UBehaviorTree* BT = BehaviorTree.LoadSynchronous();
		if (BT)
		{
			AIController->RunBehaviorTree(BT);
		}
	}
}

void UEnemyAIComponent::ConfigureBehaviorForPattern()
{
	switch (BehaviorSettings.BehaviorPattern)
	{
		case EAIBehaviorPattern::Aggressive:
			BehaviorSettings.AggressionLevel = 0.9f;
			BehaviorSettings.CautiousLevel = 0.1f;
			BehaviorSettings.AttackDistance = 200.0f;
			BehaviorSettings.FleeHealthThreshold = 0.1f;
			break;

		case EAIBehaviorPattern::Defensive:
			BehaviorSettings.AggressionLevel = 0.3f;
			BehaviorSettings.CautiousLevel = 0.8f;
			BehaviorSettings.AttackDistance = 100.0f;
			BehaviorSettings.FleeHealthThreshold = 0.4f;
			BehaviorSettings.bCanTakeCover = true;
			break;

		case EAIBehaviorPattern::Cautious:
			BehaviorSettings.AggressionLevel = 0.4f;
			BehaviorSettings.CautiousLevel = 0.9f;
			BehaviorSettings.ReactionTime = 1.0f;
			BehaviorSettings.FleeHealthThreshold = 0.5f;
			break;

		case EAIBehaviorPattern::Pack:
			BehaviorSettings.GroupCoordination = 0.9f;
			BehaviorSettings.bCanCallForHelp = true;
			BehaviorSettings.CallForHelpRange = 1200.0f;
			break;

		case EAIBehaviorPattern::Ambush:
			BehaviorSettings.CautiousLevel = 0.8f;
			BehaviorSettings.PatrolSpeed = 100.0f;
			BehaviorSettings.bCanFlank = true;
			break;
	}
}

void UEnemyAIComponent::UpdatePatrol()
{
	if (!PatrolPoints.IsValidIndex(CurrentPatrolIndex))
	{
		return;
	}

	const FPatrolPoint& CurrentPoint = PatrolPoints[CurrentPatrolIndex];
	
	// Set timer for next patrol point
	if (CurrentPoint.WaitTime > 0.0f)
	{
		GetWorld()->GetTimerManager().SetTimer(PatrolTimerHandle, this, &UEnemyAIComponent::NextPatrolPoint, 
			CurrentPoint.WaitTime, false);
	}
}

void UEnemyAIComponent::ProcessCombatDecision()
{
	if (!CurrentTarget)
	{
		OwnerEnemyCharacter->SetEnemyState(EEnemyState::Patrolling);
		return;
	}

	// Check if we should flee
	if (ShouldFlee())
	{
		OwnerEnemyCharacter->SetEnemyState(EEnemyState::Fleeing);
		OnAIDecisionMade.Broadcast(TEXT("Decided to flee - health too low"));
		return;
	}

	// Check if we should call for help
	if (ShouldCallForHelp())
	{
		CallForHelp();
	}

	// Check if we can attack
	if (ShouldAttack())
	{
		OwnerEnemyCharacter->SetEnemyState(EEnemyState::Attacking);
		OnAIDecisionMade.Broadcast(TEXT("Decided to attack - target in range"));
	}
	else
	{
		OwnerEnemyCharacter->SetEnemyState(EEnemyState::Chasing);
		OnAIDecisionMade.Broadcast(TEXT("Decided to chase - target out of range"));
	}
}

void UEnemyAIComponent::ProcessPatrolDecision()
{
	// Simple patrol logic - move to next patrol point
	if (PatrolPoints.Num() > 0)
	{
		FVector CurrentPatrolTarget = GetCurrentPatrolTarget();
		FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();
		
		float DistanceToPatrolPoint = FVector::Dist(OwnerLocation, CurrentPatrolTarget);
		if (DistanceToPatrolPoint < 100.0f)
		{
			NextPatrolPoint();
		}
	}
}

void UEnemyAIComponent::ProcessInvestigationDecision()
{
	if (InvestigationLocation != FVector::ZeroVector)
	{
		FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();
		float DistanceToInvestigation = FVector::Dist(OwnerLocation, InvestigationLocation);
		
		if (DistanceToInvestigation < 150.0f)
		{
			// Finished investigating
			InvestigationLocation = FVector::ZeroVector;
			OwnerEnemyCharacter->SetEnemyState(EEnemyState::Patrolling);
			OnAIDecisionMade.Broadcast(TEXT("Investigation complete - returning to patrol"));
		}
	}
}

FVector UEnemyAIComponent::FindRandomPatrolLocation() const
{
	if (!OwnerEnemyCharacter)
	{
		return FVector::ZeroVector;
	}

	FVector OwnerLocation = OwnerEnemyCharacter->GetActorLocation();
	FVector RandomDirection = FMath::VRand();
	RandomDirection.Z = 0; // Keep on ground level
	RandomDirection.Normalize();
	
	return OwnerLocation + RandomDirection * FMath::RandRange(200.0f, BehaviorSettings.TerritorialRadius);
}

bool UEnemyAIComponent::IsLocationSafe(const FVector& Location) const
{
	// Simple safety check - ensure location is on navmesh
	UNavigationSystemV1* NavSystem = UNavigationSystemV1::GetCurrent(GetWorld());
	if (NavSystem)
	{
		FNavLocation NavLocation;
		return NavSystem->ProjectPointToNavigation(Location, NavLocation);
	}
	return true;
}
