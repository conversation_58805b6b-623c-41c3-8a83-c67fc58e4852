// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "SLT/Core/Systems/ObjectPoolManager.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeObjectPoolManager() {}

// Begin Cross Module References
COREUOBJECT_API UClass* Z_Construct_UClass_UClass();
COREUOBJECT_API UClass* Z_Construct_UClass_UInterface();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FRotator();
COREUOBJECT_API UScriptStruct* Z_Construct_UScriptStruct_FVector();
ENGINE_API UClass* Z_Construct_UClass_AActor_NoRegister();
ENGINE_API UClass* Z_Construct_UClass_UWorldSubsystem();
SLT_API UClass* Z_Construct_UClass_UObjectPoolManager();
SLT_API UClass* Z_Construct_UClass_UObjectPoolManager_NoRegister();
SLT_API UClass* Z_Construct_UClass_UPoolableActor();
SLT_API UClass* Z_Construct_UClass_UPoolableActor_NoRegister();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature();
SLT_API UFunction* Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FActorPool();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FObjectPoolSettings();
SLT_API UScriptStruct* Z_Construct_UScriptStruct_FPooledActorInfo();
UPackage* Z_Construct_UPackage__Script_SLT();
// End Cross Module References

// Begin Delegate FOnActorPooled
struct Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnActorPooled_Parms
	{
		AActor* Actor;
		bool bWasReturned;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bWasReturned_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bWasReturned;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnActorPooled_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_bWasReturned_SetBit(void* Obj)
{
	((_Script_SLT_eventOnActorPooled_Parms*)Obj)->bWasReturned = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_bWasReturned = { "bWasReturned", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(_Script_SLT_eventOnActorPooled_Parms), &Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_bWasReturned_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::NewProp_bWasReturned,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnActorPooled__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::_Script_SLT_eventOnActorPooled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::_Script_SLT_eventOnActorPooled_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnActorPooled_DelegateWrapper(const FMulticastScriptDelegate& OnActorPooled, AActor* Actor, bool bWasReturned)
{
	struct _Script_SLT_eventOnActorPooled_Parms
	{
		AActor* Actor;
		bool bWasReturned;
	};
	_Script_SLT_eventOnActorPooled_Parms Parms;
	Parms.Actor=Actor;
	Parms.bWasReturned=bWasReturned ? true : false;
	OnActorPooled.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnActorPooled

// Begin Delegate FOnPoolActorSpawned
struct Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics
{
	struct _Script_SLT_eventOnPoolActorSpawned_Parms
	{
		AActor* Actor;
		TSubclassOf<AActor> ActorClass;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPoolActorSpawned_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(_Script_SLT_eventOnPoolActorSpawned_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::NewProp_ActorClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::FuncParams = { (UObject*(*)())Z_Construct_UPackage__Script_SLT, nullptr, "OnPoolActorSpawned__DelegateSignature", nullptr, nullptr, Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::PropPointers), sizeof(Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::_Script_SLT_eventOnPoolActorSpawned_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x00130000, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::Function_MetaDataParams), Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::_Script_SLT_eventOnPoolActorSpawned_Parms) < MAX_uint16);
UFunction* Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature_Statics::FuncParams);
	}
	return ReturnFunction;
}
void FOnPoolActorSpawned_DelegateWrapper(const FMulticastScriptDelegate& OnPoolActorSpawned, AActor* Actor, TSubclassOf<AActor> ActorClass)
{
	struct _Script_SLT_eventOnPoolActorSpawned_Parms
	{
		AActor* Actor;
		TSubclassOf<AActor> ActorClass;
	};
	_Script_SLT_eventOnPoolActorSpawned_Parms Parms;
	Parms.Actor=Actor;
	Parms.ActorClass=ActorClass;
	OnPoolActorSpawned.ProcessMulticastDelegate<UObject>(&Parms);
}
// End Delegate FOnPoolActorSpawned

// Begin ScriptStruct FObjectPoolSettings
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ObjectPoolSettings;
class UScriptStruct* FObjectPoolSettings::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ObjectPoolSettings.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ObjectPoolSettings.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FObjectPoolSettings, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ObjectPoolSettings"));
	}
	return Z_Registration_Info_UScriptStruct_ObjectPoolSettings.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FObjectPoolSettings>()
{
	return FObjectPoolSettings::StaticStruct();
}
struct Z_Construct_UScriptStruct_FObjectPoolSettings_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_InitialPoolSize_MetaData[] = {
		{ "Category", "Pool Settings" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Initial number of objects to create\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Initial number of objects to create" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxPoolSize_MetaData[] = {
		{ "Category", "Pool Settings" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Maximum number of objects in pool\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Maximum number of objects in pool" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bAutoExpand_MetaData[] = {
		{ "Category", "Pool Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Can the pool expand beyond initial size?\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Can the pool expand beyond initial size?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bPrewarmPool_MetaData[] = {
		{ "Category", "Pool Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Should we create initial objects at startup?\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Should we create initial objects at startup?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolExpansionSize_MetaData[] = {
		{ "Category", "Pool Settings" },
		{ "ClampMin", "1" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How many objects to add when expanding\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How many objects to add when expanding" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnablePooling_MetaData[] = {
		{ "Category", "Pool Settings" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Is pooling enabled for this class?\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Is pooling enabled for this class?" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolCleanupInterval_MetaData[] = {
		{ "Category", "Pool Settings" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How often to clean up unused objects (seconds)\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How often to clean up unused objects (seconds)" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_MaxIdleTime_MetaData[] = {
		{ "Category", "Pool Settings" },
		{ "ClampMin", "1.0" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// How long objects can be idle before cleanup (seconds)\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "How long objects can be idle before cleanup (seconds)" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FIntPropertyParams NewProp_InitialPoolSize;
	static const UECodeGen_Private::FIntPropertyParams NewProp_MaxPoolSize;
	static void NewProp_bAutoExpand_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bAutoExpand;
	static void NewProp_bPrewarmPool_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bPrewarmPool;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolExpansionSize;
	static void NewProp_bEnablePooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnablePooling;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PoolCleanupInterval;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_MaxIdleTime;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FObjectPoolSettings>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_InitialPoolSize = { "InitialPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolSettings, InitialPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_InitialPoolSize_MetaData), NewProp_InitialPoolSize_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_MaxPoolSize = { "MaxPoolSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolSettings, MaxPoolSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxPoolSize_MetaData), NewProp_MaxPoolSize_MetaData) };
void Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bAutoExpand_SetBit(void* Obj)
{
	((FObjectPoolSettings*)Obj)->bAutoExpand = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bAutoExpand = { "bAutoExpand", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectPoolSettings), &Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bAutoExpand_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bAutoExpand_MetaData), NewProp_bAutoExpand_MetaData) };
void Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bPrewarmPool_SetBit(void* Obj)
{
	((FObjectPoolSettings*)Obj)->bPrewarmPool = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bPrewarmPool = { "bPrewarmPool", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectPoolSettings), &Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bPrewarmPool_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bPrewarmPool_MetaData), NewProp_bPrewarmPool_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_PoolExpansionSize = { "PoolExpansionSize", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolSettings, PoolExpansionSize), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolExpansionSize_MetaData), NewProp_PoolExpansionSize_MetaData) };
void Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bEnablePooling_SetBit(void* Obj)
{
	((FObjectPoolSettings*)Obj)->bEnablePooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bEnablePooling = { "bEnablePooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FObjectPoolSettings), &Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bEnablePooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnablePooling_MetaData), NewProp_bEnablePooling_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_PoolCleanupInterval = { "PoolCleanupInterval", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolSettings, PoolCleanupInterval), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolCleanupInterval_MetaData), NewProp_PoolCleanupInterval_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_MaxIdleTime = { "MaxIdleTime", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FObjectPoolSettings, MaxIdleTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_MaxIdleTime_MetaData), NewProp_MaxIdleTime_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_InitialPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_MaxPoolSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bAutoExpand,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bPrewarmPool,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_PoolExpansionSize,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_bEnablePooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_PoolCleanupInterval,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewProp_MaxIdleTime,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ObjectPoolSettings",
	Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::PropPointers),
	sizeof(FObjectPoolSettings),
	alignof(FObjectPoolSettings),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FObjectPoolSettings()
{
	if (!Z_Registration_Info_UScriptStruct_ObjectPoolSettings.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ObjectPoolSettings.InnerSingleton, Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ObjectPoolSettings.InnerSingleton;
}
// End ScriptStruct FObjectPoolSettings

// Begin ScriptStruct FPooledActorInfo
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_PooledActorInfo;
class UScriptStruct* FPooledActorInfo::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_PooledActorInfo.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_PooledActorInfo.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FPooledActorInfo, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("PooledActorInfo"));
	}
	return Z_Registration_Info_UScriptStruct_PooledActorInfo.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FPooledActorInfo>()
{
	return FPooledActorInfo::StaticStruct();
}
struct Z_Construct_UScriptStruct_FPooledActorInfo_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Actor_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bIsActive_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_LastUsedTime_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PooledTime_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_UsageCount_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_bIsActive_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bIsActive;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_LastUsedTime;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_PooledTime;
	static const UECodeGen_Private::FIntPropertyParams NewProp_UsageCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FPooledActorInfo>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledActorInfo, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Actor_MetaData), NewProp_Actor_MetaData) };
void Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_bIsActive_SetBit(void* Obj)
{
	((FPooledActorInfo*)Obj)->bIsActive = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_bIsActive = { "bIsActive", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(FPooledActorInfo), &Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_bIsActive_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bIsActive_MetaData), NewProp_bIsActive_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_LastUsedTime = { "LastUsedTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledActorInfo, LastUsedTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_LastUsedTime_MetaData), NewProp_LastUsedTime_MetaData) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_PooledTime = { "PooledTime", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledActorInfo, PooledTime), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PooledTime_MetaData), NewProp_PooledTime_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_UsageCount = { "UsageCount", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FPooledActorInfo, UsageCount), METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_UsageCount_MetaData), NewProp_UsageCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FPooledActorInfo_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_bIsActive,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_LastUsedTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_PooledTime,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewProp_UsageCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledActorInfo_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FPooledActorInfo_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"PooledActorInfo",
	Z_Construct_UScriptStruct_FPooledActorInfo_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledActorInfo_Statics::PropPointers),
	sizeof(FPooledActorInfo),
	alignof(FPooledActorInfo),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FPooledActorInfo_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FPooledActorInfo_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FPooledActorInfo()
{
	if (!Z_Registration_Info_UScriptStruct_PooledActorInfo.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_PooledActorInfo.InnerSingleton, Z_Construct_UScriptStruct_FPooledActorInfo_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_PooledActorInfo.InnerSingleton;
}
// End ScriptStruct FPooledActorInfo

// Begin ScriptStruct FActorPool
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_ActorPool;
class UScriptStruct* FActorPool::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_ActorPool.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_ActorPool.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FActorPool, (UObject*)Z_Construct_UPackage__Script_SLT(), TEXT("ActorPool"));
	}
	return Z_Registration_Info_UScriptStruct_ActorPool.OuterSingleton;
}
template<> SLT_API UScriptStruct* StaticStruct<FActorPool>()
{
	return FActorPool::StaticStruct();
}
struct Z_Construct_UScriptStruct_FActorPool_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorClass_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PooledActors_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActiveActors_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static const UECodeGen_Private::FStructPropertyParams NewProp_PooledActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_PooledActors;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActiveActors_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ActiveActors;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FActorPool>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActorPool, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorClass_MetaData), NewProp_ActorClass_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActorPool, Settings), Z_Construct_UScriptStruct_FObjectPoolSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 3085377380
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_PooledActors_Inner = { "PooledActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPooledActorInfo, METADATA_PARAMS(0, nullptr) }; // 3302117993
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_PooledActors = { "PooledActors", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActorPool, PooledActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PooledActors_MetaData), NewProp_PooledActors_MetaData) }; // 3302117993
const UECodeGen_Private::FStructPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActiveActors_Inner = { "ActiveActors", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UScriptStruct_FPooledActorInfo, METADATA_PARAMS(0, nullptr) }; // 3302117993
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActiveActors = { "ActiveActors", nullptr, (EPropertyFlags)0x0010000000000000, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(FActorPool, ActiveActors), EArrayPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActiveActors_MetaData), NewProp_ActiveActors_MetaData) }; // 3302117993
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UScriptStruct_FActorPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_Settings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_PooledActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_PooledActors,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActiveActors_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UScriptStruct_FActorPool_Statics::NewProp_ActiveActors,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActorPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FActorPool_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
	nullptr,
	&NewStructOps,
	"ActorPool",
	Z_Construct_UScriptStruct_FActorPool_Statics::PropPointers,
	UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActorPool_Statics::PropPointers),
	sizeof(FActorPool),
	alignof(FActorPool),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FActorPool_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FActorPool_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FActorPool()
{
	if (!Z_Registration_Info_UScriptStruct_ActorPool.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_ActorPool.InnerSingleton, Z_Construct_UScriptStruct_FActorPool_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_ActorPool.InnerSingleton;
}
// End ScriptStruct FActorPool

// Begin Interface UPoolableActor Function CanBePooled
struct PoolableActor_eventCanBePooled_Parms
{
	bool ReturnValue;

	/** Constructor, initializes return property only **/
	PoolableActor_eventCanBePooled_Parms()
		: ReturnValue(false)
	{
	}
};
bool IPoolableActor::CanBePooled() const
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_CanBePooled instead.");
	PoolableActor_eventCanBePooled_Parms Parms;
	return Parms.ReturnValue;
}
static FName NAME_UPoolableActor_CanBePooled = FName(TEXT("CanBePooled"));
bool IPoolableActor::Execute_CanBePooled(const UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UPoolableActor::StaticClass()));
	PoolableActor_eventCanBePooled_Parms Parms;
	UFunction* const Func = O->FindFunction(NAME_UPoolableActor_CanBePooled);
	if (Func)
	{
		const_cast<UObject*>(O)->ProcessEvent(Func, &Parms);
	}
	else if (auto I = (const IPoolableActor*)(O->GetNativeInterfaceAddress(UPoolableActor::StaticClass())))
	{
		Parms.ReturnValue = I->CanBePooled_Implementation();
	}
	return Parms.ReturnValue;
}
struct Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Check if actor can be returned to pool\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Check if actor can be returned to pool" },
#endif
	};
#endif // WITH_METADATA
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((PoolableActor_eventCanBePooled_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(PoolableActor_eventCanBePooled_Parms), &Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPoolableActor, nullptr, "CanBePooled", nullptr, nullptr, Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::PropPointers), sizeof(PoolableActor_eventCanBePooled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x5C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::Function_MetaDataParams) };
static_assert(sizeof(PoolableActor_eventCanBePooled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UPoolableActor_CanBePooled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPoolableActor_CanBePooled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IPoolableActor::execCanBePooled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->CanBePooled_Implementation();
	P_NATIVE_END;
}
// End Interface UPoolableActor Function CanBePooled

// Begin Interface UPoolableActor Function OnActorPooled
void IPoolableActor::OnActorPooled()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_OnActorPooled instead.");
}
static FName NAME_UPoolableActor_OnActorPooled = FName(TEXT("OnActorPooled"));
void IPoolableActor::Execute_OnActorPooled(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UPoolableActor::StaticClass()));
	UFunction* const Func = O->FindFunction(NAME_UPoolableActor_OnActorPooled);
	if (Func)
	{
		O->ProcessEvent(Func, NULL);
	}
	else if (auto I = (IPoolableActor*)(O->GetNativeInterfaceAddress(UPoolableActor::StaticClass())))
	{
		I->OnActorPooled_Implementation();
	}
}
struct Z_Construct_UFunction_UPoolableActor_OnActorPooled_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when actor is retrieved from pool\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when actor is retrieved from pool" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPoolableActor_OnActorPooled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPoolableActor, nullptr, "OnActorPooled", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_OnActorPooled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPoolableActor_OnActorPooled_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPoolableActor_OnActorPooled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPoolableActor_OnActorPooled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IPoolableActor::execOnActorPooled)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorPooled_Implementation();
	P_NATIVE_END;
}
// End Interface UPoolableActor Function OnActorPooled

// Begin Interface UPoolableActor Function OnActorReturned
void IPoolableActor::OnActorReturned()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_OnActorReturned instead.");
}
static FName NAME_UPoolableActor_OnActorReturned = FName(TEXT("OnActorReturned"));
void IPoolableActor::Execute_OnActorReturned(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UPoolableActor::StaticClass()));
	UFunction* const Func = O->FindFunction(NAME_UPoolableActor_OnActorReturned);
	if (Func)
	{
		O->ProcessEvent(Func, NULL);
	}
	else if (auto I = (IPoolableActor*)(O->GetNativeInterfaceAddress(UPoolableActor::StaticClass())))
	{
		I->OnActorReturned_Implementation();
	}
}
struct Z_Construct_UFunction_UPoolableActor_OnActorReturned_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called when actor is returned to pool\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called when actor is returned to pool" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPoolableActor_OnActorReturned_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPoolableActor, nullptr, "OnActorReturned", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_OnActorReturned_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPoolableActor_OnActorReturned_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPoolableActor_OnActorReturned()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPoolableActor_OnActorReturned_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IPoolableActor::execOnActorReturned)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->OnActorReturned_Implementation();
	P_NATIVE_END;
}
// End Interface UPoolableActor Function OnActorReturned

// Begin Interface UPoolableActor Function ResetActorState
void IPoolableActor::ResetActorState()
{
	check(0 && "Do not directly call Event functions in Interfaces. Call Execute_ResetActorState instead.");
}
static FName NAME_UPoolableActor_ResetActorState = FName(TEXT("ResetActorState"));
void IPoolableActor::Execute_ResetActorState(UObject* O)
{
	check(O != NULL);
	check(O->GetClass()->ImplementsInterface(UPoolableActor::StaticClass()));
	UFunction* const Func = O->FindFunction(NAME_UPoolableActor_ResetActorState);
	if (Func)
	{
		O->ProcessEvent(Func, NULL);
	}
	else if (auto I = (IPoolableActor*)(O->GetNativeInterfaceAddress(UPoolableActor::StaticClass())))
	{
		I->ResetActorState_Implementation();
	}
}
struct Z_Construct_UFunction_UPoolableActor_ResetActorState_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Called to reset actor state for reuse\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Called to reset actor state for reuse" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UPoolableActor_ResetActorState_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UPoolableActor, nullptr, "ResetActorState", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x0C020C00, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UPoolableActor_ResetActorState_Statics::Function_MetaDataParams), Z_Construct_UFunction_UPoolableActor_ResetActorState_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UPoolableActor_ResetActorState()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UPoolableActor_ResetActorState_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(IPoolableActor::execResetActorState)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ResetActorState_Implementation();
	P_NATIVE_END;
}
// End Interface UPoolableActor Function ResetActorState

// Begin Interface UPoolableActor
void UPoolableActor::StaticRegisterNativesUPoolableActor()
{
	UClass* Class = UPoolableActor::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CanBePooled", &IPoolableActor::execCanBePooled },
		{ "OnActorPooled", &IPoolableActor::execOnActorPooled },
		{ "OnActorReturned", &IPoolableActor::execOnActorReturned },
		{ "ResetActorState", &IPoolableActor::execResetActorState },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UPoolableActor);
UClass* Z_Construct_UClass_UPoolableActor_NoRegister()
{
	return UPoolableActor::StaticClass();
}
struct Z_Construct_UClass_UPoolableActor_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UPoolableActor_CanBePooled, "CanBePooled" }, // 1983828800
		{ &Z_Construct_UFunction_UPoolableActor_OnActorPooled, "OnActorPooled" }, // 1573119794
		{ &Z_Construct_UFunction_UPoolableActor_OnActorReturned, "OnActorReturned" }, // 3151826886
		{ &Z_Construct_UFunction_UPoolableActor_ResetActorState, "ResetActorState" }, // 2909455563
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<IPoolableActor>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
UObject* (*const Z_Construct_UClass_UPoolableActor_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UInterface,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UPoolableActor_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UPoolableActor_Statics::ClassParams = {
	&UPoolableActor::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	nullptr,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	0,
	0,
	0x000840A1u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UPoolableActor_Statics::Class_MetaDataParams), Z_Construct_UClass_UPoolableActor_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UPoolableActor()
{
	if (!Z_Registration_Info_UClass_UPoolableActor.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UPoolableActor.OuterSingleton, Z_Construct_UClass_UPoolableActor_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UPoolableActor.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UPoolableActor>()
{
	return UPoolableActor::StaticClass();
}
UPoolableActor::UPoolableActor(const FObjectInitializer& ObjectInitializer) : Super(ObjectInitializer) {}
DEFINE_VTABLE_PTR_HELPER_CTOR(UPoolableActor);
UPoolableActor::~UPoolableActor() {}
// End Interface UPoolableActor

// Begin Class UObjectPoolManager Function CleanupIdleActors
struct Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pool management\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool management" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "CleanupIdleActors", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execCleanupIdleActors)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->CleanupIdleActors();
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function CleanupIdleActors

// Begin Class UObjectPoolManager Function ClearAllPools
struct Z_Construct_UFunction_UObjectPoolManager_ClearAllPools_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_ClearAllPools_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "ClearAllPools", nullptr, nullptr, nullptr, 0, 0, RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ClearAllPools_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_ClearAllPools_Statics::Function_MetaDataParams) };
UFunction* Z_Construct_UFunction_UObjectPoolManager_ClearAllPools()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_ClearAllPools_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execClearAllPools)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearAllPools();
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function ClearAllPools

// Begin Class UObjectPoolManager Function ClearPool
struct Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics
{
	struct ObjectPoolManager_eventClearPool_Parms
	{
		TSubclassOf<AActor> ActorClass;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventClearPool_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::NewProp_ActorClass,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "ClearPool", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::ObjectPoolManager_eventClearPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::ObjectPoolManager_eventClearPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_ClearPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_ClearPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execClearPool)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->ClearPool(Z_Param_ActorClass);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function ClearPool

// Begin Class UObjectPoolManager Function GetActiveActorCount
struct Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics
{
	struct ObjectPoolManager_eventGetActiveActorCount_Parms
	{
		TSubclassOf<AActor> ActorClass;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetActiveActorCount_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetActiveActorCount_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "GetActiveActorCount", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::ObjectPoolManager_eventGetActiveActorCount_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::ObjectPoolManager_eventGetActiveActorCount_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execGetActiveActorCount)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetActiveActorCount(Z_Param_ActorClass);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function GetActiveActorCount

// Begin Class UObjectPoolManager Function GetPooledActor
struct Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics
{
	struct ObjectPoolManager_eventGetPooledActor_Parms
	{
		TSubclassOf<AActor> ActorClass;
		FVector Location;
		FRotator Rotation;
		AActor* ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Main pool functions\n" },
#endif
		{ "CPP_Default_Location", "" },
		{ "CPP_Default_Rotation", "" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Main pool functions" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Location_MetaData[] = {
		{ "NativeConst", "" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Rotation_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Location;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Rotation;
	static const UECodeGen_Private::FObjectPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPooledActor_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_Location = { "Location", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPooledActor_Parms, Location), Z_Construct_UScriptStruct_FVector, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Location_MetaData), NewProp_Location_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_Rotation = { "Rotation", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPooledActor_Parms, Rotation), Z_Construct_UScriptStruct_FRotator, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Rotation_MetaData), NewProp_Rotation_MetaData) };
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPooledActor_Parms, ReturnValue), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_Location,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_Rotation,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "GetPooledActor", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::ObjectPoolManager_eventGetPooledActor_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04C20401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::ObjectPoolManager_eventGetPooledActor_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_GetPooledActor()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_GetPooledActor_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execGetPooledActor)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_GET_STRUCT_REF(FVector,Z_Param_Out_Location);
	P_GET_STRUCT_REF(FRotator,Z_Param_Out_Rotation);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(AActor**)Z_Param__Result=P_THIS->GetPooledActor(Z_Param_ActorClass,Z_Param_Out_Location,Z_Param_Out_Rotation);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function GetPooledActor

// Begin Class UObjectPoolManager Function GetPoolEfficiency
struct Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics
{
	struct ObjectPoolManager_eventGetPoolEfficiency_Parms
	{
		TSubclassOf<AActor> ActorClass;
		float ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FFloatPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolEfficiency_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FFloatPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Float, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolEfficiency_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "GetPoolEfficiency", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::ObjectPoolManager_eventGetPoolEfficiency_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::ObjectPoolManager_eventGetPoolEfficiency_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execGetPoolEfficiency)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(float*)Z_Param__Result=P_THIS->GetPoolEfficiency(Z_Param_ActorClass);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function GetPoolEfficiency

// Begin Class UObjectPoolManager Function GetPoolSize
struct Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics
{
	struct ObjectPoolManager_eventGetPoolSize_Parms
	{
		TSubclassOf<AActor> ActorClass;
		int32 ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics" },
#endif
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FIntPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolSize_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolSize_Parms, ReturnValue), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "GetPoolSize", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::ObjectPoolManager_eventGetPoolSize_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::ObjectPoolManager_eventGetPoolSize_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_GetPoolSize()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_GetPoolSize_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execGetPoolSize)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(int32*)Z_Param__Result=P_THIS->GetPoolSize(Z_Param_ActorClass);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function GetPoolSize

// Begin Class UObjectPoolManager Function GetPoolStatistics
struct Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics
{
	struct ObjectPoolManager_eventGetPoolStatistics_Parms
	{
		TArray<FString> ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStrPropertyParams NewProp_ReturnValue_Inner;
	static const UECodeGen_Private::FArrayPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FStrPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::NewProp_ReturnValue_Inner = { "ReturnValue", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Str, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FArrayPropertyParams Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Array, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventGetPoolStatistics_Parms, ReturnValue), EArrayPropertyFlags::None, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::NewProp_ReturnValue_Inner,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "GetPoolStatistics", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::ObjectPoolManager_eventGetPoolStatistics_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x54020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::ObjectPoolManager_eventGetPoolStatistics_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execGetPoolStatistics)
{
	P_FINISH;
	P_NATIVE_BEGIN;
	*(TArray<FString>*)Z_Param__Result=P_THIS->GetPoolStatistics();
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function GetPoolStatistics

// Begin Class UObjectPoolManager Function PrewarmPool
struct Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics
{
	struct ObjectPoolManager_eventPrewarmPool_Parms
	{
		TSubclassOf<AActor> ActorClass;
		int32 Count;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "CPP_Default_Count", "-1" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FIntPropertyParams NewProp_Count;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventPrewarmPool_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::NewProp_Count = { "Count", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventPrewarmPool_Parms, Count), METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::NewProp_Count,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "PrewarmPool", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::ObjectPoolManager_eventPrewarmPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::ObjectPoolManager_eventPrewarmPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_PrewarmPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_PrewarmPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execPrewarmPool)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_GET_PROPERTY(FIntProperty,Z_Param_Count);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->PrewarmPool(Z_Param_ActorClass,Z_Param_Count);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function PrewarmPool

// Begin Class UObjectPoolManager Function RegisterPoolConfiguration
struct Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics
{
	struct ObjectPoolManager_eventRegisterPoolConfiguration_Parms
	{
		TSubclassOf<AActor> ActorClass;
		FObjectPoolSettings Settings;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_Settings_MetaData[] = {
		{ "NativeConst", "" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorClass;
	static const UECodeGen_Private::FStructPropertyParams NewProp_Settings;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FClassPropertyParams Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::NewProp_ActorClass = { "ActorClass", nullptr, (EPropertyFlags)0x0014000000000080, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventRegisterPoolConfiguration_Parms, ActorClass), Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::NewProp_Settings = { "Settings", nullptr, (EPropertyFlags)0x0010000008000182, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventRegisterPoolConfiguration_Parms, Settings), Z_Construct_UScriptStruct_FObjectPoolSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_Settings_MetaData), NewProp_Settings_MetaData) }; // 3085377380
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::NewProp_ActorClass,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::NewProp_Settings,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "RegisterPoolConfiguration", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::ObjectPoolManager_eventRegisterPoolConfiguration_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04420401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::ObjectPoolManager_eventRegisterPoolConfiguration_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execRegisterPoolConfiguration)
{
	P_GET_OBJECT(UClass,Z_Param_ActorClass);
	P_GET_STRUCT_REF(FObjectPoolSettings,Z_Param_Out_Settings);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->RegisterPoolConfiguration(Z_Param_ActorClass,Z_Param_Out_Settings);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function RegisterPoolConfiguration

// Begin Class UObjectPoolManager Function ReturnActorToPool
struct Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics
{
	struct ObjectPoolManager_eventReturnActorToPool_Parms
	{
		AActor* Actor;
		bool ReturnValue;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FObjectPropertyParams NewProp_Actor;
	static void NewProp_ReturnValue_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_ReturnValue;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
const UECodeGen_Private::FObjectPropertyParams Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_Actor = { "Actor", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Object, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(ObjectPoolManager_eventReturnActorToPool_Parms, Actor), Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
void Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_ReturnValue_SetBit(void* Obj)
{
	((ObjectPoolManager_eventReturnActorToPool_Parms*)Obj)->ReturnValue = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_ReturnValue = { "ReturnValue", nullptr, (EPropertyFlags)0x0010000000000580, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ObjectPoolManager_eventReturnActorToPool_Parms), &Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_ReturnValue_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_Actor,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::NewProp_ReturnValue,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "ReturnActorToPool", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::ObjectPoolManager_eventReturnActorToPool_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::ObjectPoolManager_eventReturnActorToPool_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execReturnActorToPool)
{
	P_GET_OBJECT(AActor,Z_Param_Actor);
	P_FINISH;
	P_NATIVE_BEGIN;
	*(bool*)Z_Param__Result=P_THIS->ReturnActorToPool(Z_Param_Actor);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function ReturnActorToPool

// Begin Class UObjectPoolManager Function SetPoolingEnabled
struct Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics
{
	struct ObjectPoolManager_eventSetPoolingEnabled_Parms
	{
		bool bEnabled;
	};
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Function_MetaDataParams[] = {
		{ "Category", "Object Pool" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static void NewProp_bEnabled_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnabled;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static const UECodeGen_Private::FFunctionParams FuncParams;
};
void Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::NewProp_bEnabled_SetBit(void* Obj)
{
	((ObjectPoolManager_eventSetPoolingEnabled_Parms*)Obj)->bEnabled = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::NewProp_bEnabled = { "bEnabled", nullptr, (EPropertyFlags)0x0010000000000080, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(ObjectPoolManager_eventSetPoolingEnabled_Parms), &Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::NewProp_bEnabled_SetBit, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::NewProp_bEnabled,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::PropPointers) < 2048);
const UECodeGen_Private::FFunctionParams Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::FuncParams = { (UObject*(*)())Z_Construct_UClass_UObjectPoolManager, nullptr, "SetPoolingEnabled", nullptr, nullptr, Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::PropPointers, UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::PropPointers), sizeof(Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::ObjectPoolManager_eventSetPoolingEnabled_Parms), RF_Public|RF_Transient|RF_MarkAsNative, (EFunctionFlags)0x04020401, 0, 0, METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::Function_MetaDataParams), Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::Function_MetaDataParams) };
static_assert(sizeof(Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::ObjectPoolManager_eventSetPoolingEnabled_Parms) < MAX_uint16);
UFunction* Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled()
{
	static UFunction* ReturnFunction = nullptr;
	if (!ReturnFunction)
	{
		UECodeGen_Private::ConstructUFunction(&ReturnFunction, Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled_Statics::FuncParams);
	}
	return ReturnFunction;
}
DEFINE_FUNCTION(UObjectPoolManager::execSetPoolingEnabled)
{
	P_GET_UBOOL(Z_Param_bEnabled);
	P_FINISH;
	P_NATIVE_BEGIN;
	P_THIS->SetPoolingEnabled(Z_Param_bEnabled);
	P_NATIVE_END;
}
// End Class UObjectPoolManager Function SetPoolingEnabled

// Begin Class UObjectPoolManager
void UObjectPoolManager::StaticRegisterNativesUObjectPoolManager()
{
	UClass* Class = UObjectPoolManager::StaticClass();
	static const FNameNativePtrPair Funcs[] = {
		{ "CleanupIdleActors", &UObjectPoolManager::execCleanupIdleActors },
		{ "ClearAllPools", &UObjectPoolManager::execClearAllPools },
		{ "ClearPool", &UObjectPoolManager::execClearPool },
		{ "GetActiveActorCount", &UObjectPoolManager::execGetActiveActorCount },
		{ "GetPooledActor", &UObjectPoolManager::execGetPooledActor },
		{ "GetPoolEfficiency", &UObjectPoolManager::execGetPoolEfficiency },
		{ "GetPoolSize", &UObjectPoolManager::execGetPoolSize },
		{ "GetPoolStatistics", &UObjectPoolManager::execGetPoolStatistics },
		{ "PrewarmPool", &UObjectPoolManager::execPrewarmPool },
		{ "RegisterPoolConfiguration", &UObjectPoolManager::execRegisterPoolConfiguration },
		{ "ReturnActorToPool", &UObjectPoolManager::execReturnActorToPool },
		{ "SetPoolingEnabled", &UObjectPoolManager::execSetPoolingEnabled },
	};
	FNativeFunctionRegistrar::RegisterFunctions(Class, Funcs, UE_ARRAY_COUNT(Funcs));
}
IMPLEMENT_CLASS_NO_AUTO_REGISTRATION(UObjectPoolManager);
UClass* Z_Construct_UClass_UObjectPoolManager_NoRegister()
{
	return UObjectPoolManager::StaticClass();
}
struct Z_Construct_UClass_UObjectPoolManager_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Class_MetaDataParams[] = {
		{ "BlueprintType", "true" },
		{ "IncludePath", "Core/Systems/ObjectPoolManager.h" },
		{ "IsBlueprintBase", "true" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolConfigurations_MetaData[] = {
		{ "Category", "Pool Configuration" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Pool configuration\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Pool configuration" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_bEnableGlobalPooling_MetaData[] = {
		{ "Category", "Pool Configuration" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_DefaultPoolSettings_MetaData[] = {
		{ "Category", "Pool Configuration" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorPooled_MetaData[] = {
		{ "Category", "Events" },
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Events\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Events" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_OnActorSpawned_MetaData[] = {
		{ "Category", "Events" },
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_ActorPools_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Internal pool storage\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Internal pool storage" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolHitCount_MetaData[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Statistics tracking\n" },
#endif
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Statistics tracking" },
#endif
	};
	static constexpr UECodeGen_Private::FMetaDataPairParam NewProp_PoolMissCount_MetaData[] = {
		{ "ModuleRelativePath", "Core/Systems/ObjectPoolManager.h" },
	};
#endif // WITH_METADATA
	static const UECodeGen_Private::FStructPropertyParams NewProp_PoolConfigurations_ValueProp;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PoolConfigurations_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PoolConfigurations;
	static void NewProp_bEnableGlobalPooling_SetBit(void* Obj);
	static const UECodeGen_Private::FBoolPropertyParams NewProp_bEnableGlobalPooling;
	static const UECodeGen_Private::FStructPropertyParams NewProp_DefaultPoolSettings;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorPooled;
	static const UECodeGen_Private::FMulticastDelegatePropertyParams NewProp_OnActorSpawned;
	static const UECodeGen_Private::FStructPropertyParams NewProp_ActorPools_ValueProp;
	static const UECodeGen_Private::FClassPropertyParams NewProp_ActorPools_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_ActorPools;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolHitCount_ValueProp;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PoolHitCount_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PoolHitCount;
	static const UECodeGen_Private::FIntPropertyParams NewProp_PoolMissCount_ValueProp;
	static const UECodeGen_Private::FClassPropertyParams NewProp_PoolMissCount_Key_KeyProp;
	static const UECodeGen_Private::FMapPropertyParams NewProp_PoolMissCount;
	static const UECodeGen_Private::FPropertyParamsBase* const PropPointers[];
	static UObject* (*const DependentSingletons[])();
	static constexpr FClassFunctionLinkInfo FuncInfo[] = {
		{ &Z_Construct_UFunction_UObjectPoolManager_CleanupIdleActors, "CleanupIdleActors" }, // 2819802509
		{ &Z_Construct_UFunction_UObjectPoolManager_ClearAllPools, "ClearAllPools" }, // 750870544
		{ &Z_Construct_UFunction_UObjectPoolManager_ClearPool, "ClearPool" }, // 2694854375
		{ &Z_Construct_UFunction_UObjectPoolManager_GetActiveActorCount, "GetActiveActorCount" }, // 2191928024
		{ &Z_Construct_UFunction_UObjectPoolManager_GetPooledActor, "GetPooledActor" }, // 3581395939
		{ &Z_Construct_UFunction_UObjectPoolManager_GetPoolEfficiency, "GetPoolEfficiency" }, // 3162653291
		{ &Z_Construct_UFunction_UObjectPoolManager_GetPoolSize, "GetPoolSize" }, // 4065513561
		{ &Z_Construct_UFunction_UObjectPoolManager_GetPoolStatistics, "GetPoolStatistics" }, // 3619940808
		{ &Z_Construct_UFunction_UObjectPoolManager_PrewarmPool, "PrewarmPool" }, // 92121039
		{ &Z_Construct_UFunction_UObjectPoolManager_RegisterPoolConfiguration, "RegisterPoolConfiguration" }, // 2756033349
		{ &Z_Construct_UFunction_UObjectPoolManager_ReturnActorToPool, "ReturnActorToPool" }, // 2813504996
		{ &Z_Construct_UFunction_UObjectPoolManager_SetPoolingEnabled, "SetPoolingEnabled" }, // 458280160
	};
	static_assert(UE_ARRAY_COUNT(FuncInfo) < 2048);
	static constexpr FCppClassTypeInfoStatic StaticCppClassTypeInfo = {
		TCppClassTypeTraits<UObjectPoolManager>::IsAbstract,
	};
	static const UECodeGen_Private::FClassParams ClassParams;
};
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations_ValueProp = { "PoolConfigurations", nullptr, (EPropertyFlags)0x0000000000000001, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FObjectPoolSettings, METADATA_PARAMS(0, nullptr) }; // 3085377380
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations_Key_KeyProp = { "PoolConfigurations_Key", nullptr, (EPropertyFlags)0x0004000000000001, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations = { "PoolConfigurations", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, PoolConfigurations), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolConfigurations_MetaData), NewProp_PoolConfigurations_MetaData) }; // 3085377380
void Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_bEnableGlobalPooling_SetBit(void* Obj)
{
	((UObjectPoolManager*)Obj)->bEnableGlobalPooling = 1;
}
const UECodeGen_Private::FBoolPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_bEnableGlobalPooling = { "bEnableGlobalPooling", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Bool | UECodeGen_Private::EPropertyGenFlags::NativeBool, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, sizeof(bool), sizeof(UObjectPoolManager), &Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_bEnableGlobalPooling_SetBit, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_bEnableGlobalPooling_MetaData), NewProp_bEnableGlobalPooling_MetaData) };
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_DefaultPoolSettings = { "DefaultPoolSettings", nullptr, (EPropertyFlags)0x0010000000000005, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, DefaultPoolSettings), Z_Construct_UScriptStruct_FObjectPoolSettings, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_DefaultPoolSettings_MetaData), NewProp_DefaultPoolSettings_MetaData) }; // 3085377380
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_OnActorPooled = { "OnActorPooled", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, OnActorPooled), Z_Construct_UDelegateFunction_SLT_OnActorPooled__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorPooled_MetaData), NewProp_OnActorPooled_MetaData) }; // 625916554
const UECodeGen_Private::FMulticastDelegatePropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_OnActorSpawned = { "OnActorSpawned", nullptr, (EPropertyFlags)0x0010000010080000, UECodeGen_Private::EPropertyGenFlags::InlineMulticastDelegate, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, OnActorSpawned), Z_Construct_UDelegateFunction_SLT_OnPoolActorSpawned__DelegateSignature, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_OnActorSpawned_MetaData), NewProp_OnActorSpawned_MetaData) }; // 3203823536
const UECodeGen_Private::FStructPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools_ValueProp = { "ActorPools", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Struct, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, Z_Construct_UScriptStruct_FActorPool, METADATA_PARAMS(0, nullptr) }; // 453530955
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools_Key_KeyProp = { "ActorPools_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools = { "ActorPools", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, ActorPools), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_ActorPools_MetaData), NewProp_ActorPools_MetaData) }; // 453530955
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount_ValueProp = { "PoolHitCount", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount_Key_KeyProp = { "PoolHitCount_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount = { "PoolHitCount", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, PoolHitCount), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolHitCount_MetaData), NewProp_PoolHitCount_MetaData) };
const UECodeGen_Private::FIntPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount_ValueProp = { "PoolMissCount", nullptr, (EPropertyFlags)0x0000000000000000, UECodeGen_Private::EPropertyGenFlags::Int, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 1, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FClassPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount_Key_KeyProp = { "PoolMissCount_Key", nullptr, (EPropertyFlags)0x0004000000000000, UECodeGen_Private::EPropertyGenFlags::Class, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, 0, Z_Construct_UClass_UClass, Z_Construct_UClass_AActor_NoRegister, METADATA_PARAMS(0, nullptr) };
const UECodeGen_Private::FMapPropertyParams Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount = { "PoolMissCount", nullptr, (EPropertyFlags)0x0020080000000000, UECodeGen_Private::EPropertyGenFlags::Map, RF_Public|RF_Transient|RF_MarkAsNative, nullptr, nullptr, 1, STRUCT_OFFSET(UObjectPoolManager, PoolMissCount), EMapPropertyFlags::None, METADATA_PARAMS(UE_ARRAY_COUNT(NewProp_PoolMissCount_MetaData), NewProp_PoolMissCount_MetaData) };
const UECodeGen_Private::FPropertyParamsBase* const Z_Construct_UClass_UObjectPoolManager_Statics::PropPointers[] = {
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolConfigurations,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_bEnableGlobalPooling,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_DefaultPoolSettings,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_OnActorPooled,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_OnActorSpawned,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_ActorPools,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolHitCount,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount_ValueProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount_Key_KeyProp,
	(const UECodeGen_Private::FPropertyParamsBase*)&Z_Construct_UClass_UObjectPoolManager_Statics::NewProp_PoolMissCount,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UObjectPoolManager_Statics::PropPointers) < 2048);
UObject* (*const Z_Construct_UClass_UObjectPoolManager_Statics::DependentSingletons[])() = {
	(UObject* (*)())Z_Construct_UClass_UWorldSubsystem,
	(UObject* (*)())Z_Construct_UPackage__Script_SLT,
};
static_assert(UE_ARRAY_COUNT(Z_Construct_UClass_UObjectPoolManager_Statics::DependentSingletons) < 16);
const UECodeGen_Private::FClassParams Z_Construct_UClass_UObjectPoolManager_Statics::ClassParams = {
	&UObjectPoolManager::StaticClass,
	nullptr,
	&StaticCppClassTypeInfo,
	DependentSingletons,
	FuncInfo,
	Z_Construct_UClass_UObjectPoolManager_Statics::PropPointers,
	nullptr,
	UE_ARRAY_COUNT(DependentSingletons),
	UE_ARRAY_COUNT(FuncInfo),
	UE_ARRAY_COUNT(Z_Construct_UClass_UObjectPoolManager_Statics::PropPointers),
	0,
	0x009000A0u,
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UClass_UObjectPoolManager_Statics::Class_MetaDataParams), Z_Construct_UClass_UObjectPoolManager_Statics::Class_MetaDataParams)
};
UClass* Z_Construct_UClass_UObjectPoolManager()
{
	if (!Z_Registration_Info_UClass_UObjectPoolManager.OuterSingleton)
	{
		UECodeGen_Private::ConstructUClass(Z_Registration_Info_UClass_UObjectPoolManager.OuterSingleton, Z_Construct_UClass_UObjectPoolManager_Statics::ClassParams);
	}
	return Z_Registration_Info_UClass_UObjectPoolManager.OuterSingleton;
}
template<> SLT_API UClass* StaticClass<UObjectPoolManager>()
{
	return UObjectPoolManager::StaticClass();
}
DEFINE_VTABLE_PTR_HELPER_CTOR(UObjectPoolManager);
UObjectPoolManager::~UObjectPoolManager() {}
// End Class UObjectPoolManager

// Begin Registration
struct Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FObjectPoolSettings::StaticStruct, Z_Construct_UScriptStruct_FObjectPoolSettings_Statics::NewStructOps, TEXT("ObjectPoolSettings"), &Z_Registration_Info_UScriptStruct_ObjectPoolSettings, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FObjectPoolSettings), 3085377380U) },
		{ FPooledActorInfo::StaticStruct, Z_Construct_UScriptStruct_FPooledActorInfo_Statics::NewStructOps, TEXT("PooledActorInfo"), &Z_Registration_Info_UScriptStruct_PooledActorInfo, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FPooledActorInfo), 3302117993U) },
		{ FActorPool::StaticStruct, Z_Construct_UScriptStruct_FActorPool_Statics::NewStructOps, TEXT("ActorPool"), &Z_Registration_Info_UScriptStruct_ActorPool, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FActorPool), 453530955U) },
	};
	static constexpr FClassRegisterCompiledInInfo ClassInfo[] = {
		{ Z_Construct_UClass_UPoolableActor, UPoolableActor::StaticClass, TEXT("UPoolableActor"), &Z_Registration_Info_UClass_UPoolableActor, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UPoolableActor), 3231275363U) },
		{ Z_Construct_UClass_UObjectPoolManager, UObjectPoolManager::StaticClass, TEXT("UObjectPoolManager"), &Z_Registration_Info_UClass_UObjectPoolManager, CONSTRUCT_RELOAD_VERSION_INFO(FClassReloadVersionInfo, sizeof(UObjectPoolManager), 275475846U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_2368602158(TEXT("/Script/SLT"),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_Statics::ClassInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_Statics::ClassInfo),
	Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_SLT_Source_SLT_Core_Systems_ObjectPoolManager_h_Statics::ScriptStructInfo),
	nullptr, 0);
// End Registration
PRAGMA_ENABLE_DEPRECATION_WARNINGS
